# 🔐 GitHub Secrets Setup Guide

This guide explains how to configure all required secrets for the BidBees CI/CD pipeline to support million-user scalability.

## Prerequisites

- GitHub CLI (`gh`) installed and authenticated
- Access to your AWS account credentials
- Supabase project credentials
- Third-party service API keys

## Required Secrets

### 🔧 AWS Configuration

| Secret Name | Description | Example Value |
|-------------|-------------|---------------|
| `AWS_ACCESS_KEY_ID` | AWS Access Key for deployment | `AKIAIOSFODNN7EXAMPLE` |
| `AWS_SECRET_ACCESS_KEY` | AWS Secret Access Key | `wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY` |
| `AWS_REGION` | AWS region for deployment | `us-east-1` |
| `AWS_ACCOUNT_ID` | Your AWS account ID | `************` |

### 🗄️ Database Configuration

| Secret Name | Description | Notes |
|-------------|-------------|-------|
| `DB_PASSWORD` | PostgreSQL master password | Use strong password (20+ chars) |
| `DOCDB_PASSWORD` | DocumentDB master password | Use strong password (20+ chars) |
| `RDS_ENDPOINT` | RDS instance endpoint | Auto-populated after infrastructure deployment |
| `REDIS_ENDPOINT` | ElastiCache Redis endpoint | Auto-populated after infrastructure deployment |

### 🔑 Supabase Configuration

| Secret Name | Description | Value Location |
|-------------|-------------|----------------|
| `SUPABASE_URL` | Your Supabase project URL | Supabase Dashboard → Settings → API |
| `SUPABASE_ANON_KEY` | Supabase anonymous key | Supabase Dashboard → Settings → API |
| `SUPABASE_SERVICE_KEY` | Supabase service role key | Supabase Dashboard → Settings → API |

### 🗺️ Third-Party Services

| Secret Name | Description | Provider |
|-------------|-------------|----------|
| `MAPBOX_ACCESS_TOKEN` | Mapbox API token for maps | Mapbox Dashboard → Account → Access Tokens |
| `JWT_SECRET` | JWT signing secret | Generate with `openssl rand -base64 32` |

### 📊 Optional Monitoring & Security

| Secret Name | Description | Required For |
|-------------|-------------|--------------|
| `SLACK_WEBHOOK_URL` | Slack notifications | Team alerts |
| `SNYK_TOKEN` | Snyk security scanning | Vulnerability scanning |
| `INFRACOST_API_KEY` | Cost estimation | Infrastructure cost analysis |
| `GTMETRIX_EMAIL` | GTmetrix performance testing | Performance monitoring |
| `GTMETRIX_API_KEY` | GTmetrix API key | Performance monitoring |

### 🔄 Infrastructure Outputs (Auto-populated)

These secrets are automatically populated after infrastructure deployment:

| Secret Name | Description | Source |
|-------------|-------------|--------|
| `ALB_DNS_NAME` | Application Load Balancer DNS | Terraform output |
| `ALB_LISTENER_ARN` | ALB listener ARN | Terraform output |
| `CLOUDFRONT_DISTRIBUTION_ID` | CloudFront distribution ID | Terraform output |
| `ECS_CLUSTER_NAME` | ECS cluster name | Terraform output |

## Setup Methods

### Method 1: Automated Script (Recommended)

```bash
# Make the script executable
chmod +x scripts/setup-github-secrets.sh

# Run the setup script
./scripts/setup-github-secrets.sh
```

### Method 2: Manual Setup via GitHub CLI

```bash
# AWS Configuration
gh secret set AWS_ACCESS_KEY_ID --body "your-access-key-id"
gh secret set AWS_SECRET_ACCESS_KEY --body "your-secret-access-key"
gh secret set AWS_REGION --body "us-east-1"
gh secret set AWS_ACCOUNT_ID --body "your-account-id"

# Database passwords
gh secret set DB_PASSWORD --body "your-postgres-password"
gh secret set DOCDB_PASSWORD --body "your-docdb-password"

# Supabase configuration
gh secret set SUPABASE_URL --body "https://your-project.supabase.co"
gh secret set SUPABASE_ANON_KEY --body "your-anon-key"
gh secret set SUPABASE_SERVICE_KEY --body "your-service-key"

# Third-party services
gh secret set MAPBOX_ACCESS_TOKEN --body "your-mapbox-token"
gh secret set JWT_SECRET --body "$(openssl rand -base64 32)"

# Optional monitoring
gh secret set SLACK_WEBHOOK_URL --body "https://hooks.slack.com/..."
gh secret set SNYK_TOKEN --body "your-snyk-token"
gh secret set INFRACOST_API_KEY --body "your-infracost-key"
```

### Method 3: GitHub Web Interface

1. Go to your repository on GitHub
2. Navigate to **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret**
4. Add each secret from the tables above

## Security Best Practices

### 🔒 Secret Management

- **Rotate secrets regularly** (every 90 days minimum)
- **Use strong passwords** (20+ characters, mixed case, numbers, symbols)
- **Limit access** to secrets to essential team members only
- **Monitor usage** through AWS CloudTrail and GitHub audit logs

### 🚨 Security Monitoring

- Enable AWS CloudTrail for API call monitoring
- Set up alerts for unusual AWS activity
- Monitor GitHub repository access logs
- Use Snyk token for automated vulnerability scanning

### 🔄 Secret Rotation Process

1. Generate new credentials in the source system
2. Update the secret in GitHub
3. Trigger a deployment to use new credentials
4. Verify the system works with new credentials
5. Revoke old credentials

## Environment-Specific Configuration

### Production Secrets

```bash
# Production-specific overrides
gh secret set ENVIRONMENT --body "production"
gh secret set DB_INSTANCE_CLASS --body "db.r6g.2xlarge"
gh secret set ENABLE_DELETION_PROTECTION --body "true"
```

### Staging Secrets

```bash
# Staging-specific overrides
gh secret set ENVIRONMENT --body "staging"
gh secret set DB_INSTANCE_CLASS --body "db.t3.medium"
gh secret set ENABLE_DELETION_PROTECTION --body "false"
```

## Verification

After setting up secrets, verify they're configured correctly:

```bash
# List all secrets (values are hidden)
gh secret list

# Test AWS credentials
aws sts get-caller-identity

# Test Supabase connection
curl -H "apikey: $SUPABASE_ANON_KEY" "$SUPABASE_URL/rest/v1/"
```

## Troubleshooting

### Common Issues

1. **AWS credentials not working**
   - Verify IAM user has necessary permissions
   - Check for typos in access key/secret
   - Ensure account ID is correct

2. **Database connection failures**
   - Verify passwords don't contain special characters that need escaping
   - Check security group configurations
   - Ensure database is accessible from ECS tasks

3. **Supabase connection issues**
   - Verify project URL format (https://your-project.supabase.co)
   - Check that API keys are copied completely
   - Ensure Supabase project is active

4. **GitHub Actions failures**
   - Check workflow logs for specific error messages
   - Verify all required secrets are set
   - Ensure secrets don't contain extra whitespace

### Getting Help

- **GitHub Actions logs**: Check the Actions tab in your repository
- **AWS CloudWatch logs**: Monitor deployment and runtime logs
- **Supabase logs**: Check the Logs section in Supabase dashboard
- **Community support**: Create an issue in the repository

## Cost Monitoring

Set up budget alerts to monitor costs:

```bash
# Set cost alert threshold
gh secret set MONTHLY_BUDGET_LIMIT --body "1000"  # $1000 USD
gh secret set COST_ALERT_EMAIL --body "<EMAIL>"
```

## Next Steps

1. ✅ Complete secret setup
2. ✅ Verify all secrets are configured
3. ✅ Run the deployment script
4. ✅ Monitor the GitHub Actions workflows
5. ✅ Check AWS resources are created correctly
6. ✅ Test the deployed application
7. ✅ Set up monitoring and alerting
8. ✅ Configure backup procedures

---

**⚠️ Security Notice**: Never commit secrets to your repository. Always use GitHub Secrets or environment variables for sensitive information.