# Frontend Environment Variables for Production
VITE_SUPABASE_URL=https://uvksgkpxeyyssvdsxbts.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzU4NDIsImV4cCI6MjA2MTYxMTg0Mn0.AF1fLlULlM-_NUJYFEL092WETAXvpKKpawUsOidHQ70

# API Configuration - Updated for CloudFront
VITE_API_URL=https://d58ser5n68qmv.cloudfront.net/api
VITE_MICROSERVICES_URL=https://d58ser5n68qmv.cloudfront.net
VITE_NEW_ENDPOINTS=auth,users,tenders,payments,courier,analytics,notifications

# Environment
VITE_NODE_ENV=production
VITE_APP_VERSION=1.0.0

# Mapbox (if needed for MapCard)
VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_TRACKING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Sentry (for error tracking)
VITE_SENTRY_DSN=your_sentry_dsn_here