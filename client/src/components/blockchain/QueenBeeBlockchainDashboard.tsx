import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Shield, 
  Link, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  FileText, 
  Hash,
  Database,
  Zap,
  Eye,
  Download,
  Verified
} from 'lucide-react';
import { supabase } from '@/lib/supabaseClient';
import { sentry } from '@/lib/sentry';

interface AnchorBatch {
  batch_id: string;
  merkle_root: string;
  anchor_networks: string[];
  batch_size: number;
  status: 'pending' | 'anchored' | 'failed';
  created_at: string;
  anchored_at?: string;
  transaction_hashes: Record<string, string>;
  verification_urls: Record<string, string>;
}

interface AnchorProof {
  proof_id: string;
  batch_id: string;
  tender_id: string;
  data_hash: string;
  merkle_path: string[];
  anchor_networks: string[];
  proof_data: any;
  verification_status: 'valid' | 'invalid' | 'pending';
  created_at: string;
  verified_at?: string;
}

interface QueenBeeLog {
  log_id: string;
  operation_type: 'anchor' | 'verify' | 'batch' | 'proof';
  status: 'success' | 'error' | 'pending';
  message: string;
  metadata: any;
  created_at: string;
}

export function QueenBeeBlockchainDashboard() {
  const [batches, setBatches] = useState<AnchorBatch[]>([]);
  const [proofs, setProofs] = useState<AnchorProof[]>([]);
  const [logs, setLogs] = useState<QueenBeeLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProof, setSelectedProof] = useState<AnchorProof | null>(null);

  useEffect(() => {
    loadBlockchainData();
  }, []);

  const loadBlockchainData = async () => {
    try {
      setLoading(true);
      
      // Load anchor batches
      const { data: batchData, error: batchError } = await supabase
        .from('queenbee_anchor_batches')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);

      if (batchError) throw batchError;

      // Load anchor proofs
      const { data: proofData, error: proofError } = await supabase
        .from('queenbee_anchor_proofs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      if (proofError) throw proofError;

      // Load QueenBee logs
      const { data: logData, error: logError } = await supabase
        .from('queen_bee_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      if (logError) throw logError;

      setBatches(batchData || []);
      setProofs(proofData || []);
      setLogs(logData || []);

      // Track blockchain dashboard view
      sentry.captureBusinessEvent('queenbee_dashboard_viewed', {
        batches_count: batchData?.length || 0,
        proofs_count: proofData?.length || 0,
        anchored_batches: batchData?.filter(b => b.status === 'anchored').length || 0
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load blockchain data';
      setError(errorMessage);
      sentry.captureException(err as Error, {
        tags: { component: 'queenbee_dashboard' }
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'anchored': case 'valid': case 'success': 
        return 'bg-green-100 text-green-800';
      case 'pending': 
        return 'bg-yellow-100 text-yellow-800';
      case 'failed': case 'invalid': case 'error': 
        return 'bg-red-100 text-red-800';
      default: 
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'anchored': case 'valid': case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'failed': case 'invalid': case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatHash = (hash: string) => {
    if (!hash) return 'N/A';
    return `${hash.substring(0, 8)}...${hash.substring(hash.length - 8)}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  const anchoredBatches = batches.filter(b => b.status === 'anchored');
  const validProofs = proofs.filter(p => p.verification_status === 'valid');
  const recentErrors = logs.filter(l => l.status === 'error' && 
    new Date(l.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000));

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">QueenBee Blockchain</h1>
          <p className="text-gray-600">Blockchain anchoring and cryptographic proof management</p>
        </div>
        <Button onClick={loadBlockchainData} variant="outline">
          <Shield className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Anchor Batches</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{batches.length}</div>
            <p className="text-xs text-muted-foreground">
              {anchoredBatches.length} anchored
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valid Proofs</CardTitle>
            <Verified className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{validProofs.length}</div>
            <p className="text-xs text-muted-foreground">
              Out of {proofs.length} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Networks</CardTitle>
            <Link className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              Polygon, Hedera, TSA
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Errors</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{recentErrors.length}</div>
            <p className="text-xs text-muted-foreground">
              Last 24 hours
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="batches" className="space-y-4">
        <TabsList>
          <TabsTrigger value="batches">Anchor Batches</TabsTrigger>
          <TabsTrigger value="proofs">Cryptographic Proofs</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="batches" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Anchor Batches
              </CardTitle>
              <CardDescription>
                Merkle tree batches anchored to blockchain networks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {batches.map((batch) => (
                  <div key={batch.batch_id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(batch.status)}
                        <div>
                          <h3 className="font-semibold">Batch {batch.batch_id.substring(0, 8)}</h3>
                          <p className="text-sm text-gray-600">
                            {new Date(batch.created_at).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(batch.status)}>
                        {batch.status}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <p className="text-sm text-gray-600">Merkle Root</p>
                        <div className="flex items-center gap-2">
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {formatHash(batch.merkle_root)}
                          </code>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(batch.merkle_root)}
                          >
                            <Hash className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600">Batch Size</p>
                        <p className="font-semibold">{batch.batch_size} items</p>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600">Networks</p>
                        <div className="flex flex-wrap gap-1">
                          {batch.anchor_networks.map((network, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {network}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    {batch.status === 'anchored' && (
                      <div className="border-t pt-3">
                        <p className="text-sm font-medium mb-2">Transaction Hashes</p>
                        <div className="space-y-1">
                          {Object.entries(batch.transaction_hashes).map(([network, hash]) => (
                            <div key={network} className="flex items-center justify-between">
                              <span className="text-sm capitalize">{network}:</span>
                              <div className="flex items-center gap-2">
                                <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                                  {formatHash(hash)}
                                </code>
                                {batch.verification_urls[network] && (
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => window.open(batch.verification_urls[network], '_blank')}
                                  >
                                    <Eye className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="proofs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Verified className="h-5 w-5" />
                Cryptographic Proofs
              </CardTitle>
              <CardDescription>
                Individual tender data proofs and verification status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {proofs.map((proof) => (
                  <div key={proof.proof_id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(proof.verification_status)}
                        <div>
                          <h3 className="font-semibold">Tender #{proof.tender_id}</h3>
                          <p className="text-sm text-gray-600">
                            Proof ID: {proof.proof_id.substring(0, 12)}...
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(proof.verification_status)}>
                          {proof.verification_status}
                        </Badge>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 mr-1" />
                              Details
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Proof Details - Tender #{proof.tender_id}</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium mb-1">Proof ID</h4>
                                  <code className="text-xs bg-gray-100 p-2 rounded block">
                                    {proof.proof_id}
                                  </code>
                                </div>
                                <div>
                                  <h4 className="font-medium mb-1">Batch ID</h4>
                                  <code className="text-xs bg-gray-100 p-2 rounded block">
                                    {proof.batch_id}
                                  </code>
                                </div>
                              </div>
                              
                              <div>
                                <h4 className="font-medium mb-1">Data Hash</h4>
                                <code className="text-xs bg-gray-100 p-2 rounded block">
                                  {proof.data_hash}
                                </code>
                              </div>
                              
                              <div>
                                <h4 className="font-medium mb-1">Merkle Path</h4>
                                <div className="max-h-32 overflow-y-auto">
                                  {proof.merkle_path.map((hash, idx) => (
                                    <code key={idx} className="text-xs bg-gray-100 p-1 rounded block mb-1">
                                      {idx}: {hash}
                                    </code>
                                  ))}
                                </div>
                              </div>
                              
                              <div>
                                <h4 className="font-medium mb-1">Anchor Networks</h4>
                                <div className="flex flex-wrap gap-1">
                                  {proof.anchor_networks.map((network, idx) => (
                                    <Badge key={idx} variant="secondary">
                                      {network}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Data Hash</p>
                        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {formatHash(proof.data_hash)}
                        </code>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600">Created</p>
                        <p className="text-sm">{new Date(proof.created_at).toLocaleDateString()}</p>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600">Verified</p>
                        <p className="text-sm">
                          {proof.verified_at 
                            ? new Date(proof.verified_at).toLocaleDateString()
                            : 'Pending'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                System Logs
              </CardTitle>
              <CardDescription>
                QueenBee blockchain service operation logs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {logs.map((log) => (
                  <div key={log.log_id} className="flex items-start gap-3 p-3 border rounded">
                    {getStatusIcon(log.status)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium capitalize">{log.operation_type}</span>
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(log.status)} variant="outline">
                            {log.status}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {new Date(log.created_at).toLocaleString()}
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{log.message}</p>
                      {log.metadata && Object.keys(log.metadata).length > 0 && (
                        <details className="mt-2">
                          <summary className="text-xs text-blue-600 cursor-pointer">
                            View metadata
                          </summary>
                          <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
                            {JSON.stringify(log.metadata, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
