import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FiDollarSign, 
  FiTruck, 
  FiStar, 
  FiShield,
  FiMessageCircle,
  FiCheckCircle,
  FiAlertTriangle,
  FiXCircle,
  FiTrendingUp,
  FiClock,
  FiUser,
  FiZap
} from 'react-icons/fi';

interface Quote {
  id: string;
  supplierId: string;
  supplierName: string;
  supplierRating: number;
  price: number;
  deliveryTime: string;
  fraudRisk: 'low' | 'medium' | 'high';
  verificationStatus: 'verified' | 'pending' | 'unverified';
  aiScore: number;
  negotiationPotential: number;
  complianceScore: number;
  pastPerformance: number;
  description: string;
  validUntil: string;
  aiRecommendation: string;
  suggestedCounterOffer?: number;
}

interface SmartQuoteManagementCardProps {
  rfqId?: string;
  className?: string;
}

export default function SmartQuoteManagementCard({ rfqId, className }: SmartQuoteManagementCardProps) {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [activeTab, setActiveTab] = useState('quotes');
  const [sortBy, setSortBy] = useState<'ai_score' | 'price' | 'delivery' | 'rating'>('ai_score');
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockQuotes: Quote[] = [
      {
        id: '1',
        supplierId: 'sup_001',
        supplierName: 'BuildCorp Solutions',
        supplierRating: 4.8,
        price: 485000,
        deliveryTime: '14 days',
        fraudRisk: 'low',
        verificationStatus: 'verified',
        aiScore: 92,
        negotiationPotential: 8,
        complianceScore: 95,
        pastPerformance: 88,
        description: 'Premium materials with fast delivery guarantee',
        validUntil: '2024-01-20',
        aiRecommendation: 'Top choice - excellent balance of price, quality, and reliability.',
        suggestedCounterOffer: 465000
      },
      {
        id: '2',
        supplierId: 'sup_002',
        supplierName: 'QuickBuild Ltd',
        supplierRating: 4.2,
        price: 420000,
        deliveryTime: '21 days',
        fraudRisk: 'medium',
        verificationStatus: 'verified',
        aiScore: 78,
        negotiationPotential: 12,
        complianceScore: 82,
        pastPerformance: 75,
        description: 'Cost-effective solution with standard materials',
        validUntil: '2024-01-18',
        aiRecommendation: 'Good value option but verify recent performance history.',
        suggestedCounterOffer: 395000
      },
      {
        id: '3',
        supplierId: 'sup_003',
        supplierName: 'Premium Contractors',
        supplierRating: 4.9,
        price: 520000,
        deliveryTime: '10 days',
        fraudRisk: 'low',
        verificationStatus: 'verified',
        aiScore: 85,
        negotiationPotential: 5,
        complianceScore: 98,
        pastPerformance: 92,
        description: 'Premium service with fastest delivery',
        validUntil: '2024-01-25',
        aiRecommendation: 'Premium option - worth the extra cost for urgent projects.',
        suggestedCounterOffer: 510000
      }
    ];
    setQuotes(mockQuotes.sort((a, b) => b.aiScore - a.aiScore));
  }, [rfqId]);

  const getFraudRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-orange-500';
      case 'high': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getFraudRiskIcon = (risk: string) => {
    switch (risk) {
      case 'low': return <FiShield className="w-3 h-3" />;
      case 'medium': return <FiAlertTriangle className="w-3 h-3" />;
      case 'high': return <FiXCircle className="w-3 h-3" />;
      default: return <FiShield className="w-3 h-3" />;
    }
  };

  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'verified': return <FiCheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending': return <FiClock className="w-4 h-4 text-orange-500" />;
      case 'unverified': return <FiXCircle className="w-4 h-4 text-red-500" />;
      default: return <FiClock className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleQuoteAction = (quoteId: string, action: 'accept' | 'negotiate' | 'message' | 'reject') => {
    const quote = quotes.find(q => q.id === quoteId);
    if (!quote) return;

    switch (action) {
      case 'accept':
        console.log(`Accepting quote ${quoteId} from ${quote.supplierName}`);
        break;
      case 'negotiate':
        console.log(`Starting negotiation for quote ${quoteId} with counter offer: R${quote.suggestedCounterOffer?.toLocaleString()}`);
        break;
      case 'message':
        console.log(`Opening chat with ${quote.supplierName}`);
        break;
      case 'reject':
        console.log(`Rejecting quote ${quoteId}`);
        break;
    }
  };

  const sortedQuotes = [...quotes].sort((a, b) => {
    switch (sortBy) {
      case 'price': return a.price - b.price;
      case 'delivery': return parseInt(a.deliveryTime) - parseInt(b.deliveryTime);
      case 'rating': return b.supplierRating - a.supplierRating;
      case 'ai_score':
      default: return b.aiScore - a.aiScore;
    }
  });

  return (
    <Card className={`bg-card-bg border-border/50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-text-primary flex items-center gap-2">
            <FiZap className="w-5 h-5 text-accent-orange" />
            Smart Quote Manager
          </CardTitle>
          <Badge variant="secondary" className="bg-accent-blue/20 text-accent-blue">
            {quotes.length} Quotes
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mx-4 mb-4">
            <TabsTrigger value="quotes">AI Ranked</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="quotes" className="px-4 pb-4">
            {/* Sort controls */}
            <div className="flex gap-2 mb-4 overflow-x-auto">
              {[
                { key: 'ai_score', label: 'AI Score', icon: FiZap },
                { key: 'price', label: 'Price', icon: FiDollarSign },
                { key: 'delivery', label: 'Delivery', icon: FiTruck },
                { key: 'rating', label: 'Rating', icon: FiStar }
              ].map(({ key, label, icon: Icon }) => (
                <Button
                  key={key}
                  size="sm"
                  variant={sortBy === key ? "default" : "outline"}
                  onClick={() => setSortBy(key as any)}
                  className="text-xs whitespace-nowrap"
                >
                  <Icon className="w-3 h-3 mr-1" />
                  {label}
                </Button>
              ))}
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {sortedQuotes.map((quote, index) => (
                <div
                  key={quote.id}
                  className={`bg-card-bg-light rounded-lg p-4 border transition-colors cursor-pointer ${
                    index === 0 ? 'border-accent-green/50 bg-accent-green/5' : 'border-border/30 hover:border-border/60'
                  }`}
                  onClick={() => setSelectedQuote(quote)}
                >
                  {/* Header with ranking and verification */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {index === 0 && (
                        <Badge className="bg-accent-green text-white text-xs px-2 py-1">
                          #1 RECOMMENDED
                        </Badge>
                      )}
                      <Badge 
                        className={`${getFraudRiskColor(quote.fraudRisk)} text-white text-xs px-2 py-1 flex items-center gap-1`}
                      >
                        {getFraudRiskIcon(quote.fraudRisk)}
                        {quote.fraudRisk.toUpperCase()} RISK
                      </Badge>
                    </div>
                    {getVerificationIcon(quote.verificationStatus)}
                  </div>

                  {/* Supplier info */}
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-text-primary text-sm flex items-center gap-2">
                      <FiUser className="w-4 h-4" />
                      {quote.supplierName}
                    </h4>
                    <div className="flex items-center gap-1">
                      <FiStar className="w-3 h-3 text-yellow-500" />
                      <span className="text-xs text-text-secondary">{quote.supplierRating}</span>
                    </div>
                  </div>

                  {/* Key metrics */}
                  <div className="grid grid-cols-3 gap-2 mb-3 text-xs">
                    <div className="text-center">
                      <div className="font-semibold text-accent-green">R{quote.price.toLocaleString()}</div>
                      <div className="text-text-secondary">Price</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-accent-blue">{quote.deliveryTime}</div>
                      <div className="text-text-secondary">Delivery</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-accent-orange">{quote.aiScore}%</div>
                      <div className="text-text-secondary">AI Score</div>
                    </div>
                  </div>

                  {/* AI Recommendation */}
                  <Alert className="mb-3 bg-accent-blue/10 border-accent-blue/30">
                    <AlertDescription className="text-xs text-text-primary">
                      <strong>AI:</strong> {quote.aiRecommendation}
                    </AlertDescription>
                  </Alert>

                  {/* Action buttons */}
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      className="flex-1 bg-accent-green hover:bg-accent-green/80 text-white text-xs"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleQuoteAction(quote.id, 'accept');
                      }}
                    >
                      <FiCheckCircle className="w-3 h-3 mr-1" />
                      Accept
                    </Button>
                    {quote.suggestedCounterOffer && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleQuoteAction(quote.id, 'negotiate');
                        }}
                      >
                        <FiTrendingUp className="w-3 h-3 mr-1" />
                        Counter R{quote.suggestedCounterOffer.toLocaleString()}
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-xs"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleQuoteAction(quote.id, 'message');
                      }}
                    >
                      <FiMessageCircle className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="px-4 pb-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-card-bg-light rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-accent-green">
                    R{Math.min(...quotes.map(q => q.price)).toLocaleString()}
                  </div>
                  <div className="text-xs text-text-secondary">Best Price</div>
                </div>
                <div className="bg-card-bg-light rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-accent-blue">
                    {Math.min(...quotes.map(q => parseInt(q.deliveryTime)))} days
                  </div>
                  <div className="text-xs text-text-secondary">Fastest Delivery</div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold text-text-primary text-sm">Quote Distribution</h4>
                <div className="space-y-1">
                  {quotes.map((quote, index) => (
                    <div key={quote.id} className="flex items-center justify-between text-xs">
                      <span className="text-text-secondary">{quote.supplierName}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-accent-blue h-2 rounded-full" 
                            style={{ width: `${(quote.aiScore / 100) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-text-primary w-8">{quote.aiScore}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Alert className="bg-accent-orange/10 border-accent-orange/30">
                <AlertDescription className="text-xs text-text-primary">
                  <strong>Market Insight:</strong> Average quote is 8% above market rate. 
                  Consider negotiating with top 2 suppliers.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
