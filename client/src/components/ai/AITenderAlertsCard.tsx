import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, Card<PERSON>itle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { 
  FiBell, 
  FiAlertTriangle, 
  FiClock, 
  FiMapPin, 
  FiDollarSign,
  FiCheckCircle,
  FiXCircle,
  FiSettings,
  FiZap,
  FiTrendingUp
} from 'react-icons/fi';

interface TenderAlert {
  id: string;
  title: string;
  description: string;
  location: string;
  budget: string;
  matchScore: number;
  urgency: 'hot' | 'warm' | 'cold';
  closingDate: string;
  complianceStatus: 'compliant' | 'needs_action' | 'blocked';
  winProbability: number;
  category: string;
  aiRecommendation: string;
}

interface AITenderAlertsCardProps {
  className?: string;
}

export default function AITenderAlertsCard({ className }: AITenderAlertsCardProps) {
  const [alerts, setAlerts] = useState<TenderAlert[]>([]);
  const [activeTab, setActiveTab] = useState('alerts');
  const [alertSettings, setAlertSettings] = useState({
    pushNotifications: true,
    whatsappAlerts: true,
    emailDigest: true,
    minMatchScore: 70,
    urgencyFilter: 'all'
  });

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockAlerts: TenderAlert[] = [
      {
        id: '1',
        title: 'Road Construction - N1 Highway Extension',
        description: 'Major highway construction project requiring civil engineering expertise',
        location: 'Gauteng, Johannesburg',
        budget: 'R1.2M',
        matchScore: 95,
        urgency: 'hot',
        closingDate: '2024-01-15',
        complianceStatus: 'compliant',
        winProbability: 85,
        category: 'Construction',
        aiRecommendation: 'High-value opportunity matching your expertise. Recommend immediate action.'
      },
      {
        id: '2',
        title: 'Hospital Equipment Supply',
        description: 'Medical equipment procurement for regional hospital',
        location: 'Western Cape, Cape Town',
        budget: 'R850K',
        matchScore: 78,
        urgency: 'warm',
        closingDate: '2024-01-22',
        complianceStatus: 'needs_action',
        winProbability: 65,
        category: 'Healthcare',
        aiRecommendation: 'Update BBBEE certificate to qualify. Strong match otherwise.'
      },
      {
        id: '3',
        title: 'School Infrastructure Development',
        description: 'Building new classrooms and facilities for rural school',
        location: 'Limpopo, Polokwane',
        budget: 'R2.1M',
        matchScore: 88,
        urgency: 'cold',
        closingDate: '2024-02-05',
        complianceStatus: 'compliant',
        winProbability: 72,
        category: 'Education',
        aiRecommendation: 'Excellent fit for your portfolio. Consider partnering for larger capacity.'
      }
    ];
    setAlerts(mockAlerts);
  }, []);

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'hot': return 'bg-red-500';
      case 'warm': return 'bg-orange-500';
      case 'cold': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'hot': return <FiZap className="w-3 h-3" />;
      case 'warm': return <FiTrendingUp className="w-3 h-3" />;
      case 'cold': return <FiClock className="w-3 h-3" />;
      default: return <FiClock className="w-3 h-3" />;
    }
  };

  const getComplianceIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <FiCheckCircle className="w-4 h-4 text-green-500" />;
      case 'needs_action': return <FiAlertTriangle className="w-4 h-4 text-orange-500" />;
      case 'blocked': return <FiXCircle className="w-4 h-4 text-red-500" />;
      default: return <FiClock className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleQuickAction = (alertId: string, action: 'create_rfq' | 'save' | 'dismiss') => {
    console.log(`Action ${action} for alert ${alertId}`);
    // Implement actual action logic here
  };

  return (
    <Card className={`bg-card-bg border-border/50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-text-primary flex items-center gap-2">
            <FiBell className="w-5 h-5 text-accent-blue" />
            AI Tender Alerts
          </CardTitle>
          <Badge variant="secondary" className="bg-accent-green/20 text-accent-green">
            {alerts.length} Active
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mx-4 mb-4">
            <TabsTrigger value="alerts">Smart Alerts</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="alerts" className="px-4 pb-4">
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {alerts.map((alert) => (
                <div
                  key={alert.id}
                  className="bg-card-bg-light rounded-lg p-4 border border-border/30 hover:border-border/60 transition-colors"
                >
                  {/* Header with urgency and match score */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge 
                        className={`${getUrgencyColor(alert.urgency)} text-white text-xs px-2 py-1 flex items-center gap-1`}
                      >
                        {getUrgencyIcon(alert.urgency)}
                        {alert.urgency.toUpperCase()}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {alert.matchScore}% Match
                      </Badge>
                    </div>
                    {getComplianceIcon(alert.complianceStatus)}
                  </div>

                  {/* Title and description */}
                  <h4 className="font-semibold text-text-primary text-sm mb-1 line-clamp-2">
                    {alert.title}
                  </h4>
                  <p className="text-text-secondary text-xs mb-3 line-clamp-2">
                    {alert.description}
                  </p>

                  {/* Details grid */}
                  <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
                    <div className="flex items-center gap-1 text-text-secondary">
                      <FiMapPin className="w-3 h-3" />
                      <span className="truncate">{alert.location}</span>
                    </div>
                    <div className="flex items-center gap-1 text-text-secondary">
                      <FiDollarSign className="w-3 h-3" />
                      <span>{alert.budget}</span>
                    </div>
                    <div className="flex items-center gap-1 text-text-secondary">
                      <FiClock className="w-3 h-3" />
                      <span>Closes: {new Date(alert.closingDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-1 text-accent-green">
                      <FiTrendingUp className="w-3 h-3" />
                      <span>{alert.winProbability}% Win Rate</span>
                    </div>
                  </div>

                  {/* AI Recommendation */}
                  <Alert className="mb-3 bg-accent-blue/10 border-accent-blue/30">
                    <AlertDescription className="text-xs text-text-primary">
                      <strong>AI Insight:</strong> {alert.aiRecommendation}
                    </AlertDescription>
                  </Alert>

                  {/* Action buttons */}
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      className="flex-1 bg-accent-blue hover:bg-accent-blue/80 text-white text-xs"
                      onClick={() => handleQuickAction(alert.id, 'create_rfq')}
                    >
                      Create RFQ
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-xs"
                      onClick={() => handleQuickAction(alert.id, 'save')}
                    >
                      Save
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-xs"
                      onClick={() => handleQuickAction(alert.id, 'dismiss')}
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="px-4 pb-4">
            <div className="space-y-4">
              <div className="space-y-3">
                <h4 className="font-semibold text-text-primary text-sm">Notification Channels</h4>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">Push Notifications</span>
                  <Switch
                    checked={alertSettings.pushNotifications}
                    onCheckedChange={(checked) => 
                      setAlertSettings(prev => ({ ...prev, pushNotifications: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">WhatsApp Alerts</span>
                  <Switch
                    checked={alertSettings.whatsappAlerts}
                    onCheckedChange={(checked) => 
                      setAlertSettings(prev => ({ ...prev, whatsappAlerts: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">Email Digest</span>
                  <Switch
                    checked={alertSettings.emailDigest}
                    onCheckedChange={(checked) => 
                      setAlertSettings(prev => ({ ...prev, emailDigest: checked }))
                    }
                  />
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold text-text-primary text-sm">Alert Filters</h4>
                
                <div className="space-y-2">
                  <label className="text-sm text-text-secondary">
                    Minimum Match Score: {alertSettings.minMatchScore}%
                  </label>
                  <input
                    type="range"
                    min="50"
                    max="100"
                    value={alertSettings.minMatchScore}
                    onChange={(e) => 
                      setAlertSettings(prev => ({ ...prev, minMatchScore: parseInt(e.target.value) }))
                    }
                    className="w-full"
                  />
                </div>
              </div>

              <Button className="w-full bg-accent-green hover:bg-accent-green/80 text-white">
                <FiSettings className="w-4 h-4 mr-2" />
                Save Settings
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
