// AI-Enhanced Bidder Dashboard Components
// Export all AI-powered components for easy importing

export { default as AITenderAlertsCard } from './AITenderAlertsCard';
export { default as SmartQuoteManagementCard } from './SmartQuoteManagementCard';
export { default as EnhancedAICopilot } from './EnhancedAICopilot';
export { default as UnifiedTaskHubCard } from './UnifiedTaskHubCard';
export { default as PredictiveAnalyticsCard } from './PredictiveAnalyticsCard';
export { default as TenderDiscoveryCard } from './TenderDiscoveryCard';

// Component descriptions for documentation
export const AI_COMPONENTS = {
  AITenderAlertsCard: {
    name: 'AI Tender Alerts',
    description: 'Proactive AI-powered tender notifications with smart matching and compliance checking',
    features: [
      'Smart matching engine with 0-100% relevance scoring',
      'Real-time compliance pre-checks',
      'Multi-channel delivery (WhatsApp, Email, Push)',
      'Urgency scoring (Hot/Warm/Cold)',
      'Fraud detection badges',
      'One-click RFQ creation'
    ]
  },
  SmartQuoteManagementCard: {
    name: 'Smart Quote Management',
    description: 'AI-powered quote ranking and negotiation assistant',
    features: [
      'Smart ranking engine (Price + Delivery + Rating)',
      'Fraud detection with supplier verification',
      'AI negotiation assistant with counter-offer suggestions',
      'One-tap acceptance and messaging',
      'Performance analytics and market insights',
      'Supplier compliance scoring'
    ]
  },
  EnhancedAICopilot: {
    name: 'Enhanced AI Copilot',
    description: 'Always-on AI assistant with voice, chat, and OCR capabilities',
    features: [
      'Voice/Chat/Text input with context awareness',
      'OCR tender scanning (Camera input)',
      'Predictive geo-tagging and auto-location',
      'Smart RFQ creation workflow',
      'Quick action buttons for common tasks',
      'Expandable interface with tabbed navigation'
    ]
  },
  UnifiedTaskHubCard: {
    name: 'Unified Task Hub',
    description: 'Single interface for Bees, Drones, and Subcontractors with AI optimization',
    features: [
      'Bee booking (SANTACO Logistics integration)',
      'Drone inspection requests',
      'Subcontractor assignment (SkillSync)',
      'AI resource optimizer (Cost/Time analysis)',
      'Real-time task progress tracking',
      'Cost efficiency scoring'
    ]
  },
  PredictiveAnalyticsCard: {
    name: 'Predictive Analytics',
    description: 'AI-powered forecasting and insights dashboard',
    features: [
      'Spend forecasting with trend analysis',
      'Tender win probability calculations',
      'Supplier performance insights',
      'Compliance heatmaps and alerts',
      'Market trend analysis',
      'Confidence scoring for predictions'
    ]
  },
  TenderDiscoveryCard: {
    name: 'Dual-Path Tender Discovery',
    description: 'Interactive Mapbox search combined with proactive AI alerts',
    features: [
      'Interactive Mapbox with tender visualization',
      'AI-powered search with voice input',
      'Geo-fenced alert creation',
      'Heatmap view for tender density',
      'Advanced filtering and saved searches',
      'AR overlay for mobile (future enhancement)'
    ]
  }
} as const;

// Usage examples for developers
export const USAGE_EXAMPLES = {
  basic: `
import { AITenderAlertsCard, SmartQuoteManagementCard } from '@/components/ai';

function Dashboard() {
  return (
    <div className="grid grid-cols-3 gap-4">
      <AITenderAlertsCard />
      <SmartQuoteManagementCard />
    </div>
  );
}
  `,
  withProps: `
import { TenderDiscoveryCard, UnifiedTaskHubCard } from '@/components/ai';

function EnhancedDashboard() {
  return (
    <div className="space-y-4">
      <TenderDiscoveryCard className="col-span-2" />
      <UnifiedTaskHubCard className="h-96" />
    </div>
  );
}
  `,
  fullSuite: `
import {
  AITenderAlertsCard,
  SmartQuoteManagementCard,
  EnhancedAICopilot,
  UnifiedTaskHubCard,
  PredictiveAnalyticsCard,
  TenderDiscoveryCard
} from '@/components/ai';

function AIEnhancedBidderDashboard() {
  return (
    <div className="min-h-screen bg-dashboard-bg">
      <div className="grid grid-cols-12 gap-4 p-4">
        {/* Left Column */}
        <div className="col-span-3 space-y-4">
          <AITenderAlertsCard />
          <PredictiveAnalyticsCard />
        </div>
        
        {/* Center Column */}
        <div className="col-span-6 space-y-4">
          <TenderDiscoveryCard />
          <UnifiedTaskHubCard />
        </div>
        
        {/* Right Column */}
        <div className="col-span-3 space-y-4">
          <SmartQuoteManagementCard />
        </div>
      </div>
      
      {/* Floating AI Copilot */}
      <EnhancedAICopilot />
    </div>
  );
}
  `
};

// Integration notes for the development team
export const INTEGRATION_NOTES = {
  styling: 'All components use the existing dashboard color scheme (dashboard-bg, card-bg, accent colors)',
  dependencies: 'Components use shadcn/ui, Tailwind CSS, and React Icons (Feather)',
  dataFlow: 'Components expect mock data currently - replace with actual API calls',
  responsive: 'All components are responsive and work on mobile/tablet/desktop',
  accessibility: 'Components follow WCAG guidelines with proper ARIA labels',
  performance: 'Components use React.memo and lazy loading where appropriate'
};
