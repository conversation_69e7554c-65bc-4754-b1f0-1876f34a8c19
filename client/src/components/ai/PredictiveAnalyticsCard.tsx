import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FiTrendingUp, 
  FiTrendingDown,
  FiDollarSign,
  FiTarget,
  FiAlertTriangle,
  FiCheckCircle,
  FiClock,
  FiBarChart3,
  FiPieChart,
  FiActivity,
  FiZap,
  FiCalendar
} from 'react-icons/fi';

interface PredictiveMetric {
  id: string;
  title: string;
  currentValue: number;
  predictedValue: number;
  trend: 'up' | 'down' | 'stable';
  confidence: number;
  timeframe: string;
  unit: string;
  insight: string;
  recommendation: string;
}

interface WinProbability {
  tenderId: string;
  tenderTitle: string;
  probability: number;
  factors: {
    priceCompetitiveness: number;
    pastPerformance: number;
    compliance: number;
    capacity: number;
  };
  recommendation: string;
}

interface ComplianceAlert {
  id: string;
  type: 'expiring' | 'missing' | 'renewal_due';
  title: string;
  description: string;
  daysUntilExpiry: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  actionRequired: string;
}

interface PredictiveAnalyticsCardProps {
  className?: string;
}

export default function PredictiveAnalyticsCard({ className }: PredictiveAnalyticsCardProps) {
  const [activeTab, setActiveTab] = useState('forecasting');
  const [metrics, setMetrics] = useState<PredictiveMetric[]>([]);
  const [winProbabilities, setWinProbabilities] = useState<WinProbability[]>([]);
  const [complianceAlerts, setComplianceAlerts] = useState<ComplianceAlert[]>([]);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockMetrics: PredictiveMetric[] = [
      {
        id: '1',
        title: 'Monthly Spend',
        currentValue: 485000,
        predictedValue: 520000,
        trend: 'up',
        confidence: 87,
        timeframe: 'Next 30 days',
        unit: 'R',
        insight: 'Spending trending 7% above average due to increased construction activity',
        recommendation: 'Consider bulk purchasing for better rates'
      },
      {
        id: '2',
        title: 'Win Rate',
        currentValue: 68,
        predictedValue: 72,
        trend: 'up',
        confidence: 92,
        timeframe: 'Next quarter',
        unit: '%',
        insight: 'Improved compliance and competitive pricing boosting success rate',
        recommendation: 'Focus on high-value tenders to maximize impact'
      },
      {
        id: '3',
        title: 'Average Bid Value',
        currentValue: 850000,
        predictedValue: 780000,
        trend: 'down',
        confidence: 78,
        timeframe: 'Next 60 days',
        unit: 'R',
        insight: 'Market shift towards smaller, more frequent projects',
        recommendation: 'Adjust strategy to target multiple smaller tenders'
      }
    ];

    const mockWinProbabilities: WinProbability[] = [
      {
        tenderId: 'T001',
        tenderTitle: 'Road Construction - N1 Extension',
        probability: 85,
        factors: {
          priceCompetitiveness: 88,
          pastPerformance: 92,
          compliance: 95,
          capacity: 78
        },
        recommendation: 'Strong candidate - submit competitive bid'
      },
      {
        tenderId: 'T002',
        tenderTitle: 'Hospital Equipment Supply',
        probability: 65,
        factors: {
          priceCompetitiveness: 72,
          pastPerformance: 85,
          compliance: 68,
          capacity: 90
        },
        recommendation: 'Update BBBEE certificate to improve compliance score'
      },
      {
        tenderId: 'T003',
        tenderTitle: 'School Infrastructure',
        probability: 72,
        factors: {
          priceCompetitiveness: 78,
          pastPerformance: 88,
          compliance: 92,
          capacity: 65
        },
        recommendation: 'Consider partnering to increase capacity score'
      }
    ];

    const mockComplianceAlerts: ComplianceAlert[] = [
      {
        id: '1',
        type: 'expiring',
        title: 'BBBEE Certificate',
        description: 'Your BBBEE Level 2 certificate expires soon',
        daysUntilExpiry: 14,
        severity: 'high',
        actionRequired: 'Schedule renewal appointment with verification agency'
      },
      {
        id: '2',
        type: 'renewal_due',
        title: 'Tax Clearance',
        description: 'Annual tax clearance certificate renewal due',
        daysUntilExpiry: 30,
        severity: 'medium',
        actionRequired: 'Submit renewal application to SARS'
      },
      {
        id: '3',
        type: 'missing',
        title: 'Professional Indemnity',
        description: 'Professional indemnity insurance not on file',
        daysUntilExpiry: 0,
        severity: 'critical',
        actionRequired: 'Upload current insurance certificate immediately'
      }
    ];

    setMetrics(mockMetrics);
    setWinProbabilities(mockWinProbabilities);
    setComplianceAlerts(mockComplianceAlerts);
  }, []);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <FiTrendingUp className="w-4 h-4 text-accent-green" />;
      case 'down': return <FiTrendingDown className="w-4 h-4 text-red-500" />;
      case 'stable': return <FiActivity className="w-4 h-4 text-accent-blue" />;
      default: return <FiActivity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === 'R') {
      return `R${value.toLocaleString()}`;
    }
    return `${value}${unit}`;
  };

  const getWinProbabilityColor = (probability: number) => {
    if (probability >= 80) return 'text-accent-green';
    if (probability >= 60) return 'text-accent-orange';
    return 'text-red-500';
  };

  return (
    <Card className={`bg-card-bg border-border/50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-text-primary flex items-center gap-2">
            <FiBarChart3 className="w-5 h-5 text-accent-blue" />
            Predictive Analytics
          </CardTitle>
          <Badge variant="secondary" className="bg-accent-green/20 text-accent-green">
            AI Powered
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mx-4 mb-4">
            <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
            <TabsTrigger value="win_rates">Win Rates</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
          </TabsList>

          <TabsContent value="forecasting" className="px-4 pb-4">
            <div className="space-y-4 max-h-80 overflow-y-auto">
              {metrics.map((metric) => (
                <div
                  key={metric.id}
                  className="bg-card-bg-light rounded-lg p-4 border border-border/30"
                >
                  {/* Header */}
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-text-primary text-sm flex items-center gap-2">
                      {getTrendIcon(metric.trend)}
                      {metric.title}
                    </h4>
                    <Badge variant="outline" className="text-xs">
                      {metric.confidence}% confidence
                    </Badge>
                  </div>

                  {/* Values comparison */}
                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div className="text-center">
                      <div className="text-lg font-bold text-text-primary">
                        {formatValue(metric.currentValue, metric.unit)}
                      </div>
                      <div className="text-xs text-text-secondary">Current</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-bold ${
                        metric.trend === 'up' ? 'text-accent-green' : 
                        metric.trend === 'down' ? 'text-red-500' : 'text-accent-blue'
                      }`}>
                        {formatValue(metric.predictedValue, metric.unit)}
                      </div>
                      <div className="text-xs text-text-secondary">Predicted</div>
                    </div>
                  </div>

                  {/* Timeframe */}
                  <div className="flex items-center gap-1 mb-3 text-xs text-text-secondary">
                    <FiCalendar className="w-3 h-3" />
                    <span>{metric.timeframe}</span>
                  </div>

                  {/* Insight */}
                  <Alert className="mb-3 bg-accent-blue/10 border-accent-blue/30">
                    <AlertDescription className="text-xs text-text-primary">
                      <strong>Insight:</strong> {metric.insight}
                    </AlertDescription>
                  </Alert>

                  {/* Recommendation */}
                  <div className="bg-accent-green/10 border border-accent-green/30 rounded p-2">
                    <div className="text-xs text-text-primary">
                      <strong>Recommendation:</strong> {metric.recommendation}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="win_rates" className="px-4 pb-4">
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {winProbabilities.map((tender) => (
                <div
                  key={tender.tenderId}
                  className="bg-card-bg-light rounded-lg p-4 border border-border/30"
                >
                  {/* Header */}
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-text-primary text-sm line-clamp-1">
                      {tender.tenderTitle}
                    </h4>
                    <div className={`text-lg font-bold ${getWinProbabilityColor(tender.probability)}`}>
                      {tender.probability}%
                    </div>
                  </div>

                  {/* Factors breakdown */}
                  <div className="space-y-2 mb-3">
                    {Object.entries(tender.factors).map(([factor, score]) => (
                      <div key={factor} className="flex items-center justify-between text-xs">
                        <span className="text-text-secondary capitalize">
                          {factor.replace(/([A-Z])/g, ' $1').trim()}
                        </span>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-1.5">
                            <div 
                              className={`h-1.5 rounded-full ${
                                score >= 80 ? 'bg-accent-green' : 
                                score >= 60 ? 'bg-accent-orange' : 'bg-red-500'
                              }`}
                              style={{ width: `${score}%` }}
                            ></div>
                          </div>
                          <span className="text-text-primary w-8">{score}%</span>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Recommendation */}
                  <Alert className="bg-accent-orange/10 border-accent-orange/30">
                    <AlertDescription className="text-xs text-text-primary">
                      <strong>AI Recommendation:</strong> {tender.recommendation}
                    </AlertDescription>
                  </Alert>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="compliance" className="px-4 pb-4">
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {complianceAlerts.map((alert) => (
                <div
                  key={alert.id}
                  className="bg-card-bg-light rounded-lg p-4 border border-border/30"
                >
                  {/* Header */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge className={`${getSeverityColor(alert.severity)} text-white text-xs px-2 py-1`}>
                        {alert.severity.toUpperCase()}
                      </Badge>
                      <h4 className="font-semibold text-text-primary text-sm">
                        {alert.title}
                      </h4>
                    </div>
                    {alert.daysUntilExpiry > 0 && (
                      <div className="text-right">
                        <div className="text-sm font-bold text-accent-orange">
                          {alert.daysUntilExpiry}
                        </div>
                        <div className="text-xs text-text-secondary">days left</div>
                      </div>
                    )}
                  </div>

                  {/* Description */}
                  <p className="text-text-secondary text-xs mb-3">{alert.description}</p>

                  {/* Action required */}
                  <div className="bg-accent-blue/10 border border-accent-blue/30 rounded p-2 mb-3">
                    <div className="text-xs text-text-primary">
                      <strong>Action Required:</strong> {alert.actionRequired}
                    </div>
                  </div>

                  {/* Action button */}
                  <Button
                    size="sm"
                    className={`w-full text-xs ${
                      alert.severity === 'critical' 
                        ? 'bg-red-500 hover:bg-red-600' 
                        : 'bg-accent-orange hover:bg-accent-orange/80'
                    } text-white`}
                  >
                    {alert.severity === 'critical' ? (
                      <>
                        <FiAlertTriangle className="w-3 h-3 mr-1" />
                        Urgent Action Required
                      </>
                    ) : (
                      <>
                        <FiClock className="w-3 h-3 mr-1" />
                        Schedule Renewal
                      </>
                    )}
                  </Button>
                </div>
              ))}

              {complianceAlerts.length === 0 && (
                <div className="text-center py-8 text-text-secondary">
                  <FiCheckCircle className="w-8 h-8 mx-auto mb-2 text-accent-green" />
                  <p className="text-sm">All compliance requirements up to date</p>
                  <p className="text-xs">Great job maintaining your certifications!</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
