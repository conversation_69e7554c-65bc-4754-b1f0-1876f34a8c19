import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  FiMessageCircle, 
  FiMic, 
  FiCamera, 
  FiSend, 
  FiCpu,
  FiZap,
  FiFileText,
  FiMapPin,
  FiDollarSign,
  FiClock,
  FiCheckCircle,
  FiSettings,
  FiX,
  FiMaximize2,
  FiMinimize2
} from 'react-icons/fi';

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  type?: 'text' | 'voice' | 'ocr' | 'action';
  metadata?: {
    confidence?: number;
    actions?: string[];
    suggestions?: string[];
  };
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  prompt: string;
  category: 'rfq' | 'search' | 'analysis' | 'task';
}

interface EnhancedAICopilotProps {
  className?: string;
}

export default function EnhancedAICopilot({ className }: EnhancedAICopilotProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '0',
      content: "Hello! I'm your enhanced BidBees AI Copilot. I can help you create RFQs, analyze tenders, manage quotes, and optimize your bidding strategy. How can I assist you today?",
      sender: 'ai',
      timestamp: new Date(),
      type: 'text'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isProcessingOCR, setIsProcessingOCR] = useState(false);
  const messageContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const quickActions: QuickAction[] = [
    {
      id: 'create_rfq',
      label: 'Create RFQ',
      icon: <FiFileText className="w-4 h-4" />,
      prompt: 'Help me create a new RFQ',
      category: 'rfq'
    },
    {
      id: 'find_tenders',
      label: 'Find Tenders',
      icon: <FiMapPin className="w-4 h-4" />,
      prompt: 'Show me relevant tender opportunities',
      category: 'search'
    },
    {
      id: 'analyze_quotes',
      label: 'Analyze Quotes',
      icon: <FiDollarSign className="w-4 h-4" />,
      prompt: 'Analyze my current quotes and provide recommendations',
      category: 'analysis'
    },
    {
      id: 'book_bee',
      label: 'Book Bee Task',
      icon: <FiClock className="w-4 h-4" />,
      prompt: 'Help me book a Bee for site inspection',
      category: 'task'
    }
  ];

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messageContainerRef.current) {
      messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isSubmitting) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsSubmitting(true);
    
    try {
      // Simulate AI processing with enhanced responses
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const aiResponse = generateAIResponse(inputValue);
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: aiResponse.content,
        sender: 'ai',
        timestamp: new Date(),
        type: 'text',
        metadata: aiResponse.metadata
      };
      
      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: "I'm sorry, I couldn't process your request. Please try again later.",
        sender: 'ai',
        timestamp: new Date(),
        type: 'text'
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateAIResponse = (input: string): { content: string; metadata?: any } => {
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('rfq') || lowerInput.includes('request for quote')) {
      return {
        content: "I'll help you create an RFQ! Based on your profile, I can auto-fill common details. What type of project is this for? I can also scan tender documents if you have them.",
        metadata: {
          confidence: 95,
          actions: ['create_rfq', 'scan_document', 'auto_fill'],
          suggestions: ['Construction RFQ', 'Supply RFQ', 'Service RFQ']
        }
      };
    }
    
    if (lowerInput.includes('tender') || lowerInput.includes('opportunity')) {
      return {
        content: "I found 12 relevant tenders in your area! Here are the top 3 matches:\n\n🏗️ Road Construction - Gauteng (85% match, R1.2M)\n🏥 Hospital Equipment - Western Cape (78% match, R850K)\n🏫 School Infrastructure - Limpopo (88% match, R2.1M)\n\nWould you like me to create RFQs for any of these?",
        metadata: {
          confidence: 92,
          actions: ['view_tenders', 'create_rfq', 'set_alerts'],
          suggestions: ['Show all matches', 'Filter by budget', 'Set up alerts']
        }
      };
    }
    
    if (lowerInput.includes('quote') || lowerInput.includes('supplier')) {
      return {
        content: "I've analyzed your current quotes. BuildCorp Solutions offers the best value at R485K with 92% AI score. They have excellent compliance and delivery history. Should I help you negotiate or accept this quote?",
        metadata: {
          confidence: 88,
          actions: ['accept_quote', 'negotiate', 'compare_quotes'],
          suggestions: ['Accept best quote', 'Counter offer R465K', 'Request more quotes']
        }
      };
    }
    
    return {
      content: "I understand you need assistance. I can help with:\n\n• Creating and managing RFQs\n• Finding relevant tenders\n• Analyzing quotes and suppliers\n• Booking Bee tasks and site visits\n• Compliance checking\n• Market analysis\n\nWhat would you like to focus on?",
      metadata: {
        confidence: 85,
        suggestions: ['Create RFQ', 'Find tenders', 'Analyze quotes', 'Book Bee task']
      }
    };
  };

  const handleQuickAction = (action: QuickAction) => {
    setInputValue(action.prompt);
    handleSubmit({ preventDefault: () => {} } as React.FormEvent);
  };

  const handleVoiceInput = () => {
    setIsListening(!isListening);
    // Implement voice recognition here
    console.log('Voice input toggled:', !isListening);
  };

  const handleOCRScan = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsProcessingOCR(true);
    
    try {
      // Simulate OCR processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const ocrMessage: ChatMessage = {
        id: Date.now().toString(),
        content: "📄 Document scanned successfully! I extracted:\n\n• Project: Road Construction\n• Location: Gauteng\n• Budget: R1.2M\n• Closing: 15 Jan 2024\n\nShould I create an RFQ with this information?",
        sender: 'ai',
        timestamp: new Date(),
        type: 'ocr',
        metadata: {
          confidence: 94,
          actions: ['create_rfq', 'edit_details', 'save_draft']
        }
      };
      
      setMessages(prev => [...prev, ocrMessage]);
    } catch (error) {
      console.error('OCR processing failed:', error);
    } finally {
      setIsProcessingOCR(false);
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const copilotSize = isExpanded ? 'w-96 h-[600px]' : 'w-80 h-96';

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*,.pdf"
        onChange={handleFileUpload}
        className="hidden"
      />
      
      {isOpen && (
        <Card className={`${copilotSize} mb-4 bg-card-bg border-border/50 shadow-2xl transition-all duration-300`}>
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b border-border/30">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-accent-orange to-accent-blue flex items-center justify-center">
                <FiZap className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-text-primary text-sm">BidBees AI Copilot</h3>
                <Badge variant="secondary" className="text-xs bg-accent-green/20 text-accent-green">
                  Enhanced
                </Badge>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Button
                size="sm"
                variant="ghost"
                onClick={toggleExpanded}
                className="p-1 h-6 w-6"
              >
                {isExpanded ? <FiMinimize2 className="w-3 h-3" /> : <FiMaximize2 className="w-3 h-3" />}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setIsOpen(false)}
                className="p-1 h-6 w-6"
              >
                <FiX className="w-3 h-3" />
              </Button>
            </div>
          </div>

          <CardContent className="p-0 flex flex-col h-full">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-2 m-2">
                <TabsTrigger value="chat">Chat</TabsTrigger>
                <TabsTrigger value="actions">Quick Actions</TabsTrigger>
              </TabsList>

              <TabsContent value="chat" className="flex-1 flex flex-col p-2 pt-0">
                {/* Messages */}
                <div 
                  ref={messageContainerRef}
                  className="flex-1 overflow-y-auto space-y-2 mb-3 bg-gray-50 rounded-lg p-2"
                >
                  {messages.map(message => (
                    <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : ''}`}>
                      {message.sender === 'ai' && (
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-accent-orange to-accent-blue flex items-center justify-center mr-2 flex-shrink-0">
                          <FiCpu className="w-3 h-3 text-white" />
                        </div>
                      )}
                      <div className={`max-w-[80%] p-2 rounded-lg shadow-sm ${
                        message.sender === 'user' 
                          ? 'bg-accent-blue text-white' 
                          : 'bg-white text-gray-800 border border-gray-200'
                      }`}>
                        <p className="text-xs whitespace-pre-line">{message.content}</p>
                        {message.metadata?.suggestions && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {message.metadata.suggestions.map((suggestion: string, index: number) => (
                              <Button
                                key={index}
                                size="sm"
                                variant="outline"
                                className="text-xs h-6 px-2"
                                onClick={() => setInputValue(suggestion)}
                              >
                                {suggestion}
                              </Button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  {isSubmitting && (
                    <div className="flex items-center gap-2 text-text-secondary">
                      <div className="w-6 h-6 rounded-full bg-gradient-to-r from-accent-orange to-accent-blue flex items-center justify-center">
                        <FiCpu className="w-3 h-3 text-white animate-pulse" />
                      </div>
                      <span className="text-xs">AI is thinking...</span>
                    </div>
                  )}
                </div>

                {/* Input area */}
                <form onSubmit={handleSubmit} className="flex gap-2">
                  <div className="flex gap-1">
                    <Button
                      type="button"
                      size="sm"
                      variant={isListening ? "default" : "outline"}
                      onClick={handleVoiceInput}
                      className="p-2 h-8 w-8"
                    >
                      <FiMic className="w-3 h-3" />
                    </Button>
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      onClick={handleOCRScan}
                      disabled={isProcessingOCR}
                      className="p-2 h-8 w-8"
                    >
                      <FiCamera className="w-3 h-3" />
                    </Button>
                  </div>
                  <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Ask me anything..."
                    className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-accent-blue"
                  />
                  <Button 
                    type="submit"
                    size="sm"
                    disabled={isSubmitting || !inputValue.trim()}
                    className="bg-accent-blue hover:bg-accent-blue/80 text-white p-2 h-8 w-8"
                  >
                    <FiSend className="w-3 h-3" />
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="actions" className="flex-1 p-2 pt-0">
                <div className="grid grid-cols-2 gap-2">
                  {quickActions.map((action) => (
                    <Button
                      key={action.id}
                      variant="outline"
                      className="h-16 flex flex-col items-center justify-center gap-1 text-xs"
                      onClick={() => handleQuickAction(action)}
                    >
                      {action.icon}
                      <span>{action.label}</span>
                    </Button>
                  ))}
                </div>
                
                <div className="mt-4 p-2 bg-accent-blue/10 rounded-lg">
                  <h4 className="font-semibold text-xs text-text-primary mb-1">Recent Activity</h4>
                  <div className="space-y-1 text-xs text-text-secondary">
                    <div className="flex items-center gap-1">
                      <FiCheckCircle className="w-3 h-3 text-accent-green" />
                      <span>RFQ created for road construction</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FiClock className="w-3 h-3 text-accent-orange" />
                      <span>3 new tender alerts</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
      
      {/* Floating button */}
      <Button 
        onClick={() => setIsOpen(!isOpen)} 
        className="bg-gradient-to-r from-accent-orange to-accent-blue hover:from-accent-orange/80 hover:to-accent-blue/80 text-white p-3 rounded-full shadow-lg w-14 h-14 flex items-center justify-center relative"
      >
        <FiMessageCircle className="text-xl" />
        {!isOpen && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-accent-green rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">3</span>
          </div>
        )}
      </Button>
    </div>
  );
}
