import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import mapboxgl from 'mapbox-gl';
import { 
  FiSearch, 
  FiMapPin, 
  FiFilter,
  FiBell,
  FiZap,
  FiEye,
  FiDollarSign,
  FiClock,
  FiTrendingUp,
  FiSettings,
  FiLayers,
  FiTarget,
  FiMic,
  FiMaximize2
} from 'react-icons/fi';

interface TenderMarker {
  id: string;
  lng: number;
  lat: number;
  title: string;
  budget: string;
  closingDate: string;
  matchScore: number;
  urgency: 'hot' | 'warm' | 'cold';
  category: string;
  status: 'new' | 'viewed' | 'applied';
}

interface SearchFilter {
  category: string[];
  budgetRange: [number, number];
  location: string;
  radius: number;
  urgency: string[];
}

interface TenderDiscoveryCardProps {
  className?: string;
}

export default function TenderDiscoveryCard({ className }: TenderDiscoveryCardProps) {
  const [activeTab, setActiveTab] = useState('map');
  const [searchQuery, setSearchQuery] = useState('');
  const [isVoiceSearch, setIsVoiceSearch] = useState(false);
  const [mapView, setMapView] = useState<'tenders' | 'heatmap' | 'alerts'>('tenders');
  const [filters, setFilters] = useState<SearchFilter>({
    category: [],
    budgetRange: [0, 5000000],
    location: '',
    radius: 50,
    urgency: []
  });
  const [tenderMarkers, setTenderMarkers] = useState<TenderMarker[]>([]);
  const [alertSettings, setAlertSettings] = useState({
    geoFenced: true,
    priceAlerts: true,
    categoryAlerts: true,
    urgencyAlerts: true
  });
  
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);

  // Mock tender data
  useEffect(() => {
    const mockTenders: TenderMarker[] = [
      {
        id: '1',
        lng: 28.0473,
        lat: -26.2041,
        title: 'Road Construction - N1 Extension',
        budget: 'R1.2M',
        closingDate: '2024-01-20',
        matchScore: 95,
        urgency: 'hot',
        category: 'Construction',
        status: 'new'
      },
      {
        id: '2',
        lng: 18.4241,
        lat: -33.9249,
        title: 'Hospital Equipment Supply',
        budget: 'R850K',
        closingDate: '2024-01-25',
        matchScore: 78,
        urgency: 'warm',
        category: 'Healthcare',
        status: 'viewed'
      },
      {
        id: '3',
        lng: 29.8587,
        lat: -23.8962,
        title: 'School Infrastructure',
        budget: 'R2.1M',
        closingDate: '2024-02-05',
        matchScore: 88,
        urgency: 'cold',
        category: 'Education',
        status: 'new'
      }
    ];
    setTenderMarkers(mockTenders);
  }, []);

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    const token = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 
                 'pk.eyJ1IjoiYmlkYmVlcyIsImEiOiJjbWJiOGh0cXQwOXJvMmtzb2x1Ymc0NGF6In0.rdxv8HdMok6ZgmXe392Aaw';
    
    mapboxgl.accessToken = token;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [24.0, -29.0],
      zoom: 5,
      attributionControl: false
    });

    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');
  }, []);

  // Add markers to map
  useEffect(() => {
    if (!map.current || !tenderMarkers.length) return;

    // Clear existing markers
    const existingMarkers = document.querySelectorAll('.tender-marker');
    existingMarkers.forEach(marker => marker.remove());

    tenderMarkers.forEach((tender) => {
      const el = document.createElement('div');
      el.className = 'tender-marker';
      el.style.width = '24px';
      el.style.height = '24px';
      el.style.borderRadius = '50%';
      el.style.cursor = 'pointer';
      el.style.border = '2px solid white';
      el.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
      
      // Color based on urgency
      switch (tender.urgency) {
        case 'hot':
          el.style.backgroundColor = '#ef4444';
          break;
        case 'warm':
          el.style.backgroundColor = '#f97316';
          break;
        case 'cold':
          el.style.backgroundColor = '#3b82f6';
          break;
      }

      // Add pulsing animation for new tenders
      if (tender.status === 'new') {
        el.style.animation = 'pulse 2s infinite';
      }

      const popup = new mapboxgl.Popup({ offset: 25 })
        .setHTML(`
          <div class="p-2">
            <h4 class="font-semibold text-sm mb-1">${tender.title}</h4>
            <div class="text-xs text-gray-600 space-y-1">
              <div>Budget: ${tender.budget}</div>
              <div>Match: ${tender.matchScore}%</div>
              <div>Closes: ${new Date(tender.closingDate).toLocaleDateString()}</div>
            </div>
            <button class="mt-2 bg-blue-500 text-white px-2 py-1 rounded text-xs">
              View Details
            </button>
          </div>
        `);

      new mapboxgl.Marker(el)
        .setLngLat([tender.lng, tender.lat])
        .setPopup(popup)
        .addTo(map.current!);
    });
  }, [tenderMarkers, mapView]);

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'hot': return 'bg-red-500';
      case 'warm': return 'bg-orange-500';
      case 'cold': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const handleVoiceSearch = () => {
    setIsVoiceSearch(!isVoiceSearch);
    // Implement voice recognition
    console.log('Voice search toggled:', !isVoiceSearch);
  };

  const handleSearch = (query: string) => {
    console.log('Searching for:', query);
    // Implement search logic
  };

  const handleCreateAlert = (tender: TenderMarker) => {
    console.log('Creating geo-fenced alert for:', tender.title);
    // Implement alert creation
  };

  return (
    <Card className={`bg-card-bg border-border/50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-text-primary flex items-center gap-2">
            <FiTarget className="w-5 h-5 text-accent-blue" />
            Tender Discovery
          </CardTitle>
          <div className="flex gap-1">
            <Badge variant="secondary" className="bg-red-500/20 text-red-500 text-xs">
              {tenderMarkers.filter(t => t.urgency === 'hot').length} Hot
            </Badge>
            <Badge variant="secondary" className="bg-orange-500/20 text-orange-500 text-xs">
              {tenderMarkers.filter(t => t.status === 'new').length} New
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mx-4 mb-4">
            <TabsTrigger value="map">Interactive Map</TabsTrigger>
            <TabsTrigger value="search">AI Search</TabsTrigger>
            <TabsTrigger value="alerts">Smart Alerts</TabsTrigger>
          </TabsList>

          <TabsContent value="map" className="px-4 pb-4">
            {/* Map view controls */}
            <div className="flex gap-2 mb-3 overflow-x-auto">
              {[
                { key: 'tenders', label: 'Tenders', icon: FiMapPin },
                { key: 'heatmap', label: 'Heatmap', icon: FiLayers },
                { key: 'alerts', label: 'Alerts', icon: FiBell }
              ].map(({ key, label, icon: Icon }) => (
                <Button
                  key={key}
                  size="sm"
                  variant={mapView === key ? "default" : "outline"}
                  onClick={() => setMapView(key as any)}
                  className="text-xs whitespace-nowrap"
                >
                  <Icon className="w-3 h-3 mr-1" />
                  {label}
                </Button>
              ))}
            </div>

            {/* Map container */}
            <div className="relative">
              <div
                ref={mapContainer}
                className="w-full h-64 rounded-lg overflow-hidden bg-gray-100"
              />
              
              {/* Map overlay controls */}
              <div className="absolute top-2 left-2 bg-white/90 backdrop-blur rounded p-2 space-y-1">
                <div className="flex items-center gap-2 text-xs">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Hot ({tenderMarkers.filter(t => t.urgency === 'hot').length})</span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span>Warm ({tenderMarkers.filter(t => t.urgency === 'warm').length})</span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Cold ({tenderMarkers.filter(t => t.urgency === 'cold').length})</span>
                </div>
              </div>

              <Button
                size="sm"
                variant="outline"
                className="absolute bottom-2 right-2 bg-white/90 backdrop-blur"
              >
                <FiMaximize2 className="w-3 h-3 mr-1" />
                Fullscreen
              </Button>
            </div>

            {/* Quick actions */}
            <div className="mt-3 grid grid-cols-2 gap-2">
              <Button size="sm" variant="outline" className="text-xs">
                <FiFilter className="w-3 h-3 mr-1" />
                Advanced Filters
              </Button>
              <Button size="sm" variant="outline" className="text-xs">
                <FiSettings className="w-3 h-3 mr-1" />
                Save Search
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="search" className="px-4 pb-4">
            {/* AI Search interface */}
            <div className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search tenders with AI... e.g., 'Construction projects under R1M in Gauteng'"
                    className="w-full px-3 py-2 text-sm border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-blue"
                  />
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleVoiceSearch}
                    className={`absolute right-1 top-1 p-1 h-6 w-6 ${isVoiceSearch ? 'text-red-500' : 'text-gray-400'}`}
                  >
                    <FiMic className="w-3 h-3" />
                  </Button>
                </div>
                <Button
                  size="sm"
                  onClick={() => handleSearch(searchQuery)}
                  className="bg-accent-blue hover:bg-accent-blue/80 text-white"
                >
                  <FiSearch className="w-3 h-3 mr-1" />
                  Search
                </Button>
              </div>

              {/* Quick search suggestions */}
              <div className="space-y-2">
                <h4 className="font-semibold text-text-primary text-sm">Quick Searches</h4>
                <div className="flex flex-wrap gap-2">
                  {[
                    'Construction R500K-R2M',
                    'Healthcare equipment',
                    'Rural school projects',
                    'Road maintenance'
                  ].map((suggestion) => (
                    <Button
                      key={suggestion}
                      size="sm"
                      variant="outline"
                      onClick={() => setSearchQuery(suggestion)}
                      className="text-xs"
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              </div>

              {/* AI-powered insights */}
              <Alert className="bg-accent-blue/10 border-accent-blue/30">
                <FiZap className="w-4 h-4" />
                <AlertDescription className="text-xs text-text-primary">
                  <strong>AI Insight:</strong> Based on your profile, I found 12 relevant tenders. 
                  3 are closing within 48 hours and match your expertise 85%+.
                </AlertDescription>
              </Alert>

              {/* Search results preview */}
              <div className="space-y-2">
                <h4 className="font-semibold text-text-primary text-sm">Recent Matches</h4>
                {tenderMarkers.slice(0, 3).map((tender) => (
                  <div
                    key={tender.id}
                    className="bg-card-bg-light rounded p-3 border border-border/30"
                  >
                    <div className="flex items-start justify-between mb-1">
                      <h5 className="font-medium text-text-primary text-sm line-clamp-1">
                        {tender.title}
                      </h5>
                      <Badge className={`${getUrgencyColor(tender.urgency)} text-white text-xs`}>
                        {tender.matchScore}%
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs text-text-secondary">
                      <div className="flex items-center gap-1">
                        <FiDollarSign className="w-3 h-3" />
                        {tender.budget}
                      </div>
                      <div className="flex items-center gap-1">
                        <FiClock className="w-3 h-3" />
                        {new Date(tender.closingDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="alerts" className="px-4 pb-4">
            <div className="space-y-4">
              {/* Alert settings */}
              <div className="space-y-3">
                <h4 className="font-semibold text-text-primary text-sm">Alert Preferences</h4>
                
                {Object.entries(alertSettings).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <span className="text-sm text-text-secondary capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                    <Switch
                      checked={value}
                      onCheckedChange={(checked) => 
                        setAlertSettings(prev => ({ ...prev, [key]: checked }))
                      }
                    />
                  </div>
                ))}
              </div>

              {/* Active alerts */}
              <div className="space-y-2">
                <h4 className="font-semibold text-text-primary text-sm">Active Geo-Alerts</h4>
                <div className="space-y-2">
                  <div className="bg-card-bg-light rounded p-3 border border-border/30">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-text-primary text-sm">
                        Construction projects in Gauteng
                      </span>
                      <Badge variant="outline" className="text-xs">Active</Badge>
                    </div>
                    <div className="text-xs text-text-secondary">
                      Budget: R500K - R2M • Radius: 50km
                    </div>
                  </div>
                  
                  <div className="bg-card-bg-light rounded p-3 border border-border/30">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-text-primary text-sm">
                        Healthcare equipment nationwide
                      </span>
                      <Badge variant="outline" className="text-xs">Active</Badge>
                    </div>
                    <div className="text-xs text-text-secondary">
                      Budget: Any • Match score: 70%+
                    </div>
                  </div>
                </div>
              </div>

              {/* Create new alert */}
              <Button className="w-full bg-accent-green hover:bg-accent-green/80 text-white text-sm">
                <FiBell className="w-4 h-4 mr-2" />
                Create New Alert
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>

      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      `}</style>
    </Card>
  );
}
