import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>H<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  <PERSON>Truck, 
  FiUsers, 
  FiCamera,
  FiClock,
  FiMapPin,
  FiDollarSign,
  FiZap,
  FiCheckCircle,
  FiAlertTriangle,
  FiTrendingUp,
  FiSettings,
  FiPlus,
  FiEye
} from 'react-icons/fi';

interface TaskOption {
  id: string;
  type: 'bee' | 'drone' | 'subcontractor';
  title: string;
  description: string;
  provider: string;
  cost: number;
  duration: string;
  availability: 'available' | 'busy' | 'unavailable';
  rating: number;
  specialties: string[];
  estimatedCompletion: string;
  aiRecommendation: string;
  costEfficiency: number;
}

interface ActiveTask {
  id: string;
  type: 'bee' | 'drone' | 'subcontractor';
  title: string;
  provider: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  progress: number;
  startTime: string;
  estimatedCompletion: string;
  location: string;
  cost: number;
}

interface UnifiedTaskHubCardProps {
  className?: string;
}

export default function UnifiedTaskHubCard({ className }: UnifiedTaskHubCardProps) {
  const [activeTab, setActiveTab] = useState('available');
  const [selectedTaskType, setSelectedTaskType] = useState<'all' | 'bee' | 'drone' | 'subcontractor'>('all');
  const [taskOptions, setTaskOptions] = useState<TaskOption[]>([]);
  const [activeTasks, setActiveTasks] = useState<ActiveTask[]>([]);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockTaskOptions: TaskOption[] = [
      {
        id: '1',
        type: 'bee',
        title: 'Site Inspection - Gauteng',
        description: 'Professional site visit and documentation for construction project',
        provider: 'SANTACO Logistics',
        cost: 300,
        duration: '2 hours',
        availability: 'available',
        rating: 4.8,
        specialties: ['Construction', 'Documentation', 'Measurements'],
        estimatedCompletion: '2024-01-16 14:00',
        aiRecommendation: 'Cost-effective option with excellent track record',
        costEfficiency: 92
      },
      {
        id: '2',
        type: 'drone',
        title: 'Aerial Site Survey',
        description: 'High-resolution aerial photography and 3D mapping',
        provider: 'SkyView Drones',
        cost: 450,
        duration: '1 hour',
        availability: 'available',
        rating: 4.9,
        specialties: ['Aerial Photography', '3D Mapping', 'Thermal Imaging'],
        estimatedCompletion: '2024-01-16 10:00',
        aiRecommendation: 'Faster and more comprehensive than ground inspection',
        costEfficiency: 88
      },
      {
        id: '3',
        type: 'subcontractor',
        title: 'Electrical Installation',
        description: 'Licensed electrician for commercial electrical work',
        provider: 'PowerTech Solutions',
        cost: 1200,
        duration: '1 day',
        availability: 'busy',
        rating: 4.7,
        specialties: ['Commercial Electrical', 'Industrial', 'Compliance'],
        estimatedCompletion: '2024-01-18 17:00',
        aiRecommendation: 'Premium service with full compliance guarantee',
        costEfficiency: 85
      }
    ];

    const mockActiveTasks: ActiveTask[] = [
      {
        id: 'at1',
        type: 'bee',
        title: 'Site Documentation - Cape Town',
        provider: 'SANTACO Logistics',
        status: 'in_progress',
        progress: 65,
        startTime: '2024-01-15 09:00',
        estimatedCompletion: '2024-01-15 15:00',
        location: 'Cape Town, Western Cape',
        cost: 280
      },
      {
        id: 'at2',
        type: 'drone',
        title: 'Progress Monitoring - Durban',
        provider: 'AerialPro',
        status: 'completed',
        progress: 100,
        startTime: '2024-01-14 08:00',
        estimatedCompletion: '2024-01-14 10:00',
        location: 'Durban, KwaZulu-Natal',
        cost: 380
      }
    ];

    setTaskOptions(mockTaskOptions);
    setActiveTasks(mockActiveTasks);
  }, []);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'bee': return <FiTruck className="w-4 h-4" />;
      case 'drone': return <FiCamera className="w-4 h-4" />;
      case 'subcontractor': return <FiUsers className="w-4 h-4" />;
      default: return <FiSettings className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'bee': return 'bg-accent-orange';
      case 'drone': return 'bg-accent-blue';
      case 'subcontractor': return 'bg-accent-green';
      default: return 'bg-gray-500';
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'available': return 'text-accent-green';
      case 'busy': return 'text-accent-orange';
      case 'unavailable': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-accent-green';
      case 'in_progress': return 'bg-accent-blue';
      case 'pending': return 'bg-accent-orange';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const filteredTaskOptions = selectedTaskType === 'all' 
    ? taskOptions 
    : taskOptions.filter(task => task.type === selectedTaskType);

  const handleBookTask = (taskId: string) => {
    console.log(`Booking task ${taskId}`);
    // Implement actual booking logic here
  };

  const handleViewTask = (taskId: string) => {
    console.log(`Viewing task details ${taskId}`);
    // Implement task details view
  };

  return (
    <Card className={`bg-card-bg border-border/50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-text-primary flex items-center gap-2">
            <FiZap className="w-5 h-5 text-accent-orange" />
            Unified Task Hub
          </CardTitle>
          <div className="flex gap-1">
            <Badge variant="secondary" className="bg-accent-orange/20 text-accent-orange text-xs">
              {taskOptions.filter(t => t.availability === 'available').length} Available
            </Badge>
            <Badge variant="secondary" className="bg-accent-blue/20 text-accent-blue text-xs">
              {activeTasks.filter(t => t.status === 'in_progress').length} Active
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mx-4 mb-4">
            <TabsTrigger value="available">Available Tasks</TabsTrigger>
            <TabsTrigger value="active">Active Tasks</TabsTrigger>
          </TabsList>

          <TabsContent value="available" className="px-4 pb-4">
            {/* Task type filters */}
            <div className="flex gap-2 mb-4 overflow-x-auto">
              {[
                { key: 'all', label: 'All', icon: FiSettings },
                { key: 'bee', label: 'Bees', icon: FiTruck },
                { key: 'drone', label: 'Drones', icon: FiCamera },
                { key: 'subcontractor', label: 'Subcontractors', icon: FiUsers }
              ].map(({ key, label, icon: Icon }) => (
                <Button
                  key={key}
                  size="sm"
                  variant={selectedTaskType === key ? "default" : "outline"}
                  onClick={() => setSelectedTaskType(key as any)}
                  className="text-xs whitespace-nowrap"
                >
                  <Icon className="w-3 h-3 mr-1" />
                  {label}
                </Button>
              ))}
            </div>

            <div className="space-y-3 max-h-80 overflow-y-auto">
              {filteredTaskOptions.map((task) => (
                <div
                  key={task.id}
                  className="bg-card-bg-light rounded-lg p-4 border border-border/30 hover:border-border/60 transition-colors"
                >
                  {/* Header */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge className={`${getTypeColor(task.type)} text-white text-xs px-2 py-1 flex items-center gap-1`}>
                        {getTypeIcon(task.type)}
                        {task.type.toUpperCase()}
                      </Badge>
                      <span className={`text-xs font-medium ${getAvailabilityColor(task.availability)}`}>
                        {task.availability.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FiTrendingUp className="w-3 h-3 text-accent-green" />
                      <span className="text-xs text-accent-green">{task.costEfficiency}%</span>
                    </div>
                  </div>

                  {/* Title and provider */}
                  <h4 className="font-semibold text-text-primary text-sm mb-1">{task.title}</h4>
                  <p className="text-text-secondary text-xs mb-2">{task.provider}</p>
                  <p className="text-text-secondary text-xs mb-3 line-clamp-2">{task.description}</p>

                  {/* Details grid */}
                  <div className="grid grid-cols-3 gap-2 mb-3 text-xs">
                    <div className="text-center">
                      <div className="font-semibold text-accent-green">R{task.cost}</div>
                      <div className="text-text-secondary">Cost</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-accent-blue">{task.duration}</div>
                      <div className="text-text-secondary">Duration</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-accent-orange">{task.rating}★</div>
                      <div className="text-text-secondary">Rating</div>
                    </div>
                  </div>

                  {/* Specialties */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {task.specialties.slice(0, 3).map((specialty, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>

                  {/* AI Recommendation */}
                  <Alert className="mb-3 bg-accent-blue/10 border-accent-blue/30">
                    <AlertDescription className="text-xs text-text-primary">
                      <strong>AI:</strong> {task.aiRecommendation}
                    </AlertDescription>
                  </Alert>

                  {/* Action buttons */}
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      className="flex-1 bg-accent-green hover:bg-accent-green/80 text-white text-xs"
                      onClick={() => handleBookTask(task.id)}
                      disabled={task.availability !== 'available'}
                    >
                      <FiPlus className="w-3 h-3 mr-1" />
                      Book Now
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-xs"
                      onClick={() => handleViewTask(task.id)}
                    >
                      <FiEye className="w-3 h-3 mr-1" />
                      Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="active" className="px-4 pb-4">
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {activeTasks.map((task) => (
                <div
                  key={task.id}
                  className="bg-card-bg-light rounded-lg p-4 border border-border/30"
                >
                  {/* Header */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge className={`${getTypeColor(task.type)} text-white text-xs px-2 py-1 flex items-center gap-1`}>
                        {getTypeIcon(task.type)}
                        {task.type.toUpperCase()}
                      </Badge>
                      <Badge className={`${getStatusColor(task.status)} text-white text-xs px-2 py-1`}>
                        {task.status.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                    <span className="text-xs text-text-secondary">R{task.cost}</span>
                  </div>

                  {/* Title and provider */}
                  <h4 className="font-semibold text-text-primary text-sm mb-1">{task.title}</h4>
                  <p className="text-text-secondary text-xs mb-2">{task.provider}</p>

                  {/* Progress bar */}
                  {task.status === 'in_progress' && (
                    <div className="mb-3">
                      <div className="flex justify-between text-xs text-text-secondary mb-1">
                        <span>Progress</span>
                        <span>{task.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-accent-blue h-2 rounded-full transition-all duration-300" 
                          style={{ width: `${task.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Details */}
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center gap-1 text-text-secondary">
                      <FiMapPin className="w-3 h-3" />
                      <span className="truncate">{task.location}</span>
                    </div>
                    <div className="flex items-center gap-1 text-text-secondary">
                      <FiClock className="w-3 h-3" />
                      <span>ETA: {new Date(task.estimatedCompletion).toLocaleTimeString()}</span>
                    </div>
                  </div>

                  {/* Action button */}
                  <div className="mt-3">
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full text-xs"
                      onClick={() => handleViewTask(task.id)}
                    >
                      <FiEye className="w-3 h-3 mr-1" />
                      View Progress
                    </Button>
                  </div>
                </div>
              ))}

              {activeTasks.length === 0 && (
                <div className="text-center py-8 text-text-secondary">
                  <FiClock className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No active tasks</p>
                  <p className="text-xs">Book a task to get started</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
