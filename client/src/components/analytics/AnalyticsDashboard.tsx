import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  DollarSign, 
  Target, 
  AlertTriangle,
  Brain,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';
import { supabase } from '@/lib/supabaseClient';
import { sentry } from '@/lib/sentry';

interface CompetitorIntelligence {
  id: string;
  competitor_name: string;
  market_share: number;
  avg_bid_amount: number;
  success_rate: number;
  active_tenders: number;
  strengths: string[];
  weaknesses: string[];
  last_updated: string;
}

interface AIRecommendation {
  id: string;
  type: 'tender_match' | 'pricing' | 'strategy';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  action_required: boolean;
  created_at: string;
}

interface PricingInsight {
  category: string;
  avg_winning_price: number;
  price_trend: number;
  competition_level: number;
  recommended_margin: number;
}

export function AnalyticsDashboard() {
  const [competitors, setCompetitors] = useState<CompetitorIntelligence[]>([]);
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [pricingInsights, setPricingInsights] = useState<PricingInsight[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Load competitor intelligence
      const { data: competitorData, error: competitorError } = await supabase
        .from('competitor_intelligence')
        .select('*')
        .order('market_share', { ascending: false })
        .limit(10);

      if (competitorError) throw competitorError;

      // Load AI recommendations
      const { data: recommendationData, error: recommendationError } = await supabase
        .from('ai_recommendations')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);

      if (recommendationError) throw recommendationError;

      // Load pricing insights from cache
      const { data: pricingData, error: pricingError } = await supabase
        .from('pricing_model_cache')
        .select('*')
        .order('last_updated', { ascending: false })
        .limit(15);

      if (pricingError) throw pricingError;

      setCompetitors(competitorData || []);
      setRecommendations(recommendationData || []);
      setPricingInsights(pricingData || []);

      // Track analytics view
      sentry.captureBusinessEvent('analytics_dashboard_viewed', {
        competitors_count: competitorData?.length || 0,
        recommendations_count: recommendationData?.length || 0,
        pricing_insights_count: pricingData?.length || 0
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analytics data';
      setError(errorMessage);
      sentry.captureException(err as Error, {
        tags: { component: 'analytics_dashboard' }
      });
    } finally {
      setLoading(false);
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: number) => {
    return trend > 0 ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-gray-600">AI-powered insights and competitive intelligence</p>
        </div>
        <Button onClick={loadAnalyticsData} variant="outline">
          <Activity className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="competitors">Competitor Intelligence</TabsTrigger>
          <TabsTrigger value="recommendations">AI Recommendations</TabsTrigger>
          <TabsTrigger value="pricing">Pricing Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Competitors</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{competitors.length}</div>
                <p className="text-xs text-muted-foreground">
                  Tracked in your market
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">AI Recommendations</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{recommendations.length}</div>
                <p className="text-xs text-muted-foreground">
                  {recommendations.filter(r => r.action_required).length} require action
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Market Categories</CardTitle>
                <PieChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{pricingInsights.length}</div>
                <p className="text-xs text-muted-foreground">
                  With pricing data
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Competition</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {pricingInsights.length > 0 
                    ? Math.round(pricingInsights.reduce((acc, p) => acc + p.competition_level, 0) / pricingInsights.length * 100)
                    : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Market competition level
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="competitors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Competitor Intelligence
              </CardTitle>
              <CardDescription>
                Real-time competitive analysis and market positioning
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {competitors.map((competitor) => (
                  <div key={competitor.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-lg">{competitor.competitor_name}</h3>
                      <Badge variant="outline">
                        {competitor.market_share.toFixed(1)}% market share
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <p className="text-sm text-gray-600">Success Rate</p>
                        <div className="flex items-center gap-2">
                          <Progress value={competitor.success_rate * 100} className="flex-1" />
                          <span className="text-sm font-medium">
                            {(competitor.success_rate * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm text-gray-600">Avg. Bid Amount</p>
                        <p className="font-semibold">
                          R{competitor.avg_bid_amount.toLocaleString()}
                        </p>
                      </div>
                      
                      <div>
                        <p className="text-sm text-gray-600">Active Tenders</p>
                        <p className="font-semibold">{competitor.active_tenders}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-green-700 mb-1">Strengths</p>
                        <div className="flex flex-wrap gap-1">
                          {competitor.strengths.map((strength, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {strength}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-red-700 mb-1">Weaknesses</p>
                        <div className="flex flex-wrap gap-1">
                          {competitor.weaknesses.map((weakness, idx) => (
                            <Badge key={idx} variant="destructive" className="text-xs">
                              {weakness}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI Recommendations
              </CardTitle>
              <CardDescription>
                Machine learning powered insights and strategic recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.map((rec) => (
                  <div key={rec.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold">{rec.title}</h3>
                      <div className="flex items-center gap-2">
                        <Badge className={getImpactColor(rec.impact)}>
                          {rec.impact} impact
                        </Badge>
                        {rec.action_required && (
                          <Badge variant="destructive">Action Required</Badge>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{rec.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Confidence:</span>
                        <Progress value={rec.confidence * 100} className="w-20" />
                        <span className="text-sm font-medium">
                          {(rec.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                      
                      <Badge variant="outline" className="text-xs">
                        {rec.type.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Pricing Insights
              </CardTitle>
              <CardDescription>
                Market pricing analysis and competitive positioning
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pricingInsights.map((insight, idx) => (
                  <div key={idx} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold capitalize">{insight.category}</h3>
                      <div className="flex items-center gap-1">
                        {getTrendIcon(insight.price_trend)}
                        <span className="text-sm font-medium">
                          {insight.price_trend > 0 ? '+' : ''}{(insight.price_trend * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Avg. Winning Price</p>
                        <p className="font-semibold text-lg">
                          R{insight.avg_winning_price.toLocaleString()}
                        </p>
                      </div>
                      
                      <div>
                        <p className="text-sm text-gray-600">Competition Level</p>
                        <div className="flex items-center gap-2">
                          <Progress value={insight.competition_level * 100} className="flex-1" />
                          <span className="text-sm font-medium">
                            {(insight.competition_level * 100).toFixed(0)}%
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm text-gray-600">Recommended Margin</p>
                        <p className="font-semibold text-lg text-green-600">
                          {(insight.recommended_margin * 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
