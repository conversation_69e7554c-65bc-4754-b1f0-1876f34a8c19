import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  AlertTriangle, 
  Shield, 
  TrendingUp, 
  FileText, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  MessageSquare
} from 'lucide-react';
import { supabase } from '@/lib/supabaseClient';
import { sentry } from '@/lib/sentry';

interface RiskDispute {
  id: string;
  dispute_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'resolved' | 'escalated';
  description: string;
  affected_tender_id: string;
  affected_bidder_id: string;
  created_at: string;
  resolution_notes?: string;
}

interface RiskMitigation {
  id: string;
  risk_type: string;
  mitigation_strategy: string;
  implementation_status: 'planned' | 'in_progress' | 'completed' | 'failed';
  effectiveness_score: number;
  cost_estimate: number;
  timeline_days: number;
  responsible_party: string;
  created_at: string;
}

interface BeeRiskProfile {
  id: string;
  bee_id: string;
  risk_score: number;
  risk_factors: string[];
  last_assessment: string;
  mitigation_recommendations: string[];
  compliance_status: 'compliant' | 'warning' | 'non_compliant';
}

export function RiskManagementDashboard() {
  const [disputes, setDisputes] = useState<RiskDispute[]>([]);
  const [mitigations, setMitigations] = useState<RiskMitigation[]>([]);
  const [riskProfiles, setRiskProfiles] = useState<BeeRiskProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDispute, setSelectedDispute] = useState<RiskDispute | null>(null);

  useEffect(() => {
    loadRiskData();
  }, []);

  const loadRiskData = async () => {
    try {
      setLoading(true);
      
      // Load risk disputes
      const { data: disputeData, error: disputeError } = await supabase
        .from('risk_disputes')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      if (disputeError) throw disputeError;

      // Load risk mitigations
      const { data: mitigationData, error: mitigationError } = await supabase
        .from('risk_mitigations')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(30);

      if (mitigationError) throw mitigationError;

      // Load bee risk profiles
      const { data: profileData, error: profileError } = await supabase
        .from('bee_risk_profiles')
        .select('*')
        .order('risk_score', { ascending: false })
        .limit(20);

      if (profileError) throw profileError;

      setDisputes(disputeData || []);
      setMitigations(mitigationData || []);
      setRiskProfiles(profileData || []);

      // Track risk dashboard view
      sentry.captureBusinessEvent('risk_dashboard_viewed', {
        disputes_count: disputeData?.length || 0,
        mitigations_count: mitigationData?.length || 0,
        high_risk_profiles: profileData?.filter(p => p.risk_score > 70).length || 0
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load risk data';
      setError(errorMessage);
      sentry.captureException(err as Error, {
        tags: { component: 'risk_dashboard' }
      });
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'resolved': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'escalated': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'investigating': return <Eye className="h-4 w-4 text-blue-600" />;
      case 'open': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-600';
    if (score >= 60) return 'text-orange-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  const openDisputes = disputes.filter(d => d.status === 'open' || d.status === 'investigating');
  const criticalDisputes = disputes.filter(d => d.severity === 'critical');
  const highRiskProfiles = riskProfiles.filter(p => p.risk_score > 70);

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Risk Management</h1>
          <p className="text-gray-600">Monitor and mitigate operational risks</p>
        </div>
        <Button onClick={loadRiskData} variant="outline">
          <Shield className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      {/* Risk Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Disputes</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{openDisputes.length}</div>
            <p className="text-xs text-muted-foreground">
              {criticalDisputes.length} critical
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Mitigations</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mitigations.filter(m => m.implementation_status === 'in_progress').length}
            </div>
            <p className="text-xs text-muted-foreground">
              {mitigations.filter(m => m.implementation_status === 'completed').length} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Risk Profiles</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{highRiskProfiles.length}</div>
            <p className="text-xs text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Risk Score</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {riskProfiles.length > 0 
                ? Math.round(riskProfiles.reduce((acc, p) => acc + p.risk_score, 0) / riskProfiles.length)
                : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Out of 100
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="disputes" className="space-y-4">
        <TabsList>
          <TabsTrigger value="disputes">Risk Disputes</TabsTrigger>
          <TabsTrigger value="mitigations">Mitigations</TabsTrigger>
          <TabsTrigger value="profiles">Risk Profiles</TabsTrigger>
        </TabsList>

        <TabsContent value="disputes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Risk Disputes
              </CardTitle>
              <CardDescription>
                Active disputes and resolution tracking
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {disputes.map((dispute) => (
                  <div key={dispute.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(dispute.status)}
                        <div>
                          <h3 className="font-semibold">{dispute.dispute_type}</h3>
                          <p className="text-sm text-gray-600">
                            {new Date(dispute.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getSeverityColor(dispute.severity)}>
                          {dispute.severity}
                        </Badge>
                        <Badge variant="outline">
                          {dispute.status}
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-3">{dispute.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500">
                        Tender: {dispute.affected_tender_id} | Bidder: {dispute.affected_bidder_id}
                      </div>
                      
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>{dispute.dispute_type}</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <h4 className="font-medium mb-2">Description</h4>
                              <p className="text-gray-700">{dispute.description}</p>
                            </div>
                            
                            {dispute.resolution_notes && (
                              <div>
                                <h4 className="font-medium mb-2">Resolution Notes</h4>
                                <p className="text-gray-700">{dispute.resolution_notes}</p>
                              </div>
                            )}
                            
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-medium mb-1">Severity</h4>
                                <Badge className={getSeverityColor(dispute.severity)}>
                                  {dispute.severity}
                                </Badge>
                              </div>
                              <div>
                                <h4 className="font-medium mb-1">Status</h4>
                                <Badge variant="outline">{dispute.status}</Badge>
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mitigations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Risk Mitigations
              </CardTitle>
              <CardDescription>
                Mitigation strategies and implementation progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mitigations.map((mitigation) => (
                  <div key={mitigation.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold">{mitigation.risk_type}</h3>
                        <p className="text-sm text-gray-600">{mitigation.responsible_party}</p>
                      </div>
                      <Badge variant="outline">
                        {mitigation.implementation_status.replace('_', ' ')}
                      </Badge>
                    </div>
                    
                    <p className="text-gray-700 mb-3">{mitigation.mitigation_strategy}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <p className="text-sm text-gray-600">Effectiveness</p>
                        <div className="flex items-center gap-2">
                          <Progress value={mitigation.effectiveness_score} className="flex-1" />
                          <span className="text-sm font-medium">
                            {mitigation.effectiveness_score}%
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm text-gray-600">Cost Estimate</p>
                        <p className="font-semibold">R{mitigation.cost_estimate.toLocaleString()}</p>
                      </div>
                      
                      <div>
                        <p className="text-sm text-gray-600">Timeline</p>
                        <p className="font-semibold">{mitigation.timeline_days} days</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profiles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Bee Risk Profiles
              </CardTitle>
              <CardDescription>
                Individual risk assessments and compliance status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {riskProfiles.map((profile) => (
                  <div key={profile.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold">Bee ID: {profile.bee_id}</h3>
                        <p className="text-sm text-gray-600">
                          Last assessed: {new Date(profile.last_assessment).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="text-right">
                          <p className="text-sm text-gray-600">Risk Score</p>
                          <p className={`text-2xl font-bold ${getRiskScoreColor(profile.risk_score)}`}>
                            {profile.risk_score}
                          </p>
                        </div>
                        <Badge 
                          variant={profile.compliance_status === 'compliant' ? 'default' : 'destructive'}
                        >
                          {profile.compliance_status.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-red-700 mb-2">Risk Factors</p>
                        <div className="flex flex-wrap gap-1">
                          {profile.risk_factors.map((factor, idx) => (
                            <Badge key={idx} variant="destructive" className="text-xs">
                              {factor}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-blue-700 mb-2">Recommendations</p>
                        <div className="flex flex-wrap gap-1">
                          {profile.mitigation_recommendations.map((rec, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {rec}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
