import React from 'react';
import { useLocation } from 'wouter';

interface PremiumUpgradePromptsProps {
  showPrompts?: boolean;
}

export default function PremiumUpgradePrompts({ showPrompts = true }: PremiumUpgradePromptsProps) {
  const [, setLocation] = useLocation();

  if (!showPrompts) return null;

  const styles = {
    container: {
      background: 'linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%)',
      borderRadius: '12px',
      padding: '2rem',
      color: 'white',
      margin: '2rem 0',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
    },
    title: {
      fontSize: '1.5rem',
      fontWeight: '600',
      marginBottom: '1rem',
    },
    description: {
      fontSize: '1rem',
      marginBottom: '1.5rem',
      opacity: 0.9,
    },
    button: {
      background: 'white',
      color: '#4f46e5',
      padding: '0.75rem 1.5rem',
      borderRadius: '8px',
      fontWeight: '600',
      cursor: 'pointer',
      border: 'none',
      transition: 'transform 0.2s ease-in-out',
      ':hover': {
        transform: 'translateY(-2px)',
      },
    },
    features: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: '1rem',
      marginTop: '1.5rem',
    },
    feature: {
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem',
    },
    checkIcon: {
      color: '#10b981',
    },
  };

  return (
    <div style={styles.container}>
      <h2 style={styles.title}>Upgrade to Premium</h2>
      <p style={styles.description}>
        Get access to advanced tender analytics, priority support, and exclusive features to help you win more tenders.
      </p>
      <button
        style={styles.button}
        onClick={() => setLocation('/premium')}
      >
        Upgrade Now
      </button>
      <div style={styles.features}>
        <div style={styles.feature}>
          <span style={styles.checkIcon}>✓</span>
          <span>Advanced Analytics</span>
        </div>
        <div style={styles.feature}>
          <span style={styles.checkIcon}>✓</span>
          <span>Priority Support</span>
        </div>
        <div style={styles.feature}>
          <span style={styles.checkIcon}>✓</span>
          <span>Exclusive Features</span>
        </div>
      </div>
    </div>
  );
} 