import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { 
  Users, 
  Search, 
  Star, 
  MapPin, 
  Briefcase, 
  Award, 
  TrendingUp,
  CheckCircle,
  Clock,
  AlertTriangle,
  Filter,
  UserPlus
} from 'lucide-react';
import { supabase } from '@/lib/supabaseClient';
import { sentry } from '@/lib/sentry';

interface SkillProvider {
  provider_id: string;
  user_id: number;
  bio: string;
  skills: string[];
  verification_status: 'pending' | 'verified' | 'rejected';
  rating: number;
  num_reviews: number;
  availability: boolean;
  hourly_rate: number;
  buyout_price: number;
  location: string;
  experience_years: number;
  certifications: string[];
  portfolio_items: string[];
  created_at: string;
}

interface RecruitmentRequest {
  request_id: string;
  tender_id: number;
  required_skills: string[];
  budget_range: { min: number; max: number };
  urgency: 'low' | 'medium' | 'high' | 'critical';
  location_preference: string;
  status: 'open' | 'matching' | 'completed' | 'cancelled';
  created_at: string;
  deadline: string;
}

interface SkillDemandAnalysis {
  skill_name: string;
  demand_score: number;
  supply_score: number;
  avg_rate: number;
  growth_trend: number;
  market_gap: number;
  projected_demand: number;
}

export function RecruitmentDashboard() {
  const [providers, setProviders] = useState<SkillProvider[]>([]);
  const [requests, setRequests] = useState<RecruitmentRequest[]>([]);
  const [demandAnalysis, setDemandAnalysis] = useState<SkillDemandAnalysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    loadRecruitmentData();
  }, []);

  const loadRecruitmentData = async () => {
    try {
      setLoading(true);
      
      // Load skill providers
      const { data: providerData, error: providerError } = await supabase
        .from('skill_provider_profiles')
        .select('*')
        .order('rating', { ascending: false })
        .limit(50);

      if (providerError) throw providerError;

      // Load recruitment requests
      const { data: requestData, error: requestError } = await supabase
        .from('bee_recruitment_requests')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(30);

      if (requestError) throw requestError;

      // Load skill demand analysis
      const { data: demandData, error: demandError } = await supabase
        .from('skill_demand_analysis')
        .select('*')
        .order('demand_score', { ascending: false })
        .limit(20);

      if (demandError) throw demandError;

      setProviders(providerData || []);
      setRequests(requestData || []);
      setDemandAnalysis(demandData || []);

      // Track recruitment dashboard view
      sentry.captureBusinessEvent('recruitment_dashboard_viewed', {
        providers_count: providerData?.length || 0,
        active_requests: requestData?.filter(r => r.status === 'open').length || 0,
        verified_providers: providerData?.filter(p => p.verification_status === 'verified').length || 0
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load recruitment data';
      setError(errorMessage);
      sentry.captureException(err as Error, {
        tags: { component: 'recruitment_dashboard' }
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'open': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = searchTerm === '' || 
      provider.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase())) ||
      provider.bio.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'all' || provider.verification_status === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  const verifiedProviders = providers.filter(p => p.verification_status === 'verified');
  const activeRequests = requests.filter(r => r.status === 'open' || r.status === 'matching');
  const availableProviders = providers.filter(p => p.availability);

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Recruitment Hub</h1>
          <p className="text-gray-600">Skill provider marketplace and recruitment management</p>
        </div>
        <Button onClick={loadRecruitmentData} variant="outline">
          <Users className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Providers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{providers.length}</div>
            <p className="text-xs text-muted-foreground">
              {verifiedProviders.length} verified
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Requests</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeRequests.length}</div>
            <p className="text-xs text-muted-foreground">
              {requests.filter(r => r.urgency === 'critical').length} critical
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Now</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{availableProviders.length}</div>
            <p className="text-xs text-muted-foreground">
              Ready for projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {verifiedProviders.length > 0 
                ? (verifiedProviders.reduce((acc, p) => acc + p.rating, 0) / verifiedProviders.length).toFixed(1)
                : '0.0'}
            </div>
            <p className="text-xs text-muted-foreground">
              Out of 5.0
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="providers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="providers">Skill Providers</TabsTrigger>
          <TabsTrigger value="requests">Recruitment Requests</TabsTrigger>
          <TabsTrigger value="analytics">Market Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          {/* Search and Filter */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by skills or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Status</option>
              <option value="verified">Verified</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Skill Providers ({filteredProviders.length})
              </CardTitle>
              <CardDescription>
                Browse and manage skill provider profiles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredProviders.map((provider) => (
                  <div key={provider.provider_id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-semibold">Provider #{provider.user_id}</p>
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            <span className="text-sm">{provider.rating.toFixed(1)}</span>
                            <span className="text-xs text-gray-500">({provider.num_reviews})</span>
                          </div>
                        </div>
                      </div>
                      <Badge className={getStatusColor(provider.verification_status)}>
                        {provider.verification_status}
                      </Badge>
                    </div>

                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{provider.bio}</p>

                    <div className="space-y-2 mb-3">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-3 w-3 text-gray-400" />
                        <span>{provider.location}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Briefcase className="h-3 w-3 text-gray-400" />
                        <span>{provider.experience_years} years experience</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Award className="h-3 w-3 text-gray-400" />
                        <span>{provider.certifications.length} certifications</span>
                      </div>
                    </div>

                    <div className="mb-3">
                      <p className="text-xs text-gray-500 mb-1">Skills</p>
                      <div className="flex flex-wrap gap-1">
                        {provider.skills.slice(0, 3).map((skill, idx) => (
                          <Badge key={idx} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                        {provider.skills.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{provider.skills.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-sm">
                        <p className="font-semibold">R{provider.hourly_rate}/hr</p>
                        <p className="text-xs text-gray-500">
                          Buyout: R{provider.buyout_price.toLocaleString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        {provider.availability ? (
                          <Badge className="bg-green-100 text-green-800">Available</Badge>
                        ) : (
                          <Badge variant="secondary">Busy</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requests" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Recruitment Requests
              </CardTitle>
              <CardDescription>
                Active and completed recruitment requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {requests.map((request) => (
                  <div key={request.request_id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold">Tender #{request.tender_id}</h3>
                        <p className="text-sm text-gray-600">
                          Created: {new Date(request.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getUrgencyColor(request.urgency)}>
                          {request.urgency}
                        </Badge>
                        <Badge className={getStatusColor(request.status)}>
                          {request.status}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                      <div>
                        <p className="text-sm font-medium mb-1">Required Skills</p>
                        <div className="flex flex-wrap gap-1">
                          {request.required_skills.map((skill, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium mb-1">Budget Range</p>
                        <p className="text-sm">
                          R{request.budget_range.min.toLocaleString()} - R{request.budget_range.max.toLocaleString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        Location: {request.location_preference} | 
                        Deadline: {new Date(request.deadline).toLocaleDateString()}
                      </div>
                      
                      {request.status === 'open' && (
                        <Button size="sm" variant="outline">
                          <UserPlus className="h-4 w-4 mr-1" />
                          Match Providers
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Skill Demand Analytics
              </CardTitle>
              <CardDescription>
                Market analysis and demand forecasting
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {demandAnalysis.map((analysis, idx) => (
                  <div key={idx} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold capitalize">{analysis.skill_name}</h3>
                      <Badge variant="outline">
                        Gap: {(analysis.market_gap * 100).toFixed(0)}%
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Demand Score</p>
                        <div className="flex items-center gap-2">
                          <Progress value={analysis.demand_score} className="flex-1" />
                          <span className="text-sm font-medium">
                            {analysis.demand_score.toFixed(0)}%
                          </span>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600">Supply Score</p>
                        <div className="flex items-center gap-2">
                          <Progress value={analysis.supply_score} className="flex-1" />
                          <span className="text-sm font-medium">
                            {analysis.supply_score.toFixed(0)}%
                          </span>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600">Avg. Rate</p>
                        <p className="font-semibold">R{analysis.avg_rate}/hr</p>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600">Growth Trend</p>
                        <div className="flex items-center gap-1">
                          {analysis.growth_trend > 0 ? (
                            <TrendingUp className="h-4 w-4 text-green-600" />
                          ) : (
                            <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />
                          )}
                          <span className="text-sm font-medium">
                            {analysis.growth_trend > 0 ? '+' : ''}{(analysis.growth_trend * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
