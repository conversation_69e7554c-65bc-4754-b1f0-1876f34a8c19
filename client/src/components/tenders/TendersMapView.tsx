import React, { useRef, useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

// Set Mapbox access token
mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiYmlkYmVlcyIsImEiOiJjbWJiOGh0cXQwOXJvMmtzb2x1Ymc0NGF6In0.rdxv8HdMok6ZgmXe392Aaw';

interface Tender {
  id: number;
  title: string;
  status: string;
  issuer: string;
  win_chance: number;
  due_date: string;
  location: string;
  value: string;
  description?: string;
  lat?: number;
  lng?: number;
}

interface TendersMapViewProps {
  tenders: Tender[];
}

export default function TendersMapView({ tenders }: TendersMapViewProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [selectedMarker, setSelectedMarker] = useState<Tender | null>(null);
  const [mapError, setMapError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!mapContainer.current) return;

    try {
      console.log('Initializing Mapbox map...');
      console.log('Mapbox token:', mapboxgl.accessToken ? 'Available' : 'Missing');
      console.log('Full token:', mapboxgl.accessToken);

      // Initialize map
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/light-v11',
        center: [18.4241, -33.9249], // Cape Town
        zoom: 5
      });

      // Add navigation controls
      map.current.addControl(new mapboxgl.NavigationControl());

      // Handle map load
      map.current.on('load', () => {
        console.log('Map loaded successfully');
        setIsLoading(false);
        setMapError(null);
      });

      // Also handle style load for better marker placement
      map.current.on('styledata', () => {
        console.log('Map style loaded');
      });

      // Handle map errors
      map.current.on('error', (e) => {
        console.error('Map error:', e);
        setMapError(`Failed to load map: ${e.error?.message || 'Unknown error'}`);
        setIsLoading(false);
      });

    } catch (error) {
      console.error('Error initializing map:', error);
      setMapError(`Failed to initialize map: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsLoading(false);
    }

    // Cleanup
    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, []);

  useEffect(() => {
    const currentMap = map.current;
    if (!currentMap || !currentMap.isStyleLoaded()) return;

    console.log('Adding markers for tenders:', tenders.length);

    // Clear existing markers
    const existingMarkers = document.querySelectorAll('.mapboxgl-marker');
    existingMarkers.forEach(marker => marker.remove());

    // Add markers for each tender with coordinates
    const tendersWithCoords = tenders.filter(tender =>
      tender.lat && tender.lng &&
      !isNaN(tender.lat) && !isNaN(tender.lng) &&
      tender.lat >= -90 && tender.lat <= 90 &&
      tender.lng >= -180 && tender.lng <= 180
    );

    console.log(`Found ${tendersWithCoords.length} tenders with valid coordinates`);

    tendersWithCoords.forEach(tender => {
      const marker = new mapboxgl.Marker({
        color: tender.win_chance > 70 ? '#10b981' : tender.win_chance > 40 ? '#f59e0b' : '#ef4444'
      })
        .setLngLat([tender.lng!, tender.lat!])
        .setPopup(
          new mapboxgl.Popup({ offset: 25 })
            .setHTML(`
              <div style="max-width: 250px;">
                <h3 style="margin: 0 0 8px 0; font-size: 14px;">${tender.title}</h3>
                ${tender.description ? `<p style="margin: 4px 0; font-size: 12px; color: #666;">${tender.description.substring(0, 100)}...</p>` : ''}
                <p style="margin: 4px 0; font-size: 12px;"><strong>Issuer:</strong> ${tender.issuer}</p>
                <p style="margin: 4px 0; font-size: 12px;"><strong>Location:</strong> ${tender.location}</p>
                <p style="margin: 4px 0; font-size: 12px;"><strong>Due Date:</strong> ${new Date(tender.due_date).toLocaleDateString()}</p>
                <p style="margin: 4px 0; font-size: 12px;"><strong>Win Chance:</strong> ${tender.win_chance}%</p>
                <p style="margin: 4px 0; font-size: 12px;"><strong>Value:</strong> ${tender.value}</p>
              </div>
            `)
        )
        .addTo(currentMap);

      marker.getElement().addEventListener('click', () => {
        setSelectedMarker(tender);
      });
    });

    // Fit map to show all markers if we have any
    if (tendersWithCoords.length > 0) {
      const bounds = new mapboxgl.LngLatBounds();
      tendersWithCoords.forEach(tender => {
        bounds.extend([tender.lng!, tender.lat!]);
      });

      currentMap.fitBounds(bounds, {
        padding: 50,
        maxZoom: 10
      });
    }
  }, [tenders]);

  const styles = {
    container: {
      height: '400px',
      borderRadius: '12px',
      overflow: 'hidden',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
    },
    selectedTender: {
      position: 'absolute' as 'absolute',
      bottom: '1rem',
      left: '1rem',
      right: '1rem',
      background: 'rgba(255, 255, 255, 0.95)',
      padding: '1rem',
      borderRadius: '8px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      zIndex: 1,
    }
  };

  // Show error state or fallback
  if (mapError) {
    const tendersWithCoords = tenders.filter(tender =>
      tender.lat && tender.lng &&
      !isNaN(tender.lat) && !isNaN(tender.lng)
    );

    return (
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '12px',
        padding: '2rem',
        textAlign: 'center',
        color: 'white',
        marginBottom: '2rem'
      }}>
        <h3>🗺️ Map View</h3>
        <p style={{ color: '#ff6b6b', marginBottom: '1rem' }}>{mapError}</p>
        <p>Found {tendersWithCoords.length} tenders with location data out of {tenders?.length || 0} total</p>
        <div style={{ marginTop: '1rem', fontSize: '0.9rem', opacity: 0.8 }}>
          <p>Locations include: {tendersWithCoords.slice(0, 3).map(t => t.issuer).join(', ')}</p>
        </div>
        <button
          onClick={() => window.location.reload()}
          style={{
            background: '#4CAF50',
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            cursor: 'pointer',
            marginTop: '1rem'
          }}
        >
          Retry Map
        </button>
      </div>
    );
  }

  return (
    <div style={{ position: 'relative' }}>
      {isLoading && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '1rem',
          borderRadius: '8px',
          zIndex: 1000
        }}>
          Loading map...
        </div>
      )}
      <div ref={mapContainer} style={styles.container} />
      {selectedMarker && (
        <div style={styles.selectedTender}>
          <h3>{selectedMarker.title}</h3>
          <p><strong>Issuer:</strong> {selectedMarker.issuer}</p>
          <p><strong>Location:</strong> {selectedMarker.location}</p>
          <p><strong>Due Date:</strong> {new Date(selectedMarker.due_date).toLocaleDateString()}</p>
          <p><strong>Win Chance:</strong> {selectedMarker.win_chance}%</p>
          <p><strong>Value:</strong> {selectedMarker.value}</p>
        </div>
      )}
    </div>
  );
} 