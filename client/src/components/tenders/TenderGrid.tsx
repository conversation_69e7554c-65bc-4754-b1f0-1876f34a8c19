import React from 'react';
import TenderCard from './TenderCard';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, Search } from 'lucide-react';

interface Tender {
  id: number;
  title: string;
  status: string;
  issuer: string;
  win_chance: number;
  due_date: string;
  location: string;
  value: string;
  description?: string;
  lat?: number;
  lng?: number;
  tender_number?: string;
  main_procurement_category?: string;
  created_at?: string;
}

interface TenderGridProps {
  tenders: Tender[];
  isLoading?: boolean;
  error?: string | null;
  onTenderView?: (tender: Tender) => void;
  emptyStateMessage?: string;
}

// Loading skeleton component
const TenderCardSkeleton = () => (
  <Card className="h-[400px]">
    <div className="p-6 space-y-4">
      <div className="flex justify-between items-start">
        <div className="flex-1 space-y-2">
          <Skeleton className="h-6 w-full" />
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <Skeleton className="h-6 w-16" />
      </div>
      
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-32" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-28" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-20" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>
      
      <Skeleton className="h-9 w-full" />
    </div>
  </Card>
);

// Empty state component
const EmptyState = ({ message, isError = false }: { message: string; isError?: boolean }) => (
  <div className="col-span-full flex flex-col items-center justify-center py-12 px-4">
    <div className="text-center space-y-4">
      {isError ? (
        <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
      ) : (
        <Search className="h-12 w-12 text-muted-foreground mx-auto" />
      )}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-foreground">
          {isError ? 'Something went wrong' : 'No tenders found'}
        </h3>
        <p className="text-muted-foreground max-w-md">
          {message}
        </p>
      </div>
    </div>
  </div>
);

export default function TenderGrid({ 
  tenders, 
  isLoading = false, 
  error = null,
  onTenderView,
  emptyStateMessage = "No tenders match your current filters. Try adjusting your search criteria."
}: TenderGridProps) {
  
  // Error state
  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <EmptyState 
          message={error} 
          isError={true}
        />
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 12 }).map((_, index) => (
          <TenderCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  // Empty state
  if (!tenders || tenders.length === 0) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <EmptyState message={emptyStateMessage} />
      </div>
    );
  }

  // Render tenders grid
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {tenders.map((tender) => (
        <TenderCard 
          key={tender.id} 
          tender={tender} 
          onView={onTenderView}
        />
      ))}
    </div>
  );
}
