import React from 'react';
import { useLocation } from 'wouter';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Calendar, DollarSign, Building2, Eye, TrendingUp, Brain, ExternalLink, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Tender {
  id: number;
  title: string;
  status: string;
  issuer: string;
  win_chance: number;
  due_date: string;
  location: string;
  value: string;
  description?: string;
  lat?: number;
  lng?: number;
  tender_number?: string;
  main_procurement_category?: string;
  created_at?: string;
}

interface TenderCardProps {
  tender: Tender;
  onView?: (tender: Tender) => void;
}

export default function TenderCard({ tender, onView }: TenderCardProps) {
  const [, setLocation] = useLocation();

  const handleCardClick = async () => {
    if (onView) {
      onView(tender);
    }

    // Publish tender viewed event
    try {
      await fetch('http://localhost:3005/api/events/tender/viewed', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenderId: tender.id.toString(),
          userId: null, // TODO: Get from auth context
        }),
      });
    } catch (error) {
      console.warn('Failed to publish tender viewed event:', error);
    }

    setLocation(`/tenders/${tender.id}`);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'open':
      case 'active':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'closed':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getWinChanceColor = (winChance: number) => {
    if (winChance >= 70) return 'bg-green-100 text-green-800';
    if (winChance >= 50) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const handleOpenInTMS = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Open Enterprise TMS with this tender
    window.open(`http://localhost:3001?tender=${tender.id}`, '_blank');
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'TBD';
    try {
      return new Date(dateString).toLocaleDateString('en-ZA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'TBD';
    }
  };

  const formatValue = (value: string | number) => {
    if (!value) return 'Not specified';
    if (typeof value === 'string' && value.includes('R')) return value;
    return `R ${Number(value).toLocaleString()}`;
  };

  const truncateText = (text: string, lines: number) => {
    if (!text) return '';
    const words = text.split(' ');
    const wordsPerLine = 12; // Approximate words per line
    const maxWords = lines * wordsPerLine;
    if (words.length <= maxWords) return text;
    return words.slice(0, maxWords).join(' ') + '...';
  };

  return (
    <Card
      className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] border-border/50 hover:border-border relative overflow-hidden"
      onClick={handleCardClick}
    >
      {/* AI Insights Indicator */}
      {tender.win_chance && tender.win_chance > 60 && (
        <div className="absolute top-2 right-2 z-10">
          <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
            <Brain className="h-3 w-3" />
            AI Match
          </Badge>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            {/* Description as heading (3 lines max) */}
            <CardTitle className="text-lg font-semibold leading-tight line-clamp-3 group-hover:text-primary transition-colors">
              {tender.description || tender.title || 'No description available'}
            </CardTitle>

            {/* Tender number (1 line, truncated) */}
            {tender.tender_number && (
              <CardDescription className="text-sm text-muted-foreground mt-1 truncate">
                Tender: {tender.tender_number}
              </CardDescription>
            )}
          </div>

          <div className="flex flex-col gap-2 shrink-0">
            <Badge className={cn("", getStatusColor(tender.status))}>
              {tender.status || 'Unknown'}
            </Badge>
            {/* Enterprise TMS Quick Access */}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleOpenInTMS}
              title="Open in Enterprise TMS"
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Issuer and Category */}
        <div className="flex items-center gap-2 text-sm">
          <Building2 className="h-4 w-4 text-muted-foreground shrink-0" />
          <span className="truncate">{tender.issuer || 'Unknown issuer'}</span>
        </div>

        {/* Category */}
        {tender.main_procurement_category && (
          <div className="flex items-center gap-2 text-sm">
            <span className="text-muted-foreground">Category:</span>
            <span className="truncate">{tender.main_procurement_category}</span>
          </div>
        )}

        {/* Location */}
        <div className="flex items-center gap-2 text-sm">
          <MapPin className="h-4 w-4 text-muted-foreground shrink-0" />
          <span className="truncate">{tender.location || 'Location not specified'}</span>
        </div>

        {/* Due Date */}
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-4 w-4 text-muted-foreground shrink-0" />
          <span>Due: {formatDate(tender.due_date)}</span>
        </div>

        {/* Value */}
        <div className="flex items-center gap-2 text-sm">
          <DollarSign className="h-4 w-4 text-muted-foreground shrink-0" />
          <span>{formatValue(tender.value)}</span>
        </div>

        {/* Win Chance with AI Insights */}
        {tender.win_chance && (
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-1">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Win Chance:</span>
            </div>
            <Badge className={cn("", getWinChanceColor(tender.win_chance))}>
              {tender.win_chance}%
            </Badge>
          </div>
        )}

        {/* Action Buttons */}
        <div className="pt-2 space-y-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              handleCardClick();
            }}
          >
            <Eye className="h-4 w-4 mr-2" />
            View Details
          </Button>

          {/* Enterprise TMS Button */}
          <Button
            variant="ghost"
            size="sm"
            className="w-full text-blue-600 hover:text-blue-700 hover:bg-blue-50"
            onClick={handleOpenInTMS}
          >
            <Zap className="h-4 w-4 mr-2" />
            Analyze in TMS
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
