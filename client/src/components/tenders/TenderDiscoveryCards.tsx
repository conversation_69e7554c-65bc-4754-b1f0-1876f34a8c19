import React from 'react';
import { useLocation } from 'wouter';

interface Tender {
  id: number;
  title: string;
  status: string;
  issuer: string;
  win_chance: number;
  due_date: string;
  location: string;
  value: string;
  description?: string;
  lat?: number;
  lng?: number;
}

interface TenderDiscoveryCardsProps {
  tenders: Tender[];
}

export default function TenderDiscoveryCards({ tenders }: TenderDiscoveryCardsProps) {
  const [, setLocation] = useLocation();

  const styles = {
    container: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
      gap: '1.5rem',
      padding: '1rem',
    },
    card: {
      background: 'white',
      borderRadius: '12px',
      padding: '1.5rem',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      cursor: 'pointer',
      transition: 'transform 0.2s ease-in-out',
      ':hover': {
        transform: 'translateY(-4px)',
      },
    },
    title: {
      fontSize: '1.25rem',
      fontWeight: '600',
      marginBottom: '0.5rem',
      color: '#1f2937',
    },
    info: {
      marginBottom: '0.5rem',
      color: '#4b5563',
    },
    winChance: {
      display: 'inline-block',
      padding: '0.25rem 0.75rem',
      borderRadius: '9999px',
      fontSize: '0.875rem',
      fontWeight: '500',
      marginTop: '1rem',
    },
    high: {
      background: '#dcfce7',
      color: '#166534',
    },
    medium: {
      background: '#fef3c7',
      color: '#92400e',
    },
    low: {
      background: '#fee2e2',
      color: '#991b1b',
    },
  };

  const getWinChanceStyle = (chance: number) => {
    if (chance > 70) return { ...styles.winChance, ...styles.high };
    if (chance > 40) return { ...styles.winChance, ...styles.medium };
    return { ...styles.winChance, ...styles.low };
  };

  return (
    <div style={styles.container}>
      {tenders.map((tender) => (
        <div
          key={tender.id}
          style={styles.card}
          onClick={() => setLocation(`/tenders/${tender.id}`)}
        >
          <h3 style={styles.title}>{tender.title}</h3>
          {tender.description && (
            <p style={{...styles.info, fontStyle: 'italic', marginBottom: '1rem', color: '#6b7280'}}>
              {tender.description.length > 120
                ? `${tender.description.substring(0, 120)}...`
                : tender.description}
            </p>
          )}
          <p style={styles.info}><strong>Issuer:</strong> {tender.issuer}</p>
          <p style={styles.info}><strong>Location:</strong> {tender.location}</p>
          <p style={styles.info}><strong>Due Date:</strong> {new Date(tender.due_date).toLocaleDateString()}</p>
          <p style={styles.info}><strong>Value:</strong> {tender.value}</p>
          <div style={getWinChanceStyle(tender.win_chance)}>
            {tender.win_chance}% Win Chance
          </div>
        </div>
      ))}
    </div>
  );
} 