# OpenAI Enterprise Agent Requirements

# Core OpenAI
openai>=1.12.0

# Database and Vector Stores
motor>=3.3.0
redis>=5.0.0
qdrant-client>=1.7.0
pymongo>=4.6.0

# Document Processing
PyMuPDF>=1.23.0
pytesseract>=0.3.10
Pillow>=10.2.0
python-docx>=1.1.0
pdfplumber>=0.10.0

# Web and HTTP
httpx>=0.26.0
requests>=2.31.0
aiohttp>=3.9.0
beautifulsoup4>=4.12.0

# Web Crawling
crawl4ai>=0.2.0
selenium>=4.16.0
playwright>=1.40.0

# Communication and Integrations
google-auth>=2.25.0
google-auth-oauthlib>=1.2.0
google-auth-httplib2>=0.2.0
google-api-python-client>=2.110.0
slack-sdk>=3.26.0

# Data Processing and Utilities
pydantic>=2.5.0
pyyaml>=6.0.1
numpy>=1.24.0
pandas>=2.1.0

# Async and Concurrency
asyncio-throttle>=1.0.2
aiofiles>=23.2.0

# Configuration and Environment
python-dotenv>=1.0.0
click>=8.1.0
typer>=0.9.0

# Logging and Monitoring
structlog>=23.2.0
loguru>=0.7.0

# Graph and Network Analysis
networkx>=3.2.0

# Testing and Development
pytest>=7.4.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0
black>=23.12.0
isort>=5.13.0
flake8>=7.0.0

# Optional: Advanced features (uncomment if needed)
# faiss-cpu>=1.7.0          # For FAISS vector store
# chromadb>=0.4.0           # For Chroma vector store
# pinecone-client>=2.2.0    # For Pinecone vector store
# weaviate-client>=3.25.0   # For Weaviate vector store

# Optional: Additional document loaders
# pypdf>=3.17.0             # Alternative PDF loader
# docx2txt>=0.8             # DOCX text extraction
# python-pptx>=0.6.0        # PowerPoint processing
# openpyxl>=3.1.0           # Excel processing

# Optional: Advanced NLP
# spacy>=3.7.0
# nltk>=3.8.0
# transformers>=4.36.0

# Optional: Computer Vision
# opencv-python>=4.8.0
# easyocr>=1.7.0

# Development and Deployment
uvicorn>=0.25.0
fastapi>=0.108.0
gunicorn>=21.2.0
