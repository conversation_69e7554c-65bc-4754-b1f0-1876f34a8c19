# OpenAI Enterprise Agent Configuration

openai:
  api_key: "${OPENAI_API_KEY}"
  organization: "${OPENAI_ORG_ID}"  # Optional
  project: "${OPENAI_PROJECT_ID}"   # Optional

assistants:
  primary:
    name: "Enterprise Primary Assistant"
    instructions: |
      You are a comprehensive enterprise assistant specializing in document processing, 
      tender management, market research, and business intelligence. You have access to 
      powerful tools for web search, website crawling, document analysis, email notifications, 
      and calendar management.
      
      Your capabilities include:
      - Processing and analyzing documents (PDFs, images, text files)
      - Searching the internet for relevant information
      - Crawling websites to extract structured data
      - Querying knowledge bases using RAG
      - Sending email notifications
      - Creating calendar events and reminders
      
      Always provide structured, actionable outputs with confidence scores when possible.
      When processing tenders or business documents, focus on extracting key dates, 
      requirements, contact information, and actionable items.
    model: "gpt-4-turbo-preview"
    
  document:
    name: "Document Processing Specialist"
    instructions: |
      You are a document processing specialist focused on extracting structured 
      information from business documents, particularly tender notices, BOQs, 
      contracts, and other enterprise documents.
      
      Your expertise includes:
      - OCR and text extraction from various file formats
      - Schema-driven data extraction
      - Entity recognition and relationship mapping
      - Document classification and categorization
      - Cross-document analysis and comparison
      
      Always extract key information systematically and provide confidence scores 
      for your extractions. Focus on dates, monetary values, requirements, 
      contact information, and compliance criteria.
    model: "gpt-4-turbo-preview"
    
  research:
    name: "Market Research Specialist"
    instructions: |
      You are a market research and competitive intelligence specialist. Your role 
      is to gather, analyze, and synthesize information from multiple sources to 
      provide actionable business insights.
      
      Your capabilities include:
      - Multi-source web research and analysis
      - Competitive intelligence gathering
      - Market trend identification
      - Opportunity and threat assessment
      - Strategic recommendations
      
      Always provide comprehensive analysis with supporting evidence, confidence 
      scores, and clear recommendations. Focus on actionable insights that can 
      drive business decisions.
    model: "gpt-4-turbo-preview"

databases:
  mongodb:
    url: "mongodb://localhost:27017"
    database: "openai_enterprise_agent"
  redis:
    url: "redis://localhost:6379"
    db: 0
  qdrant:
    host: "localhost"
    port: 6333
    collection_prefix: "openai_"

integrations:
  brave_search:
    api_key: "${BRAVE_API_KEY}"
    base_url: "https://api.search.brave.com/res/v1/web/search"
  google:
    credentials_path: "credentials/google_creds.json"
    custom_search_engine_id: "${GOOGLE_CSE_ID}"
    api_key: "${GOOGLE_API_KEY}"
  slack:
    token: "${SLACK_BOT_TOKEN}"
    default_channel: "#openai-agent"
  smtp:
    server: "smtp.gmail.com"
    port: 587
    from_email: "<EMAIL>"
    username: "${SMTP_USERNAME}"
    password: "${SMTP_PASSWORD}"

crawl4ai:
  max_concurrent: 5
  delay_between_requests: 1.0
  timeout: 30
  user_agent: "OpenAIAgent/1.0"
  bypass_cache: false

# Vector store configurations
vector_stores:
  documents:
    collection_name: "documents"
    vector_size: 1536  # OpenAI embedding dimension
    distance_metric: "cosine"
  web_content:
    collection_name: "web_content"
    vector_size: 1536
    distance_metric: "cosine"
  conversations:
    collection_name: "conversations"
    vector_size: 1536
    distance_metric: "cosine"

# Function configurations
functions:
  search_internet:
    enabled: true
    default_source: "brave"
    max_results: 10
    cache_ttl: 3600
  crawl_website:
    enabled: true
    max_pages_per_crawl: 10
    respect_robots_txt: true
    extraction_timeout: 30
  process_document:
    enabled: true
    supported_formats: [".pdf", ".txt", ".md", ".png", ".jpg", ".jpeg"]
    max_file_size_mb: 50
  query_knowledge_base:
    enabled: true
    default_top_k: 5
    similarity_threshold: 0.7
  send_email:
    enabled: true
    max_recipients: 10
  create_calendar_event:
    enabled: true
    default_reminder_minutes: [60, 1440]

# Workflow configurations
workflows:
  tender_analysis:
    timeout_seconds: 300
    max_documents: 10
    enable_notifications: true
    enable_calendar_events: true
    assistant: "primary"
  market_research:
    timeout_seconds: 600
    max_search_queries: 5
    max_crawl_urls: 10
    assistant: "research"
  document_intelligence:
    timeout_seconds: 180
    max_documents: 20
    enable_cross_analysis: true
    assistant: "document"
  competitive_analysis:
    timeout_seconds: 450
    max_competitors: 15
    deep_analysis: true
    assistant: "research"

# Assistant runtime configurations
assistant_runtime:
  max_run_time_seconds: 300
  polling_interval_seconds: 1
  max_function_calls_per_run: 20
  enable_parallel_function_calls: true

# Security and performance
security:
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_limit: 100
  input_validation:
    enabled: true
    max_input_length: 32000  # OpenAI context limit consideration
  output_filtering:
    enabled: true
    filter_sensitive_data: true
  file_upload_limits:
    max_file_size_mb: 512
    allowed_extensions: [".pdf", ".txt", ".md", ".png", ".jpg", ".jpeg", ".docx"]

performance:
  max_concurrent_workflows: 3
  max_concurrent_assistants: 5
  memory_limit_mb: 2048
  cpu_limit_percent: 80
  cache_size_mb: 512
  embedding_batch_size: 100

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/openai_agent.log"
  max_size_mb: 100
  backup_count: 5
  log_function_calls: true
  log_assistant_runs: true

# Monitoring and metrics
monitoring:
  enabled: true
  metrics_collection: true
  performance_tracking: true
  error_tracking: true
  cost_tracking: true
  token_usage_tracking: true

# Cost management
cost_management:
  daily_budget_usd: 100.0
  monthly_budget_usd: 2000.0
  alert_threshold_percent: 80
  auto_pause_on_budget_exceeded: false
  track_costs_by_workflow: true
