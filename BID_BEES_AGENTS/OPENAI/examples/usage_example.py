#!/usr/bin/env python3
"""
OpenAI Enterprise Agent Usage Example

This example demonstrates the comprehensive capabilities of the OpenAI Enterprise Agent
using the Assistants API with function calling for document processing, web crawling,
RAG queries, and workflow execution.
"""

import asyncio
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add the parent directory to the path to import the agent
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents.openai_enterprise_agent import OpenAIEnterpriseAgent

async def main():
    """Main example function demonstrating OpenAI agent capabilities"""
    
    # Initialize the agent
    print("🚀 Initializing OpenAI Enterprise Agent...")
    agent = OpenAIEnterpriseAgent()
    await agent.initialize()
    
    try:
        # ============================================================================
        # 1. ASSISTANT INFORMATION
        # ============================================================================
        print("\n🤖 Available Assistants")
        print("-" * 50)
        
        assistants = agent.get_assistants()
        for name, assistant_id in assistants.items():
            print(f"✅ {name}: {assistant_id}")
        
        # ============================================================================
        # 2. SIMPLE CHAT EXAMPLE
        # ============================================================================
        print("\n💬 Simple Chat Example")
        print("-" * 50)
        
        chat_queries = [
            "Hello! What are your capabilities?",
            "How can you help with tender management?",
            "What document formats can you process?"
        ]
        
        for query in chat_queries:
            try:
                response = await agent.chat(query, assistant_name="primary")
                print(f"Q: {query}")
                print(f"A: {response[:200]}...")
                print()
            except Exception as e:
                print(f"Chat query failed: {e}")
        
        # ============================================================================
        # 3. DOCUMENT PROCESSING WITH ASSISTANT
        # ============================================================================
        print("\n📄 Document Processing with Assistant")
        print("-" * 50)
        
        # Create a sample document for testing
        sample_doc_path = "sample_documents/test_tender.txt"
        os.makedirs("sample_documents", exist_ok=True)
        
        with open(sample_doc_path, 'w') as f:
            f.write("""
            TENDER NOTICE - CONSTRUCTION PROJECT
            
            Tender Number: T2024-002
            Project: New Government Office Complex
            Closing Date: 2024-12-15 17:00
            
            The Ministry of Public Works invites sealed bids for the construction
            of a new government office complex. The project includes:
            
            - Site preparation and foundation work
            - Structural steel and concrete construction
            - Electrical and mechanical systems
            - Interior finishing and landscaping
            
            Estimated Contract Value: $5,200,000
            Project Duration: 18 months
            
            Mandatory Briefing Session:
            Date: 2024-11-20 at 14:00
            Location: Ministry Conference Room A
            Contact: Sarah Johnson, Project Director
            Email: <EMAIL>
            Phone: (*************
            
            Submission Requirements:
            - Company registration certificate
            - Financial statements (last 3 years)
            - Previous project portfolio
            - Technical proposal
            - Pricing schedule
            """)
        
        # Process document using assistant
        try:
            message = f"Process the tender document at '{sample_doc_path}' and extract all key information including dates, requirements, contact details, and create a structured summary."
            
            result = await agent.run_assistant("document", message)
            
            if 'error' not in result:
                print(f"✅ Document processed successfully")
                print(f"   Thread ID: {result['thread_id']}")
                print(f"   Assistant: {result['assistant_name']}")
                
                if 'result' in result and 'response' in result['result']:
                    response = result['result']['response']
                    print(f"   Response: {response[:300]}...")
            else:
                print(f"❌ Document processing failed: {result['error']}")
                
        except Exception as e:
            print(f"⚠️  Document processing failed: {e}")
        
        # ============================================================================
        # 4. WEB RESEARCH WITH ASSISTANT
        # ============================================================================
        print("\n🔍 Web Research with Assistant")
        print("-" * 50)
        
        try:
            research_message = """
            Conduct comprehensive market research on "government construction contracts 2024".
            Please:
            1. Search for recent government construction opportunities
            2. Crawl relevant government websites for tender information
            3. Analyze market trends and opportunities
            4. Provide actionable insights for a construction company
            """
            
            result = await agent.run_assistant("research", research_message)
            
            if 'error' not in result:
                print(f"✅ Market research completed")
                print(f"   Thread ID: {result['thread_id']}")
                
                if 'result' in result and 'response' in result['result']:
                    response = result['result']['response']
                    print(f"   Research Summary: {response[:400]}...")
            else:
                print(f"❌ Market research failed: {result['error']}")
                
        except Exception as e:
            print(f"⚠️  Market research failed: {e}")
        
        # ============================================================================
        # 5. KNOWLEDGE BASE QUERY
        # ============================================================================
        print("\n🧠 Knowledge Base Query")
        print("-" * 50)
        
        try:
            kb_message = "Query the knowledge base for information about construction tender requirements and best practices for bid submissions."
            
            result = await agent.run_assistant("primary", kb_message)
            
            if 'error' not in result:
                print(f"✅ Knowledge base query completed")
                
                if 'result' in result and 'response' in result['result']:
                    response = result['result']['response']
                    print(f"   KB Response: {response[:300]}...")
            else:
                print(f"❌ Knowledge base query failed: {result['error']}")
                
        except Exception as e:
            print(f"⚠️  Knowledge base query failed: {e}")
        
        # ============================================================================
        # 6. WORKFLOW EXECUTION EXAMPLE
        # ============================================================================
        print("\n⚙️  Workflow Execution Example")
        print("-" * 50)
        
        # Execute tender analysis workflow
        workflow_params = {
            'document_path': sample_doc_path,
            'search_query': 'government construction tender opportunities 2024',
            'analysis_query': 'What are the key success factors for government construction tenders?',
            'create_reminders': True,
            'notify_email': '<EMAIL>'
        }
        
        print("🔄 Starting tender analysis workflow...")
        try:
            workflow_result = await agent.execute_workflow('tender_analysis', workflow_params)
            
            print(f"✅ Workflow completed: {workflow_result.status}")
            print(f"   Workflow ID: {workflow_result.workflow_id}")
            print(f"   Thread ID: {workflow_result.thread_id}")
            print(f"   Assistant ID: {workflow_result.assistant_id}")
            print(f"   Execution time: {workflow_result.execution_time_ms:.2f}ms")
            print(f"   Confidence score: {workflow_result.confidence_score:.2f}")
            print(f"   Result components: {len(workflow_result.results)}")
            
            # Display workflow results
            for component, data in workflow_result.results.items():
                if isinstance(data, dict):
                    print(f"   - {component}: {len(data)} items")
                elif isinstance(data, str):
                    print(f"   - {component}: {data[:50]}...")
                else:
                    print(f"   - {component}: {type(data).__name__}")
                    
        except Exception as e:
            print(f"❌ Workflow execution failed: {e}")
        
        # ============================================================================
        # 7. COMPETITIVE ANALYSIS WORKFLOW
        # ============================================================================
        print("\n📊 Competitive Analysis Workflow")
        print("-" * 50)
        
        competitive_params = {
            'competitor_query': 'construction companies government contracts',
            'competitor_urls': [
                'https://www.example-construction.com',
                'https://www.sample-builders.com'
            ]
        }
        
        try:
            competitive_result = await agent.execute_workflow('competitive_analysis', competitive_params)
            print(f"✅ Competitive analysis completed: {competitive_result.status}")
            print(f"   Analysis components: {len(competitive_result.results)}")
            print(f"   Confidence score: {competitive_result.confidence_score:.2f}")
        except Exception as e:
            print(f"⚠️  Competitive analysis workflow failed: {e}")
        
        # ============================================================================
        # 8. MULTI-DOCUMENT INTELLIGENCE WORKFLOW
        # ============================================================================
        print("\n📚 Multi-Document Intelligence Workflow")
        print("-" * 50)
        
        # Create additional sample documents
        sample_doc2_path = "sample_documents/boq_sample.txt"
        with open(sample_doc2_path, 'w') as f:
            f.write("""
            BILL OF QUANTITIES
            Project: Government Office Complex
            
            Item 1: Excavation - 500 m³ @ $25/m³ = $12,500
            Item 2: Concrete Foundation - 200 m³ @ $150/m³ = $30,000
            Item 3: Structural Steel - 50 tons @ $2,000/ton = $100,000
            Item 4: Electrical Work - Lump Sum = $75,000
            
            Total Estimated Cost: $217,500
            """)
        
        document_params = {
            'document_paths': [sample_doc_path, sample_doc2_path],
            'analysis_query': 'Compare the tender notice with the BOQ and identify any discrepancies or important relationships'
        }
        
        try:
            doc_result = await agent.execute_workflow('document_intelligence', document_params)
            print(f"✅ Document intelligence completed: {doc_result.status}")
            print(f"   Document analysis components: {len(doc_result.results)}")
            print(f"   Confidence score: {doc_result.confidence_score:.2f}")
        except Exception as e:
            print(f"⚠️  Document intelligence workflow failed: {e}")
        
        # ============================================================================
        # 9. ADVANCED ASSISTANT INTERACTION
        # ============================================================================
        print("\n🎯 Advanced Assistant Interaction")
        print("-" * 50)
        
        # Create a dedicated thread for extended conversation
        thread_id = await agent.create_thread()
        print(f"Created conversation thread: {thread_id}")
        
        # Multi-turn conversation
        conversation_queries = [
            "Analyze the tender documents I've processed and summarize the key opportunities.",
            "What are the main risks I should consider for this project?",
            "Create a timeline of important dates and milestones.",
            "Draft an email template for following up with the project contact."
        ]
        
        for i, query in enumerate(conversation_queries):
            try:
                result = await agent.run_assistant("primary", query, thread_id)
                print(f"Turn {i+1}: {query}")
                
                if 'error' not in result and 'result' in result:
                    response = result['result'].get('response', 'No response')
                    print(f"Response: {response[:150]}...")
                else:
                    print(f"Error: {result.get('error', 'Unknown error')}")
                print()
            except Exception as e:
                print(f"Conversation turn {i+1} failed: {e}")
        
        print("\n🎉 OpenAI Enterprise Agent Demo Completed Successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error during demo execution: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup resources
        print("\n🧹 Cleaning up resources...")
        await agent.cleanup()
        print("✅ Cleanup completed")

def setup_environment():
    """Setup environment variables and directories"""
    print("🔧 Setting up environment...")
    
    # Create necessary directories
    directories = [
        "logs",
        "sample_documents",
        "credentials"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # Check for required environment variables
    required_env_vars = [
        'OPENAI_API_KEY'
    ]
    
    optional_env_vars = [
        'OPENAI_ORG_ID',
        'OPENAI_PROJECT_ID',
        'BRAVE_API_KEY',
        'SMTP_USERNAME',
        'SMTP_PASSWORD',
        'SLACK_BOT_TOKEN'
    ]
    
    missing_required = []
    missing_optional = []
    
    for var in required_env_vars:
        if not os.getenv(var):
            missing_required.append(var)
    
    for var in optional_env_vars:
        if not os.getenv(var):
            missing_optional.append(var)
    
    if missing_required:
        print("❌ Error: The following required environment variables are not set:")
        for var in missing_required:
            print(f"   - {var}")
        print("   Please set these variables before running the demo.")
        return False
    
    if missing_optional:
        print("⚠️  Warning: The following optional environment variables are not set:")
        for var in missing_optional:
            print(f"   - {var}")
        print("   Some features may not work without proper configuration.")
    
    print("✅ Environment setup completed")
    return True

if __name__ == "__main__":
    print("🚀 OpenAI Enterprise Agent Demo")
    print("=" * 60)
    
    # Setup environment
    if not setup_environment():
        exit(1)
    
    # Run the main demo
    asyncio.run(main())
