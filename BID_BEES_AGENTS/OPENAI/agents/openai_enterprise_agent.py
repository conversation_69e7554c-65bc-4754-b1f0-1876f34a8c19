# agents/openai_enterprise_agent.py

import asyncio
import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union, Callable
from pathlib import Path
import yaml
import numpy as np

# OpenAI imports
import openai
from openai import AsyncOpenAI
from openai.types.beta import Assistant, Thread
from openai.types.beta.threads import Run, Message

# Database and Vector Store imports
import motor.motor_asyncio
import redis.asyncio as redis
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
import networkx as nx

# Web and communication imports
import httpx
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from slack_sdk.web.async_client import AsyncWebClient

# Crawl4AI and document processing
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy
import fitz  # PyMuPDF
import pytesseract
from PIL import Image

# Pydantic for data validation
from pydantic import BaseModel, Field, ConfigDict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# PYDANTIC MODELS FOR DATA STRUCTURES
# ============================================================================

class DocumentMetadata(BaseModel):
    """Document metadata structure"""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    document_id: str
    title: str
    document_type: str
    source: str
    processed_at: datetime
    confidence_score: float = Field(ge=0.0, le=1.0)
    extracted_entities: List[Dict[str, Any]] = []
    vector_id: Optional[str] = None
    tags: List[str] = []
    assistant_file_id: Optional[str] = None

class SearchResult(BaseModel):
    """Search results structure"""
    query: str
    results: List[Dict[str, Any]]
    timestamp: datetime
    source: str
    total_results: int = 0
    cached: bool = False

class CrawlResult(BaseModel):
    """Web crawling results"""
    url: str
    title: str
    content: str
    structured_data: Dict[str, Any] = {}
    links: List[str] = []
    crawled_at: datetime
    success: bool = True
    error_message: Optional[str] = None

class WorkflowResult(BaseModel):
    """Workflow execution results"""
    workflow_id: str
    workflow_type: str
    status: str
    results: Dict[str, Any]
    created_at: datetime
    completed_at: Optional[datetime] = None
    execution_time_ms: float = 0.0
    confidence_score: float = 0.0
    thread_id: Optional[str] = None
    assistant_id: Optional[str] = None

class AssistantConfig(BaseModel):
    """OpenAI Assistant configuration"""
    name: str
    instructions: str
    model: str = "gpt-4-turbo-preview"
    tools: List[Dict[str, Any]] = []
    file_ids: List[str] = []
    metadata: Dict[str, Any] = {}

# ============================================================================
# OPENAI FUNCTION DEFINITIONS
# ============================================================================

FUNCTION_DEFINITIONS = [
    {
        "type": "function",
        "function": {
            "name": "search_internet",
            "description": "Search the internet using various search engines",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query"
                    },
                    "source": {
                        "type": "string",
                        "enum": ["brave", "google", "bing", "duckduckgo"],
                        "description": "The search engine to use",
                        "default": "brave"
                    },
                    "count": {
                        "type": "integer",
                        "description": "Number of results to return",
                        "default": 10
                    }
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "crawl_website",
            "description": "Crawl a website and extract structured information",
            "parameters": {
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "The URL to crawl"
                    },
                    "extraction_strategy": {
                        "type": "string",
                        "description": "Instructions for what information to extract"
                    }
                },
                "required": ["url"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "process_document",
            "description": "Process and analyze documents (PDF, images, text)",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the document file"
                    },
                    "document_type": {
                        "type": "string",
                        "enum": ["tender_notice", "boq", "contract", "auto"],
                        "description": "Type of document to process",
                        "default": "auto"
                    }
                },
                "required": ["file_path"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "query_knowledge_base",
            "description": "Query the knowledge base using RAG",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The query to search for"
                    },
                    "top_k": {
                        "type": "integer",
                        "description": "Number of top results to return",
                        "default": 5
                    }
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "send_email",
            "description": "Send email notifications",
            "parameters": {
                "type": "object",
                "properties": {
                    "recipient": {
                        "type": "string",
                        "description": "Email address of the recipient"
                    },
                    "subject": {
                        "type": "string",
                        "description": "Email subject"
                    },
                    "message": {
                        "type": "string",
                        "description": "Email message content"
                    }
                },
                "required": ["recipient", "subject", "message"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_calendar_event",
            "description": "Create Google Calendar events",
            "parameters": {
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "Event title"
                    },
                    "start_time": {
                        "type": "string",
                        "description": "Start time in ISO format"
                    },
                    "end_time": {
                        "type": "string",
                        "description": "End time in ISO format"
                    },
                    "description": {
                        "type": "string",
                        "description": "Event description"
                    }
                },
                "required": ["title", "start_time", "end_time"]
            }
        }
    }
]

# ============================================================================
# OPENAI ENTERPRISE AGENT
# ============================================================================

class OpenAIEnterpriseAgent:
    """
    Comprehensive OpenAI-based enterprise agent using Assistants API with
    document processing, web crawling, RAG capabilities, and communication features.
    """
    
    def __init__(self, config_path: str = "config/openai_agent_config.yaml"):
        self.config = self._load_config(config_path)
        self.client = None
        self.assistants = {}
        self.threads = {}
        self.vector_stores = {}
        
        # Initialize components
        self._initialize_client()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Default configuration"""
        return {
            'openai': {
                'api_key': '',
                'organization': '',
                'project': ''
            },
            'assistants': {
                'primary': {
                    'name': 'Enterprise Primary Assistant',
                    'instructions': 'You are a comprehensive enterprise assistant...',
                    'model': 'gpt-4-turbo-preview'
                },
                'document': {
                    'name': 'Document Processing Assistant',
                    'instructions': 'You specialize in document analysis...',
                    'model': 'gpt-4-turbo-preview'
                },
                'research': {
                    'name': 'Research Assistant',
                    'instructions': 'You specialize in market research...',
                    'model': 'gpt-4-turbo-preview'
                }
            },
            'databases': {
                'mongodb': {'url': 'mongodb://localhost:27017', 'database': 'openai_agent'},
                'redis': {'url': 'redis://localhost:6379'},
                'qdrant': {'host': 'localhost', 'port': 6333}
            },
            'integrations': {
                'brave_search': {'api_key': ''},
                'smtp': {'server': 'smtp.gmail.com', 'port': 587, 'from_email': '', 'username': '', 'password': ''},
                'google': {'credentials_path': 'credentials/google_creds.json'}
            }
        }
    
    def _initialize_client(self):
        """Initialize OpenAI client"""
        openai_config = self.config['openai']
        self.client = AsyncOpenAI(
            api_key=openai_config['api_key'],
            organization=openai_config.get('organization'),
            project=openai_config.get('project')
        )

    async def initialize(self):
        """Initialize all components asynchronously"""
        await self._initialize_databases()
        await self._initialize_assistants()
        await self._initialize_tools()
        logger.info("OpenAI agent system initialized successfully")

    async def _initialize_databases(self):
        """Initialize database connections"""
        try:
            # MongoDB connection
            self.mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(
                self.config['databases']['mongodb']['url']
            )

            # Redis connection
            self.redis_client = redis.from_url(
                self.config['databases']['redis']['url']
            )

            # Qdrant vector store
            qdrant_config = self.config['databases']['qdrant']
            self.qdrant_client = QdrantClient(
                host=qdrant_config['host'],
                port=qdrant_config['port']
            )

            # Initialize vector stores
            await self._initialize_vector_stores()

        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise

    async def _initialize_vector_stores(self):
        """Initialize vector stores for different data types"""
        try:
            # Create collections if they don't exist
            collections = [
                ("documents", 1536),  # OpenAI embedding dimension
                ("web_content", 1536),
                ("conversations", 1536)
            ]

            for collection_name, vector_size in collections:
                try:
                    self.qdrant_client.create_collection(
                        collection_name=collection_name,
                        vectors_config=VectorParams(
                            size=vector_size,
                            distance=Distance.COSINE
                        )
                    )
                    logger.info(f"Created Qdrant collection: {collection_name}")
                except Exception as e:
                    if "already exists" not in str(e):
                        logger.error(f"Failed to create collection {collection_name}: {e}")

        except Exception as e:
            logger.error(f"Vector store initialization failed: {e}")

    async def _initialize_assistants(self):
        """Initialize OpenAI Assistants"""
        try:
            for assistant_name, config in self.config['assistants'].items():
                assistant_config = AssistantConfig(
                    name=config['name'],
                    instructions=config['instructions'],
                    model=config['model'],
                    tools=FUNCTION_DEFINITIONS
                )

                # Create assistant
                assistant = await self.client.beta.assistants.create(
                    name=assistant_config.name,
                    instructions=assistant_config.instructions,
                    model=assistant_config.model,
                    tools=assistant_config.tools
                )

                self.assistants[assistant_name] = assistant
                logger.info(f"Created assistant: {assistant_name} ({assistant.id})")

        except Exception as e:
            logger.error(f"Assistant initialization failed: {e}")
            raise

    async def _initialize_tools(self):
        """Initialize external tools and services"""
        try:
            # HTTP client for web requests
            self.http_client = httpx.AsyncClient(
                headers={
                    "X-Subscription-Token": self.config['integrations']['brave_search']['api_key'],
                    "User-Agent": "OpenAIAgent/1.0"
                },
                timeout=30.0
            )

            # Crawl4AI crawler
            self.crawler = AsyncWebCrawler(verbose=True, max_concurrent=5)

            # Knowledge graph
            self.knowledge_graph = nx.DiGraph()

            logger.info("External tools initialized successfully")

        except Exception as e:
            logger.error(f"Tool initialization failed: {e}")

    # ============================================================================
    # FUNCTION IMPLEMENTATIONS
    # ============================================================================

    async def _search_internet(self, query: str, source: str = "brave", count: int = 10) -> Dict[str, Any]:
        """Search the internet using various sources"""
        try:
            # Check cache first
            cache_key = f"search:{hashlib.md5(f'{query}:{source}'.encode()).hexdigest()}"
            cached_result = await self.redis_client.get(cache_key)

            if cached_result:
                return json.loads(cached_result)

            if source == "brave":
                response = await self.http_client.get(
                    "https://api.search.brave.com/res/v1/web/search",
                    params={"q": query, "count": count}
                )
                response.raise_for_status()
                data = response.json()
                results = data.get('web', {}).get('results', [])
            else:
                # Implement other search sources
                results = []

            search_result = {
                'query': query,
                'results': results,
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'total_results': len(results)
            }

            # Cache results
            await self.redis_client.setex(cache_key, 3600, json.dumps(search_result, default=str))

            return search_result

        except Exception as e:
            logger.error(f"Search failed: {e}")
            return {'error': str(e), 'query': query, 'source': source}

    async def _crawl_website(self, url: str, extraction_strategy: Optional[str] = None) -> Dict[str, Any]:
        """Crawl website and extract structured information"""
        try:
            strategy = None
            if extraction_strategy:
                strategy = LLMExtractionStrategy(
                    provider="openai",
                    api_token=self.config['openai']['api_key'],
                    instruction=extraction_strategy
                )

            result = await self.crawler.arun(
                url=url,
                extraction_strategy=strategy,
                bypass_cache=False
            )

            if result.success:
                structured_data = {}
                if strategy and result.extracted_content:
                    try:
                        structured_data = json.loads(result.extracted_content)
                    except json.JSONDecodeError:
                        structured_data = {"raw_extraction": result.extracted_content}

                crawl_result = {
                    'url': url,
                    'title': result.metadata.get('title', ''),
                    'content': result.markdown or result.cleaned_html,
                    'structured_data': structured_data,
                    'links': result.links.get('internal', []) + result.links.get('external', []),
                    'images': result.media.get('images', []),
                    'crawled_at': datetime.now().isoformat(),
                    'success': True
                }

                # Store in vector database
                await self._store_crawl_result(crawl_result)

                return crawl_result
            else:
                return {
                    'url': url,
                    'success': False,
                    'error_message': result.error_message,
                    'crawled_at': datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Crawling failed: {e}")
            return {'url': url, 'success': False, 'error_message': str(e)}

    async def _store_crawl_result(self, crawl_result: Dict[str, Any]):
        """Store crawl results in vector database"""
        try:
            # Generate embedding
            embedding_response = await self.client.embeddings.create(
                model="text-embedding-3-large",
                input=f"{crawl_result['title']} {crawl_result['content'][:1000]}"
            )
            embedding = embedding_response.data[0].embedding

            # Store in Qdrant
            vector_id = hashlib.md5(crawl_result['url'].encode()).hexdigest()
            point = PointStruct(
                id=vector_id,
                vector=embedding,
                payload={
                    'url': crawl_result['url'],
                    'title': crawl_result['title'],
                    'content_preview': crawl_result['content'][:500],
                    'crawled_at': crawl_result['crawled_at'],
                    'structured_data': crawl_result['structured_data']
                }
            )

            self.qdrant_client.upsert(
                collection_name="web_content",
                points=[point]
            )

            # Store full result in MongoDB
            await self.mongodb_client.crawl_results.web_content.insert_one(crawl_result)

        except Exception as e:
            logger.error(f"Failed to store crawl result: {e}")

    async def _process_document(self, file_path: str, document_type: str = "auto") -> Dict[str, Any]:
        """Process documents and extract structured information"""
        try:
            file_path = Path(file_path)

            # Extract text based on file type
            if file_path.suffix.lower() == '.pdf':
                doc = fitz.open(file_path)
                text = ""
                for page in doc:
                    text += page.get_text()
                doc.close()
            elif file_path.suffix.lower() in ['.png', '.jpg', '.jpeg']:
                image = Image.open(file_path)
                text = pytesseract.image_to_string(image)
            elif file_path.suffix.lower() in ['.txt', '.md']:
                text = file_path.read_text(encoding='utf-8')
            else:
                return {'error': f'Unsupported file format: {file_path.suffix}'}

            # Upload file to OpenAI for assistant processing
            with open(file_path, 'rb') as f:
                file_obj = await self.client.files.create(
                    file=f,
                    purpose='assistants'
                )

            # Generate embedding for vector storage
            embedding_response = await self.client.embeddings.create(
                model="text-embedding-3-large",
                input=text[:8000]  # Limit input size
            )
            embedding = embedding_response.data[0].embedding

            # Create document metadata
            doc_metadata = {
                'document_id': hashlib.md5(str(file_path).encode()).hexdigest(),
                'title': file_path.stem,
                'document_type': document_type,
                'source': str(file_path),
                'processed_at': datetime.now().isoformat(),
                'confidence_score': 0.85,
                'assistant_file_id': file_obj.id,
                'content_preview': text[:500]
            }

            # Store in vector database
            point = PointStruct(
                id=doc_metadata['document_id'],
                vector=embedding,
                payload=doc_metadata
            )

            self.qdrant_client.upsert(
                collection_name="documents",
                points=[point]
            )

            # Store in MongoDB
            await self.mongodb_client.documents.metadata.insert_one(doc_metadata)

            return doc_metadata

        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            return {'error': str(e), 'file_path': str(file_path)}

    async def _query_knowledge_base(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """Query knowledge base using RAG"""
        try:
            # Generate query embedding
            embedding_response = await self.client.embeddings.create(
                model="text-embedding-3-large",
                input=query
            )
            query_embedding = embedding_response.data[0].embedding

            # Search documents
            doc_results = self.qdrant_client.search(
                collection_name="documents",
                query_vector=query_embedding,
                limit=top_k
            )

            # Search web content
            web_results = self.qdrant_client.search(
                collection_name="web_content",
                query_vector=query_embedding,
                limit=top_k
            )

            # Combine and format results
            context_docs = []
            for result in doc_results + web_results:
                context_docs.append({
                    'content': result.payload.get('content_preview', ''),
                    'title': result.payload.get('title', ''),
                    'score': result.score,
                    'source': result.payload.get('source', result.payload.get('url', 'unknown'))
                })

            # Generate response using OpenAI
            context = "\n\n".join([f"Title: {doc['title']}\nContent: {doc['content']}"
                                 for doc in context_docs[:top_k]])

            response = await self.client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that answers questions based on the provided context."},
                    {"role": "user", "content": f"Context:\n{context}\n\nQuestion: {query}\n\nAnswer:"}
                ]
            )

            return {
                'query': query,
                'answer': response.choices[0].message.content,
                'sources': context_docs,
                'confidence_score': 0.8
            }

        except Exception as e:
            logger.error(f"Knowledge base query failed: {e}")
            return {'error': str(e), 'query': query}

    async def _send_email(self, recipient: str, subject: str, message: str) -> Dict[str, Any]:
        """Send email notification"""
        try:
            smtp_config = self.config['integrations']['smtp']

            msg = MIMEMultipart()
            msg['From'] = smtp_config['from_email']
            msg['To'] = recipient
            msg['Subject'] = subject
            msg.attach(MIMEText(message, 'html'))

            with smtplib.SMTP(smtp_config['server'], smtp_config['port']) as server:
                server.starttls()
                server.login(smtp_config['username'], smtp_config['password'])
                server.send_message(msg)

            return {'status': 'sent', 'recipient': recipient, 'subject': subject}

        except Exception as e:
            logger.error(f"Email sending failed: {e}")
            return {'error': str(e), 'recipient': recipient}

    async def _create_calendar_event(self, title: str, start_time: str, end_time: str,
                                   description: str = "") -> Dict[str, Any]:
        """Create Google Calendar event"""
        try:
            creds_path = self.config['integrations']['google']['credentials_path']
            creds = Credentials.from_authorized_user_file(creds_path)
            service = build('calendar', 'v3', credentials=creds)

            event = {
                'summary': title,
                'description': description,
                'start': {'dateTime': start_time, 'timeZone': 'UTC'},
                'end': {'dateTime': end_time, 'timeZone': 'UTC'},
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'email', 'minutes': 24 * 60},
                        {'method': 'popup', 'minutes': 60},
                    ],
                }
            }

            created_event = service.events().insert(calendarId='primary', body=event).execute()

            return {
                'status': 'created',
                'event_id': created_event['id'],
                'title': title,
                'start_time': start_time,
                'end_time': end_time
            }

        except Exception as e:
            logger.error(f"Calendar event creation failed: {e}")
            return {'error': str(e), 'title': title}

    # ============================================================================
    # FUNCTION DISPATCHER
    # ============================================================================

    async def _execute_function(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute function calls from assistants"""
        try:
            if function_name == "search_internet":
                return await self._search_internet(**arguments)
            elif function_name == "crawl_website":
                return await self._crawl_website(**arguments)
            elif function_name == "process_document":
                return await self._process_document(**arguments)
            elif function_name == "query_knowledge_base":
                return await self._query_knowledge_base(**arguments)
            elif function_name == "send_email":
                return await self._send_email(**arguments)
            elif function_name == "create_calendar_event":
                return await self._create_calendar_event(**arguments)
            else:
                return {'error': f'Unknown function: {function_name}'}

        except Exception as e:
            logger.error(f"Function execution failed: {e}")
            return {'error': str(e), 'function': function_name}

    # ============================================================================
    # MAIN AGENT METHODS
    # ============================================================================

    async def create_thread(self) -> str:
        """Create a new conversation thread"""
        try:
            thread = await self.client.beta.threads.create()
            return thread.id
        except Exception as e:
            logger.error(f"Thread creation failed: {e}")
            raise

    async def run_assistant(self, assistant_name: str, message: str,
                          thread_id: Optional[str] = None) -> Dict[str, Any]:
        """Run an assistant with a message"""
        try:
            if assistant_name not in self.assistants:
                return {'error': f'Assistant {assistant_name} not found'}

            assistant = self.assistants[assistant_name]

            # Create thread if not provided
            if not thread_id:
                thread_id = await self.create_thread()

            # Add message to thread
            await self.client.beta.threads.messages.create(
                thread_id=thread_id,
                role="user",
                content=message
            )

            # Run assistant
            run = await self.client.beta.threads.runs.create(
                thread_id=thread_id,
                assistant_id=assistant.id
            )

            # Wait for completion and handle function calls
            result = await self._wait_for_run_completion(thread_id, run.id)

            return {
                'thread_id': thread_id,
                'run_id': run.id,
                'assistant_name': assistant_name,
                'result': result
            }

        except Exception as e:
            logger.error(f"Assistant run failed: {e}")
            return {'error': str(e), 'assistant_name': assistant_name}

    async def _wait_for_run_completion(self, thread_id: str, run_id: str) -> Dict[str, Any]:
        """Wait for run completion and handle function calls"""
        try:
            while True:
                run = await self.client.beta.threads.runs.retrieve(
                    thread_id=thread_id,
                    run_id=run_id
                )

                if run.status == "completed":
                    # Get messages
                    messages = await self.client.beta.threads.messages.list(
                        thread_id=thread_id,
                        order="desc",
                        limit=1
                    )

                    if messages.data:
                        return {
                            'status': 'completed',
                            'response': messages.data[0].content[0].text.value
                        }
                    else:
                        return {'status': 'completed', 'response': 'No response'}

                elif run.status == "requires_action":
                    # Handle function calls
                    tool_calls = run.required_action.submit_tool_outputs.tool_calls
                    tool_outputs = []

                    for tool_call in tool_calls:
                        function_name = tool_call.function.name
                        arguments = json.loads(tool_call.function.arguments)

                        # Execute function
                        result = await self._execute_function(function_name, arguments)

                        tool_outputs.append({
                            "tool_call_id": tool_call.id,
                            "output": json.dumps(result)
                        })

                    # Submit tool outputs
                    await self.client.beta.threads.runs.submit_tool_outputs(
                        thread_id=thread_id,
                        run_id=run_id,
                        tool_outputs=tool_outputs
                    )

                elif run.status in ["failed", "cancelled", "expired"]:
                    return {'status': run.status, 'error': run.last_error}

                # Wait before checking again
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Run completion failed: {e}")
            return {'status': 'error', 'error': str(e)}

    async def chat(self, message: str, assistant_name: str = "primary",
                  thread_id: Optional[str] = None) -> str:
        """Simple chat interface"""
        try:
            result = await self.run_assistant(assistant_name, message, thread_id)

            if 'error' in result:
                return f"Error: {result['error']}"

            return result['result'].get('response', 'No response')

        except Exception as e:
            logger.error(f"Chat failed: {e}")
            return f"Chat error: {str(e)}"

    # ============================================================================
    # WORKFLOW EXECUTION
    # ============================================================================

    async def execute_workflow(self, workflow_type: str, parameters: Dict[str, Any]) -> WorkflowResult:
        """Execute complex workflows using assistants"""
        workflow_id = f"workflow_{hashlib.md5(f'{workflow_type}_{datetime.now()}'.encode()).hexdigest()}"
        start_time = datetime.now()

        try:
            # Create dedicated thread for workflow
            thread_id = await self.create_thread()

            if workflow_type == "tender_analysis":
                results = await self._execute_tender_analysis_workflow(parameters, thread_id)
            elif workflow_type == "market_research":
                results = await self._execute_market_research_workflow(parameters, thread_id)
            elif workflow_type == "document_intelligence":
                results = await self._execute_document_intelligence_workflow(parameters, thread_id)
            elif workflow_type == "competitive_analysis":
                results = await self._execute_competitive_analysis_workflow(parameters, thread_id)
            else:
                raise ValueError(f"Unknown workflow type: {workflow_type}")

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            return WorkflowResult(
                workflow_id=workflow_id,
                workflow_type=workflow_type,
                status="completed",
                results=results,
                created_at=start_time,
                completed_at=datetime.now(),
                execution_time_ms=execution_time,
                confidence_score=results.get('confidence_score', 0.8),
                thread_id=thread_id,
                assistant_id=self.assistants.get('primary', {}).id if self.assistants.get('primary') else None
            )

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")

            return WorkflowResult(
                workflow_id=workflow_id,
                workflow_type=workflow_type,
                status="failed",
                results={"error": str(e)},
                created_at=start_time,
                completed_at=datetime.now(),
                execution_time_ms=(datetime.now() - start_time).total_seconds() * 1000,
                confidence_score=0.0
            )

    async def _execute_tender_analysis_workflow(self, parameters: Dict[str, Any],
                                              thread_id: str) -> Dict[str, Any]:
        """Execute tender analysis workflow"""
        results = {}

        # Use primary assistant for orchestration
        assistant_name = "primary"

        # 1. Process document if provided
        if 'document_path' in parameters:
            message = f"Process the tender document at '{parameters['document_path']}' and extract key information including tender number, closing date, requirements, and briefing details."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['document_analysis'] = result

        # 2. Market research
        if 'search_query' in parameters:
            message = f"Search for information about '{parameters['search_query']}' and analyze the market opportunities. Include both web search and website crawling for comprehensive analysis."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['market_research'] = result

        # 3. Knowledge base insights
        if 'analysis_query' in parameters:
            message = f"Query the knowledge base for insights about: {parameters['analysis_query']}"

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['insights'] = result

        # 4. Create reminders and notifications
        if parameters.get('create_reminders', False):
            message = "Create calendar reminders for important tender deadlines based on the processed documents."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['calendar_events'] = result

        if 'notify_email' in parameters:
            message = f"Send a summary email to {parameters['notify_email']} about the tender analysis results."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['notification'] = result

        results['confidence_score'] = 0.85
        return results

    async def _execute_market_research_workflow(self, parameters: Dict[str, Any],
                                              thread_id: str) -> Dict[str, Any]:
        """Execute market research workflow"""
        results = {}

        # Use research assistant
        assistant_name = "research"

        search_queries = parameters.get('search_queries', [])
        if search_queries:
            message = f"Conduct comprehensive market research for the following topics: {', '.join(search_queries)}. Include web searches, website crawling, and analysis of trends, opportunities, and competitive landscape."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['comprehensive_research'] = result

        # Competitor analysis
        competitor_urls = parameters.get('competitor_urls', [])
        if competitor_urls:
            message = f"Analyze the following competitor websites for business intelligence: {', '.join(competitor_urls)}. Extract information about their services, pricing, and competitive advantages."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['competitor_analysis'] = result

        results['confidence_score'] = 0.8
        return results

    async def _execute_document_intelligence_workflow(self, parameters: Dict[str, Any],
                                                    thread_id: str) -> Dict[str, Any]:
        """Execute document intelligence workflow"""
        results = {}

        # Use document assistant
        assistant_name = "document"

        document_paths = parameters.get('document_paths', [])
        if document_paths:
            message = f"Process and analyze the following documents: {', '.join(document_paths)}. Extract key information, identify relationships, and provide comprehensive analysis."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['document_analysis'] = result

        # Cross-document analysis
        if len(document_paths) > 1:
            analysis_query = parameters.get('analysis_query', 'Compare and analyze the relationships between these documents')
            message = f"Perform cross-document analysis: {analysis_query}"

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['cross_analysis'] = result

        results['confidence_score'] = 0.9
        return results

    async def _execute_competitive_analysis_workflow(self, parameters: Dict[str, Any],
                                                   thread_id: str) -> Dict[str, Any]:
        """Execute competitive analysis workflow"""
        results = {}

        assistant_name = "research"

        # Search for competitors
        competitor_query = parameters.get('competitor_query', '')
        if competitor_query:
            message = f"Search for and analyze competitors related to: {competitor_query}. Provide detailed competitive intelligence including market positioning, strengths, and weaknesses."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['competitor_search'] = result

        # Analyze specific competitors
        competitor_urls = parameters.get('competitor_urls', [])
        if competitor_urls:
            message = f"Conduct detailed analysis of these competitor websites: {', '.join(competitor_urls)}. Extract business intelligence and competitive insights."

            result = await self.run_assistant(assistant_name, message, thread_id)
            results['detailed_analysis'] = result

        results['confidence_score'] = 0.75
        return results

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if hasattr(self, 'http_client'):
                await self.http_client.aclose()

            if hasattr(self, 'redis_client'):
                await self.redis_client.close()

            if hasattr(self, 'mongodb_client'):
                self.mongodb_client.close()

            if hasattr(self, 'crawler'):
                await self.crawler.aclose()

            logger.info("OpenAI agent cleanup completed")

        except Exception as e:
            logger.error(f"Cleanup failed: {e}")

    def get_assistants(self) -> Dict[str, str]:
        """Get list of available assistants"""
        return {name: assistant.id for name, assistant in self.assistants.items()}

    def get_threads(self) -> Dict[str, str]:
        """Get list of active threads"""
        return self.threads
