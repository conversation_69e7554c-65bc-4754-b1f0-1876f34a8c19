#!/usr/bin/env python3
"""
LangChain Multi-Agent Usage Example

This example demonstrates the comprehensive capabilities of the LangChain Multi-Agent system
including document processing, web crawling, RAG queries, and workflow execution.
"""

import asyncio
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add the parent directory to the path to import the agent
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents.langchain_multi_agent import EnhancedLangChainAgent

async def main():
    """Main example function demonstrating LangChain agent capabilities"""
    
    # Initialize the agent
    print("🚀 Initializing LangChain Multi-Agent System...")
    agent = EnhancedLangChainAgent()
    await agent.initialize()
    
    try:
        # ============================================================================
        # 1. DOCUMENT PROCESSING EXAMPLE
        # ============================================================================
        print("\n📄 Document Processing Example")
        print("-" * 50)
        
        # Create a sample document for testing
        sample_doc_path = "sample_documents/test_tender.txt"
        os.makedirs("sample_documents", exist_ok=True)
        
        with open(sample_doc_path, 'w') as f:
            f.write("""
            TENDER NOTICE
            
            Tender Number: T2024-001
            Project: Construction of Office Building
            Closing Date: 2024-12-31
            
            The Department of Public Works invites sealed bids for the construction
            of a new office building. The project includes:
            - Foundation work
            - Structural construction
            - Electrical installation
            - Plumbing systems
            
            Estimated Value: $2,500,000
            
            Briefing Session: 2024-11-15 at 10:00 AM
            Location: Main Conference Room
            Attendance: Compulsory
            
            Contact: John Smith, Project Manager
            Email: <EMAIL>
            Phone: (*************
            """)
        
        # Process the document
        try:
            doc_metadata = await agent.process_document(sample_doc_path, "tender_notice")
            print(f"✅ Processed document: {doc_metadata.title}")
            print(f"   Document Type: {doc_metadata.document_type}")
            print(f"   Confidence Score: {doc_metadata.confidence_score:.2f}")
            print(f"   Document ID: {doc_metadata.document_id}")
        except Exception as e:
            print(f"⚠️  Document processing failed: {e}")
        
        # ============================================================================
        # 2. WEB SEARCH AND CRAWLING EXAMPLE
        # ============================================================================
        print("\n🔍 Web Search and Crawling Example")
        print("-" * 50)
        
        try:
            search_results = await agent.search_and_crawl(
                "government construction tenders 2024",
                max_results=5,
                crawl_top_results=2
            )
            
            print(f"✅ Search and crawl completed")
            print(f"   Query: government construction tenders 2024")
            print(f"   Results type: {type(search_results)}")
            
            if isinstance(search_results, dict):
                for key, value in search_results.items():
                    if isinstance(value, str):
                        print(f"   {key}: {value[:100]}...")
                    else:
                        print(f"   {key}: {type(value).__name__}")
        except Exception as e:
            print(f"⚠️  Search and crawl failed: {e}")
        
        # ============================================================================
        # 3. RAG QUERY EXAMPLE
        # ============================================================================
        print("\n🧠 RAG Query Example")
        print("-" * 50)
        
        try:
            rag_response = await agent.query_knowledge_base(
                "What are the key requirements for construction tenders?",
                top_k=3
            )
            
            print(f"✅ RAG query completed")
            print(f"   Query: What are the key requirements for construction tenders?")
            print(f"   Response: {rag_response[:200]}...")
        except Exception as e:
            print(f"⚠️  RAG query failed: {e}")
        
        # ============================================================================
        # 4. COMMUNICATION EXAMPLES
        # ============================================================================
        print("\n📧 Communication Examples")
        print("-" * 50)
        
        # Send email notification (if configured)
        try:
            email_result = await agent.send_notification(
                "<EMAIL>",
                "LangChain Agent Test",
                "<h1>Test Email</h1><p>This is a test email from the LangChain Multi-Agent system.</p>"
            )
            print(f"✅ Email notification: {email_result}")
        except Exception as e:
            print(f"⚠️  Email notification failed (likely not configured): {e}")
        
        # Create calendar event
        try:
            calendar_result = await agent.create_calendar_event(
                "LangChain Agent Demo",
                (datetime.now() + timedelta(days=1)).isoformat(),
                (datetime.now() + timedelta(days=1, hours=1)).isoformat(),
                "Demo meeting created by LangChain Multi-Agent system"
            )
            print(f"✅ Calendar event: {calendar_result}")
        except Exception as e:
            print(f"⚠️  Calendar event creation failed (likely not configured): {e}")
        
        # ============================================================================
        # 5. WORKFLOW EXECUTION EXAMPLE
        # ============================================================================
        print("\n⚙️  Workflow Execution Example")
        print("-" * 50)
        
        # Execute tender analysis workflow
        workflow_params = {
            'document_path': sample_doc_path,
            'search_query': 'construction tender opportunities',
            'analysis_query': 'What are the current trends in construction tenders?',
            'create_reminders': True,
            'notify_email': '<EMAIL>'
        }
        
        print("🔄 Starting tender analysis workflow...")
        try:
            workflow_result = await agent.execute_workflow('tender_analysis', workflow_params)
            
            print(f"✅ Workflow completed: {workflow_result.status}")
            print(f"   Workflow ID: {workflow_result.workflow_id}")
            print(f"   Execution time: {workflow_result.execution_time_ms:.2f}ms")
            print(f"   Confidence score: {workflow_result.confidence_score:.2f}")
            print(f"   Result components: {len(workflow_result.results)}")
            
            # Display workflow results
            for component, data in workflow_result.results.items():
                if isinstance(data, dict):
                    print(f"   - {component}: {len(data)} items")
                elif isinstance(data, str):
                    print(f"   - {component}: {data[:50]}...")
                else:
                    print(f"   - {component}: {type(data).__name__}")
                    
        except Exception as e:
            print(f"❌ Workflow execution failed: {e}")
        
        # ============================================================================
        # 6. INTERACTIVE CHAT EXAMPLE
        # ============================================================================
        print("\n💬 Interactive Chat Example")
        print("-" * 50)
        
        chat_queries = [
            "What documents have been processed?",
            "Summarize the tender information",
            "What are the key dates I should remember?",
            "How can I improve my tender submission?"
        ]
        
        for query in chat_queries:
            try:
                response = await agent.chat(query)
                print(f"Q: {query}")
                print(f"A: {response[:150]}...")
                print()
            except Exception as e:
                print(f"Chat query failed: {e}")
        
        # ============================================================================
        # 7. EXECUTION LOG AND MONITORING
        # ============================================================================
        print("\n📊 Execution Log and Monitoring")
        print("-" * 50)
        
        execution_log = agent.get_execution_log()
        print(f"✅ Execution log retrieved:")
        print(f"   Total actions: {len(execution_log)}")
        
        # Show recent actions
        for i, log_entry in enumerate(execution_log[-5:]):  # Last 5 actions
            print(f"   {i+1}. {log_entry.get('type', 'unknown')} - "
                  f"{log_entry.get('tool', log_entry.get('timestamp', 'N/A'))}")
        
        # ============================================================================
        # 8. MARKET RESEARCH WORKFLOW EXAMPLE
        # ============================================================================
        print("\n📈 Market Research Workflow Example")
        print("-" * 50)
        
        market_research_params = {
            'search_queries': [
                'construction industry trends 2024',
                'government infrastructure spending',
                'building material costs 2024'
            ]
        }
        
        try:
            market_result = await agent.execute_workflow('market_research', market_research_params)
            print(f"✅ Market research completed: {market_result.status}")
            print(f"   Research components: {len(market_result.results)}")
            print(f"   Confidence score: {market_result.confidence_score:.2f}")
        except Exception as e:
            print(f"⚠️  Market research workflow failed: {e}")
        
        print("\n🎉 LangChain Multi-Agent Demo Completed Successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error during demo execution: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup resources
        print("\n🧹 Cleaning up resources...")
        await agent.cleanup()
        print("✅ Cleanup completed")

def setup_environment():
    """Setup environment variables and directories"""
    print("🔧 Setting up environment...")
    
    # Create necessary directories
    directories = [
        "logs",
        "sample_documents",
        "credentials"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # Check for required environment variables
    required_env_vars = [
        'OPENAI_API_KEY',
        'BRAVE_API_KEY'
    ]
    
    optional_env_vars = [
        'ANTHROPIC_API_KEY',
        'GOOGLE_API_KEY',
        'SMTP_USERNAME',
        'SMTP_PASSWORD',
        'SLACK_BOT_TOKEN'
    ]
    
    missing_required = []
    missing_optional = []
    
    for var in required_env_vars:
        if not os.getenv(var):
            missing_required.append(var)
    
    for var in optional_env_vars:
        if not os.getenv(var):
            missing_optional.append(var)
    
    if missing_required:
        print("❌ Error: The following required environment variables are not set:")
        for var in missing_required:
            print(f"   - {var}")
        print("   Please set these variables before running the demo.")
        return False
    
    if missing_optional:
        print("⚠️  Warning: The following optional environment variables are not set:")
        for var in missing_optional:
            print(f"   - {var}")
        print("   Some features may not work without proper configuration.")
    
    print("✅ Environment setup completed")
    return True

if __name__ == "__main__":
    print("🚀 LangChain Multi-Agent Demo")
    print("=" * 60)
    
    # Setup environment
    if not setup_environment():
        exit(1)
    
    # Run the main demo
    asyncio.run(main())
