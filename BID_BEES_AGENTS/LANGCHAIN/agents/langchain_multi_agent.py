# agents/langchain_multi_agent.py

import asyncio
import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union, Callable
from pathlib import Path
import yaml
import numpy as np

# Core LangChain imports
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.agents.agent_types import AgentType
from langchain.agents.initialize import initialize_agent
from langchain.tools import BaseTool, Tool
from langchain.schema import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.memory import ConversationBufferWindowMemory, ConversationSummaryBufferMemory
from langchain.callbacks.manager import CallbackManagerForToolRun
from langchain.callbacks.base import BaseCallbackHandler
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.chains import <PERSON><PERSON><PERSON>n
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.chains.retrieval import create_retrieval_chain

# LLM imports
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_anthropic import ChatAnthropic
from langchain_google_genai import ChatGoogleGenerativeAI

# Vector stores and retrievers
from langchain_qdrant import QdrantVectorStore
from langchain_mongodb import MongoDBAtlasVectorSearch
from langchain_community.vectorstores import Redis as RedisVectorStore
from langchain_community.document_loaders import PyPDFLoader, TextLoader, UnstructuredImageLoader
from langchain_community.document_transformers import Html2TextTransformer
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

# Database and storage
import motor.motor_asyncio
import redis.asyncio as redis
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
import networkx as nx

# Web and communication
import httpx
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from slack_sdk.web.async_client import AsyncWebClient

# Crawl4AI and document processing
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy
import fitz  # PyMuPDF
import pytesseract
from PIL import Image

# Pydantic for data validation
from pydantic import BaseModel, Field, ConfigDict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# PYDANTIC MODELS FOR DATA STRUCTURES
# ============================================================================

class DocumentMetadata(BaseModel):
    """Document metadata structure"""
    model_config = ConfigDict(arbitrary_types_allowed=True)

    document_id: str
    title: str
    document_type: str
    source: str
    processed_at: datetime
    confidence_score: float = Field(ge=0.0, le=1.0)
    extracted_entities: List[Dict[str, Any]] = []
    vector_id: Optional[str] = None
    tags: List[str] = []

class SearchResult(BaseModel):
    """Search results structure"""
    query: str
    results: List[Dict[str, Any]]
    timestamp: datetime
    source: str
    total_results: int = 0
    cached: bool = False

class CrawlResult(BaseModel):
    """Web crawling results"""
    url: str
    title: str
    content: str
    structured_data: Dict[str, Any] = {}
    links: List[str] = []
    crawled_at: datetime
    success: bool = True
    error_message: Optional[str] = None

class WorkflowResult(BaseModel):
    """Workflow execution results"""
    workflow_id: str
    workflow_type: str
    status: str
    results: Dict[str, Any]
    created_at: datetime
    completed_at: Optional[datetime] = None
    execution_time_ms: float = 0.0
    confidence_score: float = 0.0

# ============================================================================
# CUSTOM LANGCHAIN TOOLS
# ============================================================================

class InternetSearchTool(BaseTool):
    """Custom tool for internet search using multiple sources"""
    name = "internet_search"
    description = "Search the internet using Brave Search, Google, or other search engines"

    def __init__(self, http_client: httpx.AsyncClient, config: Dict[str, Any]):
        super().__init__()
        self.http_client = http_client
        self.config = config

    def _run(self, query: str, source: str = "brave", count: int = 10,
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Synchronous search (not recommended for async operations)"""
        return asyncio.run(self._arun(query, source, count, run_manager))

    async def _arun(self, query: str, source: str = "brave", count: int = 10,
                   run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Asynchronous internet search"""
        try:
            if source == "brave":
                response = await self.http_client.get(
                    "https://api.search.brave.com/res/v1/web/search",
                    params={"q": query, "count": count}
                )
                response.raise_for_status()
                data = response.json()
                results = data.get('web', {}).get('results', [])
            else:
                # Implement other search sources
                results = []

            # Format results for LLM consumption
            formatted_results = []
            for i, result in enumerate(results[:count]):
                formatted_results.append(f"{i+1}. {result.get('title', 'No title')}\n"
                                       f"   URL: {result.get('url', 'No URL')}\n"
                                       f"   Snippet: {result.get('description', 'No description')}\n")

            return f"Search results for '{query}':\n\n" + "\n".join(formatted_results)

        except Exception as e:
            logger.error(f"Search failed: {e}")
            return f"Search failed: {str(e)}"

class WebCrawlerTool(BaseTool):
    """Custom tool for web crawling with Crawl4AI"""
    name = "web_crawler"
    description = "Crawl websites and extract structured information"

    def __init__(self, crawler: AsyncWebCrawler, config: Dict[str, Any]):
        super().__init__()
        self.crawler = crawler
        self.config = config

    def _run(self, url: str, extraction_strategy: Optional[str] = None,
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Synchronous crawling"""
        return asyncio.run(self._arun(url, extraction_strategy, run_manager))

    async def _arun(self, url: str, extraction_strategy: Optional[str] = None,
                   run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Asynchronous web crawling"""
        try:
            strategy = None
            if extraction_strategy:
                strategy = LLMExtractionStrategy(
                    provider="openai",
                    api_token=self.config.get('openai_api_key'),
                    instruction=extraction_strategy
                )

            result = await self.crawler.arun(
                url=url,
                extraction_strategy=strategy,
                bypass_cache=False
            )

            if result.success:
                content_preview = result.markdown[:1000] if result.markdown else "No content"
                structured_data = ""

                if strategy and result.extracted_content:
                    try:
                        structured_data = f"\n\nStructured Data:\n{result.extracted_content}"
                    except:
                        structured_data = f"\n\nRaw Extraction:\n{result.extracted_content}"

                return f"Successfully crawled {url}\n\nContent Preview:\n{content_preview}{structured_data}"
            else:
                return f"Failed to crawl {url}: {result.error_message}"

        except Exception as e:
            logger.error(f"Crawling failed: {e}")
            return f"Crawling failed: {str(e)}"

class DocumentProcessorTool(BaseTool):
    """Custom tool for document processing and OCR"""
    name = "document_processor"
    description = "Process documents (PDF, images, text) and extract structured information"

    def __init__(self, vector_store, embeddings, config: Dict[str, Any]):
        super().__init__()
        self.vector_store = vector_store
        self.embeddings = embeddings
        self.config = config

    def _run(self, file_path: str, document_type: str = "auto",
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Synchronous document processing"""
        return asyncio.run(self._arun(file_path, document_type, run_manager))

    async def _arun(self, file_path: str, document_type: str = "auto",
                   run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Asynchronous document processing"""
        try:
            # Load document based on file type
            file_path = Path(file_path)

            if file_path.suffix.lower() == '.pdf':
                loader = PyPDFLoader(str(file_path))
                documents = loader.load()
            elif file_path.suffix.lower() in ['.txt', '.md']:
                loader = TextLoader(str(file_path))
                documents = loader.load()
            elif file_path.suffix.lower() in ['.png', '.jpg', '.jpeg']:
                # OCR for images
                image = Image.open(file_path)
                text = pytesseract.image_to_string(image)
                documents = [Document(page_content=text, metadata={"source": str(file_path)})]
            else:
                return f"Unsupported file format: {file_path.suffix}"

            # Split documents for better processing
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200
            )
            split_docs = text_splitter.split_documents(documents)

            # Add to vector store
            if self.vector_store and split_docs:
                await self.vector_store.aadd_documents(split_docs)

            # Extract key information
            content_preview = documents[0].page_content[:500] if documents else "No content"

            return f"Successfully processed {file_path.name}\n" \
                   f"Document type: {document_type}\n" \
                   f"Pages/chunks: {len(split_docs)}\n" \
                   f"Content preview: {content_preview}..."

        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            return f"Document processing failed: {str(e)}"

class RAGQueryTool(BaseTool):
    """Custom tool for RAG-based information retrieval"""
    name = "rag_query"
    description = "Query the knowledge base using retrieval-augmented generation"

    def __init__(self, retriever, llm):
        super().__init__()
        self.retriever = retriever
        self.llm = llm

    def _run(self, query: str, top_k: int = 5,
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Synchronous RAG query"""
        return asyncio.run(self._arun(query, top_k, run_manager))

    async def _arun(self, query: str, top_k: int = 5,
                   run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Asynchronous RAG query"""
        try:
            # Retrieve relevant documents
            docs = await self.retriever.aget_relevant_documents(query)

            if not docs:
                return f"No relevant documents found for query: '{query}'"

            # Create context from retrieved documents
            context = "\n\n".join([doc.page_content for doc in docs[:top_k]])

            # Generate response using LLM
            prompt = f"""Based on the following context, answer the question: {query}

Context:
{context}

Answer:"""

            response = await self.llm.ainvoke(prompt)

            return f"Query: {query}\n\nAnswer: {response.content}\n\n" \
                   f"Sources: {len(docs)} documents retrieved"

        except Exception as e:
            logger.error(f"RAG query failed: {e}")
            return f"RAG query failed: {str(e)}"

class EmailNotificationTool(BaseTool):
    """Custom tool for sending email notifications"""
    name = "email_notification"
    description = "Send email notifications to specified recipients"

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config

    def _run(self, recipient: str, subject: str, message: str,
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Send email notification"""
        try:
            smtp_config = self.config['integrations']['smtp']

            msg = MIMEMultipart()
            msg['From'] = smtp_config['from_email']
            msg['To'] = recipient
            msg['Subject'] = subject
            msg.attach(MIMEText(message, 'html'))

            with smtplib.SMTP(smtp_config['server'], smtp_config['port']) as server:
                server.starttls()
                server.login(smtp_config['username'], smtp_config['password'])
                server.send_message(msg)

            return f"Email sent successfully to {recipient}"

        except Exception as e:
            logger.error(f"Email sending failed: {e}")
            return f"Email sending failed: {str(e)}"

    async def _arun(self, recipient: str, subject: str, message: str,
                   run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Async email sending"""
        return self._run(recipient, subject, message, run_manager)

class CalendarEventTool(BaseTool):
    """Custom tool for creating calendar events"""
    name = "calendar_event"
    description = "Create Google Calendar events with reminders"

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config

    def _run(self, title: str, start_time: str, end_time: str, description: str = "",
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Create calendar event"""
        try:
            creds_path = self.config['integrations']['google']['credentials_path']
            creds = Credentials.from_authorized_user_file(creds_path)
            service = build('calendar', 'v3', credentials=creds)

            event = {
                'summary': title,
                'description': description,
                'start': {'dateTime': start_time, 'timeZone': 'UTC'},
                'end': {'dateTime': end_time, 'timeZone': 'UTC'},
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'email', 'minutes': 24 * 60},
                        {'method': 'popup', 'minutes': 60},
                    ],
                }
            }

            created_event = service.events().insert(calendarId='primary', body=event).execute()

            return f"Calendar event created: {title}\nEvent ID: {created_event['id']}"

        except Exception as e:
            logger.error(f"Calendar event creation failed: {e}")
            return f"Calendar event creation failed: {str(e)}"

    async def _arun(self, title: str, start_time: str, end_time: str, description: str = "",
                   run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """Async calendar event creation"""
        return self._run(title, start_time, end_time, description, run_manager)

# ============================================================================
# CUSTOM CALLBACK HANDLER
# ============================================================================

class EnhancedCallbackHandler(BaseCallbackHandler):
    """Custom callback handler for monitoring agent execution"""

    def __init__(self):
        self.execution_log = []
        self.start_time = None

    def on_agent_action(self, action, **kwargs):
        """Called when agent takes an action"""
        self.execution_log.append({
            'type': 'action',
            'tool': action.tool,
            'input': action.tool_input,
            'timestamp': datetime.now()
        })
        logger.info(f"Agent action: {action.tool} with input: {action.tool_input}")

    def on_agent_finish(self, finish, **kwargs):
        """Called when agent finishes"""
        self.execution_log.append({
            'type': 'finish',
            'output': finish.return_values,
            'timestamp': datetime.now()
        })
        logger.info(f"Agent finished with output: {finish.return_values}")

    def on_tool_start(self, serialized, input_str, **kwargs):
        """Called when tool starts"""
        logger.info(f"Tool started: {serialized.get('name', 'Unknown')} with input: {input_str}")

    def on_tool_end(self, output, **kwargs):
        """Called when tool ends"""
        logger.info(f"Tool completed with output: {output[:100]}...")

# ============================================================================
# ENHANCED LANGCHAIN MULTI-AGENT SYSTEM
# ============================================================================

class EnhancedLangChainAgent:
    """
    Comprehensive LangChain-based multi-agent system with document processing,
    web crawling, RAG capabilities, and communication features.
    """

    def __init__(self, config_path: str = "config/langchain_agent_config.yaml"):
        self.config = self._load_config(config_path)
        self.agents = {}
        self.tools = {}
        self.vector_stores = {}
        self.memory = None
        self.callback_handler = EnhancedCallbackHandler()

        # Initialize components
        self._initialize_llms()
        self._initialize_embeddings()
        self._initialize_databases()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Default configuration"""
        return {
            'llms': {
                'primary': {'provider': 'openai', 'model': 'gpt-4-turbo-preview', 'temperature': 0.1},
                'reasoning': {'provider': 'anthropic', 'model': 'claude-3-sonnet-20240229', 'temperature': 0.1},
                'document': {'provider': 'google', 'model': 'gemini-1.5-pro', 'temperature': 0.1}
            },
            'embeddings': {
                'provider': 'openai',
                'model': 'text-embedding-3-large'
            },
            'databases': {
                'mongodb': {'url': 'mongodb://localhost:27017', 'database': 'langchain_agent'},
                'redis': {'url': 'redis://localhost:6379'},
                'qdrant': {'host': 'localhost', 'port': 6333}
            },
            'integrations': {
                'brave_search': {'api_key': ''},
                'smtp': {'server': 'smtp.gmail.com', 'port': 587, 'from_email': '', 'username': '', 'password': ''},
                'google': {'credentials_path': 'credentials/google_creds.json'}
            },
            'memory': {
                'type': 'conversation_summary_buffer',
                'max_token_limit': 2000,
                'return_messages': True
            }
        }

    def _initialize_llms(self):
        """Initialize LLM instances"""
        self.llms = {}

        for name, config in self.config['llms'].items():
            if config['provider'] == 'openai':
                self.llms[name] = ChatOpenAI(
                    model=config['model'],
                    temperature=config['temperature']
                )
            elif config['provider'] == 'anthropic':
                self.llms[name] = ChatAnthropic(
                    model=config['model'],
                    temperature=config['temperature']
                )
            elif config['provider'] == 'google':
                self.llms[name] = ChatGoogleGenerativeAI(
                    model=config['model'],
                    temperature=config['temperature']
                )

    def _initialize_embeddings(self):
        """Initialize embedding models"""
        embedding_config = self.config['embeddings']

        if embedding_config['provider'] == 'openai':
            self.embeddings = OpenAIEmbeddings(
                model=embedding_config['model']
            )

    async def _initialize_databases(self):
        """Initialize database connections"""
        try:
            # MongoDB connection
            self.mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(
                self.config['databases']['mongodb']['url']
            )

            # Redis connection
            self.redis_client = redis.from_url(
                self.config['databases']['redis']['url']
            )

            # Qdrant vector store
            qdrant_config = self.config['databases']['qdrant']
            self.qdrant_client = QdrantClient(
                host=qdrant_config['host'],
                port=qdrant_config['port']
            )

            # Initialize vector stores
            await self._initialize_vector_stores()

        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise

    async def _initialize_vector_stores(self):
        """Initialize vector stores for different data types"""
        try:
            # Qdrant vector store for documents
            self.vector_stores['documents'] = QdrantVectorStore(
                client=self.qdrant_client,
                collection_name="documents",
                embeddings=self.embeddings
            )

            # Qdrant vector store for web content
            self.vector_stores['web_content'] = QdrantVectorStore(
                client=self.qdrant_client,
                collection_name="web_content",
                embeddings=self.embeddings
            )

            logger.info("Vector stores initialized successfully")

        except Exception as e:
            logger.error(f"Vector store initialization failed: {e}")
            # Continue without vector stores if they fail
            self.vector_stores = {}

    async def initialize(self):
        """Initialize all components asynchronously"""
        await self._initialize_databases()
        await self._initialize_tools()
        await self._initialize_agents()
        await self._initialize_memory()
        logger.info("LangChain agent system initialized successfully")

    async def _initialize_tools(self):
        """Initialize custom tools"""
        try:
            # HTTP client for web requests
            self.http_client = httpx.AsyncClient(
                headers={
                    "X-Subscription-Token": self.config['integrations']['brave_search']['api_key'],
                    "User-Agent": "LangChainAgent/1.0"
                },
                timeout=30.0
            )

            # Crawl4AI crawler
            self.crawler = AsyncWebCrawler(verbose=True, max_concurrent=5)

            # Initialize tools
            self.tools = {
                'internet_search': InternetSearchTool(self.http_client, self.config),
                'web_crawler': WebCrawlerTool(self.crawler, self.config),
                'document_processor': DocumentProcessorTool(
                    self.vector_stores.get('documents'),
                    self.embeddings,
                    self.config
                ),
                'email_notification': EmailNotificationTool(self.config),
                'calendar_event': CalendarEventTool(self.config)
            }

            # Add RAG tool if vector store is available
            if self.vector_stores.get('documents'):
                retriever = self.vector_stores['documents'].as_retriever(search_kwargs={"k": 5})
                self.tools['rag_query'] = RAGQueryTool(retriever, self.llms['primary'])

            logger.info(f"Initialized {len(self.tools)} tools")

        except Exception as e:
            logger.error(f"Tool initialization failed: {e}")
            self.tools = {}

    async def _initialize_memory(self):
        """Initialize conversation memory"""
        memory_config = self.config['memory']

        if memory_config['type'] == 'conversation_summary_buffer':
            self.memory = ConversationSummaryBufferMemory(
                llm=self.llms['primary'],
                max_token_limit=memory_config['max_token_limit'],
                return_messages=memory_config['return_messages'],
                memory_key="chat_history",
                input_key="input",
                output_key="output"
            )
        else:
            self.memory = ConversationBufferWindowMemory(
                k=10,
                return_messages=True,
                memory_key="chat_history",
                input_key="input",
                output_key="output"
            )

    async def _initialize_agents(self):
        """Initialize specialized agents"""
        try:
            # Convert tools to list for agent initialization
            tool_list = list(self.tools.values())

            # Primary agent for general tasks
            self.agents['primary'] = initialize_agent(
                tools=tool_list,
                llm=self.llms['primary'],
                agent=AgentType.OPENAI_FUNCTIONS,
                memory=self.memory,
                callbacks=[self.callback_handler],
                verbose=True,
                handle_parsing_errors=True
            )

            # Document processing specialist
            doc_tools = [
                self.tools.get('document_processor'),
                self.tools.get('rag_query')
            ]
            doc_tools = [tool for tool in doc_tools if tool is not None]

            if doc_tools:
                self.agents['document'] = initialize_agent(
                    tools=doc_tools,
                    llm=self.llms.get('document', self.llms['primary']),
                    agent=AgentType.OPENAI_FUNCTIONS,
                    callbacks=[self.callback_handler],
                    verbose=True
                )

            # Research specialist
            research_tools = [
                self.tools.get('internet_search'),
                self.tools.get('web_crawler'),
                self.tools.get('rag_query')
            ]
            research_tools = [tool for tool in research_tools if tool is not None]

            if research_tools:
                self.agents['research'] = initialize_agent(
                    tools=research_tools,
                    llm=self.llms.get('reasoning', self.llms['primary']),
                    agent=AgentType.OPENAI_FUNCTIONS,
                    callbacks=[self.callback_handler],
                    verbose=True
                )

            logger.info(f"Initialized {len(self.agents)} specialized agents")

        except Exception as e:
            logger.error(f"Agent initialization failed: {e}")
            # Fallback to basic agent
            self.agents = {'primary': None}

    # ============================================================================
    # CORE AGENT METHODS
    # ============================================================================

    async def process_document(self, file_path: str, document_type: str = "auto") -> DocumentMetadata:
        """Process documents using the document specialist agent"""
        try:
            if 'document' in self.agents and self.agents['document']:
                agent = self.agents['document']
            else:
                agent = self.agents['primary']

            if agent is None:
                # Direct tool usage if agent initialization failed
                tool = self.tools.get('document_processor')
                if tool:
                    result = await tool._arun(file_path, document_type)
                    return DocumentMetadata(
                        document_id=hashlib.md5(file_path.encode()).hexdigest(),
                        title=Path(file_path).stem,
                        document_type=document_type,
                        source=file_path,
                        processed_at=datetime.now(),
                        confidence_score=0.8
                    )
                else:
                    raise ValueError("Document processing tool not available")

            # Use agent to process document
            prompt = f"""Process the document at '{file_path}' with document type '{document_type}'.
                      Extract key information and store it in the vector database for future retrieval.
                      Provide a summary of the extracted information."""

            result = await agent.arun(prompt)

            # Create metadata object
            doc_metadata = DocumentMetadata(
                document_id=hashlib.md5(file_path.encode()).hexdigest(),
                title=Path(file_path).stem,
                document_type=document_type,
                source=file_path,
                processed_at=datetime.now(),
                confidence_score=0.85
            )

            return doc_metadata

        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            raise

    async def search_and_crawl(self, query: str, max_results: int = 5,
                              crawl_top_results: int = 2) -> Dict[str, Any]:
        """Search internet and crawl top results"""
        try:
            agent = self.agents.get('research', self.agents['primary'])

            if agent is None:
                # Direct tool usage
                search_tool = self.tools.get('internet_search')
                crawl_tool = self.tools.get('web_crawler')

                if not search_tool:
                    raise ValueError("Search tool not available")

                search_result = await search_tool._arun(query, "brave", max_results)

                results = {
                    'search_results': search_result,
                    'crawl_results': []
                }

                if crawl_tool:
                    # Extract URLs from search results (simplified)
                    # In a real implementation, you'd parse the search results properly
                    sample_urls = ["https://example.com"]  # Placeholder

                    for url in sample_urls[:crawl_top_results]:
                        crawl_result = await crawl_tool._arun(url)
                        results['crawl_results'].append(crawl_result)

                return results

            # Use agent for search and crawl
            prompt = f"""Search for '{query}' and then crawl the top {crawl_top_results} results.
                      Provide a comprehensive analysis of the findings."""

            result = await agent.arun(prompt)

            return {
                'analysis': result,
                'query': query,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Search and crawl failed: {e}")
            raise

    async def query_knowledge_base(self, query: str, top_k: int = 5) -> str:
        """Query the knowledge base using RAG"""
        try:
            rag_tool = self.tools.get('rag_query')

            if not rag_tool:
                return "RAG query tool not available. Please process some documents first."

            result = await rag_tool._arun(query, top_k)
            return result

        except Exception as e:
            logger.error(f"Knowledge base query failed: {e}")
            return f"Query failed: {str(e)}"

    async def send_notification(self, recipient: str, subject: str, message: str) -> str:
        """Send email notification"""
        try:
            email_tool = self.tools.get('email_notification')

            if not email_tool:
                return "Email notification tool not available"

            result = await email_tool._arun(recipient, subject, message)
            return result

        except Exception as e:
            logger.error(f"Notification sending failed: {e}")
            return f"Notification failed: {str(e)}"

    async def create_calendar_event(self, title: str, start_time: str, end_time: str,
                                  description: str = "") -> str:
        """Create calendar event"""
        try:
            calendar_tool = self.tools.get('calendar_event')

            if not calendar_tool:
                return "Calendar event tool not available"

            result = await calendar_tool._arun(title, start_time, end_time, description)
            return result

        except Exception as e:
            logger.error(f"Calendar event creation failed: {e}")
            return f"Calendar event creation failed: {str(e)}"

    # ============================================================================
    # WORKFLOW EXECUTION
    # ============================================================================

    async def execute_workflow(self, workflow_type: str, parameters: Dict[str, Any]) -> WorkflowResult:
        """Execute complex workflows"""
        workflow_id = f"workflow_{hashlib.md5(f'{workflow_type}_{datetime.now()}'.encode()).hexdigest()}"
        start_time = datetime.now()

        try:
            if workflow_type == "tender_analysis":
                results = await self._execute_tender_analysis_workflow(parameters)
            elif workflow_type == "market_research":
                results = await self._execute_market_research_workflow(parameters)
            elif workflow_type == "document_intelligence":
                results = await self._execute_document_intelligence_workflow(parameters)
            elif workflow_type == "competitive_analysis":
                results = await self._execute_competitive_analysis_workflow(parameters)
            else:
                raise ValueError(f"Unknown workflow type: {workflow_type}")

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            return WorkflowResult(
                workflow_id=workflow_id,
                workflow_type=workflow_type,
                status="completed",
                results=results,
                created_at=start_time,
                completed_at=datetime.now(),
                execution_time_ms=execution_time,
                confidence_score=results.get('confidence_score', 0.8)
            )

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")

            return WorkflowResult(
                workflow_id=workflow_id,
                workflow_type=workflow_type,
                status="failed",
                results={"error": str(e)},
                created_at=start_time,
                completed_at=datetime.now(),
                execution_time_ms=(datetime.now() - start_time).total_seconds() * 1000,
                confidence_score=0.0
            )

    async def _execute_tender_analysis_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute tender analysis workflow"""
        results = {}

        # 1. Process tender document if provided
        if 'document_path' in parameters:
            doc_metadata = await self.process_document(
                parameters['document_path'],
                'tender_notice'
            )
            results['document_analysis'] = doc_metadata.model_dump()

        # 2. Search for related information
        if 'search_query' in parameters:
            search_results = await self.search_and_crawl(
                parameters['search_query'],
                max_results=5,
                crawl_top_results=2
            )
            results['market_research'] = search_results

        # 3. Query knowledge base for insights
        if 'analysis_query' in parameters:
            insights = await self.query_knowledge_base(parameters['analysis_query'])
            results['insights'] = insights

        # 4. Create calendar reminders if requested
        if parameters.get('create_reminders', False) and 'document_analysis' in results:
            # Extract closing date from document analysis (simplified)
            reminder_result = await self.create_calendar_event(
                "Tender Deadline Reminder",
                (datetime.now() + timedelta(days=7)).isoformat(),
                (datetime.now() + timedelta(days=7, hours=1)).isoformat(),
                "Tender submission deadline approaching"
            )
            results['calendar_reminder'] = reminder_result

        # 5. Send notification if requested
        if 'notify_email' in parameters:
            notification_result = await self.send_notification(
                parameters['notify_email'],
                "Tender Analysis Complete",
                f"Analysis completed for workflow {parameters.get('search_query', 'Unknown')}"
            )
            results['notification'] = notification_result

        results['confidence_score'] = 0.85
        return results

    async def _execute_market_research_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute market research workflow"""
        results = {}

        # Multi-query search and analysis
        search_queries = parameters.get('search_queries', [])
        for i, query in enumerate(search_queries):
            search_results = await self.search_and_crawl(query, max_results=10, crawl_top_results=3)
            results[f'research_{i+1}'] = search_results

        # Comprehensive analysis using primary agent
        if self.agents.get('primary'):
            analysis_prompt = f"""
            Analyze the market research data for the following queries: {search_queries}
            Provide insights on:
            1. Market trends
            2. Competitive landscape
            3. Opportunities and threats
            4. Key players
            5. Recommendations
            """

            try:
                analysis = await self.agents['primary'].arun(analysis_prompt)
                results['comprehensive_analysis'] = analysis
            except Exception as e:
                results['analysis_error'] = str(e)

        results['confidence_score'] = 0.8
        return results

    async def _execute_document_intelligence_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute document intelligence workflow"""
        results = {}

        # Process multiple documents
        document_paths = parameters.get('document_paths', [])
        processed_docs = []

        for doc_path in document_paths:
            try:
                doc_metadata = await self.process_document(doc_path)
                processed_docs.append(doc_metadata.model_dump())
            except Exception as e:
                processed_docs.append({'error': str(e), 'path': doc_path})

        results['processed_documents'] = processed_docs

        # Cross-document analysis if multiple documents
        if len(processed_docs) > 1:
            analysis_query = parameters.get('analysis_query', 'Compare and analyze these documents')
            cross_analysis = await self.query_knowledge_base(analysis_query)
            results['cross_analysis'] = cross_analysis

        results['confidence_score'] = 0.9
        return results

    async def _execute_competitive_analysis_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute competitive analysis workflow"""
        results = {}

        # Search for competitors
        competitor_query = parameters.get('competitor_query', '')
        if competitor_query:
            competitor_search = await self.search_and_crawl(
                competitor_query,
                max_results=10,
                crawl_top_results=5
            )
            results['competitor_search'] = competitor_search

        # Analyze specific competitor URLs
        competitor_urls = parameters.get('competitor_urls', [])
        competitor_analyses = []

        crawl_tool = self.tools.get('web_crawler')
        if crawl_tool:
            for url in competitor_urls:
                try:
                    analysis = await crawl_tool._arun(
                        url,
                        "Extract company information, services, pricing, and competitive advantages"
                    )
                    competitor_analyses.append({'url': url, 'analysis': analysis})
                except Exception as e:
                    competitor_analyses.append({'url': url, 'error': str(e)})

        results['competitor_profiles'] = competitor_analyses
        results['confidence_score'] = 0.75
        return results

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if hasattr(self, 'http_client'):
                await self.http_client.aclose()

            if hasattr(self, 'redis_client'):
                await self.redis_client.close()

            if hasattr(self, 'mongodb_client'):
                self.mongodb_client.close()

            if hasattr(self, 'crawler'):
                await self.crawler.aclose()

            logger.info("LangChain agent cleanup completed")

        except Exception as e:
            logger.error(f"Cleanup failed: {e}")

    def get_execution_log(self) -> List[Dict[str, Any]]:
        """Get execution log from callback handler"""
        return self.callback_handler.execution_log

    def clear_memory(self):
        """Clear conversation memory"""
        if self.memory:
            self.memory.clear()

    async def chat(self, message: str) -> str:
        """Interactive chat with the primary agent"""
        try:
            if self.agents.get('primary'):
                response = await self.agents['primary'].arun(message)
                return response
            else:
                return "Primary agent not available"

        except Exception as e:
            logger.error(f"Chat failed: {e}")
            return f"Chat error: {str(e)}"