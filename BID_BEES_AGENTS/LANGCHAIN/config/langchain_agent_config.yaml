# Lang<PERSON>hain Multi-Agent Configuration

llms:
  primary:
    provider: "openai"
    model: "gpt-4-turbo-preview"
    temperature: 0.1
    max_tokens: 4000
  reasoning:
    provider: "anthropic"
    model: "claude-3-sonnet-20240229"
    temperature: 0.1
    max_tokens: 4000
  document:
    provider: "google"
    model: "gemini-1.5-pro"
    temperature: 0.1
    max_tokens: 4000

embeddings:
  provider: "openai"
  model: "text-embedding-3-large"
  dimensions: 3072

databases:
  mongodb:
    url: "mongodb://localhost:27017"
    database: "langchain_agent"
  redis:
    url: "redis://localhost:6379"
    db: 0
  qdrant:
    host: "localhost"
    port: 6333
    collection_prefix: "langchain_"

integrations:
  brave_search:
    api_key: "${BRAVE_API_KEY}"
    base_url: "https://api.search.brave.com/res/v1/web/search"
  google:
    credentials_path: "credentials/google_creds.json"
    custom_search_engine_id: "${GOOGLE_CSE_ID}"
    api_key: "${GOOGLE_API_KEY}"
  slack:
    token: "${SLACK_BOT_TOKEN}"
    default_channel: "#langchain-agent"
  smtp:
    server: "smtp.gmail.com"
    port: 587
    from_email: "<EMAIL>"
    username: "${SMTP_USERNAME}"
    password: "${SMTP_PASSWORD}"
  openai:
    api_key: "${OPENAI_API_KEY}"
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"

memory:
  type: "conversation_summary_buffer"  # or "conversation_buffer_window"
  max_token_limit: 2000
  return_messages: true
  k: 10  # for window memory

crawl4ai:
  max_concurrent: 5
  delay_between_requests: 1.0
  timeout: 30
  user_agent: "LangChainAgent/1.0"
  bypass_cache: false

# Agent configurations
agents:
  primary:
    agent_type: "OPENAI_FUNCTIONS"
    verbose: true
    handle_parsing_errors: true
    max_iterations: 10
    early_stopping_method: "generate"
  document:
    agent_type: "OPENAI_FUNCTIONS"
    verbose: true
    handle_parsing_errors: true
    max_iterations: 5
  research:
    agent_type: "OPENAI_FUNCTIONS"
    verbose: true
    handle_parsing_errors: true
    max_iterations: 8

# Tool configurations
tools:
  internet_search:
    enabled: true
    default_source: "brave"
    max_results: 10
    cache_ttl: 3600
  web_crawler:
    enabled: true
    max_pages_per_crawl: 10
    respect_robots_txt: true
    extraction_timeout: 30
  document_processor:
    enabled: true
    supported_formats: [".pdf", ".txt", ".md", ".png", ".jpg", ".jpeg"]
    max_file_size_mb: 50
    chunk_size: 1000
    chunk_overlap: 200
  rag_query:
    enabled: true
    default_top_k: 5
    similarity_threshold: 0.7
  email_notification:
    enabled: true
    max_recipients: 10
  calendar_event:
    enabled: true
    default_reminder_minutes: [60, 1440]

# Vector store configurations
vector_stores:
  documents:
    collection_name: "documents"
    vector_size: 3072
    distance_metric: "cosine"
  web_content:
    collection_name: "web_content"
    vector_size: 3072
    distance_metric: "cosine"

# Workflow configurations
workflows:
  tender_analysis:
    timeout_seconds: 300
    max_documents: 10
    enable_notifications: true
    enable_calendar_events: true
  market_research:
    timeout_seconds: 600
    max_search_queries: 5
    max_crawl_urls: 10
  document_intelligence:
    timeout_seconds: 180
    max_documents: 20
    enable_cross_analysis: true
  competitive_analysis:
    timeout_seconds: 450
    max_competitors: 15
    deep_analysis: true

# Security and performance
security:
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_limit: 100
  input_validation:
    enabled: true
    max_input_length: 10000
  output_filtering:
    enabled: true
    filter_sensitive_data: true

performance:
  max_concurrent_workflows: 5
  memory_limit_mb: 2048
  cpu_limit_percent: 80
  cache_size_mb: 512

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/langchain_agent.log"
  max_size_mb: 100
  backup_count: 5
  enable_callback_logging: true

# Monitoring and metrics
monitoring:
  enabled: true
  metrics_collection: true
  performance_tracking: true
  error_tracking: true
  execution_logging: true
