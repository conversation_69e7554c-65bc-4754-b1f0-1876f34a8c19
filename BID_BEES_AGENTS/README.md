# BID BEES AGENTS - Multi-Framework AI Agent Suite

A comprehensive collection of enterprise-grade AI agents built with three different frameworks: **PydanticAI**, **LangChain**, and **OpenAI Assistants API**. Each agent provides the same core capabilities but leverages different architectural approaches and strengths.

## 🚀 Overview

This project implements three powerful AI agents with identical capabilities but different underlying frameworks:

### 🔧 **Framework Comparison**

| Feature | PydanticAI | LangChain | OpenAI Assistants |
|---------|------------|-----------|-------------------|
| **Type Safety** | ✅ Excellent | ⚠️ Good | ⚠️ Good |
| **Async Support** | ✅ Native | ✅ Native | ✅ Native |
| **Function Calling** | ✅ Built-in | ✅ Tools System | ✅ Native |
| **Memory Management** | 🔧 Custom | ✅ Built-in | ✅ Thread-based |
| **Model Flexibility** | ✅ Multi-provider | ✅ Extensive | ❌ OpenAI Only |
| **Ease of Use** | ✅ Simple | ⚠️ Complex | ✅ Simple |
| **Production Ready** | ✅ Yes | ✅ Yes | ✅ Yes |

## 📁 Project Structure

```
BID_BEES_AGENTS/
├── PYDANTIC/                    # PydanticAI Implementation
│   ├── agents/
│   │   └── enhanced_pydantic_agent.py
│   ├── config/
│   │   └── enhanced_agent_config.yaml
│   ├── examples/
│   │   └── usage_example.py
│   ├── tests/
│   │   └── test_enhanced_agent.py
│   ├── requirements.txt
│   └── README.md
├── LANGCHAIN/                   # LangChain Implementation
│   ├── agents/
│   │   └── langchain_multi_agent.py
│   ├── config/
│   │   └── langchain_agent_config.yaml
│   ├── examples/
│   │   └── usage_example.py
│   ├── requirements.txt
│   └── README.md
├── OPENAI/                      # OpenAI Assistants Implementation
│   ├── agents/
│   │   └── openai_enterprise_agent.py
│   ├── config/
│   │   └── openai_agent_config.yaml
│   ├── examples/
│   │   └── usage_example.py
│   ├── requirements.txt
│   └── README.md
└── README.md                    # This file
```

## 🎯 Core Capabilities

All three agents provide identical functionality:

### 📄 **Document Processing**
- **OCR Integration**: PDF, images, text files
- **Schema-Driven Extraction**: JSON schema-based structured data extraction
- **Multi-Format Support**: PDF, DOCX, images, plain text
- **Confidence Scoring**: AI-powered confidence assessment
- **Entity Recognition**: Automatic entity extraction and relationship mapping

### 🔍 **Web Research & Crawling**
- **Multi-Source Search**: Brave Search, Google, Bing, DuckDuckGo
- **Advanced Web Crawling**: Crawl4AI integration with structured data extraction
- **Content Vectorization**: Automatic storage in vector databases
- **Link Analysis**: Internal/external link extraction
- **Media Extraction**: Image and media content identification

### 🧠 **RAG & Knowledge Management**
- **Vector Databases**: Qdrant, Milvus, MongoDB Atlas support
- **Graph Memory**: NetworkX-based relationship mapping
- **Multi-Source Context**: Documents, web content, conversation history
- **Confidence Scoring**: Weighted relevance scoring
- **Long-term Memory**: Redis-based caching and persistence

### 📧 **Communication & Integration**
- **Email Notifications**: SMTP integration with HTML support
- **Slack Integration**: Rich messaging and bot capabilities
- **Google Calendar**: Event creation with reminders
- **Webhook Support**: Custom notification endpoints

### ⚙️ **Workflow Automation**
- **Tender Analysis**: Complete tender document processing
- **Market Research**: Multi-source intelligence gathering
- **Document Intelligence**: Batch processing and cross-analysis
- **Competitive Analysis**: Competitor research and profiling

## 🚀 Quick Start

### Prerequisites

```bash
# Required for all agents
export OPENAI_API_KEY="your_openai_key"
export BRAVE_API_KEY="your_brave_search_key"

# Optional but recommended
export ANTHROPIC_API_KEY="your_anthropic_key"
export GOOGLE_API_KEY="your_google_key"
export SMTP_USERNAME="your_email"
export SMTP_PASSWORD="your_password"
```

### Database Setup

```bash
# Start required services with Docker
docker run -d --name mongodb -p 27017:27017 mongo:latest
docker run -d --name redis -p 6379:6379 redis:latest
docker run -d --name qdrant -p 6333:6333 qdrant/qdrant:latest
```

### Choose Your Framework

#### 🔧 **PydanticAI Agent** (Recommended for Type Safety)

```bash
cd PYDANTIC
pip install -r requirements.txt
python examples/usage_example.py
```

#### 🔗 **LangChain Agent** (Recommended for Ecosystem)

```bash
cd LANGCHAIN
pip install -r requirements.txt
python examples/usage_example.py
```

#### 🤖 **OpenAI Agent** (Recommended for Simplicity)

```bash
cd OPENAI
pip install -r requirements.txt
python examples/usage_example.py
```

## 💡 Usage Examples

### Basic Document Processing

```python
# PydanticAI
agent = EnhancedPydanticAgent()
await agent.initialize()
doc_metadata = await agent.process_document("tender.pdf", "tender_notice")

# LangChain
agent = EnhancedLangChainAgent()
await agent.initialize()
doc_metadata = await agent.process_document("tender.pdf", "tender_notice")

# OpenAI
agent = OpenAIEnterpriseAgent()
await agent.initialize()
result = await agent.run_assistant("document", "Process tender.pdf")
```

### Workflow Execution

```python
# All agents support the same workflow interface
workflow_params = {
    'document_path': 'tender.pdf',
    'search_query': 'construction opportunities',
    'create_reminders': True,
    'notify_email': '<EMAIL>'
}

result = await agent.execute_workflow('tender_analysis', workflow_params)
```

### RAG Queries

```python
# Query knowledge base across all processed documents
context = await agent.query_rag(
    "What are the key requirements for government tenders?",
    top_k=5,
    include_web=True
)
```

## 🔧 Configuration

Each agent uses YAML configuration files for easy customization:

```yaml
# Example configuration (similar across all agents)
databases:
  mongodb:
    url: "mongodb://localhost:27017"
  redis:
    url: "redis://localhost:6379"
  qdrant:
    host: "localhost"
    port: 6333

models:
  primary: "gpt-4-turbo-preview"
  reasoning: "claude-3-sonnet-20240229"
  embedding: "text-embedding-3-large"

workflows:
  tender_analysis:
    timeout_seconds: 300
    enable_notifications: true
```

## 🧪 Testing

Each agent includes comprehensive test suites:

```bash
# Run tests for specific agent
cd PYDANTIC && pytest tests/ -v
cd LANGCHAIN && pytest tests/ -v
cd OPENAI && pytest tests/ -v
```

## 📊 Performance Comparison

| Metric | PydanticAI | LangChain | OpenAI Assistants |
|--------|------------|-----------|-------------------|
| **Startup Time** | ~2s | ~3s | ~1s |
| **Memory Usage** | Low | Medium | Low |
| **Type Safety** | Excellent | Good | Good |
| **Error Handling** | Excellent | Good | Good |
| **Debugging** | Excellent | Good | Limited |

## 🔒 Security Features

- **Rate Limiting**: Configurable request limits
- **Input Validation**: Comprehensive input sanitization
- **Data Encryption**: Sensitive data protection
- **Audit Logging**: Complete activity tracking
- **Access Control**: Role-based permissions

## 🚀 Production Deployment

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Kubernetes Deployment

```bash
# Deploy to Kubernetes
kubectl apply -f k8s/
```

### Environment Variables

```bash
# Production environment setup
export ENVIRONMENT="production"
export LOG_LEVEL="INFO"
export ENABLE_METRICS="true"
export ENABLE_TRACING="true"
```

## 📈 Monitoring & Observability

- **Metrics Collection**: Prometheus integration
- **Distributed Tracing**: OpenTelemetry support
- **Health Checks**: Kubernetes-ready health endpoints
- **Cost Tracking**: Token usage and cost monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check individual agent README files
- **Issues**: Create GitHub issues for bugs
- **Discussions**: Use GitHub Discussions for questions
- **Examples**: Comprehensive examples in each agent's directory

## 🔄 Roadmap

- [ ] **Multi-Agent Orchestration**: Coordinate between different agents
- [ ] **Advanced Workflows**: More complex business process automation
- [ ] **Real-time Processing**: Stream processing capabilities
- [ ] **Mobile Integration**: Mobile app connectivity
- [ ] **Advanced Analytics**: Business intelligence dashboards

---

**Choose the framework that best fits your needs:**
- **PydanticAI**: For type safety and modern Python development
- **LangChain**: For extensive ecosystem and community support
- **OpenAI Assistants**: For simplicity and direct OpenAI integration
