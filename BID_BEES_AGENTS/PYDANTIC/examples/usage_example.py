#!/usr/bin/env python3
"""
Enhanced Pydantic Agent Usage Example

This example demonstrates the comprehensive capabilities of the Enhanced Pydantic Agent
including document processing, web crawling, RAG queries, and workflow execution.
"""

import asyncio
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add the parent directory to the path to import the agent
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents.enhanced_pydantic_agent import EnhancedPydanticAgent

async def main():
    """Main example function demonstrating agent capabilities"""
    
    # Initialize the agent
    print("🚀 Initializing Enhanced Pydantic Agent...")
    agent = EnhancedPydanticAgent()
    await agent.initialize()
    
    try:
        # ============================================================================
        # 1. DOCUMENT PROCESSING EXAMPLE
        # ============================================================================
        print("\n📄 Document Processing Example")
        print("-" * 50)
        
        # Process a tender document (replace with actual file path)
        document_path = "sample_documents/tender_notice.pdf"
        if Path(document_path).exists():
            doc_metadata = await agent.process_document(document_path, "tender_notice")
            print(f"✅ Processed document: {doc_metadata.title}")
            print(f"   Document Type: {doc_metadata.document_type}")
            print(f"   Confidence Score: {doc_metadata.confidence_score:.2f}")
            print(f"   Entities Found: {len(doc_metadata.extracted_entities)}")
        else:
            print("⚠️  Sample document not found, skipping document processing")
        
        # ============================================================================
        # 2. WEB SEARCH EXAMPLE
        # ============================================================================
        print("\n🔍 Web Search Example")
        print("-" * 50)
        
        search_query = "government tender opportunities construction 2024"
        search_result = await agent.search_internet(search_query, source="brave", count=5)
        
        print(f"✅ Search completed for: '{search_query}'")
        print(f"   Results found: {len(search_result.results)}")
        print(f"   Search time: {search_result.search_time_ms:.2f}ms")
        print(f"   Cached: {search_result.cached}")
        
        # Display first few results
        for i, result in enumerate(search_result.results[:3]):
            print(f"   {i+1}. {result.get('title', 'No title')}")
            print(f"      URL: {result.get('url', 'No URL')}")
        
        # ============================================================================
        # 3. WEB CRAWLING EXAMPLE
        # ============================================================================
        print("\n🕷️  Web Crawling Example")
        print("-" * 50)
        
        # Crawl a government tender website
        target_url = "https://www.tenders.gov.za"  # Example URL
        extraction_strategy = """
        Extract the following information from this tender website:
        - Available tender opportunities
        - Closing dates
        - Tender categories
        - Contact information
        Return the data in structured JSON format.
        """
        
        crawl_result = await agent.crawl_website(target_url, extraction_strategy)
        
        if crawl_result.success:
            print(f"✅ Successfully crawled: {target_url}")
            print(f"   Title: {crawl_result.title}")
            print(f"   Content length: {len(crawl_result.content)} characters")
            print(f"   Links found: {len(crawl_result.links)}")
            print(f"   Images found: {len(crawl_result.images)}")
            
            if crawl_result.structured_data:
                print(f"   Structured data extracted: {len(crawl_result.structured_data)} fields")
        else:
            print(f"❌ Failed to crawl {target_url}: {crawl_result.error_message}")
        
        # ============================================================================
        # 4. RAG QUERY EXAMPLE
        # ============================================================================
        print("\n🧠 RAG Query Example")
        print("-" * 50)
        
        rag_query = "What are the key requirements for construction tenders?"
        rag_context = await agent.query_rag(rag_query, top_k=5, include_web=True)
        
        print(f"✅ RAG query completed: '{rag_query}'")
        print(f"   Relevant documents: {len(rag_context.relevant_documents)}")
        print(f"   Vector matches: {len(rag_context.vector_matches)}")
        print(f"   Graph context: {len(rag_context.graph_context)}")
        print(f"   Confidence score: {rag_context.confidence_score:.2f}")
        print(f"   Context summary: {rag_context.context_summary}")
        
        # ============================================================================
        # 5. COMMUNICATION EXAMPLES
        # ============================================================================
        print("\n📧 Communication Examples")
        print("-" * 50)
        
        # Send email notification (if configured)
        try:
            email_result = await agent.send_email(
                recipient="<EMAIL>",
                subject="Agent Test Notification",
                message="<h1>Test Email</h1><p>This is a test email from the Enhanced Pydantic Agent.</p>",
                html=True
            )
            print(f"✅ Email sent successfully: {email_result['status']}")
        except Exception as e:
            print(f"⚠️  Email sending failed (likely not configured): {e}")
        
        # Create calendar event
        try:
            calendar_result = await agent.create_calendar_event(
                title="Agent Demo Meeting",
                start_time=datetime.now() + timedelta(days=1),
                end_time=datetime.now() + timedelta(days=1, hours=1),
                description="Demo meeting created by Enhanced Pydantic Agent",
                attendees=["<EMAIL>"]
            )
            print(f"✅ Calendar event created: {calendar_result['status']}")
        except Exception as e:
            print(f"⚠️  Calendar event creation failed (likely not configured): {e}")
        
        # ============================================================================
        # 6. WORKFLOW EXECUTION EXAMPLE
        # ============================================================================
        print("\n⚙️  Workflow Execution Example")
        print("-" * 50)
        
        # Execute a comprehensive tender analysis workflow
        workflow_params = {
            'search_query': 'construction tender opportunities South Africa',
            'competitor_urls': [
                'https://www.example-construction.com',
                'https://www.sample-contractor.com'
            ],
            'analysis_query': 'What are the current market trends in construction tenders?',
            'create_reminders': True,
            'notify_email': '<EMAIL>'
        }
        
        print("🔄 Starting tender analysis workflow...")
        workflow_result = await agent.execute_workflow('tender_analysis', workflow_params)
        
        print(f"✅ Workflow completed: {workflow_result.status}")
        print(f"   Task ID: {workflow_result.task_id}")
        print(f"   Execution time: {workflow_result.execution_time_ms:.2f}ms")
        print(f"   Confidence score: {workflow_result.confidence_score:.2f}")
        print(f"   Result components: {len(workflow_result.result)}")
        
        # Display workflow results
        for component, data in workflow_result.result.items():
            if isinstance(data, dict):
                print(f"   - {component}: {len(data)} items")
            else:
                print(f"   - {component}: {type(data).__name__}")
        
        # ============================================================================
        # 7. TASK STATUS MONITORING
        # ============================================================================
        print("\n📊 Task Status Monitoring")
        print("-" * 50)
        
        # Check task status
        task_status = await agent.get_task_status(workflow_result.task_id)
        if task_status:
            print(f"✅ Task status retrieved:")
            print(f"   Status: {task_status.status}")
            print(f"   Created: {task_status.created_at}")
            print(f"   Updated: {task_status.updated_at}")
        
        # ============================================================================
        # 8. MEMORY AND KNOWLEDGE GRAPH EXAMPLE
        # ============================================================================
        print("\n🧠 Memory and Knowledge Graph")
        print("-" * 50)
        
        # The knowledge graph is automatically updated during document processing
        # and web crawling. Let's check its current state.
        graph = agent.dependencies.knowledge_graph
        print(f"✅ Knowledge graph status:")
        print(f"   Nodes: {graph.number_of_nodes()}")
        print(f"   Edges: {graph.number_of_edges()}")
        
        if graph.number_of_nodes() > 0:
            # Show some sample nodes
            sample_nodes = list(graph.nodes(data=True))[:5]
            print("   Sample nodes:")
            for node_id, node_data in sample_nodes:
                node_type = node_data.get('type', 'unknown')
                title = node_data.get('title', node_data.get('value', 'No title'))
                print(f"     - {node_type}: {title[:50]}...")
        
        print("\n🎉 Enhanced Pydantic Agent Demo Completed Successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error during demo execution: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup resources
        print("\n🧹 Cleaning up resources...")
        await agent.cleanup()
        print("✅ Cleanup completed")

def setup_environment():
    """Setup environment variables and directories"""
    print("🔧 Setting up environment...")
    
    # Create necessary directories
    directories = [
        "logs",
        "sample_documents",
        "credentials",
        "configs/schemas"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # Check for required environment variables
    required_env_vars = [
        'BRAVE_API_KEY',
        'OPENAI_API_KEY',
        'SMTP_USERNAME',
        'SMTP_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("⚠️  Warning: The following environment variables are not set:")
        for var in missing_vars:
            print(f"   - {var}")
        print("   Some features may not work without proper configuration.")
    else:
        print("✅ All required environment variables are set")

if __name__ == "__main__":
    print("🚀 Enhanced Pydantic Agent Demo")
    print("=" * 60)
    
    # Setup environment
    setup_environment()
    
    # Run the main demo
    asyncio.run(main())
