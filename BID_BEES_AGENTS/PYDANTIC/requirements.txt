# Enhanced Pydantic Agent Requirements

# Core Pydantic and AI
pydantic>=2.5.0
pydantic-ai>=0.0.13
openai>=1.12.0
anthropic>=0.18.0
google-generativeai>=0.3.0

# Database and Vector Stores
motor>=3.3.0
redis>=5.0.0
qdrant-client>=1.7.0
pymilvus>=2.3.0
pymongo>=4.6.0

# Web and HTTP
httpx>=0.26.0
requests>=2.31.0
aiohttp>=3.9.0

# Document Processing and OCR
PyMuPDF>=1.23.0
pytesseract>=0.3.10
Pillow>=10.2.0
python-docx>=1.1.0
pdfplumber>=0.10.0

# Web Crawling
crawl4ai>=0.2.0
beautifulsoup4>=4.12.0
selenium>=4.16.0
playwright>=1.40.0

# Machine Learning and Embeddings
sentence-transformers>=2.2.0
transformers>=4.36.0
torch>=2.1.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Graph and Network Analysis
networkx>=3.2.0
igraph>=0.11.0

# Communication and Integrations
google-auth>=2.25.0
google-auth-oauthlib>=1.2.0
google-auth-httplib2>=0.2.0
google-api-python-client>=2.110.0
slack-sdk>=3.26.0
twilio>=8.11.0

# Configuration and Utilities
pyyaml>=6.0.1
python-dotenv>=1.0.0
click>=8.1.0
rich>=13.7.0
typer>=0.9.0

# Async and Concurrency
asyncio-throttle>=1.0.2
aiofiles>=23.2.0
uvloop>=0.19.0

# Logging and Monitoring
structlog>=23.2.0
loguru>=0.7.0
prometheus-client>=0.19.0

# Security and Encryption
cryptography>=41.0.0
bcrypt>=4.1.0
passlib>=1.7.4

# Data Processing
pandas>=2.1.0
polars>=0.20.0
pyarrow>=14.0.0

# Testing and Development
pytest>=7.4.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0
black>=23.12.0
isort>=5.13.0
flake8>=7.0.0
mypy>=1.8.0

# Optional: GPU acceleration (uncomment if needed)
# torch-audio>=2.1.0
# torchaudio>=2.1.0
# torchvision>=0.16.0

# Optional: Advanced NLP (uncomment if needed)
# spacy>=3.7.0
# nltk>=3.8.0

# Optional: Computer Vision (uncomment if needed)
# opencv-python>=4.8.0
# easyocr>=1.7.0

# Optional: Database drivers (uncomment as needed)
# psycopg2-binary>=2.9.0  # PostgreSQL
# aiomysql>=0.2.0         # MySQL
# aiosqlite>=0.19.0       # SQLite

# Development and Deployment
gunicorn>=21.2.0
uvicorn>=0.25.0
fastapi>=0.108.0
celery>=5.3.0
flower>=2.0.0

# Environment and Configuration
python-multipart>=0.0.6
jinja2>=3.1.0
markupsafe>=2.1.0
