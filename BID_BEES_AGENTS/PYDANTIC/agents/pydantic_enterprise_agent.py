# agents/pydantic_enterprise_agent.py

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.models import OpenAIModel, AnthropicModel
import motor.motor_asyncio
import redis.asyncio as redis
import qdrant_client
from qdrant_client.models import Distance, VectorParams, PointStruct
import httpx
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
import networkx as nx
import json
import logging
from pathlib import Path

# Pydantic Models for Structured Data
class DocumentMetadata(BaseModel):
    document_id: str
    title: str
    document_type: str
    source: str
    processed_at: datetime
    confidence_score: float
    extracted_entities: List[Dict[str, Any]]

class SearchResult(BaseModel):
    query: str
    results: List[Dict[str, Any]]
    timestamp: datetime
    source: str

class TaskResult(BaseModel):
    task_id: str
    status: str
    result: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

class TenderData(BaseModel):
    tender_number: str
    title: str
    closing_date: datetime
    issuer: str
    briefing_sessions: List[Dict[str, Any]]
    requirements: List[str]
    estimated_value: Optional[float] = None

# Agent Dependencies
class AgentDependencies(BaseModel):
    mongodb_client: motor.motor_asyncio.AsyncIOMotorClient
    redis_client: redis.Redis
    qdrant_client: qdrant_client.QdrantClient
    http_client: httpx.AsyncClient
    knowledge_graph: nx.DiGraph
    config: Dict[str, Any]

# PydanticAI Enterprise Agent
class PydanticEnterpriseAgent:
    def __init__(self, config_path: str = "config/agent_config.yaml"):
        self.config = self._load_config(config_path)
        self.dependencies = self._initialize_dependencies()
        
        # Initialize PydanticAI agent with multiple models
        self.primary_agent = Agent(
            model=OpenAIModel('gpt-4-turbo-preview'),
            deps_type=AgentDependencies,
            result_type=TaskResult,
            system_prompt="""You are a sophisticated enterprise automation agent specializing in 
                          document processing, tender management, and business intelligence. 
                          You can search the internet, process documents, manage databases, 
                          send notifications, and create calendar events. Always provide 
                          structured, actionable outputs."""
        )
        
        # Secondary agent for complex reasoning
        self.reasoning_agent = Agent(
            model=AnthropicModel('claude-3-sonnet-20240229'),
            deps_type=AgentDependencies,
            result_type=Dict[str, Any],
            system_prompt="""You are a reasoning specialist focused on complex analysis,
                          risk assessment, and strategic insights. Provide detailed
                          analytical outputs with confidence scores."""
        )
        
        self.logger = logging.getLogger(__name__)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        import yaml
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)

    def _initialize_dependencies(self) -> AgentDependencies:
        # MongoDB Connection
        mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(
            self.config['databases']['mongodb']['url']
        )
        
        # Redis Connection
        redis_client = redis.from_url(
            self.config['databases']['redis']['url']
        )
        
        # Qdrant Vector Database
        qdrant_client = qdrant_client.QdrantClient(
            host=self.config['databases']['qdrant']['host'],
            port=self.config['databases']['qdrant']['port']
        )
        
        # HTTP Client for web searches
        http_client = httpx.AsyncClient(
            headers={"X-Subscription-Token": self.config['brave_search']['api_key']}
        )
        
        # Knowledge Graph
        knowledge_graph = nx.DiGraph()
        
        return AgentDependencies(
            mongodb_client=mongodb_client,
            redis_client=redis_client,
            qdrant_client=qdrant_client,
            http_client=http_client,
            knowledge_graph=knowledge_graph,
            config=self.config
        )

    @primary_agent.tool
    async def search_internet(self, ctx: RunContext[AgentDependencies], query: str, count: int = 10) -> SearchResult:
        """Search the internet using Brave Search API"""
        try:
            response = await ctx.deps.http_client.get(
                "https://api.search.brave.com/res/v1/web/search",
                params={"q": query, "count": count}
            )
            results = response.json()
            
            search_result = SearchResult(
                query=query,
                results=results.get('web', {}).get('results', []),
                timestamp=datetime.now(),
                source="brave_search"
            )
            
            # Cache results in Redis
            await ctx.deps.redis_client.setex(
                f"search:{hash(query)}", 
                3600, 
                search_result.model_dump_json()
            )
            
            return search_result
            
        except Exception as e:
            self.logger.error(f"Search failed: {e}")
            raise

    @primary_agent.tool
    async def process_document(self, ctx: RunContext[AgentDependencies], 
                             document_path: str, document_type: str) -> DocumentMetadata:
        """Process documents using OCR and LLM extraction"""
        try:
            # Load document (placeholder for actual OCR)
            document_content = self._load_document(document_path)
            
            # Extract structured data based on document type
            if document_type == "tender_notice":
                extraction_prompt = """
                Extract tender information from this document and return structured JSON:
                - tender_number
                - title
                - closing_date
                - issuer details
                - briefing sessions
                - submission requirements
                """
            else:
                extraction_prompt = f"Extract key information from this {document_type} document."
            
            # Use secondary agent for complex extraction
            extraction_result = await self.reasoning_agent.run(
                f"{extraction_prompt}\n\nDocument content:\n{document_content}",
                deps=ctx.deps
            )
            
            # Store in MongoDB
            doc_metadata = DocumentMetadata(
                document_id=str(hash(document_path)),
                title=extraction_result.data.get('title', 'Unknown'),
                document_type=document_type,
                source=document_path,
                processed_at=datetime.now(),
                confidence_score=extraction_result.data.get('confidence', 0.8),
                extracted_entities=extraction_result.data.get('entities', [])
            )
            
            await ctx.deps.mongodb_client.documents.documents.insert_one(
                doc_metadata.model_dump()
            )
            
            # Create vector embedding and store in Qdrant
            await self._store_document_embedding(ctx.deps, doc_metadata, document_content)
            
            # Update knowledge graph
            self._update_knowledge_graph(ctx.deps.knowledge_graph, doc_metadata)
            
            return doc_metadata
            
        except Exception as e:
            self.logger.error(f"Document processing failed: {e}")
            raise

    @primary_agent.tool
    async def create_calendar_event(self, ctx: RunContext[AgentDependencies], 
                                  title: str, start_time: datetime, 
                                  end_time: datetime, description: str) -> Dict[str, Any]:
        """Create calendar events using Google Calendar API"""
        try:
            # Load Google Calendar credentials
            creds = Credentials.from_authorized_user_file(
                self.config['integrations']['google']['credentials_path']
            )
            
            service = build('calendar', 'v3', credentials=creds)
            
            event = {
                'summary': title,
                'description': description,
                'start': {
                    'dateTime': start_time.isoformat(),
                    'timeZone': 'UTC',
                },
                'end': {
                    'dateTime': end_time.isoformat(),
                    'timeZone': 'UTC',
                },
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'email', 'minutes': 24 * 60},
                        {'method': 'popup', 'minutes': 60},
                    ],
                },
            }
            
            created_event = service.events().insert(
                calendarId='primary', body=event
            ).execute()
            
            # Store event reference in MongoDB
            await ctx.deps.mongodb_client.calendar.events.insert_one({
                'google_event_id': created_event['id'],
                'title': title,
                'start_time': start_time,
                'end_time': end_time,
                'created_at': datetime.now()
            })
            
            return {'status': 'success', 'event_id': created_event['id']}
            
        except Exception as e:
            self.logger.error(f"Calendar event creation failed: {e}")
            raise

    @primary_agent.tool
    async def send_notification(self, ctx: RunContext[AgentDependencies], 
                              recipient: str, subject: str, message: str, 
                              notification_type: str = "email") -> Dict[str, Any]:
        """Send email notifications and other alerts"""
        try:
            if notification_type == "email":
                return await self._send_email(recipient, subject, message)
            elif notification_type == "slack":
                return await self._send_slack_message(ctx.deps, recipient, message)
            else:
                raise ValueError(f"Unsupported notification type: {notification_type}")
                
        except Exception as e:
            self.logger.error(f"Notification sending failed: {e}")
            raise

    async def _send_email(self, recipient: str, subject: str, message: str) -> Dict[str, Any]:
        """Send email using SMTP"""
        smtp_config = self.config['integrations']['smtp']
        
        msg = MIMEMultipart()
        msg['From'] = smtp_config['from_email']
        msg['To'] = recipient
        msg['Subject'] = subject
        msg.attach(MIMEText(message, 'html'))
        
        with smtplib.SMTP(smtp_config['server'], smtp_config['port']) as server:
            server.starttls()
            server.login(smtp_config['username'], smtp_config['password'])
            server.send_message(msg)
        
        return {'status': 'sent', 'recipient': recipient}

    async def _store_document_embedding(self, deps: AgentDependencies, 
                                      doc_metadata: DocumentMetadata, content: str):
        """Store document embeddings in Qdrant for RAG"""
        # Generate embedding (placeholder - use actual embedding model)
        embedding = await self._generate_embedding(content)
        
        point = PointStruct(
            id=hash(doc_metadata.document_id),
            vector=embedding,
            payload={
                'document_id': doc_metadata.document_id,
                'title': doc_metadata.title,
                'type': doc_metadata.document_type,
                'processed_at': doc_metadata.processed_at.isoformat()
            }
        )
        
        deps.qdrant_client.upsert(
            collection_name="documents",
            points=[point]
        )

    def _update_knowledge_graph(self, graph: nx.DiGraph, doc_metadata: DocumentMetadata):
        """Update knowledge graph with document relationships"""
        graph.add_node(
            doc_metadata.document_id,
            type='document',
            title=doc_metadata.title,
            doc_type=doc_metadata.document_type
        )
        
        # Add entity relationships
        for entity in doc_metadata.extracted_entities:
            entity_id = f"entity_{hash(str(entity))}"
            graph.add_node(entity_id, type='entity', **entity)
            graph.add_edge(doc_metadata.document_id, entity_id, relationship='contains')

    async def _generate_embedding(self, text: str) -> List[float]:
        """Generate text embeddings"""
        # Placeholder - implement with actual embedding model
        return [0.1] * 1536  # OpenAI embedding dimension

    def _load_document(self, document_path: str) -> str:
        """Load and preprocess document content"""
        # Placeholder for actual document loading with OCR
        return f"Document content from {document_path}"

    async def run_workflow(self, task_type: str, parameters: Dict[str, Any]) -> TaskResult:
        """Execute complete workflows"""
        try:
            result = await self.primary_agent.run(
                f"Execute {task_type} workflow with parameters: {parameters}",
                deps=self.dependencies
            )
            return result
            
        except Exception as e:
            self.logger.error(f"Workflow execution failed: {e}")
            raise

# Configuration Example
agent_config_yaml = """
databases:
  mongodb:
    url: "mongodb://localhost:27017"
    database: "enterprise_agent"
  redis:
    url: "redis://localhost:6379"
  qdrant:
    host: "localhost"
    port: 6333

integrations:
  brave_search:
    api_key: "${BRAVE_API_KEY}"
  google:
    credentials_path: "credentials/google_creds.json"
  smtp:
    server: "smtp.gmail.com"
    port: 587
    from_email: "<EMAIL>"
    username: "${SMTP_USERNAME}"
    password: "${SMTP_PASSWORD}"

models:
  primary: "gpt-4-turbo-preview"
  reasoning: "claude-3-sonnet-20240229"
  embedding: "text-embedding-3-large"
"""

# Usage Example
async def main():
    agent = PydanticEnterpriseAgent()
    
    # Process a tender document
    doc_result = await agent.process_document(
        agent.dependencies,
        "documents/tender_notice.pdf",
        "tender_notice"
    )
    
    # Search for related information
    search_result = await agent.search_internet(
        agent.dependencies,
        f"tender opportunities {doc_result.title}",
        count=5
    )
    
    # Create calendar reminder
    await agent.create_calendar_event(
        agent.dependencies,
        f"Tender Deadline: {doc_result.title}",
        datetime.now() + timedelta(days=7),
        datetime.now() + timedelta(days=7, hours=1),
        f"Submit tender for {doc_result.title}"
    )
    
    print(f"Processed document: {doc_result.title}")

if __name__ == "__main__":
    asyncio.run(main())
