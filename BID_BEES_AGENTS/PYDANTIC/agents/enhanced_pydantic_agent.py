# agents/enhanced_pydantic_agent.py

import asyncio
import json
import logging
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import yaml
import numpy as np

# Core Pydantic and PydanticAI imports
from pydantic import BaseModel, Field, ConfigDict
from pydantic_ai import Agent, RunContext
from pydantic_ai.models import OpenAIModel, AnthropicModel, GeminiModel

# Database and Vector Store imports
import motor.motor_asyncio
import redis.asyncio as redis
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
import networkx as nx

# Web and Communication imports
import httpx
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from slack_sdk.web.async_client import AsyncWebClient

# Crawl4AI and Document Processing
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy
import fitz  # PyMuPDF for PDF processing
import pytesseract
from PIL import Image

# Embedding and LLM utilities
from sentence_transformers import SentenceTransformer
import openai

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# PYDANTIC MODELS FOR STRUCTURED DATA
# ============================================================================

class DocumentMetadata(BaseModel):
    """Enhanced document metadata with comprehensive tracking"""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    document_id: str
    title: str
    document_type: str  # tender_notice, boq, contract, etc.
    source: str
    file_path: Optional[str] = None
    processed_at: datetime
    confidence_score: float = Field(ge=0.0, le=1.0)
    extracted_entities: List[Dict[str, Any]] = []
    vector_id: Optional[str] = None
    graph_node_id: Optional[str] = None
    tags: List[str] = []
    language: str = "en"
    page_count: Optional[int] = None
    file_size: Optional[int] = None

class TenderData(BaseModel):
    """Structured tender information matching your OCR pipeline schema"""
    tender_number: str
    tender_title: str
    closing_date: datetime
    issuer_details: Dict[str, Any]
    briefing_sessions: List[Dict[str, Any]] = []
    submission_method: str
    estimated_value: Optional[float] = None
    requirements: List[str] = []
    boq_items: List[Dict[str, Any]] = []
    suppliers: List[Dict[str, Any]] = []
    analytics: Dict[str, Any] = {}
    confidence_scores: Dict[str, float] = {}

class SearchResult(BaseModel):
    """Enhanced search results with multiple sources"""
    query: str
    results: List[Dict[str, Any]]
    timestamp: datetime
    source: str  # brave_search, google, bing, duckduckgo
    total_results: int = 0
    search_time_ms: float = 0.0
    cached: bool = False

class CrawlResult(BaseModel):
    """Web crawling results with structured data"""
    url: str
    title: str
    content: str
    structured_data: Dict[str, Any] = {}
    links: List[str] = []
    images: List[str] = []
    crawled_at: datetime
    success: bool = True
    error_message: Optional[str] = None
    vector_id: Optional[str] = None

class TaskResult(BaseModel):
    """Task execution results with comprehensive tracking"""
    task_id: str
    task_type: str
    status: str  # pending, running, completed, failed
    result: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    execution_time_ms: float = 0.0
    error_message: Optional[str] = None
    confidence_score: float = 1.0

class MemoryNode(BaseModel):
    """Graph memory node structure"""
    node_id: str
    node_type: str  # document, entity, concept, relationship
    content: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    importance_score: float = 0.5
    access_count: int = 0
    last_accessed: datetime

class RAGContext(BaseModel):
    """RAG context with multiple sources"""
    query: str
    relevant_documents: List[Dict[str, Any]]
    graph_context: List[Dict[str, Any]]
    vector_matches: List[Dict[str, Any]]
    confidence_score: float
    context_summary: str

# ============================================================================
# AGENT DEPENDENCIES
# ============================================================================

class AgentDependencies(BaseModel):
    """Enhanced dependencies with multiple database support"""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    # Database clients
    mongodb_client: motor.motor_asyncio.AsyncIOMotorClient
    redis_client: redis.Redis
    qdrant_client: QdrantClient
    milvus_collection: Optional[Any] = None
    
    # Communication clients
    http_client: httpx.AsyncClient
    slack_client: Optional[AsyncWebClient] = None
    
    # AI and Processing
    embedding_model: SentenceTransformer
    knowledge_graph: nx.DiGraph
    crawl4ai_crawler: AsyncWebCrawler
    
    # Configuration
    config: Dict[str, Any]
    
    # Document processing pipeline (integration with your OCR system)
    doc_intelligence_runner: Optional[Any] = None

# ============================================================================
# ENHANCED PYDANTIC ENTERPRISE AGENT
# ============================================================================

class EnhancedPydanticAgent:
    """
    Comprehensive enterprise agent with document processing, web crawling,
    multi-database support, graph memory, and communication capabilities.
    """
    
    def __init__(self, config_path: str = "config/enhanced_agent_config.yaml"):
        self.config = self._load_config(config_path)
        self.dependencies = None
        self.agents = {}
        self._initialize_agents()
        
    async def initialize(self):
        """Async initialization of dependencies"""
        self.dependencies = await self._initialize_dependencies()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Default configuration"""
        return {
            'databases': {
                'mongodb': {'url': 'mongodb://localhost:27017', 'database': 'enterprise_agent'},
                'redis': {'url': 'redis://localhost:6379'},
                'qdrant': {'host': 'localhost', 'port': 6333},
                'milvus': {'host': 'localhost', 'port': 19530}
            },
            'models': {
                'primary': 'gpt-4-turbo-preview',
                'reasoning': 'claude-3-sonnet-20240229',
                'embedding': 'all-MiniLM-L6-v2',
                'document_extraction': 'gemini-1.5-pro'
            },
            'integrations': {
                'brave_search': {'api_key': ''},
                'google': {'credentials_path': 'credentials/google_creds.json'},
                'slack': {'token': ''},
                'smtp': {
                    'server': 'smtp.gmail.com',
                    'port': 587,
                    'from_email': '<EMAIL>',
                    'username': '',
                    'password': ''
                }
            },
            'crawl4ai': {
                'max_concurrent': 5,
                'delay_between_requests': 1.0,
                'timeout': 30
            }
        }
    
    def _initialize_agents(self):
        """Initialize PydanticAI agents with different specializations"""
        
        # Primary agent for general tasks
        self.agents['primary'] = Agent(
            model=OpenAIModel(self.config['models']['primary']),
            deps_type=AgentDependencies,
            result_type=TaskResult,
            system_prompt="""You are an advanced enterprise automation agent specializing in 
                          document processing, tender management, web research, and business intelligence. 
                          You can search the internet, process documents, manage databases, 
                          send notifications, create calendar events, and crawl websites for data.
                          Always provide structured, actionable outputs with confidence scores."""
        )
        
        # Document processing specialist
        self.agents['document'] = Agent(
            model=GeminiModel('gemini-1.5-pro'),
            deps_type=AgentDependencies,
            result_type=TenderData,
            system_prompt="""You are a document processing specialist focused on extracting 
                          structured information from tender notices, BOQs, contracts, and other 
                          business documents. Use the provided schemas to ensure consistent output."""
        )
        
        # Research and analysis specialist
        self.agents['research'] = Agent(
            model=AnthropicModel('claude-3-sonnet-20240229'),
            deps_type=AgentDependencies,
            result_type=Dict[str, Any],
            system_prompt="""You are a research and analysis specialist. Perform deep analysis,
                          risk assessment, market research, and strategic insights. Provide detailed
                          analytical outputs with confidence scores and supporting evidence."""
        )
        
        # Web crawling and data extraction specialist
        self.agents['crawler'] = Agent(
            model=OpenAIModel('gpt-4-turbo-preview'),
            deps_type=AgentDependencies,
            result_type=CrawlResult,
            system_prompt="""You are a web crawling and data extraction specialist. Extract
                          structured information from web pages, identify relevant links,
                          and organize data for further processing."""
        )

    async def _initialize_dependencies(self) -> AgentDependencies:
        """Initialize all dependencies asynchronously"""
        try:
            # MongoDB Connection
            mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(
                self.config['databases']['mongodb']['url']
            )

            # Redis Connection
            redis_client = redis.from_url(
                self.config['databases']['redis']['url']
            )

            # Qdrant Vector Database
            qdrant_client = QdrantClient(
                host=self.config['databases']['qdrant']['host'],
                port=self.config['databases']['qdrant']['port']
            )

            # Initialize Qdrant collections
            await self._initialize_qdrant_collections(qdrant_client)

            # Milvus Connection (optional)
            milvus_collection = None
            try:
                connections.connect(
                    alias="default",
                    host=self.config['databases']['milvus']['host'],
                    port=self.config['databases']['milvus']['port']
                )
                milvus_collection = await self._initialize_milvus_collection()
            except Exception as e:
                logger.warning(f"Milvus connection failed: {e}")

            # HTTP Client for web searches
            http_client = httpx.AsyncClient(
                headers={
                    "X-Subscription-Token": self.config['integrations']['brave_search']['api_key'],
                    "User-Agent": "EnhancedPydanticAgent/1.0"
                },
                timeout=30.0
            )

            # Slack Client (optional)
            slack_client = None
            if self.config['integrations']['slack']['token']:
                slack_client = AsyncWebClient(
                    token=self.config['integrations']['slack']['token']
                )

            # Embedding Model
            embedding_model = SentenceTransformer(
                self.config['models']['embedding']
            )

            # Knowledge Graph
            knowledge_graph = nx.DiGraph()

            # Crawl4AI Crawler
            crawl4ai_crawler = AsyncWebCrawler(
                verbose=True,
                max_concurrent=self.config['crawl4ai']['max_concurrent']
            )

            # Document Intelligence Runner (integration with your OCR system)
            doc_intelligence_runner = None
            try:
                # Import your document intelligence runner
                from ..ocr.doc_intelligence_runner import DocumentIntelligenceRunner
                doc_intelligence_runner = DocumentIntelligenceRunner()
            except ImportError:
                logger.warning("Document intelligence runner not available")

            return AgentDependencies(
                mongodb_client=mongodb_client,
                redis_client=redis_client,
                qdrant_client=qdrant_client,
                milvus_collection=milvus_collection,
                http_client=http_client,
                slack_client=slack_client,
                embedding_model=embedding_model,
                knowledge_graph=knowledge_graph,
                crawl4ai_crawler=crawl4ai_crawler,
                config=self.config,
                doc_intelligence_runner=doc_intelligence_runner
            )

        except Exception as e:
            logger.error(f"Failed to initialize dependencies: {e}")
            raise

    async def _initialize_qdrant_collections(self, client: QdrantClient):
        """Initialize Qdrant collections for different data types"""
        collections = [
            ("documents", 384),  # Document embeddings
            ("web_content", 384),  # Web crawl results
            ("entities", 384),  # Extracted entities
            ("conversations", 384)  # Conversation history
        ]

        for collection_name, vector_size in collections:
            try:
                client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(
                        size=vector_size,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created Qdrant collection: {collection_name}")
            except Exception as e:
                if "already exists" not in str(e):
                    logger.error(f"Failed to create collection {collection_name}: {e}")

    async def _initialize_milvus_collection(self):
        """Initialize Milvus collection for vector storage"""
        try:
            collection_name = "enterprise_vectors"

            if utility.has_collection(collection_name):
                return Collection(collection_name)

            # Define schema
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=384),
                FieldSchema(name="document_id", dtype=DataType.VARCHAR, max_length=100),
                FieldSchema(name="content_type", dtype=DataType.VARCHAR, max_length=50),
                FieldSchema(name="metadata", dtype=DataType.JSON)
            ]

            schema = CollectionSchema(fields, "Enterprise vector collection")
            collection = Collection(collection_name, schema)

            # Create index
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }
            collection.create_index("vector", index_params)

            logger.info(f"Created Milvus collection: {collection_name}")
            return collection

        except Exception as e:
            logger.error(f"Failed to initialize Milvus collection: {e}")
            return None

    # ============================================================================
    # CORE AGENT TOOLS AND METHODS
    # ============================================================================

    async def search_internet(self, query: str, source: str = "brave", count: int = 10) -> SearchResult:
        """Multi-source internet search with caching"""
        start_time = datetime.now()

        # Check cache first
        cache_key = f"search:{hashlib.md5(f'{query}:{source}'.encode()).hexdigest()}"
        cached_result = await self.dependencies.redis_client.get(cache_key)

        if cached_result:
            result = SearchResult.model_validate_json(cached_result)
            result.cached = True
            return result

        try:
            if source == "brave":
                results = await self._search_brave(query, count)
            elif source == "google":
                results = await self._search_google(query, count)
            elif source == "bing":
                results = await self._search_bing(query, count)
            elif source == "duckduckgo":
                results = await self._search_duckduckgo(query, count)
            else:
                raise ValueError(f"Unsupported search source: {source}")

            search_result = SearchResult(
                query=query,
                results=results,
                timestamp=datetime.now(),
                source=source,
                total_results=len(results),
                search_time_ms=(datetime.now() - start_time).total_seconds() * 1000,
                cached=False
            )

            # Cache results for 1 hour
            await self.dependencies.redis_client.setex(
                cache_key, 3600, search_result.model_dump_json()
            )

            return search_result

        except Exception as e:
            logger.error(f"Search failed for {source}: {e}")
            raise

    async def _search_brave(self, query: str, count: int) -> List[Dict[str, Any]]:
        """Search using Brave Search API"""
        response = await self.dependencies.http_client.get(
            "https://api.search.brave.com/res/v1/web/search",
            params={"q": query, "count": count}
        )
        response.raise_for_status()
        data = response.json()
        return data.get('web', {}).get('results', [])

    async def _search_google(self, query: str, count: int) -> List[Dict[str, Any]]:
        """Search using Google Custom Search API"""
        # Implement Google Custom Search API integration
        # This requires Google API key and Custom Search Engine ID
        pass

    async def _search_bing(self, query: str, count: int) -> List[Dict[str, Any]]:
        """Search using Bing Search API"""
        # Implement Bing Search API integration
        pass

    async def _search_duckduckgo(self, query: str, count: int) -> List[Dict[str, Any]]:
        """Search using DuckDuckGo API"""
        # Implement DuckDuckGo search integration
        pass

    async def crawl_website(self, url: str, extraction_strategy: Optional[str] = None) -> CrawlResult:
        """Advanced web crawling with Crawl4AI"""
        try:
            # Configure extraction strategy
            strategy = None
            if extraction_strategy:
                strategy = LLMExtractionStrategy(
                    provider="openai",
                    api_token=self.config.get('openai_api_key'),
                    instruction=extraction_strategy
                )

            # Perform crawling
            result = await self.dependencies.crawl4ai_crawler.arun(
                url=url,
                extraction_strategy=strategy,
                bypass_cache=False,
                include_raw_html=False
            )

            if result.success:
                # Extract structured data if strategy was used
                structured_data = {}
                if strategy and result.extracted_content:
                    try:
                        structured_data = json.loads(result.extracted_content)
                    except json.JSONDecodeError:
                        structured_data = {"raw_extraction": result.extracted_content}

                crawl_result = CrawlResult(
                    url=url,
                    title=result.metadata.get('title', ''),
                    content=result.markdown or result.cleaned_html,
                    structured_data=structured_data,
                    links=result.links.get('internal', []) + result.links.get('external', []),
                    images=result.media.get('images', []),
                    crawled_at=datetime.now(),
                    success=True
                )

                # Store in vector database
                await self._store_crawl_result(crawl_result)

                return crawl_result
            else:
                return CrawlResult(
                    url=url,
                    title="",
                    content="",
                    crawled_at=datetime.now(),
                    success=False,
                    error_message=result.error_message
                )

        except Exception as e:
            logger.error(f"Web crawling failed for {url}: {e}")
            return CrawlResult(
                url=url,
                title="",
                content="",
                crawled_at=datetime.now(),
                success=False,
                error_message=str(e)
            )

    async def _store_crawl_result(self, crawl_result: CrawlResult):
        """Store crawl results in vector database and MongoDB"""
        try:
            # Generate embedding
            embedding = self.dependencies.embedding_model.encode(
                f"{crawl_result.title} {crawl_result.content[:1000]}"
            ).tolist()

            # Store in Qdrant
            vector_id = hashlib.md5(crawl_result.url.encode()).hexdigest()
            point = PointStruct(
                id=vector_id,
                vector=embedding,
                payload={
                    'url': crawl_result.url,
                    'title': crawl_result.title,
                    'content_preview': crawl_result.content[:500],
                    'crawled_at': crawl_result.crawled_at.isoformat(),
                    'structured_data': crawl_result.structured_data
                }
            )

            self.dependencies.qdrant_client.upsert(
                collection_name="web_content",
                points=[point]
            )

            crawl_result.vector_id = vector_id

            # Store full result in MongoDB
            await self.dependencies.mongodb_client.crawl_results.web_content.insert_one(
                crawl_result.model_dump()
            )

            logger.info(f"Stored crawl result for {crawl_result.url}")

        except Exception as e:
            logger.error(f"Failed to store crawl result: {e}")

    async def process_document(self, file_path: str, document_type: str = "auto") -> DocumentMetadata:
        """Enhanced document processing with OCR integration"""
        try:
            start_time = datetime.now()

            # Auto-detect document type if not specified
            if document_type == "auto":
                document_type = self._detect_document_type(file_path)

            # Use integrated document intelligence runner if available
            if self.dependencies.doc_intelligence_runner:
                # Use your existing OCR pipeline
                result = self.dependencies.doc_intelligence_runner.process_document(
                    file_path, tender_id=None
                )

                # Convert to our DocumentMetadata format
                doc_metadata = DocumentMetadata(
                    document_id=str(hashlib.md5(file_path.encode()).hexdigest()),
                    title=result.get('tender_title', Path(file_path).stem),
                    document_type=document_type,
                    source=file_path,
                    file_path=file_path,
                    processed_at=datetime.now(),
                    confidence_score=result.get('analytics', {}).get('confidence_score', 0.8),
                    extracted_entities=self._extract_entities_from_result(result),
                    tags=self._generate_tags(result),
                    file_size=Path(file_path).stat().st_size if Path(file_path).exists() else None
                )
            else:
                # Fallback to basic processing
                content = await self._extract_text_from_file(file_path)

                # Use document agent for extraction
                extraction_result = await self.agents['document'].run(
                    f"Extract structured information from this {document_type} document:\n\n{content[:2000]}",
                    deps=self.dependencies
                )

                doc_metadata = DocumentMetadata(
                    document_id=str(hashlib.md5(file_path.encode()).hexdigest()),
                    title=extraction_result.data.get('tender_title', Path(file_path).stem),
                    document_type=document_type,
                    source=file_path,
                    file_path=file_path,
                    processed_at=datetime.now(),
                    confidence_score=0.7,  # Default confidence for fallback
                    extracted_entities=[],
                    tags=[],
                    file_size=Path(file_path).stat().st_size if Path(file_path).exists() else None
                )

            # Store in databases
            await self._store_document_metadata(doc_metadata, content if 'content' in locals() else "")

            # Update knowledge graph
            await self._update_knowledge_graph(doc_metadata)

            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            logger.info(f"Processed document {file_path} in {processing_time:.2f}ms")

            return doc_metadata

        except Exception as e:
            logger.error(f"Document processing failed for {file_path}: {e}")
            raise

    def _detect_document_type(self, file_path: str) -> str:
        """Auto-detect document type based on content and filename"""
        filename = Path(file_path).name.lower()

        if any(keyword in filename for keyword in ['tender', 'bid', 'rfp', 'invitation']):
            return 'tender_notice'
        elif any(keyword in filename for keyword in ['boq', 'bill', 'quantities', 'schedule']):
            return 'boq'
        elif any(keyword in filename for keyword in ['contract', 'agreement']):
            return 'contract'
        else:
            return 'document'

    async def _extract_text_from_file(self, file_path: str) -> str:
        """Extract text from various file formats"""
        file_path = Path(file_path)

        if file_path.suffix.lower() == '.pdf':
            # Use PyMuPDF for PDF extraction
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            return text
        elif file_path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.tiff']:
            # Use OCR for images
            image = Image.open(file_path)
            return pytesseract.image_to_string(image)
        elif file_path.suffix.lower() in ['.txt', '.md']:
            # Plain text files
            return file_path.read_text(encoding='utf-8')
        else:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")

    def _extract_entities_from_result(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract entities from OCR pipeline result"""
        entities = []

        # Extract issuer information
        if 'issuer_details' in result:
            entities.append({
                'type': 'organization',
                'value': result['issuer_details'].get('name', ''),
                'confidence': 0.9
            })

        # Extract dates
        if 'closing_date' in result:
            entities.append({
                'type': 'date',
                'value': result['closing_date'],
                'confidence': 0.95
            })

        # Extract monetary values
        if 'estimated_value' in result:
            entities.append({
                'type': 'money',
                'value': result['estimated_value'],
                'confidence': 0.8
            })

        return entities

    def _generate_tags(self, result: Dict[str, Any]) -> List[str]:
        """Generate tags from document content"""
        tags = []

        if 'tender_title' in result:
            # Extract keywords from title
            title_words = result['tender_title'].lower().split()
            tags.extend([word for word in title_words if len(word) > 3])

        if 'document_type' in result:
            tags.append(result['document_type'])

        return list(set(tags))[:10]  # Limit to 10 unique tags

    async def _store_document_metadata(self, doc_metadata: DocumentMetadata, content: str):
        """Store document metadata in multiple databases"""
        try:
            # Generate embedding
            embedding = self.dependencies.embedding_model.encode(
                f"{doc_metadata.title} {content[:1000]}"
            ).tolist()

            # Store in Qdrant
            vector_id = doc_metadata.document_id
            point = PointStruct(
                id=vector_id,
                vector=embedding,
                payload={
                    'document_id': doc_metadata.document_id,
                    'title': doc_metadata.title,
                    'document_type': doc_metadata.document_type,
                    'source': doc_metadata.source,
                    'processed_at': doc_metadata.processed_at.isoformat(),
                    'confidence_score': doc_metadata.confidence_score,
                    'tags': doc_metadata.tags,
                    'content_preview': content[:500]
                }
            )

            self.dependencies.qdrant_client.upsert(
                collection_name="documents",
                points=[point]
            )

            doc_metadata.vector_id = vector_id

            # Store in MongoDB
            await self.dependencies.mongodb_client.documents.metadata.insert_one(
                doc_metadata.model_dump()
            )

            # Store in Milvus if available
            if self.dependencies.milvus_collection:
                try:
                    self.dependencies.milvus_collection.insert([
                        [embedding],
                        [doc_metadata.document_id],
                        [doc_metadata.document_type],
                        [doc_metadata.model_dump()]
                    ])
                except Exception as e:
                    logger.warning(f"Milvus storage failed: {e}")

            logger.info(f"Stored document metadata for {doc_metadata.title}")

        except Exception as e:
            logger.error(f"Failed to store document metadata: {e}")

    async def _update_knowledge_graph(self, doc_metadata: DocumentMetadata):
        """Update knowledge graph with document and entity relationships"""
        try:
            graph = self.dependencies.knowledge_graph

            # Add document node
            graph.add_node(
                doc_metadata.document_id,
                type='document',
                title=doc_metadata.title,
                document_type=doc_metadata.document_type,
                processed_at=doc_metadata.processed_at.isoformat(),
                confidence_score=doc_metadata.confidence_score
            )

            # Add entity nodes and relationships
            for entity in doc_metadata.extracted_entities:
                entity_id = f"entity_{hashlib.md5(str(entity).encode()).hexdigest()}"

                graph.add_node(
                    entity_id,
                    type='entity',
                    entity_type=entity.get('type', 'unknown'),
                    value=entity.get('value', ''),
                    confidence=entity.get('confidence', 0.5)
                )

                # Add relationship between document and entity
                graph.add_edge(
                    doc_metadata.document_id,
                    entity_id,
                    relationship='contains',
                    confidence=entity.get('confidence', 0.5)
                )

            # Store graph state in Redis
            graph_data = {
                'nodes': dict(graph.nodes(data=True)),
                'edges': list(graph.edges(data=True))
            }

            await self.dependencies.redis_client.set(
                "knowledge_graph",
                json.dumps(graph_data, default=str),
                ex=86400  # 24 hours
            )

            doc_metadata.graph_node_id = doc_metadata.document_id

        except Exception as e:
            logger.error(f"Failed to update knowledge graph: {e}")

    async def query_rag(self, query: str, top_k: int = 5, include_web: bool = True) -> RAGContext:
        """Advanced RAG query with multiple sources"""
        try:
            # Generate query embedding
            query_embedding = self.dependencies.embedding_model.encode(query).tolist()

            # Search documents in Qdrant
            doc_results = self.dependencies.qdrant_client.search(
                collection_name="documents",
                query_vector=query_embedding,
                limit=top_k
            )

            # Search web content if requested
            web_results = []
            if include_web:
                web_results = self.dependencies.qdrant_client.search(
                    collection_name="web_content",
                    query_vector=query_embedding,
                    limit=top_k
                )

            # Get graph context
            graph_context = await self._get_graph_context(query)

            # Combine results
            relevant_documents = []
            for result in doc_results:
                relevant_documents.append({
                    'id': result.id,
                    'score': result.score,
                    'payload': result.payload,
                    'source': 'document'
                })

            vector_matches = []
            for result in web_results:
                vector_matches.append({
                    'id': result.id,
                    'score': result.score,
                    'payload': result.payload,
                    'source': 'web'
                })

            # Calculate overall confidence
            confidence_score = self._calculate_rag_confidence(
                doc_results, web_results, graph_context
            )

            # Generate context summary
            context_summary = await self._generate_context_summary(
                query, relevant_documents, vector_matches, graph_context
            )

            return RAGContext(
                query=query,
                relevant_documents=relevant_documents,
                graph_context=graph_context,
                vector_matches=vector_matches,
                confidence_score=confidence_score,
                context_summary=context_summary
            )

        except Exception as e:
            logger.error(f"RAG query failed: {e}")
            raise

    async def _get_graph_context(self, query: str) -> List[Dict[str, Any]]:
        """Get relevant context from knowledge graph"""
        try:
            graph = self.dependencies.knowledge_graph
            context = []

            # Simple keyword matching for now - can be enhanced with embeddings
            query_words = query.lower().split()

            for node_id, node_data in graph.nodes(data=True):
                node_text = f"{node_data.get('title', '')} {node_data.get('value', '')}".lower()

                if any(word in node_text for word in query_words):
                    # Get connected nodes
                    neighbors = list(graph.neighbors(node_id))

                    context.append({
                        'node_id': node_id,
                        'node_data': node_data,
                        'neighbors': neighbors[:5],  # Limit neighbors
                        'relevance_score': 0.8  # Placeholder scoring
                    })

            return context[:10]  # Limit context size

        except Exception as e:
            logger.error(f"Failed to get graph context: {e}")
            return []

    def _calculate_rag_confidence(self, doc_results, web_results, graph_context) -> float:
        """Calculate confidence score for RAG results"""
        if not doc_results and not web_results and not graph_context:
            return 0.0

        # Simple confidence calculation based on result scores
        total_score = 0.0
        count = 0

        for result in doc_results:
            total_score += result.score
            count += 1

        for result in web_results:
            total_score += result.score * 0.8  # Web results get lower weight
            count += 1

        for context in graph_context:
            total_score += context.get('relevance_score', 0.5)
            count += 1

        return total_score / count if count > 0 else 0.0

    async def _generate_context_summary(self, query: str, documents: List,
                                      web_matches: List, graph_context: List) -> str:
        """Generate a summary of the RAG context"""
        try:
            context_parts = []

            if documents:
                context_parts.append(f"Found {len(documents)} relevant documents")

            if web_matches:
                context_parts.append(f"Found {len(web_matches)} web sources")

            if graph_context:
                context_parts.append(f"Found {len(graph_context)} related entities")

            if not context_parts:
                return "No relevant context found"

            return f"Query: '{query}' - " + ", ".join(context_parts)

        except Exception as e:
            logger.error(f"Failed to generate context summary: {e}")
            return "Context summary unavailable"

    # ============================================================================
    # COMMUNICATION AND NOTIFICATION METHODS
    # ============================================================================

    async def send_email(self, recipient: str, subject: str, message: str,
                        html: bool = True) -> Dict[str, Any]:
        """Send email notifications"""
        try:
            smtp_config = self.config['integrations']['smtp']

            msg = MIMEMultipart('alternative')
            msg['From'] = smtp_config['from_email']
            msg['To'] = recipient
            msg['Subject'] = subject

            if html:
                msg.attach(MIMEText(message, 'html'))
            else:
                msg.attach(MIMEText(message, 'plain'))

            with smtplib.SMTP(smtp_config['server'], smtp_config['port']) as server:
                server.starttls()
                server.login(smtp_config['username'], smtp_config['password'])
                server.send_message(msg)

            # Log notification
            await self._log_notification('email', recipient, subject, 'sent')

            return {'status': 'sent', 'recipient': recipient, 'subject': subject}

        except Exception as e:
            logger.error(f"Email sending failed: {e}")
            await self._log_notification('email', recipient, subject, 'failed', str(e))
            raise

    async def send_slack_message(self, channel: str, message: str,
                               blocks: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Send Slack messages"""
        try:
            if not self.dependencies.slack_client:
                raise ValueError("Slack client not configured")

            response = await self.dependencies.slack_client.chat_postMessage(
                channel=channel,
                text=message,
                blocks=blocks
            )

            # Log notification
            await self._log_notification('slack', channel, message, 'sent')

            return {
                'status': 'sent',
                'channel': channel,
                'timestamp': response['ts']
            }

        except Exception as e:
            logger.error(f"Slack message sending failed: {e}")
            await self._log_notification('slack', channel, message, 'failed', str(e))
            raise

    async def create_calendar_event(self, title: str, start_time: datetime,
                                  end_time: datetime, description: str = "",
                                  attendees: List[str] = None) -> Dict[str, Any]:
        """Create Google Calendar events"""
        try:
            # Load Google Calendar credentials
            creds_path = self.config['integrations']['google']['credentials_path']
            creds = Credentials.from_authorized_user_file(creds_path)

            service = build('calendar', 'v3', credentials=creds)

            event = {
                'summary': title,
                'description': description,
                'start': {
                    'dateTime': start_time.isoformat(),
                    'timeZone': 'UTC',
                },
                'end': {
                    'dateTime': end_time.isoformat(),
                    'timeZone': 'UTC',
                },
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'email', 'minutes': 24 * 60},
                        {'method': 'popup', 'minutes': 60},
                    ],
                }
            }

            if attendees:
                event['attendees'] = [{'email': email} for email in attendees]

            created_event = service.events().insert(
                calendarId='primary', body=event
            ).execute()

            # Store event reference in MongoDB
            await self.dependencies.mongodb_client.calendar.events.insert_one({
                'google_event_id': created_event['id'],
                'title': title,
                'start_time': start_time,
                'end_time': end_time,
                'description': description,
                'attendees': attendees or [],
                'created_at': datetime.now()
            })

            return {
                'status': 'created',
                'event_id': created_event['id'],
                'event_url': created_event.get('htmlLink', '')
            }

        except Exception as e:
            logger.error(f"Calendar event creation failed: {e}")
            raise

    async def _log_notification(self, notification_type: str, recipient: str,
                              content: str, status: str, error: str = None):
        """Log notification attempts"""
        try:
            log_entry = {
                'type': notification_type,
                'recipient': recipient,
                'content_preview': content[:100],
                'status': status,
                'timestamp': datetime.now(),
                'error': error
            }

            await self.dependencies.mongodb_client.logs.notifications.insert_one(log_entry)

        except Exception as e:
            logger.error(f"Failed to log notification: {e}")

    # ============================================================================
    # WORKFLOW MANAGEMENT AND EXECUTION
    # ============================================================================

    async def execute_workflow(self, workflow_type: str, parameters: Dict[str, Any]) -> TaskResult:
        """Execute complex workflows combining multiple capabilities"""
        task_id = f"task_{hashlib.md5(f'{workflow_type}_{datetime.now()}'.encode()).hexdigest()}"
        start_time = datetime.now()

        try:
            if workflow_type == "tender_analysis":
                result = await self._execute_tender_analysis_workflow(parameters)
            elif workflow_type == "market_research":
                result = await self._execute_market_research_workflow(parameters)
            elif workflow_type == "document_intelligence":
                result = await self._execute_document_intelligence_workflow(parameters)
            elif workflow_type == "competitive_analysis":
                result = await self._execute_competitive_analysis_workflow(parameters)
            else:
                raise ValueError(f"Unknown workflow type: {workflow_type}")

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            task_result = TaskResult(
                task_id=task_id,
                task_type=workflow_type,
                status="completed",
                result=result,
                created_at=start_time,
                updated_at=datetime.now(),
                execution_time_ms=execution_time,
                confidence_score=result.get('confidence_score', 0.8)
            )

            # Store task result
            await self._store_task_result(task_result)

            return task_result

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")

            task_result = TaskResult(
                task_id=task_id,
                task_type=workflow_type,
                status="failed",
                result={},
                created_at=start_time,
                updated_at=datetime.now(),
                execution_time_ms=(datetime.now() - start_time).total_seconds() * 1000,
                error_message=str(e),
                confidence_score=0.0
            )

            await self._store_task_result(task_result)
            return task_result

    async def _execute_tender_analysis_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute comprehensive tender analysis workflow"""
        results = {}

        # 1. Process tender document
        if 'document_path' in parameters:
            doc_metadata = await self.process_document(
                parameters['document_path'],
                'tender_notice'
            )
            results['document_analysis'] = doc_metadata.model_dump()

        # 2. Search for related opportunities
        if 'search_query' in parameters:
            search_result = await self.search_internet(
                parameters['search_query'],
                source='brave'
            )
            results['market_search'] = search_result.model_dump()

        # 3. Crawl competitor websites
        if 'competitor_urls' in parameters:
            crawl_results = []
            for url in parameters['competitor_urls']:
                crawl_result = await self.crawl_website(url)
                crawl_results.append(crawl_result.model_dump())
            results['competitor_analysis'] = crawl_results

        # 4. Generate insights using RAG
        if 'analysis_query' in parameters:
            rag_context = await self.query_rag(parameters['analysis_query'])
            results['insights'] = rag_context.model_dump()

        # 5. Create calendar reminders
        if 'create_reminders' in parameters and parameters['create_reminders']:
            if 'document_analysis' in results:
                doc_data = results['document_analysis']
                if 'closing_date' in doc_data:
                    # Create reminder 3 days before closing
                    reminder_time = datetime.fromisoformat(doc_data['closing_date']) - timedelta(days=3)
                    calendar_result = await self.create_calendar_event(
                        f"Tender Deadline Reminder: {doc_data.get('title', 'Unknown')}",
                        reminder_time,
                        reminder_time + timedelta(hours=1),
                        f"Tender submission deadline in 3 days"
                    )
                    results['calendar_reminder'] = calendar_result

        # 6. Send notification
        if 'notify_email' in parameters:
            await self.send_email(
                parameters['notify_email'],
                "Tender Analysis Complete",
                f"Analysis completed for workflow. Results: {len(results)} components processed."
            )

        results['confidence_score'] = 0.85
        return results

    async def _execute_market_research_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute market research workflow"""
        results = {}

        # Multi-source search
        search_queries = parameters.get('search_queries', [])
        for query in search_queries:
            search_result = await self.search_internet(query, source='brave')
            results[f'search_{hashlib.md5(query.encode()).hexdigest()[:8]}'] = search_result.model_dump()

        # Web crawling for detailed analysis
        target_urls = parameters.get('target_urls', [])
        for url in target_urls:
            crawl_result = await self.crawl_website(
                url,
                extraction_strategy="Extract key business information, pricing, and market positioning"
            )
            results[f'crawl_{hashlib.md5(url.encode()).hexdigest()[:8]}'] = crawl_result.model_dump()

        results['confidence_score'] = 0.8
        return results

    async def _execute_document_intelligence_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute document intelligence workflow"""
        results = {}

        # Process multiple documents
        document_paths = parameters.get('document_paths', [])
        for doc_path in document_paths:
            doc_metadata = await self.process_document(doc_path)
            results[f'doc_{doc_metadata.document_id}'] = doc_metadata.model_dump()

        # Perform cross-document analysis
        if len(document_paths) > 1:
            analysis_query = parameters.get('analysis_query', 'Compare and analyze these documents')
            rag_context = await self.query_rag(analysis_query)
            results['cross_analysis'] = rag_context.model_dump()

        results['confidence_score'] = 0.9
        return results

    async def _execute_competitive_analysis_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute competitive analysis workflow"""
        results = {}

        # Search for competitors
        competitor_query = parameters.get('competitor_query', '')
        if competitor_query:
            search_result = await self.search_internet(competitor_query)
            results['competitor_search'] = search_result.model_dump()

        # Analyze competitor websites
        competitor_urls = parameters.get('competitor_urls', [])
        competitor_data = []
        for url in competitor_urls:
            crawl_result = await self.crawl_website(
                url,
                extraction_strategy="Extract company information, services, pricing, and competitive advantages"
            )
            competitor_data.append(crawl_result.model_dump())

        results['competitor_profiles'] = competitor_data
        results['confidence_score'] = 0.75
        return results

    async def _store_task_result(self, task_result: TaskResult):
        """Store task execution results"""
        try:
            await self.dependencies.mongodb_client.tasks.results.insert_one(
                task_result.model_dump()
            )

            # Also cache in Redis for quick access
            await self.dependencies.redis_client.setex(
                f"task:{task_result.task_id}",
                3600,  # 1 hour
                task_result.model_dump_json()
            )

        except Exception as e:
            logger.error(f"Failed to store task result: {e}")

    async def get_task_status(self, task_id: str) -> Optional[TaskResult]:
        """Get task execution status"""
        try:
            # Try Redis first
            cached_result = await self.dependencies.redis_client.get(f"task:{task_id}")
            if cached_result:
                return TaskResult.model_validate_json(cached_result)

            # Fall back to MongoDB
            result = await self.dependencies.mongodb_client.tasks.results.find_one(
                {"task_id": task_id}
            )

            if result:
                return TaskResult.model_validate(result)

            return None

        except Exception as e:
            logger.error(f"Failed to get task status: {e}")
            return None

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.dependencies:
                await self.dependencies.http_client.aclose()
                await self.dependencies.redis_client.close()
                self.dependencies.mongodb_client.close()

                if self.dependencies.crawl4ai_crawler:
                    await self.dependencies.crawl4ai_crawler.aclose()

            logger.info("Agent cleanup completed")

        except Exception as e:
            logger.error(f"Cleanup failed: {e}")

# ============================================================================
# CONFIGURATION TEMPLATE
# ============================================================================

ENHANCED_AGENT_CONFIG_TEMPLATE = """
# Enhanced Pydantic Agent Configuration

databases:
  mongodb:
    url: "mongodb://localhost:27017"
    database: "enhanced_enterprise_agent"
  redis:
    url: "redis://localhost:6379"
  qdrant:
    host: "localhost"
    port: 6333
  milvus:
    host: "localhost"
    port: 19530

models:
  primary: "gpt-4-turbo-preview"
  reasoning: "claude-3-sonnet-20240229"
  document_extraction: "gemini-1.5-pro"
  embedding: "all-MiniLM-L6-v2"

integrations:
  brave_search:
    api_key: "${BRAVE_API_KEY}"
  google:
    credentials_path: "credentials/google_creds.json"
    custom_search_engine_id: "${GOOGLE_CSE_ID}"
    api_key: "${GOOGLE_API_KEY}"
  slack:
    token: "${SLACK_BOT_TOKEN}"
  smtp:
    server: "smtp.gmail.com"
    port: 587
    from_email: "<EMAIL>"
    username: "${SMTP_USERNAME}"
    password: "${SMTP_PASSWORD}"
  openai:
    api_key: "${OPENAI_API_KEY}"

crawl4ai:
  max_concurrent: 5
  delay_between_requests: 1.0
  timeout: 30
  user_agent: "EnhancedPydanticAgent/1.0"

# Document processing schemas
document_schemas:
  tender_notice: "configs/schemas/tender_notice.json"
  boq: "configs/schemas/boq.json"
  contract: "configs/schemas/contract.json"

# Workflow configurations
workflows:
  tender_analysis:
    default_timeout: 300
    max_concurrent_crawls: 3
  market_research:
    search_sources: ["brave", "google"]
    max_results_per_source: 10
"""
