# Enhanced Pydantic Agent

A comprehensive, production-ready AI agent built with PydanticAI that combines document processing, web crawling, vector databases, graph memory, and communication capabilities for enterprise automation.

## 🚀 Features

### Core Capabilities
- **🔍 Multi-Source Web Search**: Brave Search, Google, Bing, DuckDuckGo integration
- **🕷️ Advanced Web Crawling**: Crawl4AI integration with structured data extraction
- **📄 Document Processing**: OCR, PDF parsing, image text extraction with schema-driven LLM extraction
- **🧠 RAG System**: Multi-vector database support (Qdrant, Milvus) with graph-based context
- **💾 Multi-Database Support**: MongoDB, Redis, Qdrant, Milvus integration
- **📊 Knowledge Graph**: NetworkX-based relationship mapping and memory
- **📧 Communication**: Email, Slack, Google Calendar integration
- **⚙️ Workflow Engine**: Complex multi-step workflow execution
- **🔒 Enterprise Security**: Rate limiting, encryption, data retention policies

### Document Intelligence
- **Schema-Driven Extraction**: JSON schema-based structured data extraction
- **Multi-Format Support**: PDF, DOCX, images, plain text
- **OCR Integration**: Tesseract and cloud OCR services
- **Confidence Scoring**: AI-powered confidence assessment
- **Entity Recognition**: Automatic entity extraction and relationship mapping

### Memory Systems
- **Short-term Memory**: Redis-based caching and session storage
- **Long-term Memory**: Vector embeddings in Qdrant/Milvus
- **Graph Memory**: NetworkX knowledge graph with relationship tracking
- **Context Retrieval**: Advanced RAG with multi-source context aggregation

## 📦 Installation

### Prerequisites
- Python 3.9+
- MongoDB (local or cloud)
- Redis (local or cloud)
- Qdrant (local or cloud)
- Optional: Milvus, Google Cloud credentials

### Quick Setup

```bash
# Clone the repository
git clone <repository-url>
cd BID_BEES_AGENTS/PYDANTIC

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys and configuration
```

### Environment Variables

Create a `.env` file with the following variables:

```bash
# API Keys
BRAVE_API_KEY=your_brave_search_api_key
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_api_key

# Database URLs
MONGODB_URL=mongodb://localhost:27017
REDIS_URL=redis://localhost:6379
QDRANT_HOST=localhost
QDRANT_PORT=6333

# Communication
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SLACK_BOT_TOKEN=xoxb-your-slack-token

# Google Services
GOOGLE_CSE_ID=your_custom_search_engine_id
```

## 🔧 Configuration

The agent uses YAML configuration files for flexible setup:

```yaml
# config/enhanced_agent_config.yaml
databases:
  mongodb:
    url: "mongodb://localhost:27017"
    database: "enhanced_enterprise_agent"
  redis:
    url: "redis://localhost:6379"
  qdrant:
    host: "localhost"
    port: 6333

models:
  primary: "gpt-4-turbo-preview"
  reasoning: "claude-3-sonnet-20240229"
  document_extraction: "gemini-1.5-pro"
  embedding: "all-MiniLM-L6-v2"

# ... additional configuration
```

## 🚀 Quick Start

### Basic Usage

```python
import asyncio
from agents.enhanced_pydantic_agent import EnhancedPydanticAgent

async def main():
    # Initialize agent
    agent = EnhancedPydanticAgent()
    await agent.initialize()
    
    try:
        # Process a document
        doc_result = await agent.process_document(
            "path/to/tender_document.pdf", 
            "tender_notice"
        )
        print(f"Processed: {doc_result.title}")
        
        # Search the internet
        search_result = await agent.search_internet(
            "construction tender opportunities 2024"
        )
        print(f"Found {len(search_result.results)} results")
        
        # Crawl a website
        crawl_result = await agent.crawl_website(
            "https://example-tender-site.com",
            extraction_strategy="Extract tender information"
        )
        
        # Query with RAG
        rag_context = await agent.query_rag(
            "What are the requirements for construction tenders?"
        )
        
        # Execute workflow
        workflow_result = await agent.execute_workflow(
            'tender_analysis',
            {
                'document_path': 'tender.pdf',
                'search_query': 'related opportunities',
                'create_reminders': True
            }
        )
        
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

### Document Processing

```python
# Process different document types
doc_metadata = await agent.process_document(
    "documents/tender_notice.pdf",
    document_type="tender_notice"  # or "boq", "contract", "auto"
)

# Access extracted data
print(f"Title: {doc_metadata.title}")
print(f"Confidence: {doc_metadata.confidence_score}")
print(f"Entities: {len(doc_metadata.extracted_entities)}")
```

### Web Crawling with Structured Extraction

```python
# Crawl with custom extraction strategy
crawl_result = await agent.crawl_website(
    "https://tender-website.com",
    extraction_strategy="""
    Extract the following information:
    - Available tender opportunities
    - Closing dates
    - Contact information
    - Requirements
    Return as structured JSON.
    """
)

if crawl_result.success:
    print(f"Extracted data: {crawl_result.structured_data}")
```

### RAG Queries

```python
# Query across documents and web content
rag_context = await agent.query_rag(
    "What are the key requirements for government construction tenders?",
    top_k=5,
    include_web=True
)

print(f"Confidence: {rag_context.confidence_score}")
print(f"Context: {rag_context.context_summary}")
```

### Workflow Execution

```python
# Execute comprehensive tender analysis
workflow_params = {
    'document_path': 'tender_notice.pdf',
    'search_query': 'construction tender opportunities',
    'competitor_urls': ['https://competitor1.com', 'https://competitor2.com'],
    'analysis_query': 'Market trends in construction',
    'create_reminders': True,
    'notify_email': '<EMAIL>'
}

result = await agent.execute_workflow('tender_analysis', workflow_params)
print(f"Workflow completed: {result.status}")
```

## 📊 Workflows

### Available Workflows

1. **Tender Analysis**: Complete tender document processing with market research
2. **Market Research**: Multi-source market intelligence gathering
3. **Document Intelligence**: Batch document processing and analysis
4. **Competitive Analysis**: Competitor research and profiling

### Custom Workflows

You can extend the agent with custom workflows:

```python
async def _execute_custom_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
    results = {}
    
    # Your custom workflow logic here
    # Use agent capabilities: search, crawl, process documents, etc.
    
    return results

# Register the workflow
agent.workflow_handlers['custom_workflow'] = agent._execute_custom_workflow
```

## 🔧 Advanced Configuration

### Vector Database Setup

```yaml
# Qdrant configuration
qdrant:
  host: "localhost"
  port: 6333
  collections:
    - name: "documents"
      vector_size: 384
    - name: "web_content"
      vector_size: 384

# Milvus configuration (optional)
milvus:
  host: "localhost"
  port: 19530
  collection_name: "enterprise_vectors"
```

### Document Schemas

The agent uses JSON schemas for structured extraction. See `configs/schemas/` for examples:

- `tender_notice.json`: Tender document schema
- `boq.json`: Bill of Quantities schema
- `contract.json`: Contract document schema

### Memory Configuration

```yaml
memory:
  redis_ttl_hours: 24
  graph_max_nodes: 10000
  vector_similarity_threshold: 0.7
```

## 🔒 Security

- **Rate Limiting**: Configurable request rate limits
- **Data Encryption**: Sensitive data encryption at rest
- **Access Control**: Role-based access control
- **Audit Logging**: Comprehensive activity logging
- **Data Retention**: Configurable data retention policies

## 📈 Monitoring

The agent includes built-in monitoring and logging:

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Performance Metrics**: Execution time tracking
- **Error Handling**: Comprehensive error tracking and recovery
- **Health Checks**: System health monitoring endpoints

## 🧪 Testing

```bash
# Run tests
pytest tests/

# Run with coverage
pytest --cov=agents tests/

# Run specific test categories
pytest -m "unit" tests/
pytest -m "integration" tests/
```

## 📚 API Reference

### Core Methods

- `process_document(file_path, document_type)`: Process documents with OCR and LLM extraction
- `search_internet(query, source, count)`: Multi-source web search
- `crawl_website(url, extraction_strategy)`: Advanced web crawling
- `query_rag(query, top_k, include_web)`: RAG-based information retrieval
- `execute_workflow(workflow_type, parameters)`: Execute complex workflows

### Communication Methods

- `send_email(recipient, subject, message)`: Send email notifications
- `send_slack_message(channel, message)`: Send Slack messages
- `create_calendar_event(title, start_time, end_time)`: Create calendar events

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the examples in `examples/`

## 🔄 Updates

The agent is actively maintained with regular updates for:
- New AI model integrations
- Enhanced document processing capabilities
- Additional workflow types
- Performance improvements
- Security enhancements
