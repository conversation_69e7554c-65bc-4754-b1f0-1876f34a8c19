#!/usr/bin/env python3
"""
Test suite for Enhanced Pydantic Agent

This file contains unit and integration tests for the Enhanced Pydantic Agent.
"""

import pytest
import asyncio
import tempfile
import json
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

# Import the agent
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents.enhanced_pydantic_agent import (
    EnhancedPydanticAgent,
    DocumentMetadata,
    SearchResult,
    CrawlResult,
    TaskResult,
    RAGContext
)

class TestEnhancedPydanticAgent:
    """Test suite for Enhanced Pydantic Agent"""
    
    @pytest.fixture
    async def agent(self):
        """Create agent instance for testing"""
        # Create a mock configuration
        mock_config = {
            'databases': {
                'mongodb': {'url': 'mongodb://localhost:27017', 'database': 'test_db'},
                'redis': {'url': 'redis://localhost:6379'},
                'qdrant': {'host': 'localhost', 'port': 6333},
                'milvus': {'host': 'localhost', 'port': 19530}
            },
            'models': {
                'primary': 'gpt-4-turbo-preview',
                'reasoning': 'claude-3-sonnet-20240229',
                'embedding': 'all-MiniLM-L6-v2'
            },
            'integrations': {
                'brave_search': {'api_key': 'test_key'},
                'smtp': {
                    'server': 'smtp.gmail.com',
                    'port': 587,
                    'from_email': '<EMAIL>',
                    'username': 'test',
                    'password': 'test'
                }
            },
            'crawl4ai': {
                'max_concurrent': 5,
                'delay_between_requests': 1.0,
                'timeout': 30
            }
        }
        
        with patch.object(EnhancedPydanticAgent, '_load_config', return_value=mock_config):
            agent = EnhancedPydanticAgent()
            
            # Mock dependencies to avoid actual database connections
            agent.dependencies = Mock()
            agent.dependencies.mongodb_client = AsyncMock()
            agent.dependencies.redis_client = AsyncMock()
            agent.dependencies.qdrant_client = Mock()
            agent.dependencies.http_client = AsyncMock()
            agent.dependencies.embedding_model = Mock()
            agent.dependencies.knowledge_graph = Mock()
            agent.dependencies.crawl4ai_crawler = AsyncMock()
            agent.dependencies.config = mock_config
            
            yield agent
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, agent):
        """Test agent initialization"""
        assert agent is not None
        assert hasattr(agent, 'config')
        assert hasattr(agent, 'agents')
        assert 'primary' in agent.agents
        assert 'document' in agent.agents
        assert 'research' in agent.agents
        assert 'crawler' in agent.agents
    
    @pytest.mark.asyncio
    async def test_search_internet(self, agent):
        """Test internet search functionality"""
        # Mock HTTP response
        mock_response = Mock()
        mock_response.json.return_value = {
            'web': {
                'results': [
                    {'title': 'Test Result 1', 'url': 'https://example1.com'},
                    {'title': 'Test Result 2', 'url': 'https://example2.com'}
                ]
            }
        }
        mock_response.raise_for_status.return_value = None
        
        agent.dependencies.http_client.get.return_value = mock_response
        agent.dependencies.redis_client.get.return_value = None  # No cache
        agent.dependencies.redis_client.setex.return_value = True
        
        # Test search
        result = await agent.search_internet("test query", source="brave", count=5)
        
        assert isinstance(result, SearchResult)
        assert result.query == "test query"
        assert result.source == "brave"
        assert len(result.results) == 2
        assert result.results[0]['title'] == 'Test Result 1'
    
    @pytest.mark.asyncio
    async def test_document_processing(self, agent):
        """Test document processing functionality"""
        # Create a temporary test file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test tender document with tender number T123.")
            temp_file_path = f.name
        
        try:
            # Mock the document processing
            with patch.object(agent, '_extract_text_from_file') as mock_extract:
                mock_extract.return_value = "Test tender document content"
                
                # Mock the document agent
                mock_agent_result = Mock()
                mock_agent_result.data = {
                    'tender_title': 'Test Tender',
                    'confidence': 0.9
                }
                
                agent.agents['document'].run = AsyncMock(return_value=mock_agent_result)
                agent.dependencies.mongodb_client.documents.metadata.insert_one = AsyncMock()
                agent.dependencies.qdrant_client.upsert = Mock()
                agent.dependencies.embedding_model.encode.return_value.tolist.return_value = [0.1] * 384
                
                # Test document processing
                result = await agent.process_document(temp_file_path, "tender_notice")
                
                assert isinstance(result, DocumentMetadata)
                assert result.document_type == "tender_notice"
                assert result.source == temp_file_path
                
        finally:
            # Clean up
            Path(temp_file_path).unlink()
    
    @pytest.mark.asyncio
    async def test_web_crawling(self, agent):
        """Test web crawling functionality"""
        # Mock crawl result
        mock_crawl_result = Mock()
        mock_crawl_result.success = True
        mock_crawl_result.markdown = "Test content from website"
        mock_crawl_result.metadata = {'title': 'Test Page'}
        mock_crawl_result.links = {'internal': ['link1'], 'external': ['link2']}
        mock_crawl_result.media = {'images': ['image1.jpg']}
        mock_crawl_result.extracted_content = '{"key": "value"}'
        
        agent.dependencies.crawl4ai_crawler.arun.return_value = mock_crawl_result
        agent.dependencies.embedding_model.encode.return_value.tolist.return_value = [0.1] * 384
        agent.dependencies.qdrant_client.upsert = Mock()
        agent.dependencies.mongodb_client.crawl_results.web_content.insert_one = AsyncMock()
        
        # Test crawling
        result = await agent.crawl_website("https://example.com", "Extract test data")
        
        assert isinstance(result, CrawlResult)
        assert result.success == True
        assert result.url == "https://example.com"
        assert result.title == "Test Page"
        assert "key" in result.structured_data
    
    @pytest.mark.asyncio
    async def test_rag_query(self, agent):
        """Test RAG query functionality"""
        # Mock vector search results
        mock_doc_results = [
            Mock(id="doc1", score=0.9, payload={'title': 'Doc 1'}),
            Mock(id="doc2", score=0.8, payload={'title': 'Doc 2'})
        ]
        
        mock_web_results = [
            Mock(id="web1", score=0.7, payload={'title': 'Web 1'})
        ]
        
        agent.dependencies.qdrant_client.search.side_effect = [mock_doc_results, mock_web_results]
        agent.dependencies.embedding_model.encode.return_value.tolist.return_value = [0.1] * 384
        agent.dependencies.knowledge_graph.nodes.return_value = []
        
        # Test RAG query
        result = await agent.query_rag("test query", top_k=5, include_web=True)
        
        assert isinstance(result, RAGContext)
        assert result.query == "test query"
        assert len(result.relevant_documents) == 2
        assert len(result.vector_matches) == 1
        assert result.confidence_score > 0
    
    @pytest.mark.asyncio
    async def test_workflow_execution(self, agent):
        """Test workflow execution"""
        # Mock workflow dependencies
        agent.process_document = AsyncMock(return_value=Mock(
            model_dump=Mock(return_value={'title': 'Test Doc'})
        ))
        agent.search_internet = AsyncMock(return_value=Mock(
            model_dump=Mock(return_value={'results': []})
        ))
        agent.crawl_website = AsyncMock(return_value=Mock(
            model_dump=Mock(return_value={'success': True})
        ))
        agent.query_rag = AsyncMock(return_value=Mock(
            model_dump=Mock(return_value={'confidence_score': 0.8})
        ))
        agent.create_calendar_event = AsyncMock(return_value={'status': 'created'})
        agent.send_email = AsyncMock(return_value={'status': 'sent'})
        agent._store_task_result = AsyncMock()
        
        # Test workflow execution
        parameters = {
            'document_path': 'test.pdf',
            'search_query': 'test search',
            'competitor_urls': ['https://competitor.com'],
            'analysis_query': 'test analysis',
            'create_reminders': True,
            'notify_email': '<EMAIL>'
        }
        
        result = await agent.execute_workflow('tender_analysis', parameters)
        
        assert isinstance(result, TaskResult)
        assert result.task_type == 'tender_analysis'
        assert result.status == 'completed'
        assert 'confidence_score' in result.result
    
    @pytest.mark.asyncio
    async def test_email_sending(self, agent):
        """Test email sending functionality"""
        with patch('smtplib.SMTP') as mock_smtp:
            mock_server = Mock()
            mock_smtp.return_value.__enter__.return_value = mock_server
            
            agent._log_notification = AsyncMock()
            
            result = await agent.send_email(
                "<EMAIL>",
                "Test Subject",
                "Test message"
            )
            
            assert result['status'] == 'sent'
            assert result['recipient'] == '<EMAIL>'
            mock_server.send_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_calendar_event_creation(self, agent):
        """Test calendar event creation"""
        with patch('google.oauth2.credentials.Credentials') as mock_creds, \
             patch('googleapiclient.discovery.build') as mock_build:
            
            mock_service = Mock()
            mock_build.return_value = mock_service
            
            mock_event = {'id': 'event123', 'htmlLink': 'https://calendar.google.com/event123'}
            mock_service.events.return_value.insert.return_value.execute.return_value = mock_event
            
            agent.dependencies.mongodb_client.calendar.events.insert_one = AsyncMock()
            
            result = await agent.create_calendar_event(
                "Test Event",
                datetime.now(),
                datetime.now() + timedelta(hours=1),
                "Test description"
            )
            
            assert result['status'] == 'created'
            assert result['event_id'] == 'event123'
    
    def test_document_type_detection(self, agent):
        """Test document type auto-detection"""
        assert agent._detect_document_type("tender_notice.pdf") == "tender_notice"
        assert agent._detect_document_type("bill_of_quantities.pdf") == "boq"
        assert agent._detect_document_type("contract_agreement.pdf") == "contract"
        assert agent._detect_document_type("unknown_document.pdf") == "document"
    
    @pytest.mark.asyncio
    async def test_task_status_retrieval(self, agent):
        """Test task status retrieval"""
        # Mock Redis cache hit
        mock_task_data = {
            'task_id': 'test123',
            'task_type': 'test',
            'status': 'completed',
            'result': {},
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'execution_time_ms': 1000.0,
            'confidence_score': 0.8
        }
        
        agent.dependencies.redis_client.get.return_value = json.dumps(mock_task_data, default=str)
        
        result = await agent.get_task_status('test123')
        
        assert isinstance(result, TaskResult)
        assert result.task_id == 'test123'
        assert result.status == 'completed'
    
    @pytest.mark.asyncio
    async def test_cleanup(self, agent):
        """Test resource cleanup"""
        agent.dependencies.http_client.aclose = AsyncMock()
        agent.dependencies.redis_client.close = AsyncMock()
        agent.dependencies.mongodb_client.close = Mock()
        agent.dependencies.crawl4ai_crawler.aclose = AsyncMock()
        
        await agent.cleanup()
        
        agent.dependencies.http_client.aclose.assert_called_once()
        agent.dependencies.redis_client.close.assert_called_once()
        agent.dependencies.mongodb_client.close.assert_called_once()

# Integration tests (require actual services)
@pytest.mark.integration
class TestEnhancedPydanticAgentIntegration:
    """Integration tests requiring actual services"""
    
    @pytest.mark.asyncio
    async def test_full_workflow_integration(self):
        """Test full workflow with real services (if available)"""
        # This test would require actual database connections
        # and API keys to run properly
        pytest.skip("Integration test requires live services")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
