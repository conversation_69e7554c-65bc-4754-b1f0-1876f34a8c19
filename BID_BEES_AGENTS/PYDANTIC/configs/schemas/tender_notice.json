{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Tender Notice Extraction Schema", "description": "Schema for extracting structured information from tender notice documents", "type": "object", "properties": {"tender_number": {"type": "string", "description": "The official tender or bid reference number"}, "tender_title": {"type": "string", "description": "The title or short description of the tender"}, "closing_date": {"type": "string", "format": "date-time", "description": "The final submission date and time in ISO 8601 format"}, "issuer_details": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the issuing organization"}, "contact_person": {"type": "string", "description": "Primary contact person for the tender"}, "contact_email": {"type": "string", "format": "email", "description": "Contact email address"}, "contact_phone": {"type": "string", "description": "Contact phone number"}, "address": {"type": "string", "description": "Physical address of the issuing organization"}, "website": {"type": "string", "format": "uri", "description": "Organization website URL"}}, "required": ["name"]}, "briefing_sessions": {"type": "array", "description": "Array of all briefing or site meetings mentioned", "items": {"type": "object", "properties": {"date_time": {"type": "string", "format": "date-time", "description": "Date and time of the briefing in ISO 8601 format"}, "location": {"type": "string", "description": "Location of the briefing session"}, "is_compulsory": {"type": "boolean", "description": "Whether attendance is mandatory"}, "rsvp_details": {"type": "string", "description": "Instructions for RSVP including deadline and contact info"}, "required_qualifications": {"type": "string", "description": "Specific certifications or roles required for attendees"}, "required_ppe": {"type": "string", "description": "Personal Protective Equipment required (e.g., safety boots, hard hat)"}, "agenda": {"type": "string", "description": "Meeting agenda or topics to be covered"}}, "required": ["date_time", "location", "is_compulsory"]}}, "submission_method": {"type": "string", "description": "Physical address or digital method for tender submission"}, "estimated_value": {"type": "number", "description": "Estimated contract value in local currency"}, "currency": {"type": "string", "description": "Currency code (e.g., USD, ZAR, EUR)"}, "project_duration": {"type": "string", "description": "Expected project duration or completion timeline"}, "requirements": {"type": "array", "description": "List of key requirements and qualifications", "items": {"type": "string"}}, "eligibility_criteria": {"type": "object", "properties": {"minimum_experience_years": {"type": "number", "description": "Minimum years of experience required"}, "required_certifications": {"type": "array", "items": {"type": "string"}, "description": "List of required certifications"}, "financial_requirements": {"type": "string", "description": "Financial capacity or turnover requirements"}, "geographic_restrictions": {"type": "string", "description": "Geographic limitations for eligible bidders"}}}, "evaluation_criteria": {"type": "object", "properties": {"technical_score_weight": {"type": "number", "description": "Percentage weight for technical evaluation"}, "financial_score_weight": {"type": "number", "description": "Percentage weight for financial evaluation"}, "experience_weight": {"type": "number", "description": "Percentage weight for experience evaluation"}, "other_criteria": {"type": "array", "items": {"type": "string"}, "description": "Other evaluation criteria"}}}, "documents_required": {"type": "array", "description": "List of documents required for submission", "items": {"type": "string"}}, "project_location": {"type": "string", "description": "Location where the project will be executed"}, "project_description": {"type": "string", "description": "Detailed description of the project or services required"}, "scope_of_work": {"type": "array", "description": "Detailed scope of work items", "items": {"type": "string"}}, "payment_terms": {"type": "string", "description": "Payment terms and conditions"}, "performance_guarantee": {"type": "object", "properties": {"required": {"type": "boolean", "description": "Whether performance guarantee is required"}, "percentage": {"type": "number", "description": "Percentage of contract value for guarantee"}, "duration": {"type": "string", "description": "Duration of the performance guarantee"}}}, "bid_validity_period": {"type": "string", "description": "Period for which the bid must remain valid"}, "late_submission_policy": {"type": "string", "description": "Policy regarding late submissions"}, "clarification_deadline": {"type": "string", "format": "date-time", "description": "Deadline for submitting clarification questions"}, "amendment_history": {"type": "array", "description": "History of amendments to the tender", "items": {"type": "object", "properties": {"amendment_number": {"type": "string"}, "date": {"type": "string", "format": "date"}, "description": {"type": "string"}}}}, "contact_for_queries": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "office_hours": {"type": "string"}}}}, "required": ["tender_number", "tender_title", "closing_date", "issuer_details", "submission_method"]}