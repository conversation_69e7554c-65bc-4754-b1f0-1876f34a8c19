{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Bill of Quantities (BOQ) Extraction Schema", "description": "Schema for extracting structured information from Bill of Quantities documents", "type": "object", "properties": {"project_title": {"type": "string", "description": "Title of the project"}, "project_number": {"type": "string", "description": "Project reference number"}, "client_details": {"type": "object", "properties": {"name": {"type": "string", "description": "Client organization name"}, "contact_person": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string", "format": "email"}}}, "consultant_details": {"type": "object", "properties": {"name": {"type": "string", "description": "Consulting firm name"}, "contact_person": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string", "format": "email"}}}, "project_location": {"type": "string", "description": "Location where the project will be executed"}, "currency": {"type": "string", "description": "Currency code for all monetary values"}, "measurement_units": {"type": "string", "description": "Standard measurement units used (metric/imperial)"}, "boq_sections": {"type": "array", "description": "Main sections of the BOQ", "items": {"type": "object", "properties": {"section_number": {"type": "string", "description": "Section identifier (e.g., 01, 02, A, B)"}, "section_title": {"type": "string", "description": "Title of the section"}, "section_description": {"type": "string", "description": "Detailed description of the section"}, "items": {"type": "array", "description": "Items within this section", "items": {"type": "object", "properties": {"item_number": {"type": "string", "description": "Item reference number"}, "description": {"type": "string", "description": "Detailed description of the work item"}, "unit": {"type": "string", "description": "Unit of measurement (m², m³, kg, no., etc.)"}, "quantity": {"type": "number", "description": "Quantity required"}, "unit_rate": {"type": "number", "description": "Rate per unit"}, "total_amount": {"type": "number", "description": "Total amount (quantity × unit_rate)"}, "specifications": {"type": "string", "description": "Technical specifications or standards"}, "material_specifications": {"type": "array", "items": {"type": "object", "properties": {"material_type": {"type": "string"}, "grade_or_class": {"type": "string"}, "standard": {"type": "string"}, "supplier_requirements": {"type": "string"}}}}, "labor_requirements": {"type": "object", "properties": {"skill_level": {"type": "string", "description": "Required skill level (skilled, semi-skilled, unskilled)"}, "estimated_hours": {"type": "number", "description": "Estimated labor hours"}, "crew_size": {"type": "number", "description": "Recommended crew size"}}}, "equipment_requirements": {"type": "array", "items": {"type": "object", "properties": {"equipment_type": {"type": "string"}, "capacity": {"type": "string"}, "duration": {"type": "string"}}}}, "notes": {"type": "string", "description": "Additional notes or special instructions"}}, "required": ["item_number", "description", "unit", "quantity"]}}, "section_total": {"type": "number", "description": "Total amount for this section"}}, "required": ["section_number", "section_title", "items"]}}, "summary": {"type": "object", "properties": {"subtotal": {"type": "number", "description": "Subtotal of all sections"}, "contingency": {"type": "object", "properties": {"percentage": {"type": "number"}, "amount": {"type": "number"}}}, "vat_tax": {"type": "object", "properties": {"percentage": {"type": "number"}, "amount": {"type": "number"}}}, "total_project_cost": {"type": "number", "description": "Final total project cost"}}}, "general_conditions": {"type": "array", "description": "General conditions and assumptions", "items": {"type": "string"}}, "exclusions": {"type": "array", "description": "Items specifically excluded from the BOQ", "items": {"type": "string"}}, "assumptions": {"type": "array", "description": "Assumptions made in preparing the BOQ", "items": {"type": "string"}}, "validity_period": {"type": "string", "description": "Period for which the BOQ prices are valid"}, "escalation_clause": {"type": "string", "description": "Price escalation terms and conditions"}, "payment_schedule": {"type": "array", "description": "Proposed payment schedule", "items": {"type": "object", "properties": {"milestone": {"type": "string"}, "percentage": {"type": "number"}, "amount": {"type": "number"}, "description": {"type": "string"}}}}, "revision_history": {"type": "array", "description": "History of BOQ revisions", "items": {"type": "object", "properties": {"revision_number": {"type": "string"}, "date": {"type": "string", "format": "date"}, "description": {"type": "string"}, "prepared_by": {"type": "string"}}}}, "preparation_date": {"type": "string", "format": "date", "description": "Date when the BOQ was prepared"}, "prepared_by": {"type": "object", "properties": {"name": {"type": "string"}, "title": {"type": "string"}, "organization": {"type": "string"}, "signature": {"type": "string"}}}}, "required": ["project_title", "boq_sections", "currency"]}