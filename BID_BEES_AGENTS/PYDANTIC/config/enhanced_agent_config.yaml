# Enhanced Pydantic Agent Configuration

databases:
  mongodb:
    url: "mongodb://localhost:27017"
    database: "enhanced_enterprise_agent"
  redis:
    url: "redis://localhost:6379"
  qdrant:
    host: "localhost"
    port: 6333
  milvus:
    host: "localhost"
    port: 19530

models:
  primary: "gpt-4-turbo-preview"
  reasoning: "claude-3-sonnet-20240229"
  document_extraction: "gemini-1.5-pro"
  embedding: "all-MiniLM-L6-v2"

integrations:
  brave_search:
    api_key: "${BRAVE_API_KEY}"
  google:
    credentials_path: "credentials/google_creds.json"
    custom_search_engine_id: "${GOOGLE_CSE_ID}"
    api_key: "${GOOGLE_API_KEY}"
  slack:
    token: "${SLACK_BOT_TOKEN}"
  smtp:
    server: "smtp.gmail.com"
    port: 587
    from_email: "<EMAIL>"
    username: "${SMTP_USERNAME}"
    password: "${SMTP_PASSWORD}"
  openai:
    api_key: "${OPENAI_API_KEY}"

crawl4ai:
  max_concurrent: 5
  delay_between_requests: 1.0
  timeout: 30
  user_agent: "EnhancedPydanticAgent/1.0"

# Document processing schemas
document_schemas:
  tender_notice: "configs/schemas/tender_notice.json"
  boq: "configs/schemas/boq.json"
  contract: "configs/schemas/contract.json"

# Workflow configurations
workflows:
  tender_analysis:
    default_timeout: 300
    max_concurrent_crawls: 3
  market_research:
    search_sources: ["brave", "google"]
    max_results_per_source: 10
  document_intelligence:
    supported_formats: [".pdf", ".docx", ".txt", ".png", ".jpg"]
    max_file_size_mb: 50
  competitive_analysis:
    max_competitors: 10
    analysis_depth: "comprehensive"

# Memory and caching settings
memory:
  redis_ttl_hours: 24
  graph_max_nodes: 10000
  vector_similarity_threshold: 0.7

# Notification settings
notifications:
  email:
    enabled: true
    templates_path: "templates/email/"
  slack:
    enabled: true
    default_channel: "#agent-notifications"
  calendar:
    enabled: true
    default_reminder_minutes: [60, 1440]  # 1 hour and 1 day

# Security settings
security:
  rate_limiting:
    enabled: true
    requests_per_minute: 60
  data_retention:
    logs_days: 30
    cache_hours: 24
  encryption:
    enabled: true
    key_rotation_days: 90

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/enhanced_agent.log"
  max_size_mb: 100
  backup_count: 5

# Performance settings
performance:
  max_concurrent_tasks: 10
  task_timeout_seconds: 300
  memory_limit_mb: 2048
  cpu_limit_percent: 80
