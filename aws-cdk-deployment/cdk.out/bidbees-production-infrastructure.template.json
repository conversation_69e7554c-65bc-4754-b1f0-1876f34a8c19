{"Description": "BidBees Infrastructure Stack - VPC, Databases, Security", "Resources": {"BidBeesVPC585BC663": {"Type": "AWS::EC2::VPC", "Properties": {"CidrBlock": "10.0.0.0/16", "EnableDnsHostnames": true, "EnableDnsSupport": true, "InstanceTenancy": "default", "Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/Resource"}}, "BidBeesVPCPublicSubnet1Subnet4EEAC752": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1a", "CidrBlock": "10.0.0.0/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/Subnet"}}, "BidBeesVPCPublicSubnet1RouteTable789E5F96": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/RouteTable"}}, "BidBeesVPCPublicSubnet1RouteTableAssociation799F999F": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "BidBeesVPCPublicSubnet1RouteTable789E5F96"}, "SubnetId": {"Ref": "BidBeesVPCPublicSubnet1Subnet4EEAC752"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/RouteTableAssociation"}}, "BidBeesVPCPublicSubnet1DefaultRoute42081087": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "BidBeesVPCIGW8F5454E0"}, "RouteTableId": {"Ref": "BidBeesVPCPublicSubnet1RouteTable789E5F96"}}, "DependsOn": ["BidBeesVPCVPCGWB2A3C941"], "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/DefaultRoute"}}, "BidBeesVPCPublicSubnet1EIP62C43981": {"Type": "AWS::EC2::EIP", "Properties": {"Domain": "vpc", "Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/EIP"}}, "BidBeesVPCPublicSubnet1NATGateway1DD5DF38": {"Type": "AWS::EC2::NatGateway", "Properties": {"AllocationId": {"Fn::GetAtt": ["BidBeesVPCPublicSubnet1EIP62C43981", "AllocationId"]}, "SubnetId": {"Ref": "BidBeesVPCPublicSubnet1Subnet4EEAC752"}, "Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1"}]}, "DependsOn": ["BidBeesVPCPublicSubnet1DefaultRoute42081087", "BidBeesVPCPublicSubnet1RouteTableAssociation799F999F"], "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/NATGateway"}}, "BidBeesVPCPublicSubnet2SubnetABA339EE": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1b", "CidrBlock": "********/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/Subnet"}}, "BidBeesVPCPublicSubnet2RouteTable4EF11C22": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/RouteTable"}}, "BidBeesVPCPublicSubnet2RouteTableAssociation3B5084C2": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "BidBeesVPCPublicSubnet2RouteTable4EF11C22"}, "SubnetId": {"Ref": "BidBeesVPCPublicSubnet2SubnetABA339EE"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/RouteTableAssociation"}}, "BidBeesVPCPublicSubnet2DefaultRouteD59FFAE6": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "BidBeesVPCIGW8F5454E0"}, "RouteTableId": {"Ref": "BidBeesVPCPublicSubnet2RouteTable4EF11C22"}}, "DependsOn": ["BidBeesVPCVPCGWB2A3C941"], "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/DefaultRoute"}}, "BidBeesVPCPublicSubnet2EIPB0F97068": {"Type": "AWS::EC2::EIP", "Properties": {"Domain": "vpc", "Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/EIP"}}, "BidBeesVPCPublicSubnet2NATGateway85C73D6B": {"Type": "AWS::EC2::NatGateway", "Properties": {"AllocationId": {"Fn::GetAtt": ["BidBeesVPCPublicSubnet2EIPB0F97068", "AllocationId"]}, "SubnetId": {"Ref": "BidBeesVPCPublicSubnet2SubnetABA339EE"}, "Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2"}]}, "DependsOn": ["BidBeesVPCPublicSubnet2DefaultRouteD59FFAE6", "BidBeesVPCPublicSubnet2RouteTableAssociation3B5084C2"], "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/NATGateway"}}, "BidBeesVPCPrivateSubnet1Subnet9CC74C9A": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1a", "CidrBlock": "********/24", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Private"}, {"Key": "aws-cdk:subnet-type", "Value": "Private"}, {"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1/Subnet"}}, "BidBeesVPCPrivateSubnet1RouteTable1FD839AD": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1/RouteTable"}}, "BidBeesVPCPrivateSubnet1RouteTableAssociationB26B135E": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "BidBeesVPCPrivateSubnet1RouteTable1FD839AD"}, "SubnetId": {"Ref": "BidBeesVPCPrivateSubnet1Subnet9CC74C9A"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1/RouteTableAssociation"}}, "BidBeesVPCPrivateSubnet1DefaultRouteC901A9C5": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "NatGatewayId": {"Ref": "BidBeesVPCPublicSubnet1NATGateway1DD5DF38"}, "RouteTableId": {"Ref": "BidBeesVPCPrivateSubnet1RouteTable1FD839AD"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1/DefaultRoute"}}, "BidBeesVPCPrivateSubnet2Subnet567B500C": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1b", "CidrBlock": "********/24", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Private"}, {"Key": "aws-cdk:subnet-type", "Value": "Private"}, {"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2/Subnet"}}, "BidBeesVPCPrivateSubnet2RouteTable2F5D94CF": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2/RouteTable"}}, "BidBeesVPCPrivateSubnet2RouteTableAssociation62D37E43": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "BidBeesVPCPrivateSubnet2RouteTable2F5D94CF"}, "SubnetId": {"Ref": "BidBeesVPCPrivateSubnet2Subnet567B500C"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2/RouteTableAssociation"}}, "BidBeesVPCPrivateSubnet2DefaultRoute36879FFB": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "NatGatewayId": {"Ref": "BidBeesVPCPublicSubnet2NATGateway85C73D6B"}, "RouteTableId": {"Ref": "BidBeesVPCPrivateSubnet2RouteTable2F5D94CF"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2/DefaultRoute"}}, "BidBeesVPCDatabaseSubnet1Subnet2DF44E8E": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1a", "CidrBlock": "********/24", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Database"}, {"Key": "aws-cdk:subnet-type", "Value": "Isolated"}, {"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet1"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet1/Subnet"}}, "BidBeesVPCDatabaseSubnet1RouteTableB6875D2B": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet1"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet1/RouteTable"}}, "BidBeesVPCDatabaseSubnet1RouteTableAssociation8EC812B9": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "BidBeesVPCDatabaseSubnet1RouteTableB6875D2B"}, "SubnetId": {"Ref": "BidBeesVPCDatabaseSubnet1Subnet2DF44E8E"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet1/RouteTableAssociation"}}, "BidBeesVPCDatabaseSubnet2Subnet601B8C61": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1b", "CidrBlock": "********/24", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Database"}, {"Key": "aws-cdk:subnet-type", "Value": "Isolated"}, {"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet2"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet2/Subnet"}}, "BidBeesVPCDatabaseSubnet2RouteTable8ACC6BCC": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet2"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet2/RouteTable"}}, "BidBeesVPCDatabaseSubnet2RouteTableAssociationC0D69C0D": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "BidBeesVPCDatabaseSubnet2RouteTable8ACC6BCC"}, "SubnetId": {"Ref": "BidBeesVPCDatabaseSubnet2Subnet601B8C61"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet2/RouteTableAssociation"}}, "BidBeesVPCIGW8F5454E0": {"Type": "AWS::EC2::InternetGateway", "Properties": {"Tags": [{"Key": "Name", "Value": "bidbees-production-infrastructure/BidBeesVPC"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/IGW"}}, "BidBeesVPCVPCGWB2A3C941": {"Type": "AWS::EC2::VPCGatewayAttachment", "Properties": {"InternetGatewayId": {"Ref": "BidBeesVPCIGW8F5454E0"}, "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/VPCGW"}}, "BidBeesVPCRestrictDefaultSecurityGroupCustomResourceF930F4E4": {"Type": "Custom::VpcRestrictDefaultSG", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E", "<PERSON><PERSON>"]}, "DefaultSecurityGroupId": {"Fn::GetAtt": ["BidBeesVPC585BC663", "DefaultSecurityGroup"]}, "Account": "************"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesVPC/RestrictDefaultSecurityGroupCustomResource/Default"}}, "CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}]}, "ManagedPolicyArns": [{"Fn::Sub": "arn:${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"}], "Policies": [{"PolicyName": "Inline", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:AuthorizeSecurityGroupIngress", "ec2:AuthorizeSecurityGroupEgress", "ec2:RevokeSecurityGroupIngress", "ec2:RevokeSecurityGroupEgress"], "Resource": [{"Fn::Join": ["", ["arn:aws:ec2:us-east-1:************:security-group/", {"Fn::GetAtt": ["BidBeesVPC585BC663", "DefaultSecurityGroup"]}]]}]}]}}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role"}}, "CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-************-us-east-1", "S3Key": "7f18a11296f35510ee16538afec983ed6312e12afbf81b777089a9f8e34e2474.zip"}, "Timeout": 900, "MemorySize": 128, "Handler": "__entrypoint__.handler", "Role": {"Fn::GetAtt": ["CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Description": "Lambda function for removing all inbound/outbound rules from the VPC default security group"}, "DependsOn": ["CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0"], "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler", "aws:asset:path": "asset.7f18a11296f35510ee16538afec983ed6312e12afbf81b777089a9f8e34e2474", "aws:asset:property": "Code"}}, "BidBeesCluster2DD1D24B": {"Type": "AWS::ECS::Cluster", "Properties": {"ClusterName": "bidbees-production-cluster", "ClusterSettings": [{"Name": "containerInsights", "Value": "enabled"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/BidBeesCluster/Resource"}}, "ALBSecurityGroup29A3BDEF": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for Application Load Balancer", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "SecurityGroupIngress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow HTTP traffic", "FromPort": 80, "IpProtocol": "tcp", "ToPort": 80}, {"CidrIp": "0.0.0.0/0", "Description": "Allow HTTPS traffic", "FromPort": 443, "IpProtocol": "tcp", "ToPort": 443}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ALBSecurityGroup/Resource"}}, "ECSSecurityGroupA14DBE7D": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for ECS services", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/Resource"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8CALLTRAFFIC405FD737": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Allow traffic from ALB", "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "-1", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:ALL TRAFFIC"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80800CED177A": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8080, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8080}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8080"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C808171517F5D": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8081, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8081}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8081"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C808239D14437": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8082, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8082}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8082"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C808362E41F64": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8083, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8083}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8083"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80846E1678A0": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8084, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8084}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8084"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8085B6701201": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8085, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8085}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8085"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8086B3532922": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8086, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8086}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8086"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C808742F0A39C": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8087, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8087}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8087"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8088492E5DA8": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8088, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8088}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8088"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8089F81403EE": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8089, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8089}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8089"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8090FA3CAD68": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8090, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8090}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8090"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8091F5625C5B": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8091, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8091}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8091"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80923633C26A": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8092, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8092}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8092"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80932EC58A4B": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8093, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8093}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8093"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C809404C76356": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8094, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8094}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8094"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80953B95A0DF": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8095, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8095}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8095"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C809624437E80": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8096, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8096}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8096"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8097FBA0B568": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8097, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8097}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8097"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8098E8F5FEFC": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8098, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8098}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8098"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8099B72CE179": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8099, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8099}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8099"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C810031D90A71": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8100, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8100}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8100"}}, "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8101D49BDFD7": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 8101, "GroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "ToPort": 8101}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8101"}}, "RDSSecurityGroup6BF2CF10": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for RDS PostgreSQL", "SecurityGroupEgress": [{"CidrIp": "***************/32", "Description": "Disallow all traffic", "FromPort": 252, "IpProtocol": "icmp", "ToPort": 86}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/RDSSecurityGroup/Resource"}}, "RDSSecurityGroupfrombidbeesproductioninfrastructureECSSecurityGroup739EDCD1543235D61678": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Allow PostgreSQL access from ECS", "FromPort": 5432, "GroupId": {"Fn::GetAtt": ["RDSSecurityGroup6BF2CF10", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "ToPort": 5432}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/RDSSecurityGroup/from bidbeesproductioninfrastructureECSSecurityGroup739EDCD1:5432"}}, "RedisSecurityGroupB05951F6": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for Redis ElastiCache", "SecurityGroupEgress": [{"CidrIp": "***************/32", "Description": "Disallow all traffic", "FromPort": 252, "IpProtocol": "icmp", "ToPort": 86}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/RedisSecurityGroup/Resource"}}, "RedisSecurityGroupfrombidbeesproductioninfrastructureECSSecurityGroup739EDCD16379348A174C": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Allow Redis access from ECS", "FromPort": 6379, "GroupId": {"Fn::GetAtt": ["RedisSecurityGroupB05951F6", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "ToPort": 6379}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/RedisSecurityGroup/from bidbeesproductioninfrastructureECSSecurityGroup739EDCD1:6379"}}, "DocDBSecurityGroup4B2123E2": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for DocumentDB", "SecurityGroupEgress": [{"CidrIp": "***************/32", "Description": "Disallow all traffic", "FromPort": 252, "IpProtocol": "icmp", "ToPort": 86}], "VpcId": {"Ref": "BidBeesVPC585BC663"}}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DocDBSecurityGroup/Resource"}}, "DocDBSecurityGroupfrombidbeesproductioninfrastructureECSSecurityGroup739EDCD12701740CC1683": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Allow DocumentDB access from ECS", "FromPort": 27017, "GroupId": {"Fn::GetAtt": ["DocDBSecurityGroup4B2123E2", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "ToPort": 27017}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DocDBSecurityGroup/from bidbeesproductioninfrastructureECSSecurityGroup739EDCD1:27017"}}, "DBSecretD58955BC": {"Type": "AWS::<PERSON>Manager::Secret", "Properties": {"Description": "RDS PostgreSQL master password", "GenerateSecretString": {"ExcludeCharacters": "\"@/\\", "GenerateStringKey": "password", "PasswordLength": 32, "SecretStringTemplate": "{\"username\":\"bidbees_admin\"}"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DBSecret/Resource"}}, "DBSecretAttachmentC565A14F": {"Type": "AWS::SecretsManager::SecretTargetAttachment", "Properties": {"SecretId": {"Ref": "DBSecretD58955BC"}, "TargetId": {"Ref": "PostgreSQLInstanceD9AD3CF0"}, "TargetType": "AWS::RDS::DBInstance"}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DBSecret/Attachment/Resource"}}, "DocDBSecret25894C0A": {"Type": "AWS::<PERSON>Manager::Secret", "Properties": {"Description": "DocumentDB master password", "GenerateSecretString": {"ExcludeCharacters": "\"@/\\", "GenerateStringKey": "password", "PasswordLength": 32, "SecretStringTemplate": "{\"username\":\"bidbees_admin\"}"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DocDBSecret/Resource"}}, "PostgreSQLInstanceSubnetGroup80C84F06": {"Type": "AWS::RDS::DBSubnetGroup", "Properties": {"DBSubnetGroupDescription": "Subnet group for PostgreSQLInstance database", "SubnetIds": [{"Ref": "BidBeesVPCDatabaseSubnet1Subnet2DF44E8E"}, {"Ref": "BidBeesVPCDatabaseSubnet2Subnet601B8C61"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/PostgreSQLInstance/SubnetGroup/Default"}}, "PostgreSQLInstanceD9AD3CF0": {"Type": "AWS::RDS::DBInstance", "Properties": {"AllocatedStorage": "100", "BackupRetentionPeriod": 7, "CopyTagsToSnapshot": true, "DBInstanceClass": "db.t3.micro", "DBName": "bidbees", "DBSubnetGroupName": {"Ref": "PostgreSQLInstanceSubnetGroup80C84F06"}, "DeletionProtection": false, "Engine": "postgres", "EngineVersion": "15.4", "MasterUserPassword": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "DBSecretD58955BC"}, ":SecretString:password::}}"]]}, "MasterUsername": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "DBSecretD58955BC"}, ":SecretString:username::}}"]]}, "MultiAZ": true, "PubliclyAccessible": false, "StorageEncrypted": true, "StorageType": "gp2", "VPCSecurityGroups": [{"Fn::GetAtt": ["RDSSecurityGroup6BF2CF10", "GroupId"]}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/PostgreSQLInstance/Resource"}}, "RedisSubnetGroup": {"Type": "AWS::ElastiCache::SubnetGroup", "Properties": {"Description": "Subnet group for Redis cluster", "SubnetIds": [{"Ref": "BidBeesVPCDatabaseSubnet1Subnet2DF44E8E"}, {"Ref": "BidBeesVPCDatabaseSubnet2Subnet601B8C61"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/RedisSubnetGroup"}}, "RedisCluster": {"Type": "AWS::ElastiCache::ReplicationGroup", "Properties": {"AtRestEncryptionEnabled": true, "AutomaticFailoverEnabled": true, "CacheNodeType": "cache.t3.micro", "CacheSubnetGroupName": {"Ref": "RedisSubnetGroup"}, "Engine": "redis", "EngineVersion": "7.0", "MultiAZEnabled": true, "NumCacheClusters": 2, "Port": 6379, "ReplicationGroupDescription": "Redis cluster for BidBees", "SecurityGroupIds": [{"Fn::GetAtt": ["RedisSecurityGroupB05951F6", "GroupId"]}], "TransitEncryptionEnabled": true}, "DependsOn": ["RedisSubnetGroup"], "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/RedisCluster"}}, "DocumentDBClusterSubnets6AC07BFC": {"Type": "AWS::DocDB::DBSubnetGroup", "Properties": {"DBSubnetGroupDescription": "Subnets for DocumentDBCluster database", "SubnetIds": [{"Ref": "BidBeesVPCDatabaseSubnet1Subnet2DF44E8E"}, {"Ref": "BidBeesVPCDatabaseSubnet2Subnet601B8C61"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DocumentDBCluster/Subnets"}}, "DocumentDBCluster51C53375": {"Type": "AWS::DocDB::DBCluster", "Properties": {"BackupRetentionPeriod": 7, "DBSubnetGroupName": {"Ref": "DocumentDBClusterSubnets6AC07BFC"}, "MasterUserPassword": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "DocDBSecret25894C0A"}, ":SecretString:password::}}"]]}, "MasterUsername": "bidbees_admin", "StorageEncrypted": true, "VpcSecurityGroupIds": [{"Fn::GetAtt": ["DocDBSecurityGroup4B2123E2", "GroupId"]}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DocumentDBCluster/Resource"}}, "DocumentDBClusterInstance18A80109A": {"Type": "AWS::DocDB::DBInstance", "Properties": {"DBClusterIdentifier": {"Ref": "DocumentDBCluster51C53375"}, "DBInstanceClass": "db.t3.medium"}, "DependsOn": ["BidBeesVPCDatabaseSubnet1RouteTableAssociation8EC812B9", "BidBeesVPCDatabaseSubnet2RouteTableAssociationC0D69C0D"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DocumentDBCluster/Instance1"}}, "DocumentDBClusterInstance22CB360D6": {"Type": "AWS::DocDB::DBInstance", "Properties": {"DBClusterIdentifier": {"Ref": "DocumentDBCluster51C53375"}, "DBInstanceClass": "db.t3.medium"}, "DependsOn": ["BidBeesVPCDatabaseSubnet1RouteTableAssociation8EC812B9", "BidBeesVPCDatabaseSubnet2RouteTableAssociationC0D69C0D"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/DocumentDBCluster/Instance2"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/31QQW7CQAx8C/dlGzj0TkNVcamiBHGtHMcNC8kuWntBKMrfqyQ0oS3qyePxyDP2Ui+iSEczuPAci+O8MrluMgE8KrjwR0O41M3uhCr+tLskVknIK4NZyC1Jx00odUFoC3lFEz9xK2aHBsQ4O4o78LpJuvIO8gZCF7iqxJszCE2LN1bIWxoFQ5JbtxIB3NdkRWWEwRu5vnkXTn2Gf4mNLT0xt4qQdRNXgYV8p7rBVjGhJ+EaLJTkdZP1/W1Rh4ayBV+S3AUZBb8nrfIF62YNAjkwbSwLWCQ13DrGXL/8Ib6lraIKWAwC7kk346NHaUqnymD/555rVeGwyCfTu0Mf+PyYjqatSold8EgqDiyuntrO8eEo8e5sCvJqxUySCZTGlq2yriB94KfzcqkXzzqaHdiYuQ9WTE06HeoX1kc/sZUCAAA="}, "Metadata": {"aws:cdk:path": "bidbees-production-infrastructure/CDKMetadata/Default"}}}, "Outputs": {"ExportsOutputFnGetAttALBSecurityGroup29A3BDEFGroupId2A9BC0BF": {"Value": {"Fn::GetAtt": ["ALBSecurityGroup29A3BDEF", "GroupId"]}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputFnGetAttALBSecurityGroup29A3BDEFGroupId2A9BC0BF"}}, "ExportsOutputRefBidBeesVPCPublicSubnet1Subnet4EEAC7520D6A904D": {"Value": {"Ref": "BidBeesVPCPublicSubnet1Subnet4EEAC752"}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPublicSubnet1Subnet4EEAC7520D6A904D"}}, "ExportsOutputRefBidBeesVPCPublicSubnet2SubnetABA339EE0B19CC48": {"Value": {"Ref": "BidBeesVPCPublicSubnet2SubnetABA339EE"}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPublicSubnet2SubnetABA339EE0B19CC48"}}, "ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F": {"Value": {"Ref": "BidBeesCluster2DD1D24B"}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, "ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2": {"Value": {"Ref": "BidBeesVPCPrivateSubnet1Subnet9CC74C9A"}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}}, "ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C": {"Value": {"Ref": "BidBeesVPCPrivateSubnet2Subnet567B500C"}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}}, "ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6": {"Value": {"Fn::GetAtt": ["ECSSecurityGroupA14DBE7D", "GroupId"]}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}}, "ExportsOutputRefBidBeesVPC585BC6635F18E126": {"Value": {"Ref": "BidBeesVPC585BC663"}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "ExportsOutputRefPostgreSQLInstanceD9AD3CF098D2E69C": {"Value": {"Ref": "PostgreSQLInstanceD9AD3CF0"}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputRefPostgreSQLInstanceD9AD3CF098D2E69C"}}, "ExportsOutputFnGetAttRedisClusterPrimaryEndPointAddressCFB16E1D": {"Value": {"Fn::GetAtt": ["RedisCluster", "PrimaryEndPoint.Address"]}, "Export": {"Name": "bidbees-production-infrastructure:ExportsOutputFnGetAttRedisClusterPrimaryEndPointAddressCFB16E1D"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}