{"Description": "BidBees Monitoring Stack - CloudWatch, Alarms, Dashboards", "Resources": {"BidBeesAlerts55A686C2": {"Type": "AWS::SNS::Topic", "Properties": {"DisplayName": "BidBees System Alerts", "TopicName": "bidbees-alerts"}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/BidBeesAlerts/Resource"}}, "BidBeesDashboardBF6956B4": {"Type": "AWS::CloudWatch::Dashboard", "Properties": {"DashboardBody": {"Fn::Join": ["", ["{\"widgets\":[{\"type\":\"metric\",\"width\":12,\"height\":6,\"x\":0,\"y\":0,\"properties\":{\"view\":\"timeSeries\",\"title\":\"ALB Request Count\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"AWS/ApplicationELB\",\"RequestCount\",\"LoadBalancer\",\"", {"Fn::ImportValue": "bidbees-production-application:ExportsOutputFnGetAttBidBeesALBDEF3E75ALoadBalancerFullNameE6F369EB"}, "\",{\"stat\":\"Sum\"}]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":12,\"height\":6,\"x\":12,\"y\":0,\"properties\":{\"view\":\"timeSeries\",\"title\":\"ALB Response Times\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"AWS/ApplicationELB\",\"TargetResponseTime\",\"LoadBalancer\",\"", {"Fn::ImportValue": "bidbees-production-application:ExportsOutputFnGetAttBidBeesALBDEF3E75ALoadBalancerFullNameE6F369EB"}, "\"]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":12,\"height\":6,\"x\":0,\"y\":6,\"properties\":{\"view\":\"timeSeries\",\"title\":\"ECS CPU Utilization\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"api-gateway\",{\"label\":\"api-gateway\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"auth-service\",{\"label\":\"auth-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"user-service\",{\"label\":\"user-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"tender-service\",{\"label\":\"tender-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"bidding-service\",{\"label\":\"bidding-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"bee-tasks-service\",{\"label\":\"bee-tasks-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"notification-service\",{\"label\":\"notification-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"document-service\",{\"label\":\"document-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"map-service\",{\"label\":\"map-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"analytics-service\",{\"label\":\"analytics-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"payment-service\",{\"label\":\"payment-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"courier-service\",{\"label\":\"courier-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"transport-service\",{\"label\":\"transport-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"supplier-service\",{\"label\":\"supplier-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"ml-service\",{\"label\":\"ml-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"queenbee-anchor-service\",{\"label\":\"queenbee-anchor-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"docling-processor\",{\"label\":\"docling-processor\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"student-verification-service\",{\"label\":\"student-verification-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"kafka-service\",{\"label\":\"kafka-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"contractorsync-service\",{\"label\":\"contractorsync-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"skillsync-service\",{\"label\":\"skillsync-service\"}],[\"AWS/ECS\",\"CPUUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"toolsync-service\",{\"label\":\"toolsync-service\"}]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":12,\"height\":6,\"x\":12,\"y\":6,\"properties\":{\"view\":\"timeSeries\",\"title\":\"ECS Memory Utilization\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"api-gateway\",{\"label\":\"api-gateway\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"auth-service\",{\"label\":\"auth-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"user-service\",{\"label\":\"user-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"tender-service\",{\"label\":\"tender-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"bidding-service\",{\"label\":\"bidding-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"bee-tasks-service\",{\"label\":\"bee-tasks-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"notification-service\",{\"label\":\"notification-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"document-service\",{\"label\":\"document-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"map-service\",{\"label\":\"map-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"analytics-service\",{\"label\":\"analytics-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"payment-service\",{\"label\":\"payment-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"courier-service\",{\"label\":\"courier-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"transport-service\",{\"label\":\"transport-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"supplier-service\",{\"label\":\"supplier-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"ml-service\",{\"label\":\"ml-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"queenbee-anchor-service\",{\"label\":\"queenbee-anchor-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"docling-processor\",{\"label\":\"docling-processor\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"student-verification-service\",{\"label\":\"student-verification-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"kafka-service\",{\"label\":\"kafka-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"contractorsync-service\",{\"label\":\"contractorsync-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"skillsync-service\",{\"label\":\"skillsync-service\"}],[\"AWS/ECS\",\"MemoryUtilization\",\"ClusterName\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "\",\"ServiceName\",\"toolsync-service\",{\"label\":\"toolsync-service\"}]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":6,\"height\":6,\"x\":0,\"y\":12,\"properties\":{\"view\":\"timeSeries\",\"title\":\"RDS CPU Utilization\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"AWS/RDS\",\"CPUUtilization\",\"DBInstanceIdentifier\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefPostgreSQLInstanceD9AD3CF098D2E69C"}, "\"]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":6,\"height\":6,\"x\":6,\"y\":12,\"properties\":{\"view\":\"timeSeries\",\"title\":\"RDS Database Connections\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"AWS/RDS\",\"DatabaseConnections\",\"DBInstanceIdentifier\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefPostgreSQLInstanceD9AD3CF098D2E69C"}, "\"]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":6,\"height\":6,\"x\":0,\"y\":18,\"properties\":{\"view\":\"timeSeries\",\"title\":\"ElastiCache CPU Utilization\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"AWS/ElastiCache\",\"CPUUtilization\",\"CacheClusterId\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttRedisClusterPrimaryEndPointAddressCFB16E1D"}, "\"]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":6,\"height\":6,\"x\":6,\"y\":18,\"properties\":{\"view\":\"timeSeries\",\"title\":\"ElastiCache Memory Usage\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"AWS/ElastiCache\",\"DatabaseMemoryUsagePercentage\",\"CacheClusterId\",\"", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttRedisClusterPrimaryEndPointAddressCFB16E1D"}, "\"]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":6,\"height\":6,\"x\":0,\"y\":24,\"properties\":{\"view\":\"timeSeries\",\"title\":\"Active Tenders\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"BidBees/Business\",\"ActiveTenders\"]],\"yAxis\":{}}},{\"type\":\"metric\",\"width\":6,\"height\":6,\"x\":6,\"y\":24,\"properties\":{\"view\":\"timeSeries\",\"title\":\"Active Bids\",\"region\":\"", {"Ref": "AWS::Region"}, "\",\"metrics\":[[\"BidBees/Business\",\"ActiveBids\"]],\"yAxis\":{}}}]}"]]}, "DashboardName": "BidBees-Production-Monitoring"}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/BidBeesDashboard/Resource"}}, "ALBHighErrorRate18B89DE3": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "ALB error rate is too high", "AlarmName": "BidBees-ALB-High-Error-Rate", "ComparisonOperator": "GreaterThanThreshold", "Dimensions": [{"Name": "LoadBalancer", "Value": {"Fn::ImportValue": "bidbees-production-application:ExportsOutputFnGetAttBidBeesALBDEF3E75ALoadBalancerFullNameE6F369EB"}}], "EvaluationPeriods": 2, "MetricName": "HTTPCode_ELB_5XX_Count", "Namespace": "AWS/ApplicationELB", "Period": 300, "Statistic": "Sum", "Threshold": 10}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/ALBHighErrorRate/Resource"}}, "RDSHighCPUE20DB240": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "RDS CPU utilization is too high", "AlarmName": "BidBees-RDS-High-CPU", "ComparisonOperator": "GreaterThanThreshold", "Dimensions": [{"Name": "DBInstanceIdentifier", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefPostgreSQLInstanceD9AD3CF098D2E69C"}}], "EvaluationPeriods": 3, "MetricName": "CPUUtilization", "Namespace": "AWS/RDS", "Period": 300, "Statistic": "Average", "Threshold": 80}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/RDSHighCPU/Resource"}}, "apigatewayHighCPU5FCEBD4B": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "api-gateway CPU utilization is too high", "AlarmName": "BidBees-api-gateway-High-CPU", "ComparisonOperator": "GreaterThanThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, {"Name": "ServiceName", "Value": "api-gateway"}], "EvaluationPeriods": 3, "MetricName": "CPUUtilization", "Namespace": "AWS/ECS", "Period": 300, "Statistic": "Average", "Threshold": 80}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/api-gateway-HighCPU/Resource"}}, "apigatewayServiceDown81307F9D": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "api-gateway has no running tasks", "AlarmName": "BidBees-api-gateway-Service-Down", "ComparisonOperator": "LessThanThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, {"Name": "ServiceName", "Value": "api-gateway"}], "EvaluationPeriods": 2, "MetricName": "RunningTaskCount", "Namespace": "AWS/ECS", "Period": 60, "Statistic": "Average", "Threshold": 1}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/api-gateway-ServiceDown/Resource"}}, "authserviceHighCPUBA734E25": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "auth-service CPU utilization is too high", "AlarmName": "BidBees-auth-service-High-CPU", "ComparisonOperator": "GreaterThanThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, {"Name": "ServiceName", "Value": "auth-service"}], "EvaluationPeriods": 3, "MetricName": "CPUUtilization", "Namespace": "AWS/ECS", "Period": 300, "Statistic": "Average", "Threshold": 80}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/auth-service-HighCPU/Resource"}}, "authserviceServiceDown08516884": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "auth-service has no running tasks", "AlarmName": "BidBees-auth-service-Service-Down", "ComparisonOperator": "LessThanThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, {"Name": "ServiceName", "Value": "auth-service"}], "EvaluationPeriods": 2, "MetricName": "RunningTaskCount", "Namespace": "AWS/ECS", "Period": 60, "Statistic": "Average", "Threshold": 1}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/auth-service-ServiceDown/Resource"}}, "tenderserviceHighCPU084FCF9F": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "tender-service CPU utilization is too high", "AlarmName": "BidBees-tender-service-High-CPU", "ComparisonOperator": "GreaterThanThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, {"Name": "ServiceName", "Value": "tender-service"}], "EvaluationPeriods": 3, "MetricName": "CPUUtilization", "Namespace": "AWS/ECS", "Period": 300, "Statistic": "Average", "Threshold": 80}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/tender-service-HighCPU/Resource"}}, "tenderserviceServiceDownC38A9884": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "tender-service has no running tasks", "AlarmName": "BidBees-tender-service-Service-Down", "ComparisonOperator": "LessThanThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, {"Name": "ServiceName", "Value": "tender-service"}], "EvaluationPeriods": 2, "MetricName": "RunningTaskCount", "Namespace": "AWS/ECS", "Period": 60, "Statistic": "Average", "Threshold": 1}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/tender-service-ServiceDown/Resource"}}, "biddingserviceHighCPU993ECC7E": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "bidding-service CPU utilization is too high", "AlarmName": "BidBees-bidding-service-High-CPU", "ComparisonOperator": "GreaterThanThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, {"Name": "ServiceName", "Value": "bidding-service"}], "EvaluationPeriods": 3, "MetricName": "CPUUtilization", "Namespace": "AWS/ECS", "Period": 300, "Statistic": "Average", "Threshold": 80}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/bidding-service-HighCPU/Resource"}}, "biddingserviceServiceDown369383E1": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"AlarmActions": [{"Ref": "BidBeesAlerts55A686C2"}], "AlarmDescription": "bidding-service has no running tasks", "AlarmName": "BidBees-bidding-service-Service-Down", "ComparisonOperator": "LessThanThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}}, {"Name": "ServiceName", "Value": "bidding-service"}], "EvaluationPeriods": 2, "MetricName": "RunningTaskCount", "Namespace": "AWS/ECS", "Period": 60, "Statistic": "Average", "Threshold": 1}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/bidding-service-ServiceDown/Resource"}}, "CustomMetricsLambdaServiceRole417218E1": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/CustomMetricsLambda/ServiceRole/Resource"}}, "CustomMetricsLambdaServiceRoleDefaultPolicy097D4B03": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["cloudwatch:PutMetricData", "rds:DescribeDBInstances"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "PolicyName": "CustomMetricsLambdaServiceRoleDefaultPolicy097D4B03", "Roles": [{"Ref": "CustomMetricsLambdaServiceRole417218E1"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/CustomMetricsLambda/ServiceRole/DefaultPolicy/Resource"}}, "CustomMetricsLambda356DBAAE": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"ZipFile": {"Fn::Join": ["", ["\n        const AWS = require('aws-sdk');\n        const cloudwatch = new AWS.CloudWatch();\n        const rds = new AWS.RDS();\n\n        exports.handler = async (event) => {\n          try {\n            // Example: Get database connection count and publish as custom metric\n            const dbParams = {\n              DBInstanceIdentifier: '", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefPostgreSQLInstanceD9AD3CF098D2E69C"}, "'\n            };\n            \n            // Publish custom business metrics\n            const params = {\n              Namespace: 'BidBees/Business',\n              MetricData: [\n                {\n                  MetricName: 'ActiveTenders',\n                  Value: Math.floor(Math.random() * 100), // Replace with actual query\n                  Unit: 'Count',\n                  Timestamp: new Date()\n                },\n                {\n                  MetricName: 'ActiveBids',\n                  Value: Math.floor(Math.random() * 200), // Replace with actual query\n                  Unit: 'Count',\n                  Timestamp: new Date()\n                }\n              ]\n            };\n            \n            await cloudwatch.putMetricData(params).promise();\n            \n            return {\n              statusCode: 200,\n              body: JSON.stringify('Custom metrics published successfully')\n            };\n          } catch (error) {\n            console.error('Error publishing custom metrics:', error);\n            throw error;\n          }\n        };\n      "]]}}, "Environment": {"Variables": {"RDS_INSTANCE_ID": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefPostgreSQLInstanceD9AD3CF098D2E69C"}}}, "FunctionName": "bidbees-custom-metrics", "Handler": "index.handler", "Role": {"Fn::GetAtt": ["CustomMetricsLambdaServiceRole417218E1", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Timeout": 300}, "DependsOn": ["CustomMetricsLambdaServiceRoleDefaultPolicy097D4B03", "CustomMetricsLambdaServiceRole417218E1"], "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/CustomMetricsLambda/Resource"}}, "CustomMetricsSchedule8732724F": {"Type": "AWS::Events::Rule", "Properties": {"ScheduleExpression": "rate(5 minutes)", "State": "ENABLED", "Targets": [{"Arn": {"Fn::GetAtt": ["CustomMetricsLambda356DBAAE", "<PERSON><PERSON>"]}, "Id": "Target0"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/CustomMetricsSchedule/Resource"}}, "CustomMetricsScheduleAllowEventRulebidbeesproductionmonitoringCustomMetricsLambdaC25EEDD6A18D003A": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["CustomMetricsLambda356DBAAE", "<PERSON><PERSON>"]}, "Principal": "events.amazonaws.com", "SourceArn": {"Fn::GetAtt": ["CustomMetricsSchedule8732724F", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/CustomMetricsSchedule/AllowEventRulebidbeesproductionmonitoringCustomMetricsLambdaC25EEDD6"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/02KwW7CMBBEv4W7s4QcuCMqrqC0d7SxXbFgeyOvTYQs/3sVF6k9zZt5M8Cu76Hf4CKdNo/O0QTlM6F+KFzkWiQIlC+eSavjd2hQlXaczYJJ36B8oNwmxmhW/1cODqNfpwZVOfSTQSinHHQiDqv6zxcbPYkQh6oIPZSRnV1Fyws70q/2a1SVfdqQBMqY37fsbK1qtMI56jadc5pzavK9VhXYWLjL9jkMsNtDv7kLURdzSOQtjL/5A6Vnbk8UAQAA"}, "Metadata": {"aws:cdk:path": "bidbees-production-monitoring/CDKMetadata/Default"}}}, "Outputs": {"DashboardURL": {"Description": "CloudWatch Dashboard URL", "Value": {"Fn::Join": ["", ["https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#dashboards:name=", {"Ref": "BidBeesDashboardBF6956B4"}]]}, "Export": {"Name": "BidBeesDashboardURL"}}, "AlertTopicArn": {"Description": "SNS Topic ARN for alerts", "Value": {"Ref": "BidBeesAlerts55A686C2"}, "Export": {"Name": "BidBeesAlertTopicArn"}}, "LogInsightsQueries": {"Description": "Useful CloudWatch Logs Insights queries", "Value": "[\"API Gateway Errors: fields @timestamp, @message | filter @message like /ERROR/ | sort @timestamp desc | limit 100\",\"Slow Database Queries: fields @timestamp, @message | filter @message like /slow query/ | sort @timestamp desc | limit 50\",\"Authentication Failures: fields @timestamp, @message | filter @message like /authentication failed/ | sort @timestamp desc | limit 100\"]", "Export": {"Name": "BidBeesLogInsightsQueries"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}