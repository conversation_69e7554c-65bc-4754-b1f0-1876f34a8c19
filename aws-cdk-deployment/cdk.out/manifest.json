{"version": "34.0.0", "artifacts": {"bidbees-production-infrastructure.assets": {"type": "cdk:asset-manifest", "properties": {"file": "bidbees-production-infrastructure.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "bidbees-production-infrastructure": {"type": "aws:cloudformation:stack", "environment": "aws://955155721411/us-east-1", "properties": {"templateFile": "bidbees-production-infrastructure.template.json", "terminationProtection": false, "tags": {"Environment": "production", "ManagedBy": "CDK", "Project": "bidbees", "Stack": "Infrastructure"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-deploy-role-955155721411-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-cfn-exec-role-955155721411-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-955155721411-us-east-1/1b82f6386a6b26b802e9ced604d3d40bb0b09ea072cb4f65746136db33e708d8.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["bidbees-production-infrastructure.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-lookup-role-955155721411-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["bidbees-production-infrastructure.assets"], "metadata": {"/bidbees-production-infrastructure": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "Environment", "Value": "production"}, {"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "bidbees"}, {"Key": "<PERSON><PERSON>", "Value": "Infrastructure"}]}], "/bidbees-production-infrastructure/BidBeesVPC/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPC585BC663"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet1Subnet4EEAC752"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet1RouteTable789E5F96"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet1RouteTableAssociation799F999F"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet1DefaultRoute42081087"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/EIP": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet1EIP62C43981"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet1/NATGateway": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet1NATGateway1DD5DF38"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet2SubnetABA339EE"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet2RouteTable4EF11C22"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet2RouteTableAssociation3B5084C2"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet2DefaultRouteD59FFAE6"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/EIP": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet2EIPB0F97068"}], "/bidbees-production-infrastructure/BidBeesVPC/PublicSubnet2/NATGateway": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPublicSubnet2NATGateway85C73D6B"}], "/bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPrivateSubnet1Subnet9CC74C9A"}], "/bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPrivateSubnet1RouteTable1FD839AD"}], "/bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPrivateSubnet1RouteTableAssociationB26B135E"}], "/bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet1/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPrivateSubnet1DefaultRouteC901A9C5"}], "/bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPrivateSubnet2Subnet567B500C"}], "/bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPrivateSubnet2RouteTable2F5D94CF"}], "/bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPrivateSubnet2RouteTableAssociation62D37E43"}], "/bidbees-production-infrastructure/BidBeesVPC/PrivateSubnet2/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCPrivateSubnet2DefaultRoute36879FFB"}], "/bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCDatabaseSubnet1Subnet2DF44E8E"}], "/bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCDatabaseSubnet1RouteTableB6875D2B"}], "/bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCDatabaseSubnet1RouteTableAssociation8EC812B9"}], "/bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCDatabaseSubnet2Subnet601B8C61"}], "/bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCDatabaseSubnet2RouteTable8ACC6BCC"}], "/bidbees-production-infrastructure/BidBeesVPC/DatabaseSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCDatabaseSubnet2RouteTableAssociationC0D69C0D"}], "/bidbees-production-infrastructure/BidBeesVPC/IGW": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCIGW8F5454E0"}], "/bidbees-production-infrastructure/BidBeesVPC/VPCGW": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCVPCGWB2A3C941"}], "/bidbees-production-infrastructure/BidBeesVPC/RestrictDefaultSecurityGroupCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "BidBeesVPCRestrictDefaultSecurityGroupCustomResourceF930F4E4"}], "/bidbees-production-infrastructure/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role": [{"type": "aws:cdk:logicalId", "data": "CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0"}], "/bidbees-production-infrastructure/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler": [{"type": "aws:cdk:logicalId", "data": "CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E"}], "/bidbees-production-infrastructure/BidBeesCluster/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesCluster2DD1D24B"}], "/bidbees-production-infrastructure/ALBSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ALBSecurityGroup29A3BDEF"}], "/bidbees-production-infrastructure/ECSSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupA14DBE7D"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:ALL TRAFFIC": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8CALLTRAFFIC405FD737"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8080": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80800CED177A"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8081": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C808171517F5D"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8082": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C808239D14437"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8083": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C808362E41F64"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8084": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80846E1678A0"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8085": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8085B6701201"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8086": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8086B3532922"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8087": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C808742F0A39C"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8088": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8088492E5DA8"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8089": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8089F81403EE"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8090": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8090FA3CAD68"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8091": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8091F5625C5B"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8092": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80923633C26A"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8093": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80932EC58A4B"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8094": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C809404C76356"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8095": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C80953B95A0DF"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8096": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C809624437E80"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8097": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8097FBA0B568"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8098": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8098E8F5FEFC"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8099": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8099B72CE179"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8100": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C810031D90A71"}], "/bidbees-production-infrastructure/ECSSecurityGroup/from bidbeesproductioninfrastructureALBSecurityGroupCB50BB8C:8101": [{"type": "aws:cdk:logicalId", "data": "ECSSecurityGroupfrombidbeesproductioninfrastructureALBSecurityGroupCB50BB8C8101D49BDFD7"}], "/bidbees-production-infrastructure/RDSSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "RDSSecurityGroup6BF2CF10"}], "/bidbees-production-infrastructure/RDSSecurityGroup/from bidbeesproductioninfrastructureECSSecurityGroup739EDCD1:5432": [{"type": "aws:cdk:logicalId", "data": "RDSSecurityGroupfrombidbeesproductioninfrastructureECSSecurityGroup739EDCD1543235D61678"}], "/bidbees-production-infrastructure/RedisSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "RedisSecurityGroupB05951F6"}], "/bidbees-production-infrastructure/RedisSecurityGroup/from bidbeesproductioninfrastructureECSSecurityGroup739EDCD1:6379": [{"type": "aws:cdk:logicalId", "data": "RedisSecurityGroupfrombidbeesproductioninfrastructureECSSecurityGroup739EDCD16379348A174C"}], "/bidbees-production-infrastructure/DocDBSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "DocDBSecurityGroup4B2123E2"}], "/bidbees-production-infrastructure/DocDBSecurityGroup/from bidbeesproductioninfrastructureECSSecurityGroup739EDCD1:27017": [{"type": "aws:cdk:logicalId", "data": "DocDBSecurityGroupfrombidbeesproductioninfrastructureECSSecurityGroup739EDCD12701740CC1683"}], "/bidbees-production-infrastructure/DBSecret/Resource": [{"type": "aws:cdk:logicalId", "data": "DBSecretD58955BC"}], "/bidbees-production-infrastructure/DBSecret/Attachment/Resource": [{"type": "aws:cdk:logicalId", "data": "DBSecretAttachmentC565A14F"}], "/bidbees-production-infrastructure/DocDBSecret/Resource": [{"type": "aws:cdk:logicalId", "data": "DocDBSecret25894C0A"}], "/bidbees-production-infrastructure/PostgreSQLInstance/SubnetGroup/Default": [{"type": "aws:cdk:logicalId", "data": "PostgreSQLInstanceSubnetGroup80C84F06"}], "/bidbees-production-infrastructure/PostgreSQLInstance/Resource": [{"type": "aws:cdk:logicalId", "data": "PostgreSQLInstanceD9AD3CF0"}], "/bidbees-production-infrastructure/RedisSubnetGroup": [{"type": "aws:cdk:logicalId", "data": "RedisSubnetGroup"}], "/bidbees-production-infrastructure/RedisCluster": [{"type": "aws:cdk:logicalId", "data": "RedisCluster"}], "/bidbees-production-infrastructure/DocumentDBCluster/Subnets": [{"type": "aws:cdk:logicalId", "data": "DocumentDBClusterSubnets6AC07BFC"}], "/bidbees-production-infrastructure/DocumentDBCluster/Resource": [{"type": "aws:cdk:logicalId", "data": "DocumentDBCluster51C53375"}], "/bidbees-production-infrastructure/DocumentDBCluster/Instance1": [{"type": "aws:cdk:logicalId", "data": "DocumentDBClusterInstance18A80109A"}], "/bidbees-production-infrastructure/DocumentDBCluster/Instance2": [{"type": "aws:cdk:logicalId", "data": "DocumentDBClusterInstance22CB360D6"}], "/bidbees-production-infrastructure/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/bidbees-production-infrastructure/Exports/Output{\"Fn::GetAtt\":[\"ALBSecurityGroup29A3BDEF\",\"GroupId\"]}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputFnGetAttALBSecurityGroup29A3BDEFGroupId2A9BC0BF"}], "/bidbees-production-infrastructure/Exports/Output{\"Ref\":\"BidBeesVPCPublicSubnet1Subnet4EEAC752\"}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputRefBidBeesVPCPublicSubnet1Subnet4EEAC7520D6A904D"}], "/bidbees-production-infrastructure/Exports/Output{\"Ref\":\"BidBeesVPCPublicSubnet2SubnetABA339EE\"}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputRefBidBeesVPCPublicSubnet2SubnetABA339EE0B19CC48"}], "/bidbees-production-infrastructure/Exports/Output{\"Ref\":\"BidBeesCluster2DD1D24B\"}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}], "/bidbees-production-infrastructure/Exports/Output{\"Ref\":\"BidBeesVPCPrivateSubnet1Subnet9CC74C9A\"}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}], "/bidbees-production-infrastructure/Exports/Output{\"Ref\":\"BidBeesVPCPrivateSubnet2Subnet567B500C\"}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}], "/bidbees-production-infrastructure/Exports/Output{\"Fn::GetAtt\":[\"ECSSecurityGroupA14DBE7D\",\"GroupId\"]}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "/bidbees-production-infrastructure/Exports/Output{\"Ref\":\"BidBeesVPC585BC663\"}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputRefBidBeesVPC585BC6635F18E126"}], "/bidbees-production-infrastructure/Exports/Output{\"Ref\":\"PostgreSQLInstanceD9AD3CF0\"}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputRefPostgreSQLInstanceD9AD3CF098D2E69C"}], "/bidbees-production-infrastructure/Exports/Output{\"Fn::GetAtt\":[\"RedisCluster\",\"PrimaryEndPoint.Address\"]}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputFnGetAttRedisClusterPrimaryEndPointAddressCFB16E1D"}], "/bidbees-production-infrastructure/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/bidbees-production-infrastructure/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "bidbees-production-infrastructure"}, "bidbees-production-application.assets": {"type": "cdk:asset-manifest", "properties": {"file": "bidbees-production-application.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "bidbees-production-application": {"type": "aws:cloudformation:stack", "environment": "aws://955155721411/us-east-1", "properties": {"templateFile": "bidbees-production-application.template.json", "terminationProtection": false, "tags": {"Environment": "production", "ManagedBy": "CDK", "Project": "bidbees", "Stack": "Application"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-deploy-role-955155721411-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-cfn-exec-role-955155721411-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-955155721411-us-east-1/ea921fd00416b73246d396604bb332cfc6dd24f09a479fd7eed8b4355a2a43c2.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["bidbees-production-application.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-lookup-role-955155721411-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["bidbees-production-infrastructure", "bidbees-production-application.assets"], "metadata": {"/bidbees-production-application": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "Environment", "Value": "production"}, {"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "bidbees"}, {"Key": "<PERSON><PERSON>", "Value": "Application"}]}], "/bidbees-production-application/BidBeesALB/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBDEF3E75A"}], "/bidbees-production-application/BidBeesALB/HTTPListener/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerEBE596C4"}], "/bidbees-production-application/BidBeesALB/HTTPListener/api-gateway-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerapigatewayruleRule4A00BDB5"}], "/bidbees-production-application/BidBeesALB/HTTPListener/auth-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerauthserviceruleRule5394C398"}], "/bidbees-production-application/BidBeesALB/HTTPListener/user-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListeneruserserviceruleRuleBDD06246"}], "/bidbees-production-application/BidBeesALB/HTTPListener/tender-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenertenderserviceruleRule9E97A12E"}], "/bidbees-production-application/BidBeesALB/HTTPListener/bidding-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerbiddingserviceruleRuleCC3B9706"}], "/bidbees-production-application/BidBeesALB/HTTPListener/bee-tasks-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerbeetasksserviceruleRule584454E6"}], "/bidbees-production-application/BidBeesALB/HTTPListener/notification-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenernotificationserviceruleRule82A0F258"}], "/bidbees-production-application/BidBeesALB/HTTPListener/document-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerdocumentserviceruleRule836574F5"}], "/bidbees-production-application/BidBeesALB/HTTPListener/map-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenermapserviceruleRuleCD2A9EAE"}], "/bidbees-production-application/BidBeesALB/HTTPListener/analytics-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListeneranalyticsserviceruleRuleB47530D4"}], "/bidbees-production-application/BidBeesALB/HTTPListener/payment-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerpaymentserviceruleRuleF9D947FF"}], "/bidbees-production-application/BidBeesALB/HTTPListener/courier-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenercourierserviceruleRuleC512C75F"}], "/bidbees-production-application/BidBeesALB/HTTPListener/transport-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenertransportserviceruleRuleEA083648"}], "/bidbees-production-application/BidBeesALB/HTTPListener/supplier-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenersupplierserviceruleRuleFC04BCED"}], "/bidbees-production-application/BidBeesALB/HTTPListener/ml-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenermlserviceruleRule7A18151A"}], "/bidbees-production-application/BidBeesALB/HTTPListener/queenbee-anchor-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerqueenbeeanchorserviceruleRuleF35A8AD5"}], "/bidbees-production-application/BidBeesALB/HTTPListener/docling-processor-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerdoclingprocessorruleRule3C70C379"}], "/bidbees-production-application/BidBeesALB/HTTPListener/student-verification-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerstudentverificationserviceruleRule04DD2D2D"}], "/bidbees-production-application/BidBeesALB/HTTPListener/kafka-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerkafkaserviceruleRule03EF114C"}], "/bidbees-production-application/BidBeesALB/HTTPListener/contractorsync-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenercontractorsyncserviceruleRuleE59808D5"}], "/bidbees-production-application/BidBeesALB/HTTPListener/skillsync-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerskillsyncserviceruleRuleDDC935F5"}], "/bidbees-production-application/BidBeesALB/HTTPListener/toolsync-service-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenertoolsyncserviceruleRuleB71EC150"}], "/bidbees-production-application/BidBeesALB/HTTPListener/api-gateway-default-ruleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesALBHTTPListenerapigatewaydefaultruleRule6C2A570D"}], "/bidbees-production-application/api-gateway-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewayrepo6C90AC26"}], "/bidbees-production-application/auth-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "authservicerepo89047BB6"}], "/bidbees-production-application/user-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "userservicerepoBD0D621C"}], "/bidbees-production-application/tender-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderservicerepoF6BB2092"}], "/bidbees-production-application/bidding-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingservicerepoAB98F515"}], "/bidbees-production-application/bee-tasks-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "beetasksservicerepoC090FA26"}], "/bidbees-production-application/notification-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "notificationservicerepo804D0692"}], "/bidbees-production-application/document-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "documentservicerepo9A6D7CB9"}], "/bidbees-production-application/map-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "mapservicerepo346DE86C"}], "/bidbees-production-application/analytics-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "analyticsservicerepo093CF65D"}], "/bidbees-production-application/payment-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "paymentservicerepo943B5440"}], "/bidbees-production-application/courier-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "courierservicerepoDA20D569"}], "/bidbees-production-application/transport-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "transportservicerepo8F304D9F"}], "/bidbees-production-application/supplier-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "supplierservicerepoE3B29C29"}], "/bidbees-production-application/ml-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "mlservicerepo401FACFD"}], "/bidbees-production-application/queenbee-anchor-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorservicerepoED4CA8B7"}], "/bidbees-production-application/docling-processor-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "doclingprocessorrepoDB6D7623"}], "/bidbees-production-application/student-verification-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "studentverificationservicerepoA397EE22"}], "/bidbees-production-application/kafka-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "kafkaservicerepoEEB199D3"}], "/bidbees-production-application/contractorsync-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "contractorsyncservicerepoCD31E3BF"}], "/bidbees-production-application/skillsync-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "skillsyncservicerepo48E0A187"}], "/bidbees-production-application/toolsync-service-repo/Resource": [{"type": "aws:cdk:logicalId", "data": "toolsyncservicerepo1BA4B4B1"}], "/bidbees-production-application/TaskExecutionRole/Resource": [{"type": "aws:cdk:logicalId", "data": "TaskExecutionRole250D2532"}], "/bidbees-production-application/TaskExecutionRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "TaskExecutionRoleDefaultPolicyA84DD1B0"}], "/bidbees-production-application/TaskExecutionRole/OverflowPolicy1/Resource": [{"type": "aws:cdk:logicalId", "data": "TaskExecutionRoleOverflowPolicy148F97F72"}], "/bidbees-production-application/TaskRole/Resource": [{"type": "aws:cdk:logicalId", "data": "TaskRole30FC0FBB"}], "/bidbees-production-application/TaskRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "TaskRoleDefaultPolicy07FC53DE"}], "/bidbees-production-application/api-gateway-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewaylogsCDAD9F45"}], "/bidbees-production-application/auth-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "authservicelogsED51B3C7"}], "/bidbees-production-application/user-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "userservicelogs8CB5CD7C"}], "/bidbees-production-application/tender-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderservicelogsAB69FE2E"}], "/bidbees-production-application/bidding-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingservicelogs049EF56B"}], "/bidbees-production-application/bee-tasks-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "beetasksservicelogs841DCF78"}], "/bidbees-production-application/notification-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "notificationservicelogs7CD973DC"}], "/bidbees-production-application/document-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "documentservicelogs78E0FE6B"}], "/bidbees-production-application/map-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "mapservicelogsBAF8FEB4"}], "/bidbees-production-application/analytics-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "analyticsservicelogsA436E2AC"}], "/bidbees-production-application/payment-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "paymentservicelogsFACD9834"}], "/bidbees-production-application/courier-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "courierservicelogsA18DE1D5"}], "/bidbees-production-application/transport-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "transportservicelogs9BE9ADEB"}], "/bidbees-production-application/supplier-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "supplierservicelogs553F878A"}], "/bidbees-production-application/ml-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "mlservicelogsC8252B12"}], "/bidbees-production-application/queenbee-anchor-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorservicelogsECC54205"}], "/bidbees-production-application/docling-processor-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "doclingprocessorlogsE5453BE7"}], "/bidbees-production-application/student-verification-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "studentverificationservicelogs4EE37A8F"}], "/bidbees-production-application/kafka-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "kafkaservicelogsF9DB15FB"}], "/bidbees-production-application/contractorsync-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "contractorsyncservicelogs7C0D6842"}], "/bidbees-production-application/skillsync-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "skillsyncservicelogsC4922B5F"}], "/bidbees-production-application/toolsync-service-logs/Resource": [{"type": "aws:cdk:logicalId", "data": "toolsyncservicelogs5835EBFE"}], "/bidbees-production-application/api-gateway-task/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewaytaskA7B31074"}], "/bidbees-production-application/api-gateway-service/Service": [{"type": "aws:cdk:logicalId", "data": "apigatewayserviceService6731300F"}], "/bidbees-production-application/api-gateway-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewayserviceTaskCountTarget1C752D9A"}], "/bidbees-production-application/api-gateway-service/TaskCount/Target/api-gateway-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewayserviceTaskCountTargetapigatewaycpuscaling660578C2"}], "/bidbees-production-application/api-gateway-service/TaskCount/Target/api-gateway-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewayserviceTaskCountTargetapigatewaymemoryscalingB3402157"}], "/bidbees-production-application/auth-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "authservicetaskAE846DD6"}], "/bidbees-production-application/auth-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "authserviceserviceServiceCC780564"}], "/bidbees-production-application/auth-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "authserviceserviceTaskCountTarget613C3495"}], "/bidbees-production-application/auth-service-service/TaskCount/Target/auth-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "authserviceserviceTaskCountTargetauthservicecpuscalingB66D2E9F"}], "/bidbees-production-application/auth-service-service/TaskCount/Target/auth-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "authserviceserviceTaskCountTargetauthservicememoryscaling8F23B75F"}], "/bidbees-production-application/user-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "userservicetaskFC7D9921"}], "/bidbees-production-application/user-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "userserviceserviceServiceC98ABF9A"}], "/bidbees-production-application/user-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "userserviceserviceTaskCountTarget5221D603"}], "/bidbees-production-application/user-service-service/TaskCount/Target/user-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "userserviceserviceTaskCountTargetuserservicecpuscaling8A1AFDA5"}], "/bidbees-production-application/user-service-service/TaskCount/Target/user-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "userserviceserviceTaskCountTargetuserservicememoryscalingEF40B12B"}], "/bidbees-production-application/tender-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderservicetask8B47CF70"}], "/bidbees-production-application/tender-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "tenderserviceserviceService351DDCE4"}], "/bidbees-production-application/tender-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderserviceserviceTaskCountTargetEEE8B21D"}], "/bidbees-production-application/tender-service-service/TaskCount/Target/tender-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderserviceserviceTaskCountTargettenderservicecpuscaling461E964E"}], "/bidbees-production-application/tender-service-service/TaskCount/Target/tender-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderserviceserviceTaskCountTargettenderservicememoryscalingD8B80567"}], "/bidbees-production-application/bidding-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingservicetaskA7A8E187"}], "/bidbees-production-application/bidding-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "biddingserviceserviceService3CFA8876"}], "/bidbees-production-application/bidding-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingserviceserviceTaskCountTargetA7F2EAE2"}], "/bidbees-production-application/bidding-service-service/TaskCount/Target/bidding-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingserviceserviceTaskCountTargetbiddingservicecpuscaling6D0D581A"}], "/bidbees-production-application/bidding-service-service/TaskCount/Target/bidding-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingserviceserviceTaskCountTargetbiddingservicememoryscaling0A0260BB"}], "/bidbees-production-application/bee-tasks-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "beetasksservicetask7F25397B"}], "/bidbees-production-application/bee-tasks-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "beetasksserviceserviceService739C71D4"}], "/bidbees-production-application/bee-tasks-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "beetasksserviceserviceTaskCountTarget310FA8B5"}], "/bidbees-production-application/bee-tasks-service-service/TaskCount/Target/bee-tasks-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "beetasksserviceserviceTaskCountTargetbeetasksservicecpuscalingEE119C7C"}], "/bidbees-production-application/bee-tasks-service-service/TaskCount/Target/bee-tasks-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "beetasksserviceserviceTaskCountTargetbeetasksservicememoryscaling3E38F76D"}], "/bidbees-production-application/notification-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "notificationservicetask442A5982"}], "/bidbees-production-application/notification-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "notificationserviceserviceService89B296A4"}], "/bidbees-production-application/notification-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "notificationserviceserviceTaskCountTarget55F2BBC7"}], "/bidbees-production-application/notification-service-service/TaskCount/Target/notification-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "notificationserviceserviceTaskCountTargetnotificationservicecpuscaling06FEDCE2"}], "/bidbees-production-application/notification-service-service/TaskCount/Target/notification-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "notificationserviceserviceTaskCountTargetnotificationservicememoryscaling298D3F96"}], "/bidbees-production-application/document-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "documentservicetask45406817"}], "/bidbees-production-application/document-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "documentserviceserviceServiceA8BEF05A"}], "/bidbees-production-application/document-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "documentserviceserviceTaskCountTarget48004D5D"}], "/bidbees-production-application/document-service-service/TaskCount/Target/document-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "documentserviceserviceTaskCountTargetdocumentservicecpuscalingCD584F1F"}], "/bidbees-production-application/document-service-service/TaskCount/Target/document-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "documentserviceserviceTaskCountTargetdocumentservicememoryscalingE3B06E20"}], "/bidbees-production-application/map-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "mapservicetask624D6A25"}], "/bidbees-production-application/map-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "mapserviceserviceServiceB4A6A16F"}], "/bidbees-production-application/map-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "mapserviceserviceTaskCountTargetDD279F24"}], "/bidbees-production-application/map-service-service/TaskCount/Target/map-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "mapserviceserviceTaskCountTargetmapservicecpuscaling35C6CDA0"}], "/bidbees-production-application/map-service-service/TaskCount/Target/map-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "mapserviceserviceTaskCountTargetmapservicememoryscaling48D8FB87"}], "/bidbees-production-application/analytics-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "analyticsservicetask6AD1EC79"}], "/bidbees-production-application/analytics-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "analyticsserviceserviceServiceC3F831E1"}], "/bidbees-production-application/analytics-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "analyticsserviceserviceTaskCountTargetF615E4CB"}], "/bidbees-production-application/analytics-service-service/TaskCount/Target/analytics-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "analyticsserviceserviceTaskCountTargetanalyticsservicecpuscaling55922504"}], "/bidbees-production-application/analytics-service-service/TaskCount/Target/analytics-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "analyticsserviceserviceTaskCountTargetanalyticsservicememoryscaling94E348F5"}], "/bidbees-production-application/payment-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "paymentservicetask8313C54C"}], "/bidbees-production-application/payment-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "paymentserviceserviceService2F613185"}], "/bidbees-production-application/payment-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "paymentserviceserviceTaskCountTarget5A24D8A5"}], "/bidbees-production-application/payment-service-service/TaskCount/Target/payment-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "paymentserviceserviceTaskCountTargetpaymentservicecpuscaling1C792CAA"}], "/bidbees-production-application/payment-service-service/TaskCount/Target/payment-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "paymentserviceserviceTaskCountTargetpaymentservicememoryscalingB3A84608"}], "/bidbees-production-application/courier-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "courierservicetask922C09C1"}], "/bidbees-production-application/courier-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "courierserviceserviceService846412FE"}], "/bidbees-production-application/courier-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "courierserviceserviceTaskCountTargetA3D4B697"}], "/bidbees-production-application/courier-service-service/TaskCount/Target/courier-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "courierserviceserviceTaskCountTargetcourierservicecpuscaling120EAB1D"}], "/bidbees-production-application/courier-service-service/TaskCount/Target/courier-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "courierserviceserviceTaskCountTargetcourierservicememoryscaling05A38C40"}], "/bidbees-production-application/transport-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "transportservicetask3E6822F3"}], "/bidbees-production-application/transport-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "transportserviceserviceService1B49B44B"}], "/bidbees-production-application/transport-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "transportserviceserviceTaskCountTargetC4C4DDBE"}], "/bidbees-production-application/transport-service-service/TaskCount/Target/transport-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "transportserviceserviceTaskCountTargettransportservicecpuscalingA946B1B5"}], "/bidbees-production-application/transport-service-service/TaskCount/Target/transport-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "transportserviceserviceTaskCountTargettransportservicememoryscalingA13540FB"}], "/bidbees-production-application/supplier-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "supplierservicetask6C8AE572"}], "/bidbees-production-application/supplier-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "supplierserviceserviceService06E116D7"}], "/bidbees-production-application/supplier-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "supplierserviceserviceTaskCountTarget8784BEBD"}], "/bidbees-production-application/supplier-service-service/TaskCount/Target/supplier-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "supplierserviceserviceTaskCountTargetsupplierservicecpuscaling0A7098B3"}], "/bidbees-production-application/supplier-service-service/TaskCount/Target/supplier-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "supplierserviceserviceTaskCountTargetsupplierservicememoryscaling783E8699"}], "/bidbees-production-application/ml-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "mlservicetaskAD6A88BA"}], "/bidbees-production-application/ml-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "mlserviceserviceService2712EBFA"}], "/bidbees-production-application/ml-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "mlserviceserviceTaskCountTarget0816B565"}], "/bidbees-production-application/ml-service-service/TaskCount/Target/ml-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "mlserviceserviceTaskCountTargetmlservicecpuscaling96F8069D"}], "/bidbees-production-application/ml-service-service/TaskCount/Target/ml-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "mlserviceserviceTaskCountTargetmlservicememoryscalingDF4D7C24"}], "/bidbees-production-application/queenbee-anchor-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorservicetask4E31F632"}], "/bidbees-production-application/queenbee-anchor-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorserviceserviceServiceCCF31EA9"}], "/bidbees-production-application/queenbee-anchor-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorserviceserviceTaskCountTarget424FC9B0"}], "/bidbees-production-application/queenbee-anchor-service-service/TaskCount/Target/queenbee-anchor-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorserviceserviceTaskCountTargetqueenbeeanchorservicecpuscaling1C1D307E"}], "/bidbees-production-application/queenbee-anchor-service-service/TaskCount/Target/queenbee-anchor-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorserviceserviceTaskCountTargetqueenbeeanchorservicememoryscaling769FEC69"}], "/bidbees-production-application/docling-processor-task/Resource": [{"type": "aws:cdk:logicalId", "data": "doclingprocessortask0691A232"}], "/bidbees-production-application/docling-processor-service/Service": [{"type": "aws:cdk:logicalId", "data": "doclingprocessorserviceServiceF6C505F2"}], "/bidbees-production-application/docling-processor-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "doclingprocessorserviceTaskCountTargetDC806016"}], "/bidbees-production-application/docling-processor-service/TaskCount/Target/docling-processor-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "doclingprocessorserviceTaskCountTargetdoclingprocessorcpuscalingCA5CF332"}], "/bidbees-production-application/docling-processor-service/TaskCount/Target/docling-processor-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "doclingprocessorserviceTaskCountTargetdoclingprocessormemoryscaling4669A19A"}], "/bidbees-production-application/student-verification-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "studentverificationservicetask1E18885E"}], "/bidbees-production-application/student-verification-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "studentverificationserviceserviceService0BE0DA56"}], "/bidbees-production-application/student-verification-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "studentverificationserviceserviceTaskCountTargetD3A72907"}], "/bidbees-production-application/student-verification-service-service/TaskCount/Target/student-verification-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "studentverificationserviceserviceTaskCountTargetstudentverificationservicecpuscaling6B0A954A"}], "/bidbees-production-application/student-verification-service-service/TaskCount/Target/student-verification-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "studentverificationserviceserviceTaskCountTargetstudentverificationservicememoryscaling5680106F"}], "/bidbees-production-application/kafka-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "kafkaservicetask180573FE"}], "/bidbees-production-application/kafka-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "kafkaserviceserviceService4C507CF4"}], "/bidbees-production-application/kafka-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "kafkaserviceserviceTaskCountTargetE9BF05BE"}], "/bidbees-production-application/kafka-service-service/TaskCount/Target/kafka-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "kafkaserviceserviceTaskCountTargetkafkaservicecpuscalingE95A502E"}], "/bidbees-production-application/kafka-service-service/TaskCount/Target/kafka-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "kafkaserviceserviceTaskCountTargetkafkaservicememoryscaling19EA1C40"}], "/bidbees-production-application/contractorsync-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "contractorsyncservicetask6A3ED726"}], "/bidbees-production-application/contractorsync-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "contractorsyncserviceserviceService929CB140"}], "/bidbees-production-application/contractorsync-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "contractorsyncserviceserviceTaskCountTarget514577E3"}], "/bidbees-production-application/contractorsync-service-service/TaskCount/Target/contractorsync-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "contractorsyncserviceserviceTaskCountTargetcontractorsyncservicecpuscaling447D8767"}], "/bidbees-production-application/contractorsync-service-service/TaskCount/Target/contractorsync-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "contractorsyncserviceserviceTaskCountTargetcontractorsyncservicememoryscaling37E0112C"}], "/bidbees-production-application/skillsync-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "skillsyncservicetask370DF481"}], "/bidbees-production-application/skillsync-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "skillsyncserviceserviceService0341C206"}], "/bidbees-production-application/skillsync-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "skillsyncserviceserviceTaskCountTarget6F5BE1A0"}], "/bidbees-production-application/skillsync-service-service/TaskCount/Target/skillsync-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "skillsyncserviceserviceTaskCountTargetskillsyncservicecpuscaling96F9AF1D"}], "/bidbees-production-application/skillsync-service-service/TaskCount/Target/skillsync-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "skillsyncserviceserviceTaskCountTargetskillsyncservicememoryscaling80884B06"}], "/bidbees-production-application/toolsync-service-task/Resource": [{"type": "aws:cdk:logicalId", "data": "toolsyncservicetaskB294F66B"}], "/bidbees-production-application/toolsync-service-service/Service": [{"type": "aws:cdk:logicalId", "data": "toolsyncserviceserviceServiceE5CCE112"}], "/bidbees-production-application/toolsync-service-service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "toolsyncserviceserviceTaskCountTargetA28E56F3"}], "/bidbees-production-application/toolsync-service-service/TaskCount/Target/toolsync-service-cpu-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "toolsyncserviceserviceTaskCountTargettoolsyncservicecpuscaling640649E7"}], "/bidbees-production-application/toolsync-service-service/TaskCount/Target/toolsync-service-memory-scaling/Resource": [{"type": "aws:cdk:logicalId", "data": "toolsyncserviceserviceTaskCountTargettoolsyncservicememoryscalingBDF34A9E"}], "/bidbees-production-application/api-gateway-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewaytg3B8B1926"}], "/bidbees-production-application/auth-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "authservicetg8ADDF0AF"}], "/bidbees-production-application/user-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "userservicetg90716C8D"}], "/bidbees-production-application/tender-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderservicetg1376547D"}], "/bidbees-production-application/bidding-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingservicetgFD35E762"}], "/bidbees-production-application/bee-tasks-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "beetasksservicetg20637D76"}], "/bidbees-production-application/notification-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "notificationservicetgB12B03CD"}], "/bidbees-production-application/document-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "documentservicetgC7D23E05"}], "/bidbees-production-application/map-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "mapservicetg348246DC"}], "/bidbees-production-application/analytics-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "analyticsservicetg6F3949F7"}], "/bidbees-production-application/payment-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "paymentservicetgE1A844DA"}], "/bidbees-production-application/courier-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "courierservicetgA18A31DB"}], "/bidbees-production-application/transport-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "transportservicetg6FE707B2"}], "/bidbees-production-application/supplier-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "supplierservicetgC1978694"}], "/bidbees-production-application/ml-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "mlservicetg7270107E"}], "/bidbees-production-application/queenbee-anchor-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorservicetgF5F7F022"}], "/bidbees-production-application/docling-processor-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "doclingprocessortgF372CF92"}], "/bidbees-production-application/student-verification-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "studentverificationservicetg29B48E80"}], "/bidbees-production-application/kafka-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "kafkaservicetg5B22B24F"}], "/bidbees-production-application/contractorsync-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "contractorsyncservicetgFA48901D"}], "/bidbees-production-application/skillsync-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "skillsyncservicetg9F463764"}], "/bidbees-production-application/toolsync-service-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "toolsyncservicetg90928E86"}], "/bidbees-production-application/api-gateway-default-tg/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewaydefaulttgAFB2C86F"}], "/bidbees-production-application/LoadBalancerDNS": [{"type": "aws:cdk:logicalId", "data": "LoadBalancerDNS"}], "/bidbees-production-application/LoadBalancerArn": [{"type": "aws:cdk:logicalId", "data": "LoadBalancerArn"}], "/bidbees-production-application/api-gateway-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "apigatewayECRURI"}], "/bidbees-production-application/auth-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "authserviceECRURI"}], "/bidbees-production-application/user-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "userserviceECRURI"}], "/bidbees-production-application/tender-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "tenderserviceECRURI"}], "/bidbees-production-application/bidding-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "biddingserviceECRURI"}], "/bidbees-production-application/bee-tasks-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "beetasksserviceECRURI"}], "/bidbees-production-application/notification-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "notificationserviceECRURI"}], "/bidbees-production-application/document-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "documentserviceECRURI"}], "/bidbees-production-application/map-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "mapserviceECRURI"}], "/bidbees-production-application/analytics-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "analyticsserviceECRURI"}], "/bidbees-production-application/payment-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "paymentserviceECRURI"}], "/bidbees-production-application/courier-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "courierserviceECRURI"}], "/bidbees-production-application/transport-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "transportserviceECRURI"}], "/bidbees-production-application/supplier-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "supplierserviceECRURI"}], "/bidbees-production-application/ml-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "mlserviceECRURI"}], "/bidbees-production-application/queenbee-anchor-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "queenbeeanchorserviceECRURI"}], "/bidbees-production-application/docling-processor-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "doclingprocessorECRURI"}], "/bidbees-production-application/student-verification-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "studentverificationserviceECRURI"}], "/bidbees-production-application/kafka-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "kafkaserviceECRURI"}], "/bidbees-production-application/contractorsync-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "contractorsyncserviceECRURI"}], "/bidbees-production-application/skillsync-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "skillsyncserviceECRURI"}], "/bidbees-production-application/toolsync-service-ECR-URI": [{"type": "aws:cdk:logicalId", "data": "toolsyncserviceECRURI"}], "/bidbees-production-application/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/bidbees-production-application/Exports/Output{\"Fn::GetAtt\":[\"BidBeesALBDEF3E75A\",\"LoadBalancerFullName\"]}": [{"type": "aws:cdk:logicalId", "data": "ExportsOutputFnGetAttBidBeesALBDEF3E75ALoadBalancerFullNameE6F369EB"}], "/bidbees-production-application/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/bidbees-production-application/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "bidbees-production-application"}, "bidbees-production-monitoring.assets": {"type": "cdk:asset-manifest", "properties": {"file": "bidbees-production-monitoring.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "bidbees-production-monitoring": {"type": "aws:cloudformation:stack", "environment": "aws://955155721411/us-east-1", "properties": {"templateFile": "bidbees-production-monitoring.template.json", "terminationProtection": false, "tags": {"Environment": "production", "ManagedBy": "CDK", "Project": "bidbees", "Stack": "Monitoring"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-deploy-role-955155721411-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-cfn-exec-role-955155721411-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-955155721411-us-east-1/95a3a94b8472fa5f313de50a31d4ba9c2d6f7b7c32d95eb9a2c55396888e2190.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["bidbees-production-monitoring.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::955155721411:role/cdk-hnb659fds-lookup-role-955155721411-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["bidbees-production-application", "bidbees-production-infrastructure", "bidbees-production-monitoring.assets"], "metadata": {"/bidbees-production-monitoring": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "Environment", "Value": "production"}, {"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "bidbees"}, {"Key": "<PERSON><PERSON>", "Value": "Monitoring"}]}], "/bidbees-production-monitoring/BidBeesAlerts/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesAlerts55A686C2"}], "/bidbees-production-monitoring/BidBeesDashboard/Resource": [{"type": "aws:cdk:logicalId", "data": "BidBeesDashboardBF6956B4"}], "/bidbees-production-monitoring/ALBHighErrorRate/Resource": [{"type": "aws:cdk:logicalId", "data": "ALBHighErrorRate18B89DE3"}], "/bidbees-production-monitoring/RDSHighCPU/Resource": [{"type": "aws:cdk:logicalId", "data": "RDSHighCPUE20DB240"}], "/bidbees-production-monitoring/api-gateway-HighCPU/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewayHighCPU5FCEBD4B"}], "/bidbees-production-monitoring/api-gateway-ServiceDown/Resource": [{"type": "aws:cdk:logicalId", "data": "apigatewayServiceDown81307F9D"}], "/bidbees-production-monitoring/auth-service-HighCPU/Resource": [{"type": "aws:cdk:logicalId", "data": "authserviceHighCPUBA734E25"}], "/bidbees-production-monitoring/auth-service-ServiceDown/Resource": [{"type": "aws:cdk:logicalId", "data": "authserviceServiceDown08516884"}], "/bidbees-production-monitoring/tender-service-HighCPU/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderserviceHighCPU084FCF9F"}], "/bidbees-production-monitoring/tender-service-ServiceDown/Resource": [{"type": "aws:cdk:logicalId", "data": "tenderserviceServiceDownC38A9884"}], "/bidbees-production-monitoring/bidding-service-HighCPU/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingserviceHighCPU993ECC7E"}], "/bidbees-production-monitoring/bidding-service-ServiceDown/Resource": [{"type": "aws:cdk:logicalId", "data": "biddingserviceServiceDown369383E1"}], "/bidbees-production-monitoring/CustomMetricsLambda/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomMetricsLambdaServiceRole417218E1"}], "/bidbees-production-monitoring/CustomMetricsLambda/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomMetricsLambdaServiceRoleDefaultPolicy097D4B03"}], "/bidbees-production-monitoring/CustomMetricsLambda/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomMetricsLambda356DBAAE"}], "/bidbees-production-monitoring/CustomMetricsSchedule/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomMetricsSchedule8732724F"}], "/bidbees-production-monitoring/CustomMetricsSchedule/AllowEventRulebidbeesproductionmonitoringCustomMetricsLambdaC25EEDD6": [{"type": "aws:cdk:logicalId", "data": "CustomMetricsScheduleAllowEventRulebidbeesproductionmonitoringCustomMetricsLambdaC25EEDD6A18D003A"}], "/bidbees-production-monitoring/DashboardURL": [{"type": "aws:cdk:logicalId", "data": "DashboardURL"}], "/bidbees-production-monitoring/AlertTopicArn": [{"type": "aws:cdk:logicalId", "data": "AlertTopicArn"}], "/bidbees-production-monitoring/LogInsightsQueries": [{"type": "aws:cdk:logicalId", "data": "LogInsightsQueries"}], "/bidbees-production-monitoring/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/bidbees-production-monitoring/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/bidbees-production-monitoring/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "bidbees-production-monitoring"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}}