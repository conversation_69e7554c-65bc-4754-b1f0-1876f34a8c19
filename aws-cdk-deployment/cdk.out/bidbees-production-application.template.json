{"Description": "BidBees Application Stack - ECS Services, Load Balancer", "Resources": {"BidBeesALBDEF3E75A": {"Type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "Properties": {"LoadBalancerAttributes": [{"Key": "deletion_protection.enabled", "Value": "false"}], "Name": "bidbees-alb", "Scheme": "internet-facing", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttALBSecurityGroup29A3BDEFGroupId2A9BC0BF"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPublicSubnet1Subnet4EEAC7520D6A904D"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPublicSubnet2SubnetABA339EE0B19CC48"}], "Type": "application"}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/Resource"}}, "BidBeesALBHTTPListenerEBE596C4": {"Type": "AWS::ElasticLoadBalancingV2::Listener", "Properties": {"DefaultActions": [{"FixedResponseConfig": {"ContentType": "text/plain", "MessageBody": "Not Found", "StatusCode": "404"}, "Type": "fixed-response"}], "LoadBalancerArn": {"Ref": "BidBeesALBDEF3E75A"}, "Port": 80, "Protocol": "HTTP"}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/Resource"}}, "BidBeesALBHTTPListenerapigatewayruleRule4A00BDB5": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "apigatewaytg3B8B1926"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/api-gateway/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 100}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/api-gateway-ruleRule/Resource"}}, "BidBeesALBHTTPListenerauthserviceruleRule5394C398": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "authservicetg8ADDF0AF"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/auth-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 200}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/auth-service-ruleRule/Resource"}}, "BidBeesALBHTTPListeneruserserviceruleRuleBDD06246": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "userservicetg90716C8D"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/user-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 300}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/user-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenertenderserviceruleRule9E97A12E": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "tenderservicetg1376547D"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/tender-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 400}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/tender-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerbiddingserviceruleRuleCC3B9706": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "biddingservicetgFD35E762"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/bidding-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 500}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/bidding-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerbeetasksserviceruleRule584454E6": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "beetasksservicetg20637D76"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/bee-tasks-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 600}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/bee-tasks-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenernotificationserviceruleRule82A0F258": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "notificationservicetgB12B03CD"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/notification-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 700}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/notification-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerdocumentserviceruleRule836574F5": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "documentservicetgC7D23E05"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/document-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 800}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/document-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenermapserviceruleRuleCD2A9EAE": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "mapservicetg348246DC"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/map-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 900}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/map-service-ruleRule/Resource"}}, "BidBeesALBHTTPListeneranalyticsserviceruleRuleB47530D4": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "analyticsservicetg6F3949F7"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/analytics-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1000}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/analytics-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerpaymentserviceruleRuleF9D947FF": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "paymentservicetgE1A844DA"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/payment-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1100}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/payment-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenercourierserviceruleRuleC512C75F": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "courierservicetgA18A31DB"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/courier-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1200}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/courier-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenertransportserviceruleRuleEA083648": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "transportservicetg6FE707B2"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/transport-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1300}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/transport-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenersupplierserviceruleRuleFC04BCED": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "supplierservicetgC1978694"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/supplier-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1400}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/supplier-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenermlserviceruleRule7A18151A": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "mlservicetg7270107E"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/ml-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1500}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/ml-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerqueenbeeanchorserviceruleRuleF35A8AD5": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "queenbeeanchorservicetgF5F7F022"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/queenbee-anchor-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1600}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/queenbee-anchor-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerdoclingprocessorruleRule3C70C379": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "doclingprocessortgF372CF92"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/docling-processor/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1700}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/docling-processor-ruleRule/Resource"}}, "BidBeesALBHTTPListenerstudentverificationserviceruleRule04DD2D2D": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "studentverificationservicetg29B48E80"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/student-verification-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1800}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/student-verification-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerkafkaserviceruleRule03EF114C": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "kafkaservicetg5B22B24F"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/kafka-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 1900}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/kafka-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenercontractorsyncserviceruleRuleE59808D5": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "contractorsyncservicetgFA48901D"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/contractorsync-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 2000}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/contractorsync-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerskillsyncserviceruleRuleDDC935F5": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "skillsyncservicetg9F463764"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/skillsync-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 2100}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/skillsync-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenertoolsyncserviceruleRuleB71EC150": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "toolsyncservicetg90928E86"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/toolsync-service/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 2200}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/toolsync-service-ruleRule/Resource"}}, "BidBeesALBHTTPListenerapigatewaydefaultruleRule6C2A570D": {"Type": "AWS::ElasticLoadBalancingV2::ListenerRule", "Properties": {"Actions": [{"TargetGroupArn": {"Ref": "apigatewaydefaulttgAFB2C86F"}, "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "PathPatternConfig": {"Values": ["/api/*"]}}], "ListenerArn": {"Ref": "BidBeesALBHTTPListenerEBE596C4"}, "Priority": 50}, "Metadata": {"aws:cdk:path": "bidbees-production-application/BidBeesALB/HTTPListener/api-gateway-default-ruleRule/Resource"}}, "apigatewayrepo6C90AC26": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/api-gateway"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-repo/Resource"}}, "authservicerepo89047BB6": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/auth-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/auth-service-repo/Resource"}}, "userservicerepoBD0D621C": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/user-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/user-service-repo/Resource"}}, "tenderservicerepoF6BB2092": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/tender-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/tender-service-repo/Resource"}}, "biddingservicerepoAB98F515": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/bidding-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/bidding-service-repo/Resource"}}, "beetasksservicerepoC090FA26": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/bee-tasks-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/bee-tasks-service-repo/Resource"}}, "notificationservicerepo804D0692": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/notification-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/notification-service-repo/Resource"}}, "documentservicerepo9A6D7CB9": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/document-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/document-service-repo/Resource"}}, "mapservicerepo346DE86C": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/map-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/map-service-repo/Resource"}}, "analyticsservicerepo093CF65D": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/analytics-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/analytics-service-repo/Resource"}}, "paymentservicerepo943B5440": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/payment-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/payment-service-repo/Resource"}}, "courierservicerepoDA20D569": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/courier-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/courier-service-repo/Resource"}}, "transportservicerepo8F304D9F": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/transport-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/transport-service-repo/Resource"}}, "supplierservicerepoE3B29C29": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/supplier-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/supplier-service-repo/Resource"}}, "mlservicerepo401FACFD": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/ml-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/ml-service-repo/Resource"}}, "queenbeeanchorservicerepoED4CA8B7": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/queenbee-anchor-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/queenbee-anchor-service-repo/Resource"}}, "doclingprocessorrepoDB6D7623": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/docling-processor"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/docling-processor-repo/Resource"}}, "studentverificationservicerepoA397EE22": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/student-verification-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/student-verification-service-repo/Resource"}}, "kafkaservicerepoEEB199D3": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/kafka-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/kafka-service-repo/Resource"}}, "contractorsyncservicerepoCD31E3BF": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/contractorsync-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/contractorsync-service-repo/Resource"}}, "skillsyncservicerepo48E0A187": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/skillsync-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/skillsync-service-repo/Resource"}}, "toolsyncservicerepo1BA4B4B1": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"description\":\"Keep last 10 images\",\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "bidbees/toolsync-service"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "bidbees-production-application/toolsync-service-repo/Resource"}}, "TaskExecutionRole250D2532": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"]]}], "Policies": [{"PolicyDocument": {"Statement": [{"Action": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetAuthorizationToken", "ecr:GetDownloadUrlForLayer"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "PolicyName": "ECRAccess"}, {"PolicyDocument": {"Statement": [{"Action": "secretsmanager:GetSecretValue", "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "PolicyName": "SecretsManagerAccess"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-application/TaskExecutionRole/Resource"}}, "TaskExecutionRoleDefaultPolicyA84DD1B0": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetDownloadUrlForLayer"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["analyticsservicerepo093CF65D", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["apigatewayrepo6C90AC26", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["authservicerepo89047BB6", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["beetasksservicerepoC090FA26", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["biddingservicerepoAB98F515", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["contractorsyncservicerepoCD31E3BF", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["courierservicerepoDA20D569", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["doclingprocessorrepoDB6D7623", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["documentservicerepo9A6D7CB9", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["kafkaservicerepoEEB199D3", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["mapservicerepo346DE86C", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["mlservicerepo401FACFD", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["notificationservicerepo804D0692", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["paymentservicerepo943B5440", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["queenbeeanchorservicerepoED4CA8B7", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["skillsyncservicerepo48E0A187", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["studentverificationservicerepoA397EE22", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["supplierservicerepoE3B29C29", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["tenderservicerepoF6BB2092", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["toolsyncservicerepo1BA4B4B1", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["transportservicerepo8F304D9F", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["userservicerepoBD0D621C", "<PERSON><PERSON>"]}]}, {"Action": "ecr:GetAuthorizationToken", "Effect": "Allow", "Resource": "*"}, {"Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["analyticsservicelogsA436E2AC", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["apigatewaylogsCDAD9F45", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["authservicelogsED51B3C7", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["beetasksservicelogs841DCF78", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["biddingservicelogs049EF56B", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["contractorsyncservicelogs7C0D6842", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["courierservicelogsA18DE1D5", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["doclingprocessorlogsE5453BE7", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["documentservicelogs78E0FE6B", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["kafkaservicelogsF9DB15FB", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["mapservicelogsBAF8FEB4", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["mlservicelogsC8252B12", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["notificationservicelogs7CD973DC", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["paymentservicelogsFACD9834", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["queenbeeanchorservicelogsECC54205", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["skillsyncservicelogsC4922B5F", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["studentverificationservicelogs4EE37A8F", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["supplierservicelogs553F878A", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["tenderservicelogsAB69FE2E", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["toolsyncservicelogs5835EBFE", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["transportservicelogs9BE9ADEB", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["userservicelogs8CB5CD7C", "<PERSON><PERSON>"]}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database-??????"}], "Version": "2012-10-17"}, "PolicyName": "TaskExecutionRoleDefaultPolicyA84DD1B0", "Roles": [{"Ref": "TaskExecutionRole250D2532"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-application/TaskExecutionRole/DefaultPolicy/Resource"}}, "TaskExecutionRoleOverflowPolicy148F97F72": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"Description": "Part of the policies for bidbees-production-application/TaskExecutionRole", "Path": "/", "PolicyDocument": {"Statement": [{"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database-??????"}], "Version": "2012-10-17"}, "Roles": [{"Ref": "TaskExecutionRole250D2532"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-application/TaskExecutionRole/OverflowPolicy1/Resource"}}, "TaskRole30FC0FBB": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}, "Policies": [{"PolicyDocument": {"Statement": [{"Action": ["s3:DeleteObject", "s3:GetObject", "s3:PutObject"], "Effect": "Allow", "Resource": "arn:aws:s3:::bidbees-*/*"}], "Version": "2012-10-17"}, "PolicyName": "S3Access"}, {"PolicyDocument": {"Statement": [{"Action": ["ses:SendEmail", "ses:SendRawEmail"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "PolicyName": "SESAccess"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-application/TaskRole/Resource"}}, "TaskRoleDefaultPolicy07FC53DE": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["logs:CreateLogStream", "logs:DescribeLogGroups", "logs:DescribeLogStreams", "logs:PutLogEvents", "ssmmessages:CreateControlChannel", "ssmmessages:CreateDataChannel", "ssmmessages:OpenControlChannel", "ssmmessages:OpenDataChannel"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "PolicyName": "TaskRoleDefaultPolicy07FC53DE", "Roles": [{"Ref": "TaskRole30FC0FBB"}]}, "Metadata": {"aws:cdk:path": "bidbees-production-application/TaskRole/DefaultPolicy/Resource"}}, "apigatewaylogsCDAD9F45": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/api-gateway", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-logs/Resource"}}, "authservicelogsED51B3C7": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/auth-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/auth-service-logs/Resource"}}, "userservicelogs8CB5CD7C": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/user-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/user-service-logs/Resource"}}, "tenderservicelogsAB69FE2E": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/tender-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/tender-service-logs/Resource"}}, "biddingservicelogs049EF56B": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/bidding-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/bidding-service-logs/Resource"}}, "beetasksservicelogs841DCF78": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/bee-tasks-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/bee-tasks-service-logs/Resource"}}, "notificationservicelogs7CD973DC": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/notification-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/notification-service-logs/Resource"}}, "documentservicelogs78E0FE6B": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/document-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/document-service-logs/Resource"}}, "mapservicelogsBAF8FEB4": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/map-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/map-service-logs/Resource"}}, "analyticsservicelogsA436E2AC": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/analytics-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/analytics-service-logs/Resource"}}, "paymentservicelogsFACD9834": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/payment-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/payment-service-logs/Resource"}}, "courierservicelogsA18DE1D5": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/courier-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/courier-service-logs/Resource"}}, "transportservicelogs9BE9ADEB": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/transport-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/transport-service-logs/Resource"}}, "supplierservicelogs553F878A": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/supplier-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/supplier-service-logs/Resource"}}, "mlservicelogsC8252B12": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/ml-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/ml-service-logs/Resource"}}, "queenbeeanchorservicelogsECC54205": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/queenbee-anchor-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/queenbee-anchor-service-logs/Resource"}}, "doclingprocessorlogsE5453BE7": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/docling-processor", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/docling-processor-logs/Resource"}}, "studentverificationservicelogs4EE37A8F": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/student-verification-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/student-verification-service-logs/Resource"}}, "kafkaservicelogsF9DB15FB": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/kafka-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/kafka-service-logs/Resource"}}, "contractorsyncservicelogs7C0D6842": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/contractorsync-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/contractorsync-service-logs/Resource"}}, "skillsyncservicelogsC4922B5F": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/skillsync-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/skillsync-service-logs/Resource"}}, "toolsyncservicelogs5835EBFE": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/ecs/bidbees/toolsync-service", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "bidbees-production-application/toolsync-service-logs/Resource"}}, "apigatewaytaskA7B31074": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8080"}, {"Name": "SERVICE_NAME", "Value": "api-gateway"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["apigatewayrepo6C90AC26", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["apigatewayrepo6C90AC26", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "apigatewayrepo6C90AC26"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "apigatewaylogsCDAD9F45"}, "awslogs-stream-prefix": "api-gateway", "awslogs-region": "us-east-1"}}, "Name": "api-gateway-container", "PortMappings": [{"ContainerPort": 8080, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationapigatewaytaskF8BE131C", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-task/Resource"}}, "apigatewayserviceService6731300F": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 2, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "api-gateway-container", "ContainerPort": 8080, "TargetGroupArn": {"Ref": "apigatewaytg3B8B1926"}}, {"ContainerName": "api-gateway-container", "ContainerPort": 8080, "TargetGroupArn": {"Ref": "apigatewaydefaulttgAFB2C86F"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "api-gateway", "TaskDefinition": {"Ref": "apigatewaytaskA7B31074"}}, "DependsOn": ["BidBeesALBHTTPListenerapigatewaydefaultruleRule6C2A570D", "BidBeesALBHTTPListenerapigatewayruleRule4A00BDB5", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-service/Service"}}, "apigatewayserviceTaskCountTarget1C752D9A": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 10, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["apigatewayserviceService6731300F", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-service/TaskCount/Target/Resource"}}, "apigatewayserviceTaskCountTargetapigatewaycpuscaling660578C2": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationapigatewayserviceTaskCountTargetapigatewaycpuscaling1E7AE323", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "apigatewayserviceTaskCountTarget1C752D9A"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-service/TaskCount/Target/api-gateway-cpu-scaling/Resource"}}, "apigatewayserviceTaskCountTargetapigatewaymemoryscalingB3402157": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationapigatewayserviceTaskCountTargetapigatewaymemoryscaling12170A9B", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "apigatewayserviceTaskCountTarget1C752D9A"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-service/TaskCount/Target/api-gateway-memory-scaling/Resource"}}, "authservicetaskAE846DD6": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8081"}, {"Name": "SERVICE_NAME", "Value": "auth-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["authservicerepo89047BB6", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["authservicerepo89047BB6", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "authservicerepo89047BB6"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "authservicelogsED51B3C7"}, "awslogs-stream-prefix": "auth-service", "awslogs-region": "us-east-1"}}, "Name": "auth-service-container", "PortMappings": [{"ContainerPort": 8081, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationauthservicetask7E591EFF", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/auth-service-task/Resource"}}, "authserviceserviceServiceCC780564": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "auth-service-container", "ContainerPort": 8081, "TargetGroupArn": {"Ref": "authservicetg8ADDF0AF"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "auth-service", "TaskDefinition": {"Ref": "authservicetaskAE846DD6"}}, "DependsOn": ["BidBeesALBHTTPListenerauthserviceruleRule5394C398", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/auth-service-service/Service"}}, "authserviceserviceTaskCountTarget613C3495": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["authserviceserviceServiceCC780564", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/auth-service-service/TaskCount/Target/Resource"}}, "authserviceserviceTaskCountTargetauthservicecpuscalingB66D2E9F": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationauthserviceserviceTaskCountTargetauthservicecpuscaling0473D05A", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "authserviceserviceTaskCountTarget613C3495"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/auth-service-service/TaskCount/Target/auth-service-cpu-scaling/Resource"}}, "authserviceserviceTaskCountTargetauthservicememoryscaling8F23B75F": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationauthserviceserviceTaskCountTargetauthservicememoryscalingE535D1A6", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "authserviceserviceTaskCountTarget613C3495"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/auth-service-service/TaskCount/Target/auth-service-memory-scaling/Resource"}}, "userservicetaskFC7D9921": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8082"}, {"Name": "SERVICE_NAME", "Value": "user-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["userservicerepoBD0D621C", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["userservicerepoBD0D621C", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "userservicerepoBD0D621C"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "userservicelogs8CB5CD7C"}, "awslogs-stream-prefix": "user-service", "awslogs-region": "us-east-1"}}, "Name": "user-service-container", "PortMappings": [{"ContainerPort": 8082, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationuserservicetask1C94AF1B", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/user-service-task/Resource"}}, "userserviceserviceServiceC98ABF9A": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "user-service-container", "ContainerPort": 8082, "TargetGroupArn": {"Ref": "userservicetg90716C8D"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "user-service", "TaskDefinition": {"Ref": "userservicetaskFC7D9921"}}, "DependsOn": ["BidBeesALBHTTPListeneruserserviceruleRuleBDD06246", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/user-service-service/Service"}}, "userserviceserviceTaskCountTarget5221D603": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["userserviceserviceServiceC98ABF9A", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/user-service-service/TaskCount/Target/Resource"}}, "userserviceserviceTaskCountTargetuserservicecpuscaling8A1AFDA5": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationuserserviceserviceTaskCountTargetuserservicecpuscaling5595A257", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "userserviceserviceTaskCountTarget5221D603"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/user-service-service/TaskCount/Target/user-service-cpu-scaling/Resource"}}, "userserviceserviceTaskCountTargetuserservicememoryscalingEF40B12B": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationuserserviceserviceTaskCountTargetuserservicememoryscaling5F37786B", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "userserviceserviceTaskCountTarget5221D603"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/user-service-service/TaskCount/Target/user-service-memory-scaling/Resource"}}, "tenderservicetask8B47CF70": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8083"}, {"Name": "SERVICE_NAME", "Value": "tender-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["tenderservicerepoF6BB2092", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["tenderservicerepoF6BB2092", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "tenderservicerepoF6BB2092"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "tenderservicelogsAB69FE2E"}, "awslogs-stream-prefix": "tender-service", "awslogs-region": "us-east-1"}}, "Name": "tender-service-container", "PortMappings": [{"ContainerPort": 8083, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationtenderservicetaskD587EBD8", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/tender-service-task/Resource"}}, "tenderserviceserviceService351DDCE4": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "tender-service-container", "ContainerPort": 8083, "TargetGroupArn": {"Ref": "tenderservicetg1376547D"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "tender-service", "TaskDefinition": {"Ref": "tenderservicetask8B47CF70"}}, "DependsOn": ["BidBeesALBHTTPListenertenderserviceruleRule9E97A12E", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/tender-service-service/Service"}}, "tenderserviceserviceTaskCountTargetEEE8B21D": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["tenderserviceserviceService351DDCE4", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/tender-service-service/TaskCount/Target/Resource"}}, "tenderserviceserviceTaskCountTargettenderservicecpuscaling461E964E": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationtenderserviceserviceTaskCountTargettenderservicecpuscalingDC29824F", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "tenderserviceserviceTaskCountTargetEEE8B21D"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/tender-service-service/TaskCount/Target/tender-service-cpu-scaling/Resource"}}, "tenderserviceserviceTaskCountTargettenderservicememoryscalingD8B80567": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationtenderserviceserviceTaskCountTargettenderservicememoryscalingB75FA680", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "tenderserviceserviceTaskCountTargetEEE8B21D"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/tender-service-service/TaskCount/Target/tender-service-memory-scaling/Resource"}}, "biddingservicetaskA7A8E187": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8084"}, {"Name": "SERVICE_NAME", "Value": "bidding-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["biddingservicerepoAB98F515", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["biddingservicerepoAB98F515", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "biddingservicerepoAB98F515"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "biddingservicelogs049EF56B"}, "awslogs-stream-prefix": "bidding-service", "awslogs-region": "us-east-1"}}, "Name": "bidding-service-container", "PortMappings": [{"ContainerPort": 8084, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationbiddingservicetask51D59144", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/bidding-service-task/Resource"}}, "biddingserviceserviceService3CFA8876": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "bidding-service-container", "ContainerPort": 8084, "TargetGroupArn": {"Ref": "biddingservicetgFD35E762"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "bidding-service", "TaskDefinition": {"Ref": "biddingservicetaskA7A8E187"}}, "DependsOn": ["BidBeesALBHTTPListenerbiddingserviceruleRuleCC3B9706", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/bidding-service-service/Service"}}, "biddingserviceserviceTaskCountTargetA7F2EAE2": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["biddingserviceserviceService3CFA8876", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/bidding-service-service/TaskCount/Target/Resource"}}, "biddingserviceserviceTaskCountTargetbiddingservicecpuscaling6D0D581A": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationbiddingserviceserviceTaskCountTargetbiddingservicecpuscalingADD13B86", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "biddingserviceserviceTaskCountTargetA7F2EAE2"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/bidding-service-service/TaskCount/Target/bidding-service-cpu-scaling/Resource"}}, "biddingserviceserviceTaskCountTargetbiddingservicememoryscaling0A0260BB": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationbiddingserviceserviceTaskCountTargetbiddingservicememoryscaling96E332EC", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "biddingserviceserviceTaskCountTargetA7F2EAE2"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/bidding-service-service/TaskCount/Target/bidding-service-memory-scaling/Resource"}}, "beetasksservicetask7F25397B": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8085"}, {"Name": "SERVICE_NAME", "Value": "bee-tasks-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["beetasksservicerepoC090FA26", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["beetasksservicerepoC090FA26", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "beetasksservicerepoC090FA26"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "beetasksservicelogs841DCF78"}, "awslogs-stream-prefix": "bee-tasks-service", "awslogs-region": "us-east-1"}}, "Name": "bee-tasks-service-container", "PortMappings": [{"ContainerPort": 8085, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationbeetasksservicetaskA171DB46", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/bee-tasks-service-task/Resource"}}, "beetasksserviceserviceService739C71D4": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "bee-tasks-service-container", "ContainerPort": 8085, "TargetGroupArn": {"Ref": "beetasksservicetg20637D76"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "bee-tasks-service", "TaskDefinition": {"Ref": "beetasksservicetask7F25397B"}}, "DependsOn": ["BidBeesALBHTTPListenerbeetasksserviceruleRule584454E6", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/bee-tasks-service-service/Service"}}, "beetasksserviceserviceTaskCountTarget310FA8B5": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["beetasksserviceserviceService739C71D4", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/bee-tasks-service-service/TaskCount/Target/Resource"}}, "beetasksserviceserviceTaskCountTargetbeetasksservicecpuscalingEE119C7C": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationbeetasksserviceserviceTaskCountTargetbeetasksservicecpuscalingE7B538F5", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "beetasksserviceserviceTaskCountTarget310FA8B5"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/bee-tasks-service-service/TaskCount/Target/bee-tasks-service-cpu-scaling/Resource"}}, "beetasksserviceserviceTaskCountTargetbeetasksservicememoryscaling3E38F76D": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationbeetasksserviceserviceTaskCountTargetbeetasksservicememoryscaling316A9DEE", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "beetasksserviceserviceTaskCountTarget310FA8B5"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/bee-tasks-service-service/TaskCount/Target/bee-tasks-service-memory-scaling/Resource"}}, "notificationservicetask442A5982": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8086"}, {"Name": "SERVICE_NAME", "Value": "notification-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["notificationservicerepo804D0692", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["notificationservicerepo804D0692", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "notificationservicerepo804D0692"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "notificationservicelogs7CD973DC"}, "awslogs-stream-prefix": "notification-service", "awslogs-region": "us-east-1"}}, "Name": "notification-service-container", "PortMappings": [{"ContainerPort": 8086, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationnotificationservicetask194EEBB5", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/notification-service-task/Resource"}}, "notificationserviceserviceService89B296A4": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "notification-service-container", "ContainerPort": 8086, "TargetGroupArn": {"Ref": "notificationservicetgB12B03CD"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "notification-service", "TaskDefinition": {"Ref": "notificationservicetask442A5982"}}, "DependsOn": ["BidBeesALBHTTPListenernotificationserviceruleRule82A0F258", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/notification-service-service/Service"}}, "notificationserviceserviceTaskCountTarget55F2BBC7": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["notificationserviceserviceService89B296A4", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/notification-service-service/TaskCount/Target/Resource"}}, "notificationserviceserviceTaskCountTargetnotificationservicecpuscaling06FEDCE2": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationnotificationserviceserviceTaskCountTargetnotificationservicecpuscalingB3091F7E", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "notificationserviceserviceTaskCountTarget55F2BBC7"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/notification-service-service/TaskCount/Target/notification-service-cpu-scaling/Resource"}}, "notificationserviceserviceTaskCountTargetnotificationservicememoryscaling298D3F96": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationnotificationserviceserviceTaskCountTargetnotificationservicememoryscalingA2731E86", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "notificationserviceserviceTaskCountTarget55F2BBC7"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/notification-service-service/TaskCount/Target/notification-service-memory-scaling/Resource"}}, "documentservicetask45406817": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8087"}, {"Name": "SERVICE_NAME", "Value": "document-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["documentservicerepo9A6D7CB9", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["documentservicerepo9A6D7CB9", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "documentservicerepo9A6D7CB9"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "documentservicelogs78E0FE6B"}, "awslogs-stream-prefix": "document-service", "awslogs-region": "us-east-1"}}, "Name": "document-service-container", "PortMappings": [{"ContainerPort": 8087, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationdocumentservicetaskFAF6C1B1", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/document-service-task/Resource"}}, "documentserviceserviceServiceA8BEF05A": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "document-service-container", "ContainerPort": 8087, "TargetGroupArn": {"Ref": "documentservicetgC7D23E05"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "document-service", "TaskDefinition": {"Ref": "documentservicetask45406817"}}, "DependsOn": ["BidBeesALBHTTPListenerdocumentserviceruleRule836574F5", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/document-service-service/Service"}}, "documentserviceserviceTaskCountTarget48004D5D": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["documentserviceserviceServiceA8BEF05A", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/document-service-service/TaskCount/Target/Resource"}}, "documentserviceserviceTaskCountTargetdocumentservicecpuscalingCD584F1F": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationdocumentserviceserviceTaskCountTargetdocumentservicecpuscaling56334AD9", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "documentserviceserviceTaskCountTarget48004D5D"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/document-service-service/TaskCount/Target/document-service-cpu-scaling/Resource"}}, "documentserviceserviceTaskCountTargetdocumentservicememoryscalingE3B06E20": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationdocumentserviceserviceTaskCountTargetdocumentservicememoryscaling5650983D", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "documentserviceserviceTaskCountTarget48004D5D"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/document-service-service/TaskCount/Target/document-service-memory-scaling/Resource"}}, "mapservicetask624D6A25": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8088"}, {"Name": "SERVICE_NAME", "Value": "map-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["mapservicerepo346DE86C", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["mapservicerepo346DE86C", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "mapservicerepo346DE86C"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "mapservicelogsBAF8FEB4"}, "awslogs-stream-prefix": "map-service", "awslogs-region": "us-east-1"}}, "Name": "map-service-container", "PortMappings": [{"ContainerPort": 8088, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationmapservicetaskB36760B9", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/map-service-task/Resource"}}, "mapserviceserviceServiceB4A6A16F": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "map-service-container", "ContainerPort": 8088, "TargetGroupArn": {"Ref": "mapservicetg348246DC"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "map-service", "TaskDefinition": {"Ref": "mapservicetask624D6A25"}}, "DependsOn": ["BidBeesALBHTTPListenermapserviceruleRuleCD2A9EAE", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/map-service-service/Service"}}, "mapserviceserviceTaskCountTargetDD279F24": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["mapserviceserviceServiceB4A6A16F", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/map-service-service/TaskCount/Target/Resource"}}, "mapserviceserviceTaskCountTargetmapservicecpuscaling35C6CDA0": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationmapserviceserviceTaskCountTargetmapservicecpuscalingCC0DED2B", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "mapserviceserviceTaskCountTargetDD279F24"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/map-service-service/TaskCount/Target/map-service-cpu-scaling/Resource"}}, "mapserviceserviceTaskCountTargetmapservicememoryscaling48D8FB87": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationmapserviceserviceTaskCountTargetmapservicememoryscaling9EBA4A1E", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "mapserviceserviceTaskCountTargetDD279F24"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/map-service-service/TaskCount/Target/map-service-memory-scaling/Resource"}}, "analyticsservicetask6AD1EC79": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8089"}, {"Name": "SERVICE_NAME", "Value": "analytics-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["analyticsservicerepo093CF65D", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["analyticsservicerepo093CF65D", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "analyticsservicerepo093CF65D"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "analyticsservicelogsA436E2AC"}, "awslogs-stream-prefix": "analytics-service", "awslogs-region": "us-east-1"}}, "Name": "analytics-service-container", "PortMappings": [{"ContainerPort": 8089, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationanalyticsservicetask2BEE0C73", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/analytics-service-task/Resource"}}, "analyticsserviceserviceServiceC3F831E1": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "analytics-service-container", "ContainerPort": 8089, "TargetGroupArn": {"Ref": "analyticsservicetg6F3949F7"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "analytics-service", "TaskDefinition": {"Ref": "analyticsservicetask6AD1EC79"}}, "DependsOn": ["BidBeesALBHTTPListeneranalyticsserviceruleRuleB47530D4", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/analytics-service-service/Service"}}, "analyticsserviceserviceTaskCountTargetF615E4CB": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["analyticsserviceserviceServiceC3F831E1", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/analytics-service-service/TaskCount/Target/Resource"}}, "analyticsserviceserviceTaskCountTargetanalyticsservicecpuscaling55922504": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationanalyticsserviceserviceTaskCountTargetanalyticsservicecpuscalingBB6BF743", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "analyticsserviceserviceTaskCountTargetF615E4CB"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/analytics-service-service/TaskCount/Target/analytics-service-cpu-scaling/Resource"}}, "analyticsserviceserviceTaskCountTargetanalyticsservicememoryscaling94E348F5": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationanalyticsserviceserviceTaskCountTargetanalyticsservicememoryscaling4F3F2830", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "analyticsserviceserviceTaskCountTargetF615E4CB"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/analytics-service-service/TaskCount/Target/analytics-service-memory-scaling/Resource"}}, "paymentservicetask8313C54C": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8090"}, {"Name": "SERVICE_NAME", "Value": "payment-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["paymentservicerepo943B5440", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["paymentservicerepo943B5440", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "paymentservicerepo943B5440"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "paymentservicelogsFACD9834"}, "awslogs-stream-prefix": "payment-service", "awslogs-region": "us-east-1"}}, "Name": "payment-service-container", "PortMappings": [{"ContainerPort": 8090, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationpaymentservicetaskA8EC13DF", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/payment-service-task/Resource"}}, "paymentserviceserviceService2F613185": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "payment-service-container", "ContainerPort": 8090, "TargetGroupArn": {"Ref": "paymentservicetgE1A844DA"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "payment-service", "TaskDefinition": {"Ref": "paymentservicetask8313C54C"}}, "DependsOn": ["BidBeesALBHTTPListenerpaymentserviceruleRuleF9D947FF", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/payment-service-service/Service"}}, "paymentserviceserviceTaskCountTarget5A24D8A5": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["paymentserviceserviceService2F613185", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/payment-service-service/TaskCount/Target/Resource"}}, "paymentserviceserviceTaskCountTargetpaymentservicecpuscaling1C792CAA": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationpaymentserviceserviceTaskCountTargetpaymentservicecpuscalingFDEBD4A7", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "paymentserviceserviceTaskCountTarget5A24D8A5"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/payment-service-service/TaskCount/Target/payment-service-cpu-scaling/Resource"}}, "paymentserviceserviceTaskCountTargetpaymentservicememoryscalingB3A84608": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationpaymentserviceserviceTaskCountTargetpaymentservicememoryscalingBEFEB8F4", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "paymentserviceserviceTaskCountTarget5A24D8A5"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/payment-service-service/TaskCount/Target/payment-service-memory-scaling/Resource"}}, "courierservicetask922C09C1": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8091"}, {"Name": "SERVICE_NAME", "Value": "courier-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["courierservicerepoDA20D569", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["courierservicerepoDA20D569", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "courierservicerepoDA20D569"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "courierservicelogsA18DE1D5"}, "awslogs-stream-prefix": "courier-service", "awslogs-region": "us-east-1"}}, "Name": "courier-service-container", "PortMappings": [{"ContainerPort": 8091, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationcourierservicetask90CED58D", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/courier-service-task/Resource"}}, "courierserviceserviceService846412FE": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "courier-service-container", "ContainerPort": 8091, "TargetGroupArn": {"Ref": "courierservicetgA18A31DB"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "courier-service", "TaskDefinition": {"Ref": "courierservicetask922C09C1"}}, "DependsOn": ["BidBeesALBHTTPListenercourierserviceruleRuleC512C75F", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/courier-service-service/Service"}}, "courierserviceserviceTaskCountTargetA3D4B697": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["courierserviceserviceService846412FE", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/courier-service-service/TaskCount/Target/Resource"}}, "courierserviceserviceTaskCountTargetcourierservicecpuscaling120EAB1D": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationcourierserviceserviceTaskCountTargetcourierservicecpuscaling9B55C0E9", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "courierserviceserviceTaskCountTargetA3D4B697"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/courier-service-service/TaskCount/Target/courier-service-cpu-scaling/Resource"}}, "courierserviceserviceTaskCountTargetcourierservicememoryscaling05A38C40": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationcourierserviceserviceTaskCountTargetcourierservicememoryscaling1E62EE7F", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "courierserviceserviceTaskCountTargetA3D4B697"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/courier-service-service/TaskCount/Target/courier-service-memory-scaling/Resource"}}, "transportservicetask3E6822F3": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8092"}, {"Name": "SERVICE_NAME", "Value": "transport-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["transportservicerepo8F304D9F", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["transportservicerepo8F304D9F", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "transportservicerepo8F304D9F"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "transportservicelogs9BE9ADEB"}, "awslogs-stream-prefix": "transport-service", "awslogs-region": "us-east-1"}}, "Name": "transport-service-container", "PortMappings": [{"ContainerPort": 8092, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationtransportservicetaskE398D380", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/transport-service-task/Resource"}}, "transportserviceserviceService1B49B44B": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "transport-service-container", "ContainerPort": 8092, "TargetGroupArn": {"Ref": "transportservicetg6FE707B2"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "transport-service", "TaskDefinition": {"Ref": "transportservicetask3E6822F3"}}, "DependsOn": ["BidBeesALBHTTPListenertransportserviceruleRuleEA083648", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/transport-service-service/Service"}}, "transportserviceserviceTaskCountTargetC4C4DDBE": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["transportserviceserviceService1B49B44B", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/transport-service-service/TaskCount/Target/Resource"}}, "transportserviceserviceTaskCountTargettransportservicecpuscalingA946B1B5": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationtransportserviceserviceTaskCountTargettransportservicecpuscaling9E3BBF3F", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "transportserviceserviceTaskCountTargetC4C4DDBE"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/transport-service-service/TaskCount/Target/transport-service-cpu-scaling/Resource"}}, "transportserviceserviceTaskCountTargettransportservicememoryscalingA13540FB": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationtransportserviceserviceTaskCountTargettransportservicememoryscalingF84BAA68", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "transportserviceserviceTaskCountTargetC4C4DDBE"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/transport-service-service/TaskCount/Target/transport-service-memory-scaling/Resource"}}, "supplierservicetask6C8AE572": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8093"}, {"Name": "SERVICE_NAME", "Value": "supplier-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["supplierservicerepoE3B29C29", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["supplierservicerepoE3B29C29", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "supplierservicerepoE3B29C29"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "supplierservicelogs553F878A"}, "awslogs-stream-prefix": "supplier-service", "awslogs-region": "us-east-1"}}, "Name": "supplier-service-container", "PortMappings": [{"ContainerPort": 8093, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationsupplierservicetaskECD990B8", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/supplier-service-task/Resource"}}, "supplierserviceserviceService06E116D7": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "supplier-service-container", "ContainerPort": 8093, "TargetGroupArn": {"Ref": "supplierservicetgC1978694"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "supplier-service", "TaskDefinition": {"Ref": "supplierservicetask6C8AE572"}}, "DependsOn": ["BidBeesALBHTTPListenersupplierserviceruleRuleFC04BCED", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/supplier-service-service/Service"}}, "supplierserviceserviceTaskCountTarget8784BEBD": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["supplierserviceserviceService06E116D7", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/supplier-service-service/TaskCount/Target/Resource"}}, "supplierserviceserviceTaskCountTargetsupplierservicecpuscaling0A7098B3": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationsupplierserviceserviceTaskCountTargetsupplierservicecpuscaling228F99B6", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "supplierserviceserviceTaskCountTarget8784BEBD"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/supplier-service-service/TaskCount/Target/supplier-service-cpu-scaling/Resource"}}, "supplierserviceserviceTaskCountTargetsupplierservicememoryscaling783E8699": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationsupplierserviceserviceTaskCountTargetsupplierservicememoryscaling09213BAE", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "supplierserviceserviceTaskCountTarget8784BEBD"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/supplier-service-service/TaskCount/Target/supplier-service-memory-scaling/Resource"}}, "mlservicetaskAD6A88BA": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8094"}, {"Name": "SERVICE_NAME", "Value": "ml-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["mlservicerepo401FACFD", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["mlservicerepo401FACFD", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "mlservicerepo401FACFD"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "mlservicelogsC8252B12"}, "awslogs-stream-prefix": "ml-service", "awslogs-region": "us-east-1"}}, "Name": "ml-service-container", "PortMappings": [{"ContainerPort": 8094, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationmlservicetaskF162E418", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/ml-service-task/Resource"}}, "mlserviceserviceService2712EBFA": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "ml-service-container", "ContainerPort": 8094, "TargetGroupArn": {"Ref": "mlservicetg7270107E"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "ml-service", "TaskDefinition": {"Ref": "mlservicetaskAD6A88BA"}}, "DependsOn": ["BidBeesALBHTTPListenermlserviceruleRule7A18151A", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/ml-service-service/Service"}}, "mlserviceserviceTaskCountTarget0816B565": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["mlserviceserviceService2712EBFA", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/ml-service-service/TaskCount/Target/Resource"}}, "mlserviceserviceTaskCountTargetmlservicecpuscaling96F8069D": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationmlserviceserviceTaskCountTargetmlservicecpuscalingADFE77D8", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "mlserviceserviceTaskCountTarget0816B565"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/ml-service-service/TaskCount/Target/ml-service-cpu-scaling/Resource"}}, "mlserviceserviceTaskCountTargetmlservicememoryscalingDF4D7C24": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationmlserviceserviceTaskCountTargetmlservicememoryscaling4DD3ED76", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "mlserviceserviceTaskCountTarget0816B565"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/ml-service-service/TaskCount/Target/ml-service-memory-scaling/Resource"}}, "queenbeeanchorservicetask4E31F632": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8095"}, {"Name": "SERVICE_NAME", "Value": "queenbee-anchor-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["queenbeeanchorservicerepoED4CA8B7", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["queenbeeanchorservicerepoED4CA8B7", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "queenbeeanchorservicerepoED4CA8B7"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "queenbeeanchorservicelogsECC54205"}, "awslogs-stream-prefix": "queenbee-anchor-service", "awslogs-region": "us-east-1"}}, "Name": "queenbee-anchor-service-container", "PortMappings": [{"ContainerPort": 8095, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationqueenbeeanchorservicetask730DD711", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/queenbee-anchor-service-task/Resource"}}, "queenbeeanchorserviceserviceServiceCCF31EA9": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "queenbee-anchor-service-container", "ContainerPort": 8095, "TargetGroupArn": {"Ref": "queenbeeanchorservicetgF5F7F022"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "queenbee-anchor-service", "TaskDefinition": {"Ref": "queenbeeanchorservicetask4E31F632"}}, "DependsOn": ["BidBeesALBHTTPListenerqueenbeeanchorserviceruleRuleF35A8AD5", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/queenbee-anchor-service-service/Service"}}, "queenbeeanchorserviceserviceTaskCountTarget424FC9B0": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["queenbeeanchorserviceserviceServiceCCF31EA9", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/queenbee-anchor-service-service/TaskCount/Target/Resource"}}, "queenbeeanchorserviceserviceTaskCountTargetqueenbeeanchorservicecpuscaling1C1D307E": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationqueenbeeanchorserviceserviceTaskCountTargetqueenbeeanchorservicecpuscaling612C268B", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "queenbeeanchorserviceserviceTaskCountTarget424FC9B0"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/queenbee-anchor-service-service/TaskCount/Target/queenbee-anchor-service-cpu-scaling/Resource"}}, "queenbeeanchorserviceserviceTaskCountTargetqueenbeeanchorservicememoryscaling769FEC69": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationqueenbeeanchorserviceserviceTaskCountTargetqueenbeeanchorservicememoryscaling9507A20D", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "queenbeeanchorserviceserviceTaskCountTarget424FC9B0"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/queenbee-anchor-service-service/TaskCount/Target/queenbee-anchor-service-memory-scaling/Resource"}}, "doclingprocessortask0691A232": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8096"}, {"Name": "SERVICE_NAME", "Value": "docling-processor"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["doclingprocessorrepoDB6D7623", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["doclingprocessorrepoDB6D7623", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "doclingprocessorrepoDB6D7623"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "doclingprocessorlogsE5453BE7"}, "awslogs-stream-prefix": "docling-processor", "awslogs-region": "us-east-1"}}, "Name": "docling-processor-container", "PortMappings": [{"ContainerPort": 8096, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationdoclingprocessortaskBED120EE", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/docling-processor-task/Resource"}}, "doclingprocessorserviceServiceF6C505F2": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "docling-processor-container", "ContainerPort": 8096, "TargetGroupArn": {"Ref": "doclingprocessortgF372CF92"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "docling-processor", "TaskDefinition": {"Ref": "doclingprocessortask0691A232"}}, "DependsOn": ["BidBeesALBHTTPListenerdoclingprocessorruleRule3C70C379", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/docling-processor-service/Service"}}, "doclingprocessorserviceTaskCountTargetDC806016": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["doclingprocessorserviceServiceF6C505F2", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/docling-processor-service/TaskCount/Target/Resource"}}, "doclingprocessorserviceTaskCountTargetdoclingprocessorcpuscalingCA5CF332": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationdoclingprocessorserviceTaskCountTargetdoclingprocessorcpuscalingE6F26F71", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "doclingprocessorserviceTaskCountTargetDC806016"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/docling-processor-service/TaskCount/Target/docling-processor-cpu-scaling/Resource"}}, "doclingprocessorserviceTaskCountTargetdoclingprocessormemoryscaling4669A19A": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationdoclingprocessorserviceTaskCountTargetdoclingprocessormemoryscalingF0DAB04D", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "doclingprocessorserviceTaskCountTargetDC806016"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/docling-processor-service/TaskCount/Target/docling-processor-memory-scaling/Resource"}}, "studentverificationservicetask1E18885E": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8097"}, {"Name": "SERVICE_NAME", "Value": "student-verification-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["studentverificationservicerepoA397EE22", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["studentverificationservicerepoA397EE22", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "studentverificationservicerepoA397EE22"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "studentverificationservicelogs4EE37A8F"}, "awslogs-stream-prefix": "student-verification-service", "awslogs-region": "us-east-1"}}, "Name": "student-verification-service-container", "PortMappings": [{"ContainerPort": 8097, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationstudentverificationservicetaskA2EB5AFD", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/student-verification-service-task/Resource"}}, "studentverificationserviceserviceService0BE0DA56": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "student-verification-service-container", "ContainerPort": 8097, "TargetGroupArn": {"Ref": "studentverificationservicetg29B48E80"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "student-verification-service", "TaskDefinition": {"Ref": "studentverificationservicetask1E18885E"}}, "DependsOn": ["BidBeesALBHTTPListenerstudentverificationserviceruleRule04DD2D2D", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/student-verification-service-service/Service"}}, "studentverificationserviceserviceTaskCountTargetD3A72907": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["studentverificationserviceserviceService0BE0DA56", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/student-verification-service-service/TaskCount/Target/Resource"}}, "studentverificationserviceserviceTaskCountTargetstudentverificationservicecpuscaling6B0A954A": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationstudentverificationserviceserviceTaskCountTargetstudentverificationservicecpuscaling89AFF564", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "studentverificationserviceserviceTaskCountTargetD3A72907"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/student-verification-service-service/TaskCount/Target/student-verification-service-cpu-scaling/Resource"}}, "studentverificationserviceserviceTaskCountTargetstudentverificationservicememoryscaling5680106F": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationstudentverificationserviceserviceTaskCountTargetstudentverificationservicememoryscalingC8BB78C5", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "studentverificationserviceserviceTaskCountTargetD3A72907"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/student-verification-service-service/TaskCount/Target/student-verification-service-memory-scaling/Resource"}}, "kafkaservicetask180573FE": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8098"}, {"Name": "SERVICE_NAME", "Value": "kafka-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["kafkaservicerepoEEB199D3", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["kafkaservicerepoEEB199D3", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "kafkaservicerepoEEB199D3"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "kafkaservicelogsF9DB15FB"}, "awslogs-stream-prefix": "kafka-service", "awslogs-region": "us-east-1"}}, "Name": "kafka-service-container", "PortMappings": [{"ContainerPort": 8098, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationkafkaservicetask00D9FCD3", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/kafka-service-task/Resource"}}, "kafkaserviceserviceService4C507CF4": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "kafka-service-container", "ContainerPort": 8098, "TargetGroupArn": {"Ref": "kafkaservicetg5B22B24F"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "kafka-service", "TaskDefinition": {"Ref": "kafkaservicetask180573FE"}}, "DependsOn": ["BidBeesALBHTTPListenerkafkaserviceruleRule03EF114C", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/kafka-service-service/Service"}}, "kafkaserviceserviceTaskCountTargetE9BF05BE": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["kafkaserviceserviceService4C507CF4", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/kafka-service-service/TaskCount/Target/Resource"}}, "kafkaserviceserviceTaskCountTargetkafkaservicecpuscalingE95A502E": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationkafkaserviceserviceTaskCountTargetkafkaservicecpuscalingCF19C51D", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "kafkaserviceserviceTaskCountTargetE9BF05BE"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/kafka-service-service/TaskCount/Target/kafka-service-cpu-scaling/Resource"}}, "kafkaserviceserviceTaskCountTargetkafkaservicememoryscaling19EA1C40": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationkafkaserviceserviceTaskCountTargetkafkaservicememoryscaling7223F5D2", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "kafkaserviceserviceTaskCountTargetE9BF05BE"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/kafka-service-service/TaskCount/Target/kafka-service-memory-scaling/Resource"}}, "contractorsyncservicetask6A3ED726": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8099"}, {"Name": "SERVICE_NAME", "Value": "contractorsync-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["contractorsyncservicerepoCD31E3BF", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["contractorsyncservicerepoCD31E3BF", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "contractorsyncservicerepoCD31E3BF"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "contractorsyncservicelogs7C0D6842"}, "awslogs-stream-prefix": "contractorsync-service", "awslogs-region": "us-east-1"}}, "Name": "contractorsync-service-container", "PortMappings": [{"ContainerPort": 8099, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationcontractorsyncservicetaskE9448D3F", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/contractorsync-service-task/Resource"}}, "contractorsyncserviceserviceService929CB140": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "contractorsync-service-container", "ContainerPort": 8099, "TargetGroupArn": {"Ref": "contractorsyncservicetgFA48901D"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "contractorsync-service", "TaskDefinition": {"Ref": "contractorsyncservicetask6A3ED726"}}, "DependsOn": ["BidBeesALBHTTPListenercontractorsyncserviceruleRuleE59808D5", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/contractorsync-service-service/Service"}}, "contractorsyncserviceserviceTaskCountTarget514577E3": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["contractorsyncserviceserviceService929CB140", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/contractorsync-service-service/TaskCount/Target/Resource"}}, "contractorsyncserviceserviceTaskCountTargetcontractorsyncservicecpuscaling447D8767": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationcontractorsyncserviceserviceTaskCountTargetcontractorsyncservicecpuscalingAC4C9983", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "contractorsyncserviceserviceTaskCountTarget514577E3"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/contractorsync-service-service/TaskCount/Target/contractorsync-service-cpu-scaling/Resource"}}, "contractorsyncserviceserviceTaskCountTargetcontractorsyncservicememoryscaling37E0112C": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationcontractorsyncserviceserviceTaskCountTargetcontractorsyncservicememoryscaling502B3105", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "contractorsyncserviceserviceTaskCountTarget514577E3"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/contractorsync-service-service/TaskCount/Target/contractorsync-service-memory-scaling/Resource"}}, "skillsyncservicetask370DF481": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8100"}, {"Name": "SERVICE_NAME", "Value": "skillsync-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["skillsyncservicerepo48E0A187", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["skillsyncservicerepo48E0A187", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "skillsyncservicerepo48E0A187"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "skillsyncservicelogsC4922B5F"}, "awslogs-stream-prefix": "skillsync-service", "awslogs-region": "us-east-1"}}, "Name": "skillsync-service-container", "PortMappings": [{"ContainerPort": 8100, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationskillsyncservicetask2D8A23A6", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/skillsync-service-task/Resource"}}, "skillsyncserviceserviceService0341C206": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "skillsync-service-container", "ContainerPort": 8100, "TargetGroupArn": {"Ref": "skillsyncservicetg9F463764"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "skillsync-service", "TaskDefinition": {"Ref": "skillsyncservicetask370DF481"}}, "DependsOn": ["BidBeesALBHTTPListenerskillsyncserviceruleRuleDDC935F5", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/skillsync-service-service/Service"}}, "skillsyncserviceserviceTaskCountTarget6F5BE1A0": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["skillsyncserviceserviceService0341C206", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/skillsync-service-service/TaskCount/Target/Resource"}}, "skillsyncserviceserviceTaskCountTargetskillsyncservicecpuscaling96F9AF1D": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationskillsyncserviceserviceTaskCountTargetskillsyncservicecpuscaling796AAC84", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "skillsyncserviceserviceTaskCountTarget6F5BE1A0"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/skillsync-service-service/TaskCount/Target/skillsync-service-cpu-scaling/Resource"}}, "skillsyncserviceserviceTaskCountTargetskillsyncservicememoryscaling80884B06": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationskillsyncserviceserviceTaskCountTargetskillsyncservicememoryscaling4E857F14", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "skillsyncserviceserviceTaskCountTarget6F5BE1A0"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/skillsync-service-service/TaskCount/Target/skillsync-service-memory-scaling/Resource"}}, "toolsyncservicetaskB294F66B": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "8101"}, {"Name": "SERVICE_NAME", "Value": "toolsync-service"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["toolsyncservicerepo1BA4B4B1", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["toolsyncservicerepo1BA4B4B1", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "toolsyncservicerepo1BA4B4B1"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "toolsyncservicelogs5835EBFE"}, "awslogs-stream-prefix": "toolsync-service", "awslogs-region": "us-east-1"}}, "Name": "toolsync-service-container", "PortMappings": [{"ContainerPort": 8101, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": "arn:aws:secretsmanager:us-east-1:955155721411:secret:bidbees/database"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "bidbeesproductionapplicationtoolsyncservicetaskDEB70FB8", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["TaskRole30FC0FBB", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/toolsync-service-task/Resource"}}, "toolsyncserviceserviceServiceE5CCE112": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "EnableExecuteCommand": true, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "toolsync-service-container", "ContainerPort": 8101, "TargetGroupArn": {"Ref": "toolsyncservicetg90928E86"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputFnGetAttECSSecurityGroupA14DBE7DGroupIdF26764D6"}], "Subnets": [{"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet1Subnet9CC74C9AE2B323D2"}, {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPCPrivateSubnet2Subnet567B500C172DBA5C"}]}}, "ServiceName": "toolsync-service", "TaskDefinition": {"Ref": "toolsyncservicetaskB294F66B"}}, "DependsOn": ["BidBeesALBHTTPListenertoolsyncserviceruleRuleB71EC150", "TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/toolsync-service-service/Service"}}, "toolsyncserviceserviceTaskCountTargetA28E56F3": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 5, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesCluster2DD1D24B52A2B97F"}, "/", {"Fn::GetAtt": ["toolsyncserviceserviceServiceE5CCE112", "Name"]}]]}, "RoleARN": "arn:aws:iam::955155721411:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/toolsync-service-service/TaskCount/Target/Resource"}}, "toolsyncserviceserviceTaskCountTargettoolsyncservicecpuscaling640649E7": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationtoolsyncserviceserviceTaskCountTargettoolsyncservicecpuscaling5AFD4712", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "toolsyncserviceserviceTaskCountTargetA28E56F3"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/toolsync-service-service/TaskCount/Target/toolsync-service-cpu-scaling/Resource"}}, "toolsyncserviceserviceTaskCountTargettoolsyncservicememoryscalingBDF34A9E": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "bidbeesproductionapplicationtoolsyncserviceserviceTaskCountTargettoolsyncservicememoryscaling8DEDA8D9", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "toolsyncserviceserviceTaskCountTargetA28E56F3"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["TaskRoleDefaultPolicy07FC53DE", "TaskRole30FC0FBB"], "Metadata": {"aws:cdk:path": "bidbees-production-application/toolsync-service-service/TaskCount/Target/toolsync-service-memory-scaling/Resource"}}, "apigatewaytg3B8B1926": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8080, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-tg/Resource"}}, "authservicetg8ADDF0AF": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8081, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/auth-service-tg/Resource"}}, "userservicetg90716C8D": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8082, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/user-service-tg/Resource"}}, "tenderservicetg1376547D": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8083, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/tender-service-tg/Resource"}}, "biddingservicetgFD35E762": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8084, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/bidding-service-tg/Resource"}}, "beetasksservicetg20637D76": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8085, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/bee-tasks-service-tg/Resource"}}, "notificationservicetgB12B03CD": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8086, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/notification-service-tg/Resource"}}, "documentservicetgC7D23E05": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8087, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/document-service-tg/Resource"}}, "mapservicetg348246DC": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8088, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/map-service-tg/Resource"}}, "analyticsservicetg6F3949F7": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8089, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/analytics-service-tg/Resource"}}, "paymentservicetgE1A844DA": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8090, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/payment-service-tg/Resource"}}, "courierservicetgA18A31DB": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8091, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/courier-service-tg/Resource"}}, "transportservicetg6FE707B2": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8092, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/transport-service-tg/Resource"}}, "supplierservicetgC1978694": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8093, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/supplier-service-tg/Resource"}}, "mlservicetg7270107E": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8094, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/ml-service-tg/Resource"}}, "queenbeeanchorservicetgF5F7F022": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8095, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/queenbee-anchor-service-tg/Resource"}}, "doclingprocessortgF372CF92": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8096, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/docling-processor-tg/Resource"}}, "studentverificationservicetg29B48E80": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8097, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/student-verification-service-tg/Resource"}}, "kafkaservicetg5B22B24F": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8098, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/kafka-service-tg/Resource"}}, "contractorsyncservicetgFA48901D": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8099, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/contractorsync-service-tg/Resource"}}, "skillsyncservicetg9F463764": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8100, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/skillsync-service-tg/Resource"}}, "toolsyncservicetg90928E86": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8101, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}, {"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/toolsync-service-tg/Resource"}}, "apigatewaydefaulttgAFB2C86F": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckPath": "/health", "Matcher": {"HttpCode": "200"}, "Port": 8080, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "VpcId": {"Fn::ImportValue": "bidbees-production-infrastructure:ExportsOutputRefBidBeesVPC585BC6635F18E126"}}, "Metadata": {"aws:cdk:path": "bidbees-production-application/api-gateway-default-tg/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/21QTW/CMAz9LdxDVnrYfWPaLqBNLffJpCYyDXGVD9AU9b9PbWBtpZ383vOz8+JSbopCFiu4+bVq2rWho0x1ANUKuPnvhAZ8IGUYmiMYsIqsvpYyvXSdIQWB2O4Ymtexh05sT0s+95EPaO+eB/6nX0WDc8/IZ74DOI3hw3HsBtuM9gKVk6nCjj0Fdj9Df2K9ILjIVHHeP9YvNqRG3x3twYLGZtIXQi8May/TjvVfgAceXvcyvYPTEPAAvn3DE1kaMuegS4VtALLoZtp9tkZ3JTWGfMBagYGjGdduOdrQC5hOAjGwV2DIapkm63CYcclSyeXgQLVkdZ3npg8vhL4XFXqOLsf5jKGLIZ81q72w3KA8+6drWcrNsyxWZ0+0dtEGuqCscv0F+i5NAWgCAAA="}, "Metadata": {"aws:cdk:path": "bidbees-production-application/CDKMetadata/Default"}}}, "Outputs": {"LoadBalancerDNS": {"Description": "Application Load Balancer DNS name", "Value": {"Fn::GetAtt": ["BidBeesALBDEF3E75A", "DNSName"]}, "Export": {"Name": "BidBeesALBDNS"}}, "LoadBalancerArn": {"Description": "Application Load Balancer ARN", "Value": {"Ref": "BidBeesALBDEF3E75A"}, "Export": {"Name": "BidBeesALBArn"}}, "apigatewayECRURI": {"Description": "ECR repository URI for api-gateway", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["apigatewayrepo6C90AC26", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["apigatewayrepo6C90AC26", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "apigatewayrepo6C90AC26"}]]}, "Export": {"Name": "BidBees-api-gateway-ECR-URI"}}, "authserviceECRURI": {"Description": "ECR repository URI for auth-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["authservicerepo89047BB6", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["authservicerepo89047BB6", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "authservicerepo89047BB6"}]]}, "Export": {"Name": "BidBees-auth-service-ECR-URI"}}, "userserviceECRURI": {"Description": "ECR repository URI for user-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["userservicerepoBD0D621C", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["userservicerepoBD0D621C", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "userservicerepoBD0D621C"}]]}, "Export": {"Name": "BidBees-user-service-ECR-URI"}}, "tenderserviceECRURI": {"Description": "ECR repository URI for tender-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["tenderservicerepoF6BB2092", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["tenderservicerepoF6BB2092", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "tenderservicerepoF6BB2092"}]]}, "Export": {"Name": "BidBees-tender-service-ECR-URI"}}, "biddingserviceECRURI": {"Description": "ECR repository URI for bidding-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["biddingservicerepoAB98F515", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["biddingservicerepoAB98F515", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "biddingservicerepoAB98F515"}]]}, "Export": {"Name": "BidBees-bidding-service-ECR-URI"}}, "beetasksserviceECRURI": {"Description": "ECR repository URI for bee-tasks-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["beetasksservicerepoC090FA26", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["beetasksservicerepoC090FA26", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "beetasksservicerepoC090FA26"}]]}, "Export": {"Name": "BidBees-bee-tasks-service-ECR-URI"}}, "notificationserviceECRURI": {"Description": "ECR repository URI for notification-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["notificationservicerepo804D0692", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["notificationservicerepo804D0692", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "notificationservicerepo804D0692"}]]}, "Export": {"Name": "BidBees-notification-service-ECR-URI"}}, "documentserviceECRURI": {"Description": "ECR repository URI for document-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["documentservicerepo9A6D7CB9", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["documentservicerepo9A6D7CB9", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "documentservicerepo9A6D7CB9"}]]}, "Export": {"Name": "BidBees-document-service-ECR-URI"}}, "mapserviceECRURI": {"Description": "ECR repository URI for map-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["mapservicerepo346DE86C", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["mapservicerepo346DE86C", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "mapservicerepo346DE86C"}]]}, "Export": {"Name": "BidBees-map-service-ECR-URI"}}, "analyticsserviceECRURI": {"Description": "ECR repository URI for analytics-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["analyticsservicerepo093CF65D", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["analyticsservicerepo093CF65D", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "analyticsservicerepo093CF65D"}]]}, "Export": {"Name": "BidBees-analytics-service-ECR-URI"}}, "paymentserviceECRURI": {"Description": "ECR repository URI for payment-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["paymentservicerepo943B5440", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["paymentservicerepo943B5440", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "paymentservicerepo943B5440"}]]}, "Export": {"Name": "BidBees-payment-service-ECR-URI"}}, "courierserviceECRURI": {"Description": "ECR repository URI for courier-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["courierservicerepoDA20D569", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["courierservicerepoDA20D569", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "courierservicerepoDA20D569"}]]}, "Export": {"Name": "BidBees-courier-service-ECR-URI"}}, "transportserviceECRURI": {"Description": "ECR repository URI for transport-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["transportservicerepo8F304D9F", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["transportservicerepo8F304D9F", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "transportservicerepo8F304D9F"}]]}, "Export": {"Name": "BidBees-transport-service-ECR-URI"}}, "supplierserviceECRURI": {"Description": "ECR repository URI for supplier-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["supplierservicerepoE3B29C29", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["supplierservicerepoE3B29C29", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "supplierservicerepoE3B29C29"}]]}, "Export": {"Name": "BidBees-supplier-service-ECR-URI"}}, "mlserviceECRURI": {"Description": "ECR repository URI for ml-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["mlservicerepo401FACFD", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["mlservicerepo401FACFD", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "mlservicerepo401FACFD"}]]}, "Export": {"Name": "BidBees-ml-service-ECR-URI"}}, "queenbeeanchorserviceECRURI": {"Description": "ECR repository URI for queenbee-anchor-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["queenbeeanchorservicerepoED4CA8B7", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["queenbeeanchorservicerepoED4CA8B7", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "queenbeeanchorservicerepoED4CA8B7"}]]}, "Export": {"Name": "BidBees-queenbee-anchor-service-ECR-URI"}}, "doclingprocessorECRURI": {"Description": "ECR repository URI for docling-processor", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["doclingprocessorrepoDB6D7623", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["doclingprocessorrepoDB6D7623", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "doclingprocessorrepoDB6D7623"}]]}, "Export": {"Name": "BidBees-docling-processor-ECR-URI"}}, "studentverificationserviceECRURI": {"Description": "ECR repository URI for student-verification-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["studentverificationservicerepoA397EE22", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["studentverificationservicerepoA397EE22", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "studentverificationservicerepoA397EE22"}]]}, "Export": {"Name": "BidBees-student-verification-service-ECR-URI"}}, "kafkaserviceECRURI": {"Description": "ECR repository URI for kafka-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["kafkaservicerepoEEB199D3", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["kafkaservicerepoEEB199D3", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "kafkaservicerepoEEB199D3"}]]}, "Export": {"Name": "BidBees-kafka-service-ECR-URI"}}, "contractorsyncserviceECRURI": {"Description": "ECR repository URI for contractorsync-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["contractorsyncservicerepoCD31E3BF", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["contractorsyncservicerepoCD31E3BF", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "contractorsyncservicerepoCD31E3BF"}]]}, "Export": {"Name": "BidBees-contractorsync-service-ECR-URI"}}, "skillsyncserviceECRURI": {"Description": "ECR repository URI for skillsync-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["skillsyncservicerepo48E0A187", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["skillsyncservicerepo48E0A187", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "skillsyncservicerepo48E0A187"}]]}, "Export": {"Name": "BidBees-skillsync-service-ECR-URI"}}, "toolsyncserviceECRURI": {"Description": "ECR repository URI for toolsync-service", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["toolsyncservicerepo1BA4B4B1", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["toolsyncservicerepo1BA4B4B1", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "toolsyncservicerepo1BA4B4B1"}]]}, "Export": {"Name": "BidBees-toolsync-service-ECR-URI"}}, "ExportsOutputFnGetAttBidBeesALBDEF3E75ALoadBalancerFullNameE6F369EB": {"Value": {"Fn::GetAtt": ["BidBeesALBDEF3E75A", "LoadBalancerFullName"]}, "Export": {"Name": "bidbees-production-application:ExportsOutputFnGetAttBidBeesALBDEF3E75ALoadBalancerFullNameE6F369EB"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}