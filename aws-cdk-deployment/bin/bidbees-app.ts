#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { BidBeesInfrastructureStack } from '../lib/bidbees-infrastructure-stack';
import { BidBeesApplicationStack } from '../lib/bidbees-application-stack';
import { BidBeesFrontendStack } from '../lib/bidbees-frontend-stack';
import { BidBeesMonitoringStack } from '../lib/bidbees-monitoring-stack';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = new cdk.App();

// Get environment configuration
const env = {
  account: process.env.CDK_DEFAULT_ACCOUNT || process.env.AWS_ACCOUNT_ID,
  region: process.env.CDK_DEFAULT_REGION || process.env.AWS_REGION || 'us-east-1',
};

const projectName = process.env.PROJECT_NAME || 'bidbees';
const environment = process.env.ENVIRONMENT || 'production';

// Stack naming convention
const stackPrefix = `${projectName}-${environment}`;

// 1. Infrastructure Stack (VPC, Security Groups, Databases)
const infrastructureStack = new BidBeesInfrastructureStack(app, `${stackPrefix}-infrastructure`, {
  env,
  description: 'BidBees Infrastructure Stack - VPC, Databases, Security',
  tags: {
    Project: projectName,
    Environment: environment,
    Stack: 'Infrastructure',
    ManagedBy: 'CDK',
  },
});

// 2. Application Stack (ECS, ALB, ECR)
const applicationStack = new BidBeesApplicationStack(app, `${stackPrefix}-application`, {
  env,
  description: 'BidBees Application Stack - ECS Services, Load Balancer',
  vpc: infrastructureStack.vpc,
  cluster: infrastructureStack.ecsCluster,
  albSecurityGroup: infrastructureStack.albSecurityGroup,
  ecsSecurityGroup: infrastructureStack.ecsSecurityGroup,
  rdsInstance: infrastructureStack.rdsInstance,
  redisCluster: infrastructureStack.redisCluster,
  documentDbCluster: infrastructureStack.documentDbCluster,
  tags: {
    Project: projectName,
    Environment: environment,
    Stack: 'Application',
    ManagedBy: 'CDK',
  },
});

// 3. Frontend Stack (S3, CloudFront)
const frontendStack = new BidBeesFrontendStack(app, `${stackPrefix}-frontend`, {
  env,
  description: 'BidBees Frontend Stack - S3, CloudFront, Static Hosting',
  loadBalancer: applicationStack.loadBalancer,
  tags: {
    Project: projectName,
    Environment: environment,
    Stack: 'Frontend',
    ManagedBy: 'CDK',
  },
});

// 4. Monitoring Stack (CloudWatch, Alarms, Dashboards)
const monitoringStack = new BidBeesMonitoringStack(app, `${stackPrefix}-monitoring`, {
  env,
  description: 'BidBees Monitoring Stack - CloudWatch, Alarms, Dashboards',
  cluster: infrastructureStack.ecsCluster,
  loadBalancer: applicationStack.loadBalancer,
  rdsInstance: infrastructureStack.rdsInstance,
  redisCluster: infrastructureStack.redisCluster,
  cloudFrontDistribution: frontendStack.distribution,
  tags: {
    Project: projectName,
    Environment: environment,
    Stack: 'Monitoring',
    ManagedBy: 'CDK',
  },
});

// Add dependencies
applicationStack.addDependency(infrastructureStack);
frontendStack.addDependency(applicationStack);
monitoringStack.addDependency(applicationStack);
monitoringStack.addDependency(frontendStack);