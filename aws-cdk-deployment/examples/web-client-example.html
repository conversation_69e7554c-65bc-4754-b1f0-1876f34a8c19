<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 BidBees MCP Client - Web Example</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.disconnected {
            background: #fed7d7;
            color: #c53030;
        }
        
        .status.connected {
            background: #c6f6d5;
            color: #2f855a;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: #4299e1;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        
        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }
        
        .config-section {
            background: #f7fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .config-section h3 {
            margin-top: 0;
            color: #2d3748;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #4a5568;
        }
        
        select, input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        select:focus, input:focus {
            outline: none;
            border-color: #4299e1;
        }
        
        .messages {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        
        .message.info { background: rgba(66, 153, 225, 0.2); }
        .message.success { background: rgba(72, 187, 120, 0.2); }
        .message.warning { background: rgba(237, 137, 54, 0.2); }
        .message.error { background: rgba(245, 101, 101, 0.2); }
        
        .timestamp {
            color: #a0aec0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 BidBees MCP Client</h1>
        
        <div id="status" class="status disconnected">
            ❌ Disconnected from MCP Server
        </div>
        
        <div class="controls">
            <button id="connectBtn" onclick="connect()">🔗 Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>🔌 Disconnect</button>
            <button id="analyzeBtn" onclick="analyzeConfig()" disabled>🔍 Analyze Config</button>
            <button id="startDeployBtn" onclick="startDeployment()" disabled>🚀 Start Deployment</button>
            <button id="requestInsightsBtn" onclick="requestInsights()" disabled>💡 Get Insights</button>
            <button id="clearBtn" onclick="clearMessages()">🗑️ Clear Messages</button>
        </div>
        
        <div class="config-section">
            <h3>⚙️ Deployment Configuration</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div class="form-group">
                    <label for="environment">Environment:</label>
                    <select id="environment">
                        <option value="development">Development</option>
                        <option value="staging">Staging</option>
                        <option value="production">Production</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="region">AWS Region:</label>
                    <select id="region">
                        <option value="us-east-1">US East (N. Virginia)</option>
                        <option value="us-west-2">US West (Oregon)</option>
                        <option value="eu-west-1">Europe (Ireland)</option>
                        <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="autoScaling">Auto Scaling:</label>
                    <select id="autoScaling">
                        <option value="true">Enabled</option>
                        <option value="false">Disabled</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="monitoring">Monitoring:</label>
                    <select id="monitoring">
                        <option value="true">Enabled</option>
                        <option value="false">Disabled</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="domainName">Domain Name:</label>
                    <input type="text" id="domainName" placeholder="api.bidbees.com">
                </div>
            </div>
        </div>
        
        <div class="messages" id="messages">
            <div class="message info">
                <span class="timestamp">[Ready]</span> 
                Welcome to BidBees MCP Client! Click "Connect" to start.
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let connected = false;
        let deploymentId = null;

        function addMessage(type, message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(isConnected) {
            const statusDiv = document.getElementById('status');
            const buttons = ['disconnectBtn', 'analyzeBtn', 'startDeployBtn', 'requestInsightsBtn'];
            
            connected = isConnected;
            
            if (isConnected) {
                statusDiv.className = 'status connected';
                statusDiv.innerHTML = '✅ Connected to MCP Server';
                document.getElementById('connectBtn').disabled = true;
                buttons.forEach(id => document.getElementById(id).disabled = false);
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.innerHTML = '❌ Disconnected from MCP Server';
                document.getElementById('connectBtn').disabled = false;
                buttons.forEach(id => document.getElementById(id).disabled = true);
            }
        }

        function connect() {
            addMessage('info', '🔗 Connecting to MCP server...');
            
            ws = new WebSocket('ws://localhost:8765');
            
            ws.onopen = function() {
                addMessage('success', '✅ Connected to MCP server');
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (error) {
                    addMessage('error', `❌ Error parsing message: ${error.message}`);
                }
            };
            
            ws.onclose = function() {
                addMessage('warning', '👋 Disconnected from MCP server');
                updateStatus(false);
            };
            
            ws.onerror = function(error) {
                addMessage('error', `❌ WebSocket error: ${error.message || 'Connection failed'}`);
                updateStatus(false);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage(message) {
            if (connected && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                addMessage('info', `📤 Sent: ${message.type}`);
            } else {
                addMessage('error', '❌ Not connected to server');
            }
        }

        function handleMessage(message) {
            addMessage('success', `📥 Received: ${message.type}`);
            
            switch (message.type) {
                case 'welcome':
                    addMessage('info', `🎉 ${message.message}`);
                    addMessage('info', `🚀 Capabilities: ${message.capabilities.join(', ')}`);
                    break;
                    
                case 'optimization_suggestion':
                    addMessage('warning', `💡 Optimization: ${message.suggestion}`);
                    addMessage('success', `💰 Savings: ${message.savings || 'N/A'}`);
                    break;
                    
                case 'security_recommendation':
                    addMessage('error', `🔒 Security (${message.severity}): ${message.recommendation}`);
                    break;
                    
                case 'performance_insight':
                    addMessage('info', `⚡ Performance: ${message.insight}`);
                    if (message.improvement) {
                        addMessage('success', `📈 Improvement: ${message.improvement}`);
                    }
                    break;
                    
                case 'cost_analysis':
                    addMessage('warning', `💰 Monthly Cost: $${message.estimatedMonthlyCost}`);
                    addMessage('info', `🌍 Region: ${message.region} | Environment: ${message.environment}`);
                    break;
                    
                case 'deployment_insight':
                    addMessage('info', `🔍 ${message.step}: ${message.insight}`);
                    break;
                    
                default:
                    addMessage('info', `📋 ${JSON.stringify(message, null, 2)}`);
            }
        }

        function getConfig() {
            return {
                environment: document.getElementById('environment').value,
                region: document.getElementById('region').value,
                enableAutoScaling: document.getElementById('autoScaling').value === 'true',
                enableMonitoring: document.getElementById('monitoring').value === 'true',
                domainName: document.getElementById('domainName').value || null
            };
        }

        function analyzeConfig() {
            const config = getConfig();
            sendMessage({
                type: 'configuration_analysis',
                config: config,
                deploymentId: 'web-demo-' + Date.now()
            });
        }

        function startDeployment() {
            deploymentId = 'web-deployment-' + Date.now();
            sendMessage({
                type: 'deployment_start',
                deploymentId: deploymentId,
                project: 'BidBees Web Demo'
            });
            
            // Simulate deployment progress
            setTimeout(() => {
                sendMessage({
                    type: 'deployment_progress',
                    deploymentId: deploymentId,
                    step: 'infrastructure',
                    status: 'in_progress'
                });
            }, 1000);
            
            setTimeout(() => {
                sendMessage({
                    type: 'deployment_progress',
                    deploymentId: deploymentId,
                    step: 'infrastructure',
                    status: 'completed'
                });
            }, 3000);
        }

        function requestInsights() {
            const currentDeploymentId = deploymentId || 'demo-deployment';
            sendMessage({
                type: 'request_insights',
                deploymentId: currentDeploymentId
            });
        }

        function clearMessages() {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '<div class="message info"><span class="timestamp">[Cleared]</span> Messages cleared.</div>';
        }
    </script>
</body>
</html>
