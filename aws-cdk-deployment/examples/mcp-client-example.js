#!/usr/bin/env node

/**
 * 🧠 BidBees MCP Client Example
 * 
 * This example shows how to connect to and interact with the BidBees MCP server
 * for intelligent deployment insights and optimization recommendations.
 */

const WebSocket = require('ws');
const chalk = require('chalk');

class MCPClient {
  constructor(serverUrl = 'ws://localhost:8765') {
    this.serverUrl = serverUrl;
    this.ws = null;
    this.clientId = null;
    this.connected = false;
    this.messageHandlers = new Map();
    
    // Set up default message handlers
    this.setupDefaultHandlers();
  }

  // 🔗 Connect to MCP server
  async connect() {
    return new Promise((resolve, reject) => {
      console.log(chalk.cyan(`🔗 Connecting to MCP server at ${this.serverUrl}...`));
      
      this.ws = new WebSocket(this.serverUrl);
      
      this.ws.on('open', () => {
        console.log(chalk.green('✅ Connected to MCP server'));
        this.connected = true;
        resolve();
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleMessage(message);
        } catch (error) {
          console.error(chalk.red('❌ Error parsing message:'), error.message);
        }
      });
      
      this.ws.on('close', () => {
        console.log(chalk.yellow('👋 Disconnected from MCP server'));
        this.connected = false;
      });
      
      this.ws.on('error', (error) => {
        console.error(chalk.red('❌ WebSocket error:'), error.message);
        reject(error);
      });
    });
  }

  // 📨 Send message to server
  sendMessage(message) {
    if (this.connected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
      console.log(chalk.blue('📤 Sent:'), message.type);
    } else {
      console.warn(chalk.yellow('⚠️ Not connected to server'));
    }
  }

  // 📥 Handle incoming messages
  handleMessage(message) {
    console.log(chalk.green('📥 Received:'), message.type);
    
    const handler = this.messageHandlers.get(message.type);
    if (handler) {
      handler(message);
    } else {
      console.log(chalk.gray('📋 Message data:'), JSON.stringify(message, null, 2));
    }
  }

  // 🎯 Set up default message handlers
  setupDefaultHandlers() {
    this.messageHandlers.set('welcome', (message) => {
      console.log(chalk.green('🎉 Welcome message:'), message.message);
      console.log(chalk.cyan('🚀 Server capabilities:'), message.capabilities.join(', '));
    });

    this.messageHandlers.set('optimization_suggestion', (message) => {
      console.log(chalk.yellow('💡 Optimization Suggestion:'));
      console.log(chalk.white(`   ${message.suggestion}`));
      console.log(chalk.green(`   Impact: ${message.impact}`));
      if (message.savings) {
        console.log(chalk.green(`   Savings: ${message.savings}`));
      }
    });

    this.messageHandlers.set('security_recommendation', (message) => {
      console.log(chalk.red('🔒 Security Recommendation:'));
      console.log(chalk.white(`   ${message.recommendation}`));
      console.log(chalk.yellow(`   Severity: ${message.severity}`));
      console.log(chalk.cyan(`   Category: ${message.category}`));
    });

    this.messageHandlers.set('performance_insight', (message) => {
      console.log(chalk.blue('⚡ Performance Insight:'));
      console.log(chalk.white(`   ${message.insight}`));
      console.log(chalk.green(`   Impact: ${message.impact}`));
      if (message.improvement) {
        console.log(chalk.green(`   Improvement: ${message.improvement}`));
      }
    });

    this.messageHandlers.set('cost_analysis', (message) => {
      console.log(chalk.magenta('💰 Cost Analysis:'));
      console.log(chalk.white(`   Estimated Monthly Cost: $${message.estimatedMonthlyCost}`));
      console.log(chalk.cyan(`   Region: ${message.region}`));
      console.log(chalk.yellow(`   Environment: ${message.environment}`));
      if (message.recommendations) {
        console.log(chalk.green('   Recommendations:'));
        message.recommendations.forEach(rec => {
          console.log(chalk.white(`     • ${rec}`));
        });
      }
    });

    this.messageHandlers.set('deployment_insight', (message) => {
      console.log(chalk.cyan(`🔍 Deployment Insight (${message.step}):`));
      console.log(chalk.white(`   ${message.insight}`));
    });
  }

  // 🚀 Start a deployment tracking
  startDeployment(deploymentId, project) {
    this.sendMessage({
      type: 'deployment_start',
      deploymentId,
      project
    });
  }

  // 🔍 Request configuration analysis
  analyzeConfiguration(config, deploymentId = 'demo-deployment') {
    this.sendMessage({
      type: 'configuration_analysis',
      config,
      deploymentId
    });
  }

  // 📊 Update deployment progress
  updateProgress(deploymentId, step, status) {
    this.sendMessage({
      type: 'deployment_progress',
      deploymentId,
      step,
      status
    });
  }

  // 💡 Request insights
  requestInsights(deploymentId) {
    this.sendMessage({
      type: 'request_insights',
      deploymentId
    });
  }

  // 🛑 Disconnect
  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

// 🎯 Example usage
async function runExample() {
  const client = new MCPClient();
  
  try {
    // Connect to server
    await client.connect();
    
    // Wait a moment for welcome message
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Example 1: Analyze a configuration
    console.log(chalk.cyan('\n🔍 Example 1: Configuration Analysis'));
    const sampleConfig = {
      environment: 'development',
      region: 'us-east-1',
      enableAutoScaling: false,
      enableMonitoring: true,
      domainName: null
    };
    
    client.analyzeConfiguration(sampleConfig);
    
    // Wait for responses
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Example 2: Start deployment tracking
    console.log(chalk.cyan('\n🚀 Example 2: Deployment Tracking'));
    client.startDeployment('demo-deployment-123', 'BidBees Platform');
    
    // Simulate deployment progress
    await new Promise(resolve => setTimeout(resolve, 1000));
    client.updateProgress('demo-deployment-123', 'infrastructure', 'in_progress');
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    client.updateProgress('demo-deployment-123', 'infrastructure', 'completed');
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    client.updateProgress('demo-deployment-123', 'application', 'in_progress');
    
    // Example 3: Request insights
    console.log(chalk.cyan('\n💡 Example 3: Request Insights'));
    client.requestInsights('demo-deployment-123');
    
    // Keep connection open for a bit to see responses
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log(chalk.green('\n✅ Example completed!'));
    
  } catch (error) {
    console.error(chalk.red('❌ Example failed:'), error.message);
  } finally {
    client.disconnect();
  }
}

// Run example if this file is executed directly
if (require.main === module) {
  runExample().catch(console.error);
}

module.exports = MCPClient;
