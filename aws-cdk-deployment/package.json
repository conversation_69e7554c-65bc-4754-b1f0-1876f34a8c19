{"name": "bidbees-aws-cdk-deployment", "version": "1.0.0", "description": "1-Click AWS CDK deployment for BidBees microservices platform", "main": "lib/bidbees-stack.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "npm run build && cdk deploy --all --require-approval never", "destroy": "cdk destroy --all --force", "diff": "cdk diff", "synth": "cdk synth", "bootstrap": "cdk bootstrap", "one-click-deploy": "node scripts/one-click-deploy.js", "setup": "node scripts/setup.js"}, "devDependencies": {"@types/jest": "^29.4.0", "@types/node": "18.14.6", "jest": "^29.5.0", "ts-jest": "^29.0.5", "aws-cdk": "2.100.0", "typescript": "~4.9.5"}, "dependencies": {"aws-cdk-lib": "2.100.0", "constructs": "^10.0.0", "@aws-cdk/aws-apigatewayv2-alpha": "2.100.0-alpha.0", "@aws-cdk/aws-apigatewayv2-integrations-alpha": "2.100.0-alpha.0", "dotenv": "^16.3.1", "inquirer": "^9.2.11", "chalk": "^4.1.2", "ora": "^5.4.1", "boxen": "^5.1.2", "figlet": "^1.6.0", "gradient-string": "^2.0.2", "cli-progress": "^3.12.0", "axios": "^1.5.0", "ws": "^8.14.2"}, "keywords": ["aws", "cdk", "bidbees", "microservices", "deployment", "automation", "mcp"], "author": "BidBees Team", "license": "MIT"}