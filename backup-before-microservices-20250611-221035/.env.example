# Database Configuration
DATABASE_URL=postgres://postgres:<EMAIL>:5432/postgres

# Supabase Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Mapbox Configuration
VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here
MAPBOX_ACCESS_TOKEN=your_mapbox_token_here

# Session Configuration
SESSION_SECRET=your-secure-random-string-at-least-32-characters-long

# Environment Configuration
NODE_ENV=development
USE_DATABASE=false

# Frontend Environment Variables (for Vite)
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Optional: Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379

# Optional: Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Optional: File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Database Pool Configuration
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000

# Redis Configuration (Enhanced)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
JWT_EXPIRES_IN=7d

# API Configuration
API_BASE_URL=http://localhost:5000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000

# Analytics Configuration
ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn-here
LOG_LEVEL=info

# CDN Configuration
CDN_URL=https://your-cdn-domain.com
STATIC_ASSETS_URL=https://your-cdn-domain.com/assets

# Performance Configuration
ENABLE_COMPRESSION=true
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
