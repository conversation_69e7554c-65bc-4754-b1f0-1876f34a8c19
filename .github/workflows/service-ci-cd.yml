name: Service CI/CD

on:
  push:
    branches: [main, develop]
    paths:
      - 'services/**'
      - '.github/workflows/service-ci-cd.yml'
  pull_request:
    branches: [main, develop]
    paths:
      - 'services/**'

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com

jobs:
  # Detect changed services
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      services: ${{ steps.changes.outputs.services }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect changed services
        id: changes
        run: |
          # Get list of changed files
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            CHANGED_FILES=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }})
          else
            CHANGED_FILES=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})
          fi
          
          # Extract unique service names
          SERVICES=$(echo "$CHANGED_FILES" | grep '^services/' | cut -d'/' -f2 | sort -u | jq -R -s -c 'split("\n")[:-1]')
          echo "services=$SERVICES" >> $GITHUB_OUTPUT
          echo "Changed services: $SERVICES"

  # Test and build services
  test-and-build:
    needs: detect-changes
    if: needs.detect-changes.outputs.services != '[]'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: ${{ fromJson(needs.detect-changes.outputs.services) }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.bun/install/cache
          key: ${{ runner.os }}-bun-${{ hashFiles('services/${{ matrix.service }}/bun.lockb') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Install dependencies
        working-directory: services/${{ matrix.service }}
        run: bun install --frozen-lockfile

      - name: Run type check
        working-directory: services/${{ matrix.service }}
        run: bun run type-check

      - name: Run linting
        working-directory: services/${{ matrix.service }}
        run: bun run lint

      - name: Run tests
        working-directory: services/${{ matrix.service }}
        run: bun run test:coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: services/${{ matrix.service }}/coverage/lcov.info
          flags: ${{ matrix.service }}

      - name: Build application
        working-directory: services/${{ matrix.service }}
        run: bun run build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push Docker image
        working-directory: services/${{ matrix.service }}
        run: |
          IMAGE_TAG=${{ github.sha }}
          IMAGE_URI=${{ env.ECR_REGISTRY }}/bidbeez-${{ matrix.service }}:$IMAGE_TAG
          
          # Build image
          docker build -t $IMAGE_URI .
          
          # Push image
          docker push $IMAGE_URI
          
          # Tag as latest for main branch
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            LATEST_URI=${{ env.ECR_REGISTRY }}/bidbeez-${{ matrix.service }}:latest
            docker tag $IMAGE_URI $LATEST_URI
            docker push $LATEST_URI
          fi

  # Security scanning
  security-scan:
    needs: [detect-changes, test-and-build]
    if: needs.detect-changes.outputs.services != '[]'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: ${{ fromJson(needs.detect-changes.outputs.services) }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: 'services/${{ matrix.service }}'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Deploy to staging
  deploy-staging:
    needs: [detect-changes, test-and-build, security-scan]
    if: github.ref == 'refs/heads/develop' && needs.detect-changes.outputs.services != '[]'
    runs-on: ubuntu-latest
    environment: staging
    strategy:
      matrix:
        service: ${{ fromJson(needs.detect-changes.outputs.services) }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Deploy to ECS Staging
        run: |
          # Update ECS service with new image
          aws ecs update-service \
            --cluster bidbeez-staging \
            --service bidbeez-${{ matrix.service }}-staging \
            --force-new-deployment \
            --task-definition bidbeez-${{ matrix.service }}-staging

      - name: Wait for deployment
        run: |
          aws ecs wait services-stable \
            --cluster bidbeez-staging \
            --services bidbeez-${{ matrix.service }}-staging

  # Deploy to production
  deploy-production:
    needs: [detect-changes, test-and-build, security-scan]
    if: github.ref == 'refs/heads/main' && needs.detect-changes.outputs.services != '[]'
    runs-on: ubuntu-latest
    environment: production
    strategy:
      matrix:
        service: ${{ fromJson(needs.detect-changes.outputs.services) }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Blue-Green Deployment
        run: |
          # Get current task definition
          TASK_DEF=$(aws ecs describe-task-definition \
            --task-definition bidbeez-${{ matrix.service }}-prod \
            --query 'taskDefinition' \
            --output json)
          
          # Update image URI in task definition
          NEW_TASK_DEF=$(echo $TASK_DEF | jq --arg IMAGE "${{ env.ECR_REGISTRY }}/bidbeez-${{ matrix.service }}:${{ github.sha }}" \
            '.containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .placementConstraints, .compatibilities, .registeredAt, .registeredBy)')
          
          # Register new task definition
          NEW_TASK_DEF_ARN=$(echo $NEW_TASK_DEF | aws ecs register-task-definition \
            --cli-input-json file:///dev/stdin \
            --query 'taskDefinition.taskDefinitionArn' \
            --output text)
          
          # Update service with new task definition
          aws ecs update-service \
            --cluster bidbeez-production \
            --service bidbeez-${{ matrix.service }}-prod \
            --task-definition $NEW_TASK_DEF_ARN

      - name: Wait for deployment
        run: |
          aws ecs wait services-stable \
            --cluster bidbeez-production \
            --services bidbeez-${{ matrix.service }}-prod

      - name: Health check
        run: |
          # Wait for health check to pass
          sleep 60
          
          # Get service endpoint
          SERVICE_URL="https://api.bidbeez.com/${{ matrix.service }}/health"
          
          # Check health endpoint
          for i in {1..10}; do
            if curl -f $SERVICE_URL; then
              echo "Health check passed"
              exit 0
            fi
            echo "Health check failed, retrying in 30s..."
            sleep 30
          done
          
          echo "Health check failed after 10 attempts"
          exit 1

  # Notification
  notify:
    needs: [detect-changes, test-and-build, security-scan, deploy-staging, deploy-production]
    if: always() && needs.detect-changes.outputs.services != '[]'
    runs-on: ubuntu-latest
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
