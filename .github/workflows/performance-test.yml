name: Performance Testing

on:
  push:
    branches: [main]
  schedule:
    - cron: '0 4 * * 1'  # Weekly on Monday at 4 AM UTC
  workflow_dispatch:
    inputs:
      test_scenario:
        description: 'Performance Test Scenario'
        required: true
        default: 'load'
        type: choice
        options:
          - load
          - stress
          - spike
          - volume
          - million-users
      duration:
        description: 'Test Duration (minutes)'
        required: false
        default: '10'

env:
  TARGET_URL: https://api.bidbees.com
  STAGING_URL: https://staging-api.bidbees.com

jobs:
  setup-test-data:
    runs-on: ubuntu-latest
    outputs:
      test-data-ready: ${{ steps.setup.outputs.ready }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Test Data
        id: setup
        run: |
          echo "Setting up test data for million user scenarios"
          # Generate test user data, tender data, etc.
          python scripts/generate-test-data.py --users 1000000 --tenders 100000
          echo "ready=true" >> $GITHUB_OUTPUT

  load-testing:
    runs-on: ubuntu-latest
    needs: setup-test-data
    strategy:
      matrix:
        scenario:
          - user-registration
          - tender-browsing
          - bidding-process
          - payment-flow
          - real-time-chat
          - document-upload
          - map-services
          - analytics-dashboard
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup K6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
      
      - name: Run Load Test - ${{ matrix.scenario }}
        env:
          SCENARIO: ${{ matrix.scenario }}
          DURATION: ${{ github.event.inputs.duration || '10' }}m
          VUS: ${{ github.event.inputs.test_scenario == 'million-users' && '10000' || '100' }}
        run: |
          k6 run \
            --vus $VUS \
            --duration $DURATION \
            --out json=k6-results-${{ matrix.scenario }}.json \
            --env TARGET_URL=$TARGET_URL \
            --env SCENARIO=$SCENARIO \
            scripts/performance-tests/${{ matrix.scenario }}.js
      
      - name: Upload K6 Results
        uses: actions/upload-artifact@v4
        with:
          name: k6-results-${{ matrix.scenario }}
          path: k6-results-${{ matrix.scenario }}.json

  million-user-simulation:
    runs-on: ubuntu-latest
    if: github.event.inputs.test_scenario == 'million-users'
    needs: setup-test-data
    strategy:
      matrix:
        phase: [1, 2, 3, 4, 5]  # Split into 5 phases of 200k users each
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Artillery
        run: |
          npm install -g artillery@latest
          npm install -g artillery-plugin-cloudwatch
      
      - name: Million User Test - Phase ${{ matrix.phase }}
        env:
          PHASE: ${{ matrix.phase }}
          USERS_PER_PHASE: 200000
          RAMP_UP_TIME: 300  # 5 minutes ramp up
          PLATEAU_TIME: 600  # 10 minutes plateau
        run: |
          artillery run \
            --config scripts/artillery/million-users-config.yml \
            --target $TARGET_URL \
            --overrides.phases[0].arrivalRate=$((USERS_PER_PHASE / RAMP_UP_TIME)) \
            --overrides.phases[0].duration=$RAMP_UP_TIME \
            --overrides.phases[1].arrivalRate=$((USERS_PER_PHASE / PLATEAU_TIME)) \
            --overrides.phases[1].duration=$PLATEAU_TIME \
            scripts/artillery/million-users-scenario.yml
      
      - name: AWS CloudWatch Metrics
        run: |
          # Collect AWS metrics during the test
          aws cloudwatch get-metric-statistics \
            --namespace AWS/ECS \
            --metric-name CPUUtilization \
            --dimensions Name=ServiceName,Value=bidbees-production-api-gateway \
            --start-time $(date -u -d '30 minutes ago' +%Y-%m-%dT%H:%M:%S) \
            --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
            --period 60 \
            --statistics Average,Maximum > cloudwatch-metrics-phase-${{ matrix.phase }}.json
      
      - name: Upload Phase Results
        uses: actions/upload-artifact@v4
        with:
          name: million-user-phase-${{ matrix.phase }}
          path: |
            artillery-report-*.json
            cloudwatch-metrics-phase-${{ matrix.phase }}.json

  database-performance:
    runs-on: ubuntu-latest
    needs: setup-test-data
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup PostgreSQL Client
        run: |
          sudo apt-get update
          sudo apt-get install postgresql-client
      
      - name: Database Load Test
        env:
          DB_HOST: ${{ secrets.RDS_ENDPOINT }}
          DB_USER: ${{ secrets.DB_USERNAME }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
          DB_NAME: bidbees
        run: |
          # Run database performance tests
          python scripts/db-performance-test.py \
            --host $DB_HOST \
            --user $DB_USER \
            --password $DB_PASSWORD \
            --database $DB_NAME \
            --concurrent-connections 1000 \
            --test-duration 600
      
      - name: Redis Performance Test
        env:
          REDIS_HOST: ${{ secrets.REDIS_ENDPOINT }}
        run: |
          # Install redis-benchmark
          sudo apt-get install redis-tools
          
          # Run Redis performance tests
          redis-benchmark -h $REDIS_HOST -p 6379 \
            -n 1000000 -c 100 -d 3 \
            --csv > redis-performance.csv
      
      - name: Upload Database Results
        uses: actions/upload-artifact@v4
        with:
          name: database-performance
          path: |
            db-performance-*.json
            redis-performance.csv

  real-time-testing:
    runs-on: ubuntu-latest
    needs: setup-test-data
    steps:
      - uses: actions/checkout@v4
      
      - name: WebSocket Load Test
        run: |
          npm install -g wscat artillery-plugin-websockets
          
          # Test real-time features under load
          artillery run scripts/artillery/websocket-load-test.yml
      
      - name: Server-Sent Events Test
        run: |
          # Test SSE endpoints for real-time notifications
          python scripts/sse-load-test.py \
            --url $TARGET_URL/api/notifications/stream \
            --concurrent-clients 10000 \
            --duration 300

  cdn-performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: CDN Performance Test
        run: |
          # Test CloudFront performance from multiple regions
          regions=("us-east-1" "us-west-2" "eu-west-1" "ap-southeast-1")
          
          for region in "${regions[@]}"; do
            echo "Testing from $region"
            # Use a service like GTmetrix API or similar
            curl -X POST "https://gtmetrix.com/api/0.1/test" \
              -u "${{ secrets.GTMETRIX_EMAIL }}:${{ secrets.GTMETRIX_API_KEY }}" \
              -d "url=https://bidbees.com" \
              -d "location=$region" > gtmetrix-$region.json
          done
      
      - name: Upload CDN Results
        uses: actions/upload-artifact@v4
        with:
          name: cdn-performance
          path: gtmetrix-*.json

  mobile-performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Mobile Performance Test
        run: |
          # Simulate mobile network conditions
          docker run --rm -v $(pwd):/workspace \
            sitespeedio/sitespeed.io:latest \
            --mobile \
            --connectivity.profile 3g \
            --connectivity.downstreamKbps 1600 \
            --connectivity.upstreamKbps 768 \
            --connectivity.latency 300 \
            --browsertime.iterations 5 \
            https://bidbees.com

  performance-analysis:
    runs-on: ubuntu-latest
    needs: [load-testing, million-user-simulation, database-performance, real-time-testing, cdn-performance, mobile-performance]
    if: always()
    steps:
      - uses: actions/checkout@v4
      
      - name: Download All Results
        uses: actions/download-artifact@v4
        with:
          path: performance-results/
      
      - name: Generate Performance Report
        run: |
          python scripts/generate-performance-report.py \
            --input-dir performance-results/ \
            --output performance-report.html \
            --baseline-file scripts/performance-baselines.json
      
      - name: Performance Regression Check
        run: |
          python scripts/check-performance-regression.py \
            --current-results performance-results/ \
            --baseline-results performance-baselines/ \
            --threshold 20  # 20% regression threshold
      
      - name: Upload Performance Report
        uses: actions/upload-artifact@v4
        with:
          name: performance-report
          path: |
            performance-report.html
            performance-results/
      
      - name: Update Performance Dashboard
        if: github.ref == 'refs/heads/main'
        run: |
          # Update CloudWatch dashboard with new performance metrics
          aws cloudwatch put-dashboard \
            --dashboard-name "BidBees-Performance-Dashboard" \
            --dashboard-body file://scripts/performance-dashboard.json
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: us-east-1
      
      - name: Slack Notification
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: "Performance tests failed! Check the results for regressions."
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  auto-scaling-test:
    runs-on: ubuntu-latest
    if: github.event.inputs.test_scenario == 'million-users'
    steps:
      - uses: actions/checkout@v4
      
      - name: Test Auto-Scaling Behavior
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: us-east-1
        run: |
          # Monitor auto-scaling during load test
          echo "Monitoring ECS auto-scaling during million user test"
          
          # Get initial service counts
          initial_counts=$(aws ecs describe-services \
            --cluster bidbees-production-cluster \
            --services bidbees-production-api-gateway \
            --query 'services[0].desiredCount')
          
          echo "Initial service count: $initial_counts"
          
          # Monitor for 30 minutes during test
          for i in {1..30}; do
            current_count=$(aws ecs describe-services \
              --cluster bidbees-production-cluster \
              --services bidbees-production-api-gateway \
              --query 'services[0].desiredCount')
            
            cpu_utilization=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/ECS \
              --metric-name CPUUtilization \
              --dimensions Name=ServiceName,Value=bidbees-production-api-gateway \
              --start-time $(date -u -d '5 minutes ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 300 \
              --statistics Average \
              --query 'Datapoints[0].Average')
            
            echo "Minute $i: Service count: $current_count, CPU: $cpu_utilization%"
            sleep 60
          done