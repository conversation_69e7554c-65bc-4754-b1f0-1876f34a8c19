name: Cost Optimization

on:
  schedule:
    - cron: '0 6 * * 1'  # Weekly on Monday at 6 AM UTC
  workflow_dispatch:
    inputs:
      action:
        description: 'Cost Optimization Action'
        required: true
        default: 'analyze'
        type: choice
        options:
          - analyze
          - optimize
          - forecast
          - budget-alert

env:
  AWS_REGION: us-east-1
  PROJECT_NAME: bidbees

jobs:
  cost-analysis:
    runs-on: ubuntu-latest
    outputs:
      monthly-cost: ${{ steps.analysis.outputs.monthly-cost }}
      recommendations: ${{ steps.analysis.outputs.recommendations }}
      savings-potential: ${{ steps.analysis.outputs.savings-potential }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Current Month Cost Analysis
        id: analysis
        run: |
          # Get current month costs
          start_date=$(date -d "$(date +%Y-%m-01)" +%Y-%m-%d)
          end_date=$(date +%Y-%m-%d)
          
          # Get cost by service
          total_cost=$(aws ce get-cost-and-usage \
            --time-period Start=$start_date,End=$end_date \
            --granularity MONTHLY \
            --metrics BlendedCost \
            --group-by Type=DIMENSION,Key=SERVICE \
            --query 'ResultsByTime[0].Total.BlendedCost.Amount' --output text)
          
          echo "Current month cost: \$$total_cost"
          echo "monthly-cost=$total_cost" >> $GITHUB_OUTPUT
          
          # Analyze by service
          echo "## Cost Breakdown by Service" > cost-report.md
          aws ce get-cost-and-usage \
            --time-period Start=$start_date,End=$end_date \
            --granularity MONTHLY \
            --metrics BlendedCost \
            --group-by Type=DIMENSION,Key=SERVICE \
            --query 'ResultsByTime[0].Groups[].[Keys[0],Total.BlendedCost.Amount]' \
            --output text | sort -k2 -nr | head -10 >> cost-report.md
      
      - name: Resource Utilization Analysis
        run: |
          echo "## Resource Utilization Analysis" >> cost-report.md
          
          # ECS Resource Utilization
          services=(api-gateway auth-service tender-service bidding-service payment-service ml-service)
          
          for service in "${services[@]}"; do
            # Get average CPU utilization for last 7 days
            cpu_avg=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/ECS \
              --metric-name CPUUtilization \
              --dimensions Name=ServiceName,Value=$PROJECT_NAME-production-$service \
              --start-time $(date -u -d '7 days ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 86400 \
              --statistics Average \
              --query 'Datapoints[*].Average | avg(@)' --output text)
            
            memory_avg=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/ECS \
              --metric-name MemoryUtilization \
              --dimensions Name=ServiceName,Value=$PROJECT_NAME-production-$service \
              --start-time $(date -u -d '7 days ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 86400 \
              --statistics Average \
              --query 'Datapoints[*].Average | avg(@)' --output text)
            
            echo "- $service: CPU ${cpu_avg}%, Memory ${memory_avg}%" >> cost-report.md
            
            # Identify under-utilized services
            if [ "${cpu_avg%.*}" -lt 30 ] && [ "${memory_avg%.*}" -lt 30 ]; then
              echo "  ⚠️ Under-utilized - consider downsizing" >> cost-report.md
            fi
          done
      
      - name: RDS Cost Analysis
        run: |
          echo "## RDS Cost Analysis" >> cost-report.md
          
          # Get RDS instances
          rds_instances=$(aws rds describe-db-instances \
            --query 'DBInstances[?DBInstanceStatus==`available`].[DBInstanceIdentifier,DBInstanceClass,Engine]' \
            --output text)
          
          echo "$rds_instances" | while read instance_id instance_class engine; do
            echo "- $instance_id ($instance_class, $engine)" >> cost-report.md
            
            # Check CPU utilization
            cpu_avg=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/RDS \
              --metric-name CPUUtilization \
              --dimensions Name=DBInstanceIdentifier,Value=$instance_id \
              --start-time $(date -u -d '7 days ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 86400 \
              --statistics Average \
              --query 'Datapoints[*].Average | avg(@)' --output text)
            
            echo "  CPU Average: ${cpu_avg}%" >> cost-report.md
            
            if [ "${cpu_avg%.*}" -lt 20 ]; then
              echo "  💡 Recommendation: Consider downsizing to smaller instance" >> cost-report.md
            fi
          done
      
      - name: S3 Cost Analysis
        run: |
          echo "## S3 Storage Analysis" >> cost-report.md
          
          # Get S3 bucket sizes and storage classes
          aws s3api list-buckets --query 'Buckets[?contains(Name, `bidbees`)].Name' --output text | \
          while read bucket; do
            size=$(aws s3api list-objects-v2 --bucket $bucket \
              --query 'sum(Contents[].Size)' --output text)
            
            if [ "$size" != "None" ]; then
              size_gb=$((size / 1024 / 1024 / 1024))
              echo "- $bucket: ${size_gb}GB" >> cost-report.md
              
              # Check for lifecycle policies
              lifecycle=$(aws s3api get-bucket-lifecycle-configuration \
                --bucket $bucket 2>/dev/null || echo "None")
              
              if [ "$lifecycle" == "None" ]; then
                echo "  💡 No lifecycle policy - consider IA/Glacier for old objects" >> cost-report.md
              fi
            fi
          done

  right-sizing-analysis:
    runs-on: ubuntu-latest
    needs: cost-analysis
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: ECS Right-Sizing Recommendations
        run: |
          echo "## ECS Right-Sizing Recommendations" > rightsizing-report.md
          
          services=(api-gateway auth-service tender-service bidding-service payment-service ml-service)
          
          for service in "${services[@]}"; do
            # Get current task definition
            task_def=$(aws ecs describe-services \
              --cluster $PROJECT_NAME-production-cluster \
              --services $PROJECT_NAME-production-$service \
              --query 'services[0].taskDefinition' --output text)
            
            current_cpu=$(aws ecs describe-task-definition \
              --task-definition $task_def \
              --query 'taskDefinition.cpu' --output text)
            
            current_memory=$(aws ecs describe-task-definition \
              --task-definition $task_def \
              --query 'taskDefinition.memory' --output text)
            
            # Get actual utilization
            cpu_p95=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/ECS \
              --metric-name CPUUtilization \
              --dimensions Name=ServiceName,Value=$PROJECT_NAME-production-$service \
              --start-time $(date -u -d '30 days ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 3600 \
              --statistics Maximum \
              --query 'Datapoints | max_by(@, &Maximum).Maximum' --output text)
            
            memory_p95=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/ECS \
              --metric-name MemoryUtilization \
              --dimensions Name=ServiceName,Value=$PROJECT_NAME-production-$service \
              --start-time $(date -u -d '30 days ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 3600 \
              --statistics Maximum \
              --query 'Datapoints | max_by(@, &Maximum).Maximum' --output text)
            
            echo "### $service" >> rightsizing-report.md
            echo "- Current: ${current_cpu} CPU, ${current_memory} Memory" >> rightsizing-report.md
            echo "- Peak Usage: ${cpu_p95}% CPU, ${memory_p95}% Memory" >> rightsizing-report.md
            
            # Calculate recommendations
            if [ "${cpu_p95%.*}" -lt 50 ]; then
              recommended_cpu=$((current_cpu / 2))
              echo "- 💡 CPU Recommendation: Reduce to ${recommended_cpu}" >> rightsizing-report.md
            fi
            
            if [ "${memory_p95%.*}" -lt 50 ]; then
              recommended_memory=$((current_memory / 2))
              echo "- 💡 Memory Recommendation: Reduce to ${recommended_memory}" >> rightsizing-report.md
            fi
            echo "" >> rightsizing-report.md
          done
      
      - name: RDS Right-Sizing Recommendations
        run: |
          echo "## RDS Right-Sizing Recommendations" >> rightsizing-report.md
          
          aws rds describe-db-instances \
            --query 'DBInstances[?DBInstanceStatus==`available`].[DBInstanceIdentifier,DBInstanceClass]' \
            --output text | \
          while read instance_id instance_class; do
            # Get CPU and connection metrics
            cpu_max=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/RDS \
              --metric-name CPUUtilization \
              --dimensions Name=DBInstanceIdentifier,Value=$instance_id \
              --start-time $(date -u -d '30 days ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 3600 \
              --statistics Maximum \
              --query 'Datapoints | max_by(@, &Maximum).Maximum' --output text)
            
            connections_max=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/RDS \
              --metric-name DatabaseConnections \
              --dimensions Name=DBInstanceIdentifier,Value=$instance_id \
              --start-time $(date -u -d '30 days ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 3600 \
              --statistics Maximum \
              --query 'Datapoints | max_by(@, &Maximum).Maximum' --output text)
            
            echo "### $instance_id ($instance_class)" >> rightsizing-report.md
            echo "- Peak CPU: ${cpu_max}%" >> rightsizing-report.md
            echo "- Peak Connections: $connections_max" >> rightsizing-report.md
            
            if [ "${cpu_max%.*}" -lt 30 ]; then
              echo "- 💡 Consider downsizing to smaller instance class" >> rightsizing-report.md
            fi
            echo "" >> rightsizing-report.md
          done

  million-user-cost-forecast:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'forecast' || github.event_name == 'schedule'
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Calculate Million User Costs
        run: |
          echo "# Million User Cost Forecast" > forecast-report.md
          echo "" >> forecast-report.md
          
          # Current baseline costs
          current_monthly=$(aws ce get-cost-and-usage \
            --time-period Start=$(date -d "$(date +%Y-%m-01)" +%Y-%m-%d),End=$(date +%Y-%m-%d) \
            --granularity MONTHLY \
            --metrics BlendedCost \
            --query 'ResultsByTime[0].Total.BlendedCost.Amount' --output text)
          
          echo "## Current Baseline" >> forecast-report.md
          echo "- Monthly Cost: \$$current_monthly" >> forecast-report.md
          echo "" >> forecast-report.md
          
          # Million user scaling calculations
          echo "## Million User Scaling Forecast" >> forecast-report.md
          echo "" >> forecast-report.md
          
          # ECS scaling costs
          echo "### ECS Services" >> forecast-report.md
          services=(api-gateway auth-service tender-service bidding-service payment-service ml-service)
          total_ecs_cost=0
          
          for service in "${services[@]}"; do
            # Assume each service needs 200 tasks for million users
            tasks_needed=200
            
            # Get current task cost (estimate $0.05 per task per hour)
            task_cost_hourly=0.05
            monthly_cost=$(echo "$tasks_needed * $task_cost_hourly * 24 * 30" | bc -l)
            total_ecs_cost=$(echo "$total_ecs_cost + $monthly_cost" | bc -l)
            
            echo "- $service: $tasks_needed tasks = \$${monthly_cost}/month" >> forecast-report.md
          done
          
          echo "- **Total ECS: \$${total_ecs_cost}/month**" >> forecast-report.md
          echo "" >> forecast-report.md
          
          # RDS scaling costs
          echo "### Database (RDS)" >> forecast-report.md
          echo "- Primary: db.r6g.4xlarge = \$1,200/month" >> forecast-report.md
          echo "- Read Replicas (3x): db.r6g.2xlarge = \$1,800/month" >> forecast-report.md
          echo "- **Total RDS: \$3,000/month**" >> forecast-report.md
          echo "" >> forecast-report.md
          
          # ElastiCache scaling costs
          echo "### ElastiCache (Redis)" >> forecast-report.md
          echo "- Cluster: cache.r6g.2xlarge (3 nodes) = \$800/month" >> forecast-report.md
          echo "" >> forecast-report.md
          
          # ALB and data transfer
          echo "### Load Balancer & Data Transfer" >> forecast-report.md
          echo "- ALB: \$50/month" >> forecast-report.md
          echo "- Data Transfer: \$500/month (estimated)" >> forecast-report.md
          echo "" >> forecast-report.md
          
          # S3 and CloudFront
          echo "### Storage & CDN" >> forecast-report.md
          echo "- S3 Storage: \$200/month" >> forecast-report.md
          echo "- CloudFront: \$300/month" >> forecast-report.md
          echo "" >> forecast-report.md
          
          # Calculate total
          total_forecast=$(echo "$total_ecs_cost + 3000 + 800 + 50 + 500 + 200 + 300" | bc -l)
          echo "## **Total Estimated Cost for 1M Users: \$${total_forecast}/month**" >> forecast-report.md
          echo "" >> forecast-report.md
          
          # Cost per user
          cost_per_user=$(echo "scale=4; $total_forecast / 1000000" | bc -l)
          echo "### Cost per user: \$${cost_per_user}/month" >> forecast-report.md

  cost-optimization:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'optimize'
    needs: [cost-analysis, right-sizing-analysis]
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Implement S3 Lifecycle Policies
        run: |
          # Apply lifecycle policies to S3 buckets
          aws s3api list-buckets --query 'Buckets[?contains(Name, `bidbees`)].Name' --output text | \
          while read bucket; do
            echo "Applying lifecycle policy to $bucket"
            
            cat > lifecycle-policy.json << 'EOF'
          {
            "Rules": [
              {
                "ID": "BidbeesCostOptimization",
                "Status": "Enabled",
                "Transitions": [
                  {
                    "Days": 30,
                    "StorageClass": "STANDARD_IA"
                  },
                  {
                    "Days": 90,
                    "StorageClass": "GLACIER"
                  },
                  {
                    "Days": 365,
                    "StorageClass": "DEEP_ARCHIVE"
                  }
                ]
              }
            ]
          }
          EOF
            
            aws s3api put-bucket-lifecycle-configuration \
              --bucket $bucket \
              --lifecycle-configuration file://lifecycle-policy.json
          done
      
      - name: Optimize ECS Auto Scaling
        run: |
          # Update auto scaling policies for better cost optimization
          services=(api-gateway auth-service tender-service bidding-service payment-service)
          
          for service in "${services[@]}"; do
            # Set more aggressive scale-down policies
            aws application-autoscaling put-scaling-policy \
              --policy-name "$PROJECT_NAME-$service-scale-down-policy" \
              --service-namespace ecs \
              --resource-id "service/$PROJECT_NAME-production-cluster/$PROJECT_NAME-production-$service" \
              --scalable-dimension ecs:service:DesiredCount \
              --policy-type StepScaling \
              --step-scaling-policy-configuration \
              AdjustmentType=ChangeInCapacity,\
              StepAdjustments='[{MetricIntervalUpperBound=0,ScalingAdjustment=-2}]',\
              Cooldown=300
          done
      
      - name: Set Up Reserved Instance Recommendations
        run: |
          # Get RI recommendations
          aws ce get-reservation-purchase-recommendation \
            --service EC2-Instance \
            --payment-option ALL_UPFRONT \
            --term-in-years ONE_YEAR > ri-recommendations.json
          
          echo "Reserved Instance recommendations saved to ri-recommendations.json"

  budget-management:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'budget-alert' || github.event_name == 'schedule'
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Create/Update Budgets
        run: |
          # Create monthly budget
          cat > budget-config.json << 'EOF'
          {
            "BudgetName": "BidBees-Monthly-Budget",
            "BudgetLimit": {
              "Amount": "5000",
              "Unit": "USD"
            },
            "TimeUnit": "MONTHLY",
            "TimePeriod": {
              "Start": "2024-01-01T00:00:00Z",
              "End": "2025-12-31T23:59:59Z"
            },
            "BudgetType": "COST",
            "CostFilters": {
              "TagKey": ["Project"],
              "TagValue": ["BidBees"]
            }
          }
          EOF
          
          # Create budget alerts
          cat > budget-notifications.json << 'EOF'
          [
            {
              "Notification": {
                "NotificationType": "ACTUAL",
                "ComparisonOperator": "GREATER_THAN",
                "Threshold": 80,
                "ThresholdType": "PERCENTAGE"
              },
              "Subscribers": [
                {
                  "SubscriptionType": "EMAIL",
                  "Address": "<EMAIL>"
                }
              ]
            },
            {
              "Notification": {
                "NotificationType": "FORECASTED",
                "ComparisonOperator": "GREATER_THAN", 
                "Threshold": 100,
                "ThresholdType": "PERCENTAGE"
              },
              "Subscribers": [
                {
                  "SubscriptionType": "EMAIL",
                  "Address": "<EMAIL>"
                }
              ]
            }
          ]
          EOF
          
          aws budgets create-budget \
            --account-id ${{ secrets.AWS_ACCOUNT_ID }} \
            --budget file://budget-config.json \
            --notifications-with-subscribers file://budget-notifications.json

  cost-report:
    runs-on: ubuntu-latest
    needs: [cost-analysis, right-sizing-analysis, million-user-cost-forecast]
    if: always()
    steps:
      - name: Generate Comprehensive Cost Report
        run: |
          echo "# 💰 BidBees Cost Optimization Report" > cost-optimization-report.md
          echo "" >> cost-optimization-report.md
          echo "Generated on: $(date)" >> cost-optimization-report.md
          echo "" >> cost-optimization-report.md
          
          echo "## Current Status" >> cost-optimization-report.md
          echo "- Monthly Cost: \$${{ needs.cost-analysis.outputs.monthly-cost || 'N/A' }}" >> cost-optimization-report.md
          echo "- Potential Savings: \$${{ needs.cost-analysis.outputs.savings-potential || 'TBD' }}" >> cost-optimization-report.md
          echo "" >> cost-optimization-report.md
          
          echo "## Recommendations" >> cost-optimization-report.md
          echo "1. **Right-size under-utilized ECS services**" >> cost-optimization-report.md
          echo "2. **Implement S3 lifecycle policies**" >> cost-optimization-report.md
          echo "3. **Consider Reserved Instances for stable workloads**" >> cost-optimization-report.md
          echo "4. **Optimize auto-scaling policies**" >> cost-optimization-report.md
          echo "" >> cost-optimization-report.md
          
          echo "## Million User Forecast" >> cost-optimization-report.md
          echo "See detailed forecast in attached report" >> cost-optimization-report.md
      
      - name: Upload Cost Reports
        uses: actions/upload-artifact@v4
        with:
          name: cost-optimization-reports
          path: |
            cost-optimization-report.md
            cost-report.md
            rightsizing-report.md
            forecast-report.md
            ri-recommendations.json
      
      - name: Slack Cost Report
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: |
            💰 Weekly Cost Optimization Report
            
            Current Monthly Cost: ${{ needs.cost-analysis.outputs.monthly-cost }}
            Potential Savings: ${{ needs.cost-analysis.outputs.savings-potential }}
            
            Full report available in GitHub Actions artifacts.
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}