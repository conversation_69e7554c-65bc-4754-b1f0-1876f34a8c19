name: Enhanced CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      deployment_type:
        description: 'Deployment Strategy'
        required: true
        default: 'rolling'
        type: choice
        options:
          - rolling
          - blue-green
          - canary
      environment:
        description: 'Target Environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com
  PROJECT_NAME: bidbees

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      services: ${{ steps.changes.outputs.services }}
      infrastructure: ${{ steps.changes.outputs.infrastructure }}
      frontend: ${{ steps.changes.outputs.frontend }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Detect Changes
        id: changes
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "services=[\"api-gateway\",\"auth-service\",\"tender-service\",\"bidding-service\",\"payment-service\",\"courier-service\",\"supplier-service\",\"transport-service\",\"ml-service\",\"docling-processor\",\"queenbee-anchor-service\",\"bee-tasks-service\",\"map-service\",\"notification-service\",\"document-service\",\"analytics-service\"]" >> $GITHUB_OUTPUT
            echo "infrastructure=true" >> $GITHUB_OUTPUT
            echo "frontend=true" >> $GITHUB_OUTPUT
          else
            changed_services=()
            for service in api-gateway auth-service tender-service bidding-service payment-service courier-service supplier-service transport-service ml-service docling-processor queenbee-anchor-service bee-tasks-service map-service notification-service document-service analytics-service; do
              if git diff --name-only HEAD~1 HEAD | grep -q "microservices/services/$service/"; then
                changed_services+=("\"$service\"")
              fi
            done
            services_json=$(IFS=,; echo "[${changed_services[*]}]")
            echo "services=$services_json" >> $GITHUB_OUTPUT
            
            if git diff --name-only HEAD~1 HEAD | grep -qE "(aws-infrastructure/|aws-cdk-deployment/)"; then
              echo "infrastructure=true" >> $GITHUB_OUTPUT
            else
              echo "infrastructure=false" >> $GITHUB_OUTPUT
            fi
            
            if git diff --name-only HEAD~1 HEAD | grep -qE "(client/|frontend/|enterprise-tms/)"; then
              echo "frontend=true" >> $GITHUB_OUTPUT
            else
              echo "frontend=false" >> $GITHUB_OUTPUT
            fi
          fi

  test-and-build:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.services != '[]'
    strategy:
      matrix:
        service: ${{ fromJSON(needs.detect-changes.outputs.services) }}
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: microservices/services/${{ matrix.service }}/package-lock.json
      
      - name: Install Dependencies
        run: |
          cd microservices/services/${{ matrix.service }}
          npm ci
      
      - name: Run Tests
        run: |
          cd microservices/services/${{ matrix.service }}
          npm test || echo "Tests not available for ${{ matrix.service }}"
      
      - name: Build Service
        run: |
          cd microservices/services/${{ matrix.service }}
          npm run build || echo "Build not required for ${{ matrix.service }}"
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Build and Push Docker Image
        env:
          ECR_REPOSITORY: ${{ env.PROJECT_NAME }}/${{ matrix.service }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          cd microservices/services/${{ matrix.service }}
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

  frontend-build:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.frontend == 'true'
    strategy:
      matrix:
        app: [client, frontend, enterprise-tms]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install and Build
        run: |
          if [ -d "${{ matrix.app }}" ]; then
            cd ${{ matrix.app }}
            npm ci --legacy-peer-deps
            npm run build
          fi
      
      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.app }}-build
          path: |
            ${{ matrix.app }}/dist/
            ${{ matrix.app }}/build/
          retention-days: 30

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [detect-changes, test-and-build, frontend-build]
    if: github.ref == 'refs/heads/develop' || github.event.inputs.environment == 'staging'
    environment: staging
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Deploy to Staging
        env:
          DEPLOYMENT_TYPE: ${{ github.event.inputs.deployment_type || 'rolling' }}
          SERVICES: ${{ needs.detect-changes.outputs.services }}
        run: |
          echo "Deploying to staging with $DEPLOYMENT_TYPE strategy"
          for service in $(echo '${{ needs.detect-changes.outputs.services }}' | jq -r '.[]'); do
            aws ecs update-service \
              --cluster bidbees-staging-cluster \
              --service bidbees-staging-$service \
              --force-new-deployment
          done

  deploy-production:
    runs-on: ubuntu-latest
    needs: [detect-changes, test-and-build, frontend-build]
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'production'
    environment: production
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Blue-Green Deployment
        if: github.event.inputs.deployment_type == 'blue-green'
        env:
          SERVICES: ${{ needs.detect-changes.outputs.services }}
        run: |
          echo "Starting Blue-Green deployment"
          for service in $(echo '${{ needs.detect-changes.outputs.services }}' | jq -r '.[]'); do
            # Create new service version
            aws ecs create-service \
              --cluster bidbees-production-cluster \
              --service-name bidbees-production-$service-green \
              --task-definition bidbees-production-$service:LATEST \
              --desired-count 1
            
            # Wait for green service to be stable
            aws ecs wait services-stable \
              --cluster bidbees-production-cluster \
              --services bidbees-production-$service-green
            
            # Switch traffic
            aws elbv2 modify-rule \
              --rule-arn $(aws elbv2 describe-rules --listener-arn ${{ secrets.ALB_LISTENER_ARN }} --query 'Rules[?Conditions[0].Values[0]==`/'$service'/*`].RuleArn' --output text) \
              --actions Type=forward,TargetGroupArn=$(aws elbv2 describe-target-groups --names bidbees-production-$service-green --query 'TargetGroups[0].TargetGroupArn' --output text)
            
            # Delete old blue service
            aws ecs delete-service \
              --cluster bidbees-production-cluster \
              --service bidbees-production-$service \
              --force
          done
      
      - name: Canary Deployment
        if: github.event.inputs.deployment_type == 'canary'
        env:
          SERVICES: ${{ needs.detect-changes.outputs.services }}
        run: |
          echo "Starting Canary deployment"
          for service in $(echo '${{ needs.detect-changes.outputs.services }}' | jq -r '.[]'); do
            # Deploy canary with 10% traffic
            aws ecs update-service \
              --cluster bidbees-production-cluster \
              --service bidbees-production-$service-canary \
              --desired-count 1
            
            # Monitor for 5 minutes
            sleep 300
            
            # Check metrics and promote or rollback
            error_rate=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/ApplicationELB \
              --metric-name HTTPCode_Target_5XX_Count \
              --start-time $(date -u -d '5 minutes ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 300 \
              --statistics Sum \
              --dimensions Name=LoadBalancer,Value=${{ secrets.ALB_ARN }} \
              --query 'Datapoints[0].Sum' --output text)
            
            if [ "$error_rate" == "None" ] || [ "$error_rate" -lt 10 ]; then
              echo "Promoting canary for $service"
              aws ecs update-service \
                --cluster bidbees-production-cluster \
                --service bidbees-production-$service \
                --task-definition bidbees-production-$service-canary:LATEST \
                --desired-count 3
            else
              echo "Rolling back canary for $service"
              aws ecs update-service \
                --cluster bidbees-production-cluster \
                --service bidbees-production-$service-canary \
                --desired-count 0
            fi
          done
      
      - name: Rolling Deployment
        if: github.event.inputs.deployment_type == 'rolling' || github.event.inputs.deployment_type == ''
        env:
          SERVICES: ${{ needs.detect-changes.outputs.services }}
        run: |
          echo "Starting Rolling deployment"
          for service in $(echo '${{ needs.detect-changes.outputs.services }}' | jq -r '.[]'); do
            aws ecs update-service \
              --cluster bidbees-production-cluster \
              --service bidbees-production-$service \
              --force-new-deployment
            
            aws ecs wait services-stable \
              --cluster bidbees-production-cluster \
              --services bidbees-production-$service
          done

  post-deployment:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    steps:
      - name: Health Checks
        run: |
          if [ "${{ needs.deploy-production.result }}" == "success" ]; then
            endpoint="https://api.bidbees.com"
          else
            endpoint="https://staging-api.bidbees.com"
          fi
          
          for i in {1..10}; do
            if curl -f $endpoint/health; then
              echo "Health check passed"
              break
            else
              echo "Health check failed, attempt $i/10"
              sleep 30
            fi
          done
      
      - name: Update Monitoring Dashboards
        run: |
          echo "Updating CloudWatch dashboards with new deployment info"
          # Update dashboard with deployment timestamp and version