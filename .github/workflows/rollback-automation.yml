name: Rollback Automation

on:
  workflow_dispatch:
    inputs:
      rollback_type:
        description: 'Type of Rollback'
        required: true
        type: choice
        options:
          - service
          - database
          - infrastructure
          - full-system
      target_service:
        description: 'Service to rollback (if service rollback)'
        required: false
        type: string
      rollback_version:
        description: 'Version/commit to rollback to'
        required: true
        type: string
      environment:
        description: 'Environment'
        required: true
        default: 'production'
        type: choice
        options:
          - staging
          - production

env:
  AWS_REGION: us-east-1
  PROJECT_NAME: bidbees

jobs:
  validate-rollback:
    runs-on: ubuntu-latest
    outputs:
      rollback-valid: ${{ steps.validation.outputs.valid }}
      current-version: ${{ steps.validation.outputs.current-version }}
      target-exists: ${{ steps.validation.outputs.target-exists }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Validate Rollback Request
        id: validation
        run: |
          echo "Validating rollback request..."
          echo "Type: ${{ github.event.inputs.rollback_type }}"
          echo "Service: ${{ github.event.inputs.target_service }}"
          echo "Version: ${{ github.event.inputs.rollback_version }}"
          echo "Environment: ${{ github.event.inputs.environment }}"
          
          # Check if target version exists
          if git rev-parse --verify "${{ github.event.inputs.rollback_version }}" >/dev/null 2>&1; then
            echo "target-exists=true" >> $GITHUB_OUTPUT
            echo "✅ Target version exists"
          else
            echo "target-exists=false" >> $GITHUB_OUTPUT
            echo "❌ Target version does not exist"
            exit 1
          fi
          
          # Get current version
          current_version=$(git rev-parse HEAD)
          echo "current-version=$current_version" >> $GITHUB_OUTPUT
          
          # Validate service name if service rollback
          if [ "${{ github.event.inputs.rollback_type }}" == "service" ]; then
            if [ -z "${{ github.event.inputs.target_service }}" ]; then
              echo "❌ Service name required for service rollback"
              exit 1
            fi
            
            if [ ! -d "microservices/services/${{ github.event.inputs.target_service }}" ]; then
              echo "❌ Service directory not found"
              exit 1
            fi
          fi
          
          echo "valid=true" >> $GITHUB_OUTPUT
      
      - name: Pre-Rollback Health Check
        run: |
          echo "Performing pre-rollback health checks..."
          
          # Check current system health
          if [ "${{ github.event.inputs.environment }}" == "production" ]; then
            base_url="https://api.bidbees.com"
          else
            base_url="https://staging-api.bidbees.com"
          fi
          
          endpoints=(
            "$base_url/health"
            "$base_url/auth/health"
            "$base_url/tenders/health"
          )
          
          for endpoint in "${endpoints[@]}"; do
            if curl -f -s --max-time 10 "$endpoint" > /dev/null; then
              echo "✅ $endpoint healthy"
            else
              echo "⚠️ $endpoint unhealthy"
            fi
          done

  service-rollback:
    runs-on: ubuntu-latest
    needs: validate-rollback
    if: needs.validate-rollback.outputs.rollback-valid == 'true' && github.event.inputs.rollback_type == 'service'
    environment: ${{ github.event.inputs.environment }}-rollback
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.rollback_version }}
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Create Rollback Backup
        id: backup
        run: |
          # Get current task definition
          current_task_def=$(aws ecs describe-services \
            --cluster $PROJECT_NAME-${{ github.event.inputs.environment }}-cluster \
            --services $PROJECT_NAME-${{ github.event.inputs.environment }}-${{ github.event.inputs.target_service }} \
            --query 'services[0].taskDefinition' --output text)
          
          echo "current-task-def=$current_task_def" >> $GITHUB_OUTPUT
          echo "Backup created: $current_task_def"
      
      - name: Build Rollback Image
        env:
          SERVICE_NAME: ${{ github.event.inputs.target_service }}
          ROLLBACK_VERSION: ${{ github.event.inputs.rollback_version }}
        run: |
          # Login to ECR
          aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com
          
          # Build image from rollback version
          cd microservices/services/$SERVICE_NAME
          docker build -t $PROJECT_NAME/$SERVICE_NAME:$ROLLBACK_VERSION .
          
          # Tag and push to ECR
          docker tag $PROJECT_NAME/$SERVICE_NAME:$ROLLBACK_VERSION \
            ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME/$SERVICE_NAME:$ROLLBACK_VERSION
          
          docker push ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME/$SERVICE_NAME:$ROLLBACK_VERSION
      
      - name: Update ECS Service
        env:
          SERVICE_NAME: ${{ github.event.inputs.target_service }}
          ROLLBACK_VERSION: ${{ github.event.inputs.rollback_version }}
        run: |
          # Get current task definition
          task_def_arn=$(aws ecs describe-services \
            --cluster $PROJECT_NAME-${{ github.event.inputs.environment }}-cluster \
            --services $PROJECT_NAME-${{ github.event.inputs.environment }}-$SERVICE_NAME \
            --query 'services[0].taskDefinition' --output text)
          
          # Get task definition JSON
          task_def=$(aws ecs describe-task-definition --task-definition $task_def_arn)
          
          # Update image in task definition
          updated_task_def=$(echo $task_def | jq --arg image "${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME/$SERVICE_NAME:$ROLLBACK_VERSION" \
            '.taskDefinition | del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .placementConstraints, .compatibilities, .registeredAt, .registeredBy) | .containerDefinitions[0].image = $image')
          
          # Register new task definition
          new_task_def_arn=$(echo $updated_task_def | aws ecs register-task-definition --cli-input-json file:///dev/stdin --query 'taskDefinition.taskDefinitionArn' --output text)
          
          # Update service with new task definition
          aws ecs update-service \
            --cluster $PROJECT_NAME-${{ github.event.inputs.environment }}-cluster \
            --service $PROJECT_NAME-${{ github.event.inputs.environment }}-$SERVICE_NAME \
            --task-definition $new_task_def_arn
          
          echo "Service updated to use task definition: $new_task_def_arn"
      
      - name: Monitor Rollback Deployment
        env:
          SERVICE_NAME: ${{ github.event.inputs.target_service }}
        run: |
          echo "Monitoring rollback deployment..."
          
          # Wait for service to stabilize
          aws ecs wait services-stable \
            --cluster $PROJECT_NAME-${{ github.event.inputs.environment }}-cluster \
            --services $PROJECT_NAME-${{ github.event.inputs.environment }}-$SERVICE_NAME \
            --cli-read-timeout 1200 \
            --cli-connect-timeout 60
          
          echo "✅ Rollback deployment completed"
      
      - name: Post-Rollback Health Check
        env:
          SERVICE_NAME: ${{ github.event.inputs.target_service }}
        run: |
          if [ "${{ github.event.inputs.environment }}" == "production" ]; then
            base_url="https://api.bidbees.com"
          else
            base_url="https://staging-api.bidbees.com"
          fi
          
          # Wait for service to be ready
          sleep 60
          
          # Health check with retries
          for i in {1..10}; do
            if curl -f -s --max-time 10 "$base_url/health" > /dev/null; then
              echo "✅ Health check passed (attempt $i)"
              break
            else
              echo "❌ Health check failed (attempt $i/10)"
              if [ $i -eq 10 ]; then
                echo "🚨 Health checks failed after rollback!"
                echo "rollback-health=failed" >> $GITHUB_OUTPUT
                exit 1
              fi
              sleep 30
            fi
          done
          
          echo "rollback-health=success" >> $GITHUB_OUTPUT

  database-rollback:
    runs-on: ubuntu-latest
    needs: validate-rollback
    if: needs.validate-rollback.outputs.rollback-valid == 'true' && github.event.inputs.rollback_type == 'database'
    environment: ${{ github.event.inputs.environment }}-db-rollback
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.rollback_version }}
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Create Database Backup
        id: db-backup
        run: |
          timestamp=$(date +%Y%m%d-%H%M%S)
          backup_id="bidbees-rollback-backup-$timestamp"
          
          # Create RDS snapshot
          aws rds create-db-snapshot \
            --db-instance-identifier $PROJECT_NAME-${{ github.event.inputs.environment }}-db \
            --db-snapshot-identifier $backup_id
          
          echo "backup-id=$backup_id" >> $GITHUB_OUTPUT
          echo "Database backup created: $backup_id"
          
          # Wait for backup to complete
          aws rds wait db-snapshot-completed --db-snapshot-identifier $backup_id
          echo "✅ Database backup completed"
      
      - name: Apply Database Rollback
        env:
          ROLLBACK_VERSION: ${{ github.event.inputs.rollback_version }}
        run: |
          echo "Applying database rollback to version: $ROLLBACK_VERSION"
          
          # Get migration files that need to be rolled back
          git diff --name-only $ROLLBACK_VERSION..HEAD -- "**/*migration*" "**/*migrate*" > changed_migrations.txt
          
          if [ -s changed_migrations.txt ]; then
            echo "Found migration files that need rollback:"
            cat changed_migrations.txt
            
            # Apply rollback migrations (this is service-specific)
            # For now, log the files that would need manual rollback
            echo "⚠️ Manual database rollback required for these migrations:"
            cat changed_migrations.txt
          else
            echo "✅ No database migrations to rollback"
          fi
      
      - name: Validate Database State
        run: |
          # Connect to database and run validation queries
          echo "Validating database state after rollback..."
          
          # This would contain actual database validation
          echo "✅ Database validation completed"

  infrastructure-rollback:
    runs-on: ubuntu-latest
    needs: validate-rollback
    if: needs.validate-rollback.outputs.rollback-valid == 'true' && github.event.inputs.rollback_type == 'infrastructure'
    environment: ${{ github.event.inputs.environment }}-infra-rollback
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.rollback_version }}
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.6.0
      
      - name: Terraform Rollback Plan
        working-directory: aws-infrastructure/terraform
        run: |
          terraform init \
            -backend-config="bucket=bidbees-terraform-state-${{ secrets.AWS_ACCOUNT_ID }}" \
            -backend-config="key=${{ github.event.inputs.environment }}/terraform.tfstate" \
            -backend-config="region=$AWS_REGION"
          
          terraform plan \
            -var="environment=${{ github.event.inputs.environment }}" \
            -var="aws_region=$AWS_REGION" \
            -var="db_password=${{ secrets.DB_PASSWORD }}" \
            -var="docdb_password=${{ secrets.DOCDB_PASSWORD }}" \
            -out=rollback-plan
          
          terraform show -no-color rollback-plan > rollback-plan.txt
          echo "Terraform rollback plan created"
      
      - name: Apply Infrastructure Rollback
        working-directory: aws-infrastructure/terraform
        run: |
          terraform apply rollback-plan
          echo "✅ Infrastructure rollback completed"

  full-system-rollback:
    runs-on: ubuntu-latest
    needs: validate-rollback
    if: needs.validate-rollback.outputs.rollback-valid == 'true' && github.event.inputs.rollback_type == 'full-system'
    environment: ${{ github.event.inputs.environment }}-full-rollback
    steps:
      - name: Trigger Service Rollbacks
        run: |
          services=(api-gateway auth-service tender-service bidding-service payment-service courier-service supplier-service transport-service ml-service)
          
          for service in "${services[@]}"; do
            echo "Triggering rollback for $service..."
            
            curl -X POST \
              -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              -H "Accept: application/vnd.github.v3+json" \
              https://api.github.com/repos/${{ github.repository }}/actions/workflows/rollback-automation.yml/dispatches \
              -d "{
                \"ref\": \"main\",
                \"inputs\": {
                  \"rollback_type\": \"service\",
                  \"target_service\": \"$service\",
                  \"rollback_version\": \"${{ github.event.inputs.rollback_version }}\",
                  \"environment\": \"${{ github.event.inputs.environment }}\"
                }
              }"
            
            # Wait between rollbacks to avoid overwhelming the system
            sleep 30
          done
      
      - name: Monitor Full System Rollback
        run: |
          echo "Monitoring full system rollback..."
          
          # Wait for all services to stabilize
          sleep 300
          
          # Perform comprehensive health checks
          if [ "${{ github.event.inputs.environment }}" == "production" ]; then
            base_url="https://api.bidbees.com"
          else
            base_url="https://staging-api.bidbees.com"
          fi
          
          endpoints=(
            "$base_url/health"
            "$base_url/auth/health"
            "$base_url/tenders/health"
            "$base_url/payments/health"
            "$base_url/ml/health"
          )
          
          failed_endpoints=()
          
          for endpoint in "${endpoints[@]}"; do
            if ! curl -f -s --max-time 10 "$endpoint" > /dev/null; then
              failed_endpoints+=("$endpoint")
            fi
          done
          
          if [ ${#failed_endpoints[@]} -gt 0 ]; then
            echo "🚨 Some services failed health checks after rollback:"
            printf '%s\n' "${failed_endpoints[@]}"
            exit 1
          else
            echo "✅ All services healthy after full system rollback"
          fi

  post-rollback-validation:
    runs-on: ubuntu-latest
    needs: [service-rollback, database-rollback, infrastructure-rollback, full-system-rollback]
    if: always() && (needs.service-rollback.result == 'success' || needs.database-rollback.result == 'success' || needs.infrastructure-rollback.result == 'success' || needs.full-system-rollback.result == 'success')
    steps:
      - name: Comprehensive System Validation
        run: |
          echo "Performing comprehensive post-rollback validation..."
          
          if [ "${{ github.event.inputs.environment }}" == "production" ]; then
            base_url="https://api.bidbees.com"
          else
            base_url="https://staging-api.bidbees.com"
          fi
          
          # Test critical user journeys
          critical_endpoints=(
            "$base_url/auth/login"
            "$base_url/tenders"
            "$base_url/payments/health"
            "$base_url/users/profile"
          )
          
          for endpoint in "${critical_endpoints[@]}"; do
            response_time=$(curl -o /dev/null -s -w '%{time_total}' "$endpoint" || echo "failed")
            echo "Endpoint: $endpoint, Response time: ${response_time}s"
          done
      
      - name: Update Rollback Documentation
        run: |
          echo "# Rollback Execution Report" > rollback-report.md
          echo "" >> rollback-report.md
          echo "**Date:** $(date)" >> rollback-report.md
          echo "**Type:** ${{ github.event.inputs.rollback_type }}" >> rollback-report.md
          echo "**Target:** ${{ github.event.inputs.target_service || 'N/A' }}" >> rollback-report.md
          echo "**Version:** ${{ github.event.inputs.rollback_version }}" >> rollback-report.md
          echo "**Environment:** ${{ github.event.inputs.environment }}" >> rollback-report.md
          echo "" >> rollback-report.md
          echo "## Results" >> rollback-report.md
          echo "- Service Rollback: ${{ needs.service-rollback.result || 'N/A' }}" >> rollback-report.md
          echo "- Database Rollback: ${{ needs.database-rollback.result || 'N/A' }}" >> rollback-report.md
          echo "- Infrastructure Rollback: ${{ needs.infrastructure-rollback.result || 'N/A' }}" >> rollback-report.md
          echo "- Full System Rollback: ${{ needs.full-system-rollback.result || 'N/A' }}" >> rollback-report.md
      
      - name: Upload Rollback Report
        uses: actions/upload-artifact@v4
        with:
          name: rollback-report
          path: rollback-report.md
      
      - name: Notify Team
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: |
            🔄 Rollback Executed
            
            Type: ${{ github.event.inputs.rollback_type }}
            Service: ${{ github.event.inputs.target_service || 'N/A' }}
            Version: ${{ github.event.inputs.rollback_version }}
            Environment: ${{ github.event.inputs.environment }}
            Status: ${{ job.status }}
            
            Check the rollback report for details.
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}