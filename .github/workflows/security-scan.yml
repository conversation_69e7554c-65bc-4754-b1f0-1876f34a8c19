name: Security Scanning

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM UTC
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  secret-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Run Truffhog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified
      
      - name: GitLeaks Scan
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  code-quality:
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      actions: read
      contents: read
    steps:
      - uses: actions/checkout@v4
      
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, python
          queries: security-extended,security-and-quality
      
      - name: Autobuild
        uses: github/codeql-action/autobuild@v3
      
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:javascript"

  dependency-scan:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        path:
          - '.'
          - 'client'
          - 'frontend'
          - 'enterprise-tms'
          - 'microservices/services/api-gateway'
          - 'microservices/services/auth-service'
          - 'microservices/services/payment-service'
          - 'aws-cdk-deployment'
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        if: contains(matrix.path, 'package.json')
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Run Snyk
        if: hashFiles(format('{0}/package.json', matrix.path)) != ''
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --file=${{ matrix.path }}/package.json
      
      - name: Run npm audit
        if: hashFiles(format('{0}/package.json', matrix.path)) != ''
        working-directory: ${{ matrix.path }}
        run: |
          npm audit --audit-level high --production
        continue-on-error: true
      
      - name: Python Security Scan
        if: hashFiles(format('{0}/requirements.txt', matrix.path)) != ''
        working-directory: ${{ matrix.path }}
        run: |
          pip install safety bandit
          safety check -r requirements.txt
          bandit -r . -f json -o bandit-results.json
        continue-on-error: true

  container-scan:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service:
          - api-gateway
          - auth-service
          - tender-service
          - bidding-service
          - payment-service
          - courier-service
          - supplier-service
          - transport-service
          - ml-service
          - docling-processor
          - queenbee-anchor-service
          - bee-tasks-service
          - map-service
          - notification-service
          - document-service
          - analytics-service
    steps:
      - uses: actions/checkout@v4
      
      - name: Build Docker Image
        if: hashFiles(format('microservices/services/{0}/Dockerfile', matrix.service)) != ''
        run: |
          docker build -t bidbees/${{ matrix.service }}:test microservices/services/${{ matrix.service }}
      
      - name: Run Trivy vulnerability scanner
        if: hashFiles(format('microservices/services/{0}/Dockerfile', matrix.service)) != ''
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'bidbees/${{ matrix.service }}:test'
          format: 'sarif'
          output: 'trivy-results-${{ matrix.service }}.sarif'
      
      - name: Upload Trivy scan results
        if: hashFiles(format('microservices/services/{0}/Dockerfile', matrix.service)) != ''
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results-${{ matrix.service }}.sarif'
          category: 'trivy-${{ matrix.service }}'

  infrastructure-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Terraform Security Scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'config'
          scan-ref: 'aws-infrastructure/terraform'
          format: 'sarif'
          output: 'trivy-terraform.sarif'
      
      - name: Upload Terraform scan results
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-terraform.sarif'
          category: 'trivy-terraform'
      
      - name: CDK Security Scan
        run: |
          npm install -g cdk-nag
          cd aws-cdk-deployment
          npm ci
          npx cdk-nag --app="npx ts-node bin/bidbees-app.ts"

  compliance-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install Compliance Tools
        run: |
          pip install prowler
          pip install checkov
      
      - name: Run Prowler (AWS Security Assessment)
        if: github.ref == 'refs/heads/main'
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: us-east-1
        run: |
          prowler aws --services ec2 rds s3 iam --output-modes json html csv
      
      - name: Run Checkov
        run: |
          checkov -d aws-infrastructure/terraform \
            --framework terraform \
            --output cli --output sarif \
            --output-file-path console,checkov-compliance.sarif \
            --check CKV_AWS_21,CKV_AWS_23,CKV_AWS_61
      
      - name: Upload Compliance Results
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: checkov-compliance.sarif
          category: 'checkov-compliance'

  license-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: License Check
        run: |
          npx license-checker --production --json --out license-report.json
          # Check for GPL licenses
          if npx license-checker --production --excludePrivatePackages | grep -i gpl; then
            echo "GPL licenses found! Please review."
            exit 1
          fi
      
      - name: Upload License Report
        uses: actions/upload-artifact@v4
        with:
          name: license-report
          path: license-report.json

  security-report:
    runs-on: ubuntu-latest
    needs: [secret-scan, code-quality, dependency-scan, container-scan, infrastructure-scan, compliance-check, license-check]
    if: always()
    steps:
      - name: Generate Security Report
        run: |
          echo "# 🔒 Security Scan Report" >> security-report.md
          echo "" >> security-report.md
          echo "## Summary" >> security-report.md
          echo "- Secret Scan: ${{ needs.secret-scan.result }}" >> security-report.md
          echo "- Code Quality: ${{ needs.code-quality.result }}" >> security-report.md
          echo "- Dependency Scan: ${{ needs.dependency-scan.result }}" >> security-report.md
          echo "- Container Scan: ${{ needs.container-scan.result }}" >> security-report.md
          echo "- Infrastructure Scan: ${{ needs.infrastructure-scan.result }}" >> security-report.md
          echo "- Compliance Check: ${{ needs.compliance-check.result }}" >> security-report.md
          echo "- License Check: ${{ needs.license-check.result }}" >> security-report.md
          echo "" >> security-report.md
          echo "Generated on: $(date)" >> security-report.md
      
      - name: Upload Security Report
        uses: actions/upload-artifact@v4
        with:
          name: security-report
          path: security-report.md
      
      - name: Slack Notification
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          fields: repo,message,commit,author,action,eventName,ref,workflow
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  million-user-security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Load Testing Security
        run: |
          echo "Running security-focused load tests for million user scenarios"
          # Simulate high-volume attacks
          docker run --rm -v $(pwd):/app \
            loadimpact/k6:latest run \
            --out json=/app/k6-security-results.json \
            /app/scripts/security-load-test.js
      
      - name: DDoS Protection Test
        run: |
          echo "Testing DDoS protection mechanisms"
          # Test rate limiting and WAF rules
          for i in {1..1000}; do
            curl -H "User-Agent: BadBot" https://api.bidbees.com/health || true
          done
      
      - name: Database Security at Scale
        run: |
          echo "Testing database security under million user load"
          # Test connection limits, query timeouts, and injection protection