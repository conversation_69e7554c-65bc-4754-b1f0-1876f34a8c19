name: Monitoring & Alerting

on:
  push:
    branches: [main]
  schedule:
    - cron: '*/15 * * * *'  # Every 15 minutes
  workflow_dispatch:
    inputs:
      action:
        description: 'Monitoring Action'
        required: true
        default: 'health-check'
        type: choice
        options:
          - health-check
          - full-monitoring-setup
          - alert-test
          - dashboard-update

env:
  AWS_REGION: us-east-1
  PROJECT_NAME: bidbees

jobs:
  health-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: API Health Checks
        id: api-health
        run: |
          endpoints=(
            "https://api.bidbees.com/health"
            "https://api.bidbees.com/auth/health"
            "https://api.bidbees.com/tenders/health"
            "https://api.bidbees.com/payments/health"
            "https://api.bidbees.com/ml/health"
          )
          
          failed_endpoints=()
          
          for endpoint in "${endpoints[@]}"; do
            echo "Checking $endpoint"
            if ! curl -f -s --max-time 10 "$endpoint" > /dev/null; then
              echo "❌ $endpoint failed"
              failed_endpoints+=("$endpoint")
            else
              echo "✅ $endpoint healthy"
            fi
          done
          
          if [ ${#failed_endpoints[@]} -gt 0 ]; then
            echo "failed_endpoints=${failed_endpoints[*]}" >> $GITHUB_OUTPUT
            echo "health_status=unhealthy" >> $GITHUB_OUTPUT
          else
            echo "health_status=healthy" >> $GITHUB_OUTPUT
          fi
      
      - name: Database Health Check
        id: db-health
        run: |
          # Check RDS connectivity
          if timeout 10 bash -c "</dev/tcp/${{ secrets.RDS_ENDPOINT }}/5432"; then
            echo "✅ PostgreSQL accessible"
            echo "postgres_status=healthy" >> $GITHUB_OUTPUT
          else
            echo "❌ PostgreSQL not accessible"
            echo "postgres_status=unhealthy" >> $GITHUB_OUTPUT
          fi
          
          # Check Redis connectivity
          if timeout 10 bash -c "</dev/tcp/${{ secrets.REDIS_ENDPOINT }}/6379"; then
            echo "✅ Redis accessible"
            echo "redis_status=healthy" >> $GITHUB_OUTPUT
          else
            echo "❌ Redis not accessible"
            echo "redis_status=unhealthy" >> $GITHUB_OUTPUT
          fi
      
      - name: Send Health Alerts
        if: steps.api-health.outputs.health_status == 'unhealthy' || steps.db-health.outputs.postgres_status == 'unhealthy' || steps.db-health.outputs.redis_status == 'unhealthy'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: |
            🚨 Health Check Alert
            API Status: ${{ steps.api-health.outputs.health_status }}
            PostgreSQL: ${{ steps.db-health.outputs.postgres_status }}
            Redis: ${{ steps.db-health.outputs.redis_status }}
            Failed Endpoints: ${{ steps.api-health.outputs.failed_endpoints }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  aws-metrics-monitoring:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Check ECS Service Health
        id: ecs-health
        run: |
          services=(api-gateway auth-service tender-service bidding-service payment-service ml-service)
          unhealthy_services=()
          
          for service in "${services[@]}"; do
            running_count=$(aws ecs describe-services \
              --cluster $PROJECT_NAME-production-cluster \
              --services $PROJECT_NAME-production-$service \
              --query 'services[0].runningCount' --output text)
            
            desired_count=$(aws ecs describe-services \
              --cluster $PROJECT_NAME-production-cluster \
              --services $PROJECT_NAME-production-$service \
              --query 'services[0].desiredCount' --output text)
            
            if [ "$running_count" != "$desired_count" ]; then
              echo "❌ $service: $running_count/$desired_count running"
              unhealthy_services+=("$service:$running_count/$desired_count")
            else
              echo "✅ $service: $running_count/$desired_count running"
            fi
          done
          
          if [ ${#unhealthy_services[@]} -gt 0 ]; then
            echo "unhealthy_services=${unhealthy_services[*]}" >> $GITHUB_OUTPUT
            echo "ecs_status=unhealthy" >> $GITHUB_OUTPUT
          else
            echo "ecs_status=healthy" >> $GITHUB_OUTPUT
          fi
      
      - name: Check CloudWatch Alarms
        id: alarm-check
        run: |
          alarm_states=$(aws cloudwatch describe-alarms \
            --alarm-name-prefix "$PROJECT_NAME" \
            --state-value ALARM \
            --query 'MetricAlarms[].AlarmName' --output text)
          
          if [ -n "$alarm_states" ]; then
            echo "active_alarms=$alarm_states" >> $GITHUB_OUTPUT
            echo "alarm_status=active" >> $GITHUB_OUTPUT
          else
            echo "alarm_status=ok" >> $GITHUB_OUTPUT
          fi
      
      - name: Monitor Resource Usage
        id: resource-usage
        run: |
          # Check ECS CPU/Memory usage
          high_cpu_services=()
          high_memory_services=()
          
          services=(api-gateway auth-service tender-service bidding-service payment-service ml-service)
          
          for service in "${services[@]}"; do
            cpu_utilization=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/ECS \
              --metric-name CPUUtilization \
              --dimensions Name=ServiceName,Value=$PROJECT_NAME-production-$service \
              --start-time $(date -u -d '5 minutes ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 300 \
              --statistics Average \
              --query 'Datapoints[0].Average' --output text)
            
            memory_utilization=$(aws cloudwatch get-metric-statistics \
              --namespace AWS/ECS \
              --metric-name MemoryUtilization \
              --dimensions Name=ServiceName,Value=$PROJECT_NAME-production-$service \
              --start-time $(date -u -d '5 minutes ago' +%Y-%m-%dT%H:%M:%S) \
              --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
              --period 300 \
              --statistics Average \
              --query 'Datapoints[0].Average' --output text)
            
            if [ "$cpu_utilization" != "None" ] && [ $(echo "$cpu_utilization > 80" | bc -l) -eq 1 ]; then
              high_cpu_services+=("$service:${cpu_utilization}%")
            fi
            
            if [ "$memory_utilization" != "None" ] && [ $(echo "$memory_utilization > 80" | bc -l) -eq 1 ]; then
              high_memory_services+=("$service:${memory_utilization}%")
            fi
          done
          
          echo "high_cpu_services=${high_cpu_services[*]}" >> $GITHUB_OUTPUT
          echo "high_memory_services=${high_memory_services[*]}" >> $GITHUB_OUTPUT

  real-user-monitoring:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Real User Monitoring
        id: rum-setup
        run: |
          # Install RUM tools
          npm install -g puppeteer
          
          # Create synthetic user journeys
          node scripts/rum/synthetic-users.js \
            --url https://bidbees.com \
            --scenarios user-registration,tender-search,bid-submission \
            --concurrent-users 10 \
            --duration 300
      
      - name: Core Web Vitals Check
        run: |
          # Check Core Web Vitals using Lighthouse CI
          npm install -g @lhci/cli
          
          lhci autorun \
            --upload.target=temporary-public-storage \
            --collect.url=https://bidbees.com \
            --collect.url=https://bidbees.com/tenders \
            --collect.url=https://bidbees.com/dashboard \
            --assert.assertions.performance=0.8 \
            --assert.assertions.accessibility=0.9
      
      - name: User Experience Monitoring
        run: |
          # Monitor critical user paths
          python scripts/rum/user-journey-monitor.py \
            --base-url https://bidbees.com \
            --journeys registration,login,tender-creation,bid-submission \
            --alert-threshold 5000  # 5 second threshold

  log-analysis:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Analyze Application Logs
        id: log-analysis
        run: |
          # Get recent CloudWatch logs
          log_groups=(
            "/ecs/$PROJECT_NAME-api-gateway"
            "/ecs/$PROJECT_NAME-auth-service"
            "/ecs/$PROJECT_NAME-payment-service"
            "/ecs/$PROJECT_NAME-ml-service"
          )
          
          error_count=0
          warning_count=0
          
          for log_group in "${log_groups[@]}"; do
            # Count errors in last 15 minutes
            errors=$(aws logs filter-log-events \
              --log-group-name "$log_group" \
              --start-time $(($(date +%s) * 1000 - 900000)) \
              --filter-pattern "ERROR" \
              --query 'events | length(@)')
            
            warnings=$(aws logs filter-log-events \
              --log-group-name "$log_group" \
              --start-time $(($(date +%s) * 1000 - 900000)) \
              --filter-pattern "WARN" \
              --query 'events | length(@)')
            
            error_count=$((error_count + errors))
            warning_count=$((warning_count + warnings))
            
            echo "$log_group: $errors errors, $warnings warnings"
          done
          
          echo "total_errors=$error_count" >> $GITHUB_OUTPUT
          echo "total_warnings=$warning_count" >> $GITHUB_OUTPUT
          
          # Alert if error rate is high
          if [ $error_count -gt 50 ]; then
            echo "high_error_rate=true" >> $GITHUB_OUTPUT
          fi
      
      - name: Security Log Analysis
        run: |
          # Check for security-related events
          security_events=$(aws logs filter-log-events \
            --log-group-name "/ecs/$PROJECT_NAME-api-gateway" \
            --start-time $(($(date +%s) * 1000 - 900000)) \
            --filter-pattern "{ $.level = \"ERROR\" && ($.message = \"*authentication*\" || $.message = \"*authorization*\" || $.message = \"*forbidden*\") }" \
            --query 'events | length(@)')
          
          if [ $security_events -gt 10 ]; then
            echo "🚨 High number of security events detected: $security_events"
            echo "security_alert=true" >> $GITHUB_OUTPUT
          fi

  setup-monitoring:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'full-monitoring-setup'
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Create CloudWatch Alarms
        run: |
          # API Gateway High Error Rate
          aws cloudwatch put-metric-alarm \
            --alarm-name "$PROJECT_NAME-api-gateway-high-error-rate" \
            --alarm-description "API Gateway error rate > 5%" \
            --metric-name 4XXError \
            --namespace AWS/ApiGateway \
            --statistic Sum \
            --period 300 \
            --threshold 50 \
            --comparison-operator GreaterThanThreshold \
            --evaluation-periods 2 \
            --alarm-actions arn:aws:sns:$AWS_REGION:${{ secrets.AWS_ACCOUNT_ID }}:$PROJECT_NAME-alerts
          
          # ECS High CPU
          services=(api-gateway auth-service tender-service payment-service ml-service)
          for service in "${services[@]}"; do
            aws cloudwatch put-metric-alarm \
              --alarm-name "$PROJECT_NAME-$service-high-cpu" \
              --alarm-description "$service CPU > 80%" \
              --metric-name CPUUtilization \
              --namespace AWS/ECS \
              --statistic Average \
              --period 300 \
              --threshold 80 \
              --comparison-operator GreaterThanThreshold \
              --evaluation-periods 2 \
              --dimensions Name=ServiceName,Value=$PROJECT_NAME-production-$service \
              --alarm-actions arn:aws:sns:$AWS_REGION:${{ secrets.AWS_ACCOUNT_ID }}:$PROJECT_NAME-alerts
          done
          
          # RDS High Connections
          aws cloudwatch put-metric-alarm \
            --alarm-name "$PROJECT_NAME-rds-high-connections" \
            --alarm-description "RDS connections > 80% of max" \
            --metric-name DatabaseConnections \
            --namespace AWS/RDS \
            --statistic Average \
            --period 300 \
            --threshold 80 \
            --comparison-operator GreaterThanThreshold \
            --evaluation-periods 2 \
            --alarm-actions arn:aws:sns:$AWS_REGION:${{ secrets.AWS_ACCOUNT_ID }}:$PROJECT_NAME-alerts
      
      - name: Create Custom Dashboards
        run: |
          # Create comprehensive monitoring dashboard
          cat > dashboard-config.json << 'EOF'
          {
            "widgets": [
              {
                "type": "metric",
                "properties": {
                  "metrics": [
                    ["AWS/ECS", "CPUUtilization", "ServiceName", "bidbees-production-api-gateway"],
                    [".", "MemoryUtilization", ".", "."],
                    ["AWS/ApplicationELB", "RequestCount", "LoadBalancer", "bidbees-production-alb"],
                    [".", "TargetResponseTime", ".", "."],
                    ["AWS/RDS", "CPUUtilization", "DBInstanceIdentifier", "bidbees-production-db"],
                    [".", "DatabaseConnections", ".", "."]
                  ],
                  "period": 300,
                  "stat": "Average",
                  "region": "us-east-1",
                  "title": "System Overview"
                }
              },
              {
                "type": "log",
                "properties": {
                  "query": "SOURCE '/ecs/bidbees-api-gateway'\n| fields @timestamp, @message\n| filter @message like /ERROR/\n| sort @timestamp desc\n| limit 100",
                  "region": "us-east-1",
                  "title": "Recent Errors",
                  "view": "table"
                }
              }
            ]
          }
          EOF
          
          aws cloudwatch put-dashboard \
            --dashboard-name "$PROJECT_NAME-production-overview" \
            --dashboard-body file://dashboard-config.json

  alert-consolidation:
    runs-on: ubuntu-latest
    needs: [health-checks, aws-metrics-monitoring, log-analysis]
    if: always()
    steps:
      - name: Consolidate Alerts
        run: |
          # Consolidate all monitoring results
          echo "# 📊 Monitoring Report" > monitoring-report.md
          echo "" >> monitoring-report.md
          echo "**Timestamp:** $(date)" >> monitoring-report.md
          echo "" >> monitoring-report.md
          
          # Health status
          echo "## 🏥 Health Status" >> monitoring-report.md
          echo "- API Health: ${{ needs.health-checks.outputs.health_status || 'unknown' }}" >> monitoring-report.md
          echo "- ECS Status: ${{ needs.aws-metrics-monitoring.outputs.ecs_status || 'unknown' }}" >> monitoring-report.md
          echo "- Database Status: ${{ needs.health-checks.outputs.postgres_status || 'unknown' }}" >> monitoring-report.md
          echo "" >> monitoring-report.md
          
          # Resource usage
          echo "## 📈 Resource Usage" >> monitoring-report.md
          echo "- High CPU Services: ${{ needs.aws-metrics-monitoring.outputs.high_cpu_services || 'none' }}" >> monitoring-report.md
          echo "- High Memory Services: ${{ needs.aws-metrics-monitoring.outputs.high_memory_services || 'none' }}" >> monitoring-report.md
          echo "" >> monitoring-report.md
          
          # Log analysis
          echo "## 📝 Log Analysis (Last 15 min)" >> monitoring-report.md
          echo "- Total Errors: ${{ needs.log-analysis.outputs.total_errors || '0' }}" >> monitoring-report.md
          echo "- Total Warnings: ${{ needs.log-analysis.outputs.total_warnings || '0' }}" >> monitoring-report.md
          echo "" >> monitoring-report.md
      
      - name: Send Critical Alerts
        if: |
          needs.health-checks.outputs.health_status == 'unhealthy' ||
          needs.aws-metrics-monitoring.outputs.ecs_status == 'unhealthy' ||
          needs.log-analysis.outputs.high_error_rate == 'true'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: |
            🚨 CRITICAL ALERT - BidBees Production Issues Detected
            
            Health Issues:
            • API Health: ${{ needs.health-checks.outputs.health_status }}
            • ECS Status: ${{ needs.aws-metrics-monitoring.outputs.ecs_status }}
            • High Error Rate: ${{ needs.log-analysis.outputs.high_error_rate }}
            
            Immediate action required!
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}