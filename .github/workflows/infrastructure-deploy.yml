name: Infrastructure Deployment

on:
  push:
    branches: [main]
    paths:
      - 'aws-infrastructure/**'
      - 'aws-cdk-deployment/**'
  workflow_dispatch:
    inputs:
      action:
        description: 'Infrastructure Action'
        required: true
        default: 'plan'
        type: choice
        options:
          - plan
          - apply
          - destroy
      environment:
        description: 'Target Environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  AWS_REGION: us-east-1
  TF_VERSION: 1.6.0

jobs:
  terraform-plan:
    runs-on: ubuntu-latest
    outputs:
      tfplan: ${{ steps.plan.outputs.tfplan }}
      cost-estimate: ${{ steps.cost.outputs.monthly-cost }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Terraform Init
        working-directory: aws-infrastructure/terraform
        run: |
          terraform init \
            -backend-config="bucket=bidbees-terraform-state-${{ secrets.AWS_ACCOUNT_ID }}" \
            -backend-config="key=${{ github.event.inputs.environment || 'production' }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_REGION }}"
      
      - name: Terraform Validate
        working-directory: aws-infrastructure/terraform
        run: terraform validate
      
      - name: Terraform Plan
        id: plan
        working-directory: aws-infrastructure/terraform
        run: |
          terraform plan \
            -var="environment=${{ github.event.inputs.environment || 'production' }}" \
            -var="aws_region=${{ env.AWS_REGION }}" \
            -var="db_password=${{ secrets.DB_PASSWORD }}" \
            -var="docdb_password=${{ secrets.DOCDB_PASSWORD }}" \
            -out=tfplan \
            -detailed-exitcode
          echo "tfplan=$(base64 -w 0 tfplan)" >> $GITHUB_OUTPUT
      
      - name: Cost Estimation
        id: cost
        run: |
          # Install Infracost
          curl -fsSL https://raw.githubusercontent.com/infracost/infracost/master/scripts/install.sh | sh
          
          cd aws-infrastructure/terraform
          infracost breakdown --path . --format json --out-file /tmp/cost.json
          monthly_cost=$(cat /tmp/cost.json | jq -r '.totalMonthlyCost')
          echo "monthly-cost=$monthly_cost" >> $GITHUB_OUTPUT
          
          # Generate cost comment for PR
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            infracost comment github --path /tmp/cost.json \
              --repo ${{ github.repository }} \
              --github-token ${{ secrets.GITHUB_TOKEN }} \
              --pull-request ${{ github.event.number }}
          fi
        env:
          INFRACOST_API_KEY: ${{ secrets.INFRACOST_API_KEY }}
      
      - name: Security Scan with Checkov
        run: |
          pip install checkov
          checkov -d aws-infrastructure/terraform --framework terraform \
            --output cli --output sarif --output-file-path console,checkov-results.sarif
      
      - name: Upload Security Scan Results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: checkov-results.sarif
      
      - name: Drift Detection
        if: github.event.inputs.action != 'destroy'
        working-directory: aws-infrastructure/terraform
        run: |
          terraform plan -detailed-exitcode
          exit_code=$?
          if [ $exit_code -eq 2 ]; then
            echo "⚠️ Infrastructure drift detected!"
            echo "drift=true" >> $GITHUB_OUTPUT
          else
            echo "✅ No infrastructure drift detected"
            echo "drift=false" >> $GITHUB_OUTPUT
          fi

  terraform-apply:
    runs-on: ubuntu-latest
    needs: terraform-plan
    if: |
      (github.event.inputs.action == 'apply' || 
       (github.ref == 'refs/heads/main' && github.event.inputs.action != 'plan')) &&
      needs.terraform-plan.outputs.cost-estimate < '1000'
    environment: 
      name: ${{ github.event.inputs.environment || 'production' }}
      url: https://${{ github.event.inputs.environment || 'production' }}.bidbees.com
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Terraform Init
        working-directory: aws-infrastructure/terraform
        run: |
          terraform init \
            -backend-config="bucket=bidbees-terraform-state-${{ secrets.AWS_ACCOUNT_ID }}" \
            -backend-config="key=${{ github.event.inputs.environment || 'production' }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_REGION }}"
      
      - name: Download Terraform Plan
        run: |
          echo "${{ needs.terraform-plan.outputs.tfplan }}" | base64 -d > aws-infrastructure/terraform/tfplan
      
      - name: Terraform Apply
        working-directory: aws-infrastructure/terraform
        run: terraform apply tfplan
      
      - name: Output Infrastructure Details
        working-directory: aws-infrastructure/terraform
        run: |
          echo "## 🏗️ Infrastructure Deployment Complete" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Outputs:" >> $GITHUB_STEP_SUMMARY
          terraform output -json | jq -r 'to_entries[] | "- **\(.key)**: \(.value.value)"' >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Cost Estimate: \$$((${{ needs.terraform-plan.outputs.cost-estimate }}))/month" >> $GITHUB_STEP_SUMMARY

  cdk-deploy:
    runs-on: ubuntu-latest
    needs: terraform-apply
    if: needs.terraform-apply.result == 'success'
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: aws-cdk-deployment/package-lock.json
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Install CDK Dependencies
        working-directory: aws-cdk-deployment
        run: npm ci
      
      - name: CDK Bootstrap
        working-directory: aws-cdk-deployment
        run: |
          npx cdk bootstrap aws://${{ secrets.AWS_ACCOUNT_ID }}/${{ env.AWS_REGION }} \
            --toolkit-stack-name CDKToolkit-${{ github.event.inputs.environment || 'production' }}
      
      - name: CDK Deploy
        working-directory: aws-cdk-deployment
        env:
          ENVIRONMENT: ${{ github.event.inputs.environment || 'production' }}
          PROJECT_NAME: bidbees
          AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
          AWS_REGION: ${{ env.AWS_REGION }}
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_KEY: ${{ secrets.SUPABASE_SERVICE_KEY }}
          MAPBOX_ACCESS_TOKEN: ${{ secrets.MAPBOX_ACCESS_TOKEN }}
        run: |
          npx cdk deploy --all \
            --require-approval never \
            --context environment=${{ github.event.inputs.environment || 'production' }} \
            --outputs-file cdk-outputs.json
      
      - name: Update ECS Task Definitions
        run: |
          # Update all microservices with latest environment variables
          services=(api-gateway auth-service tender-service bidding-service payment-service courier-service supplier-service transport-service ml-service docling-processor queenbee-anchor-service bee-tasks-service map-service notification-service document-service analytics-service)
          
          for service in "${services[@]}"; do
            echo "Updating task definition for $service"
            
            # Get current task definition
            task_def=$(aws ecs describe-task-definition \
              --task-definition bidbees-${{ github.event.inputs.environment || 'production' }}-$service \
              --query 'taskDefinition' --output json)
            
            # Update environment variables
            updated_task_def=$(echo $task_def | jq --arg supabase_url "${{ secrets.SUPABASE_URL }}" \
              --arg supabase_key "${{ secrets.SUPABASE_ANON_KEY }}" \
              --arg mapbox_token "${{ secrets.MAPBOX_ACCESS_TOKEN }}" \
              '.containerDefinitions[0].environment |= map(
                if .name == "SUPABASE_URL" then .value = $supabase_url
                elif .name == "SUPABASE_ANON_KEY" then .value = $supabase_key
                elif .name == "MAPBOX_ACCESS_TOKEN" then .value = $mapbox_token
                else . end
              )')
            
            # Register new task definition
            aws ecs register-task-definition \
              --cli-input-json "$updated_task_def" > /dev/null
          done

  terraform-destroy:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'destroy'
    environment: 
      name: destroy-${{ github.event.inputs.environment }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: CDK Destroy
        working-directory: aws-cdk-deployment
        run: |
          npm ci
          npx cdk destroy --all --force \
            --context environment=${{ github.event.inputs.environment }}
        continue-on-error: true
      
      - name: Terraform Init
        working-directory: aws-infrastructure/terraform
        run: |
          terraform init \
            -backend-config="bucket=bidbees-terraform-state-${{ secrets.AWS_ACCOUNT_ID }}" \
            -backend-config="key=${{ github.event.inputs.environment }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_REGION }}"
      
      - name: Terraform Destroy
        working-directory: aws-infrastructure/terraform
        run: |
          terraform destroy -auto-approve \
            -var="environment=${{ github.event.inputs.environment }}" \
            -var="aws_region=${{ env.AWS_REGION }}" \
            -var="db_password=${{ secrets.DB_PASSWORD }}" \
            -var="docdb_password=${{ secrets.DOCDB_PASSWORD }}"