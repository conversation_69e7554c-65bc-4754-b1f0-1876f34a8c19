# GitHub Secrets Configuration

Copy and set these secrets in your GitHub repository settings:
Settings > Secrets and variables > Actions > New repository secret

## Required Secrets

### AWS Credentials
- `AWS_ACCESS_KEY_ID`: Your AWS access key
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
- `AWS_ACCOUNT_ID`: Your AWS account ID (12 digits)
- `AWS_REGION`: AWS region (e.g., us-east-1)

### Database
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `MONGODB_URI`: MongoDB connection string (if using)

### Authentication
- `JWT_SECRET`: Random string for JWT signing
- `SESSION_SECRET`: Random string for session encryption

### External Services
- `SENTRY_DSN`: Sentry error tracking DSN
- `STRIPE_API_KEY`: Stripe payment API key
- `SENDGRID_API_KEY`: SendGrid email API key

### Docker Registry (if using Docker Hub)
- `DOCKER_USERNAME`: Docker Hub username
- `DOCKER_PASSWORD`: Docker Hub password

## Environment Variables

Create these environments in Settings > Environments:

### staging
- Protection rules: None
- Secrets: Same as above but with staging values

### production
- Protection rules: Required reviewers
- Secrets: Same as above but with production values
