#!/bin/bash

# BidBeez Environment Variables Configuration Script
# This script helps configure all production environment variables

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Function to generate secure random strings
generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

echo "🔧 BidBeez Production Environment Configuration"
echo "=============================================="
echo ""

# Create main .env file for docker-compose
ENV_FILE="microservices/.env"

echo "📝 Creating main environment file: $ENV_FILE"
echo ""

# Check if .env already exists
if [ -f "$ENV_FILE" ]; then
    print_warning "Environment file already exists. Creating backup..."
    cp "$ENV_FILE" "$ENV_FILE.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Start creating the .env file
cat > "$ENV_FILE" << EOF
# BidBeez Production Environment Variables
# Generated on $(date)

# ========================================
# 1. DATABASE CONFIGURATION
# ========================================

# PostgreSQL Configuration
DB_USER=bidbees_user
DB_PASSWORD=$(generate_secret)
DATABASE_URL=**********************************************************/bidbees

# MongoDB Configuration (if using MongoDB instead)
# MONGODB_URI=***************************************************************

# Redis Configuration
REDIS_PASSWORD=$(generate_secret)
REDIS_URL=redis://:$(generate_secret)@redis:6379

# ========================================
# 2. SECURITY & AUTHENTICATION
# ========================================

# JWT Secrets (minimum 32 characters)
JWT_SECRET=$(generate_secret)$(generate_secret)
JWT_REFRESH_SECRET=$(generate_secret)$(generate_secret)

# Session Secret
SESSION_SECRET=$(generate_secret)$(generate_secret)

# ========================================
# 3. MESSAGING & EVENTS
# ========================================

# Kafka Configuration
KAFKA_BROKERS=kafka:9092
KAFKA_CLIENT_ID=bidbees-production

# ========================================
# 4. EXTERNAL SERVICES
# ========================================

# Stripe Payment Processing
STRIPE_API_KEY=sk_live_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# SendGrid Email Service
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>

# AWS Services
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
S3_BUCKET=bidbees-documents-production

# Mapbox (for mapping features)
MAPBOX_ACCESS_TOKEN=pk.your_mapbox_token_here

# ========================================
# 5. CLOUDFRONT & CDN
# ========================================

# CloudFront Configuration
CLOUDFRONT_DISTRIBUTION_ID=your_distribution_id_here
CLOUDFRONT_DOMAIN=d58ser5n68qmv.cloudfront.net

# ========================================
# 6. MONITORING & LOGGING
# ========================================

# Sentry Error Tracking
SENTRY_DSN=https://<EMAIL>/project_id

# DataDog (optional)
# DATADOG_API_KEY=your_datadog_api_key_here

# ========================================
# 7. APPLICATION SETTINGS
# ========================================

# Environment
NODE_ENV=production
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# ========================================
# 8. THIRD-PARTY INTEGRATIONS
# ========================================

# OpenAI (for AI features)
OPENAI_API_KEY=sk-your_openai_api_key_here

# Google APIs (if needed)
# GOOGLE_CLIENT_ID=your_google_client_id_here
# GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# ========================================
# 9. BACKUP SERVICES (Optional)
# ========================================

# Backup Database URL (for read replicas)
# BACKUP_DATABASE_URL=*******************************************/bidbees

EOF

print_status "Main environment file created: $ENV_FILE"
echo ""

# Now let's update individual service .env.production files
echo "📋 Updating individual service configurations..."
echo ""

# Function to update service env file
update_service_env() {
    local service_name=$1
    local service_path="microservices/services/$service_name"
    local env_file="$service_path/.env.production"
    
    if [ -f "$env_file" ]; then
        print_info "Updating $service_name configuration..."
        
        # Update common variables
        sed -i.bak "s|NODE_ENV=.*|NODE_ENV=production|g" "$env_file"
        sed -i.bak "s|CORS_ORIGIN=.*|CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net,https://bidbees.com|g" "$env_file"
        
        # Service-specific updates
        case $service_name in
            "api-gateway")
                sed -i.bak "s|\${REDIS_URL}|redis://:${REDIS_PASSWORD}@redis:6379|g" "$env_file"
                sed -i.bak "s|\${JWT_SECRET}|${JWT_SECRET}|g" "$env_file"
                sed -i.bak "s|\${JWT_REFRESH_SECRET}|${JWT_REFRESH_SECRET}|g" "$env_file"
                sed -i.bak "s|\${KAFKA_BROKERS}|kafka:9092|g" "$env_file"
                sed -i.bak "s|\${CLOUDFRONT_DISTRIBUTION_ID}|your_distribution_id_here|g" "$env_file"
                ;;
            "auth-service")
                # Update with production values
                sed -i.bak "s|MONGODB_URI=.*|DATABASE_URL=postgresql://bidbees_user:${DB_PASSWORD}@postgres:5432/bidbees|g" "$env_file"
                sed -i.bak "s|REDIS_URL=.*|REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379|g" "$env_file"
                sed -i.bak "s|JWT_SECRET=.*|JWT_SECRET=${JWT_SECRET}|g" "$env_file"
                sed -i.bak "s|JWT_REFRESH_SECRET=.*|JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}|g" "$env_file"
                ;;
            "payment-service")
                echo "STRIPE_API_KEY=sk_live_your_stripe_secret_key_here" >> "$env_file"
                echo "STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here" >> "$env_file"
                ;;
            "notification-service")
                echo "SENDGRID_API_KEY=SG.your_sendgrid_api_key_here" >> "$env_file"
                echo "FROM_EMAIL=<EMAIL>" >> "$env_file"
                ;;
        esac
        
        # Remove backup files
        rm -f "$env_file.bak"
        print_status "Updated $service_name"
    fi
}

# Update all services
services=(
    "api-gateway"
    "auth-service"
    "user-service"
    "tender-service"
    "bidding-service"
    "payment-service"
    "notification-service"
    "analytics-service"
    "ml-service"
    "document-service"
    "supplier-service"
    "transport-service"
    "courier-service"
)

for service in "${services[@]}"; do
    update_service_env "$service"
done

echo ""
echo "📄 Creating environment variables checklist..."

# Create a checklist for manual configuration
cat > "ENV_VARS_CHECKLIST.md" << EOF
# Environment Variables Configuration Checklist

## ✅ Auto-Generated Variables
- [x] JWT_SECRET - Generated secure random string
- [x] JWT_REFRESH_SECRET - Generated secure random string
- [x] SESSION_SECRET - Generated secure random string
- [x] DB_PASSWORD - Generated secure random string
- [x] REDIS_PASSWORD - Generated secure random string

## ❌ Required Manual Configuration

### 1. Database
- [ ] Verify DATABASE_URL points to your production PostgreSQL
- [ ] Update DB_USER if different from 'bidbees_user'

### 2. External Services
- [ ] **STRIPE_API_KEY** - Get from Stripe Dashboard
- [ ] **STRIPE_WEBHOOK_SECRET** - Get from Stripe Webhooks
- [ ] **SENDGRID_API_KEY** - Get from SendGrid
- [ ] **AWS_ACCESS_KEY_ID** - Get from AWS IAM
- [ ] **AWS_SECRET_ACCESS_KEY** - Get from AWS IAM
- [ ] **S3_BUCKET** - Create S3 bucket and update name

### 3. CloudFront
- [ ] **CLOUDFRONT_DISTRIBUTION_ID** - Get from AWS CloudFront

### 4. Optional Services
- [ ] **SENTRY_DSN** - For error tracking (optional)
- [ ] **MAPBOX_ACCESS_TOKEN** - For map features (optional)
- [ ] **OPENAI_API_KEY** - For AI features (optional)

## 📝 Next Steps

1. Review the generated \`.env\` file in \`microservices/.env\`
2. Update all placeholder values with actual credentials
3. Ensure all secrets are kept secure
4. Test connections before deploying

## 🔒 Security Notes

- Never commit .env files to Git
- Use AWS Secrets Manager or similar for production
- Rotate secrets regularly
- Use different credentials for staging/production
EOF

print_status "Environment variables checklist created: ENV_VARS_CHECKLIST.md"

echo ""
echo "✅ Environment Configuration Complete!"
echo "===================================="
echo ""
echo "📋 Summary:"
echo "- Main .env file created: $ENV_FILE"
echo "- Service configurations updated"
echo "- Secure secrets generated for JWT and passwords"
echo ""
echo "⚠️  IMPORTANT: You must still configure:"
echo "1. Stripe API credentials"
echo "2. SendGrid API key"
echo "3. AWS credentials"
echo "4. CloudFront Distribution ID"
echo ""
echo "📄 Check ENV_VARS_CHECKLIST.md for detailed instructions"
echo ""
print_warning "Remember to keep your .env files secure and never commit them to Git!"