# MCP Server Environment Variables

# GitHub Personal Access Token for GitHub MCP server
# Get one at: https://github.com/settings/tokens
GITHUB_PERSONAL_ACCESS_TOKEN=your-github-token-here

# Brave Search API Key for web search MCP server
# Get one at: https://brave.com/search/api/
BRAVE_API_KEY=your-brave-api-key-here

# PostgreSQL connection string (adjust as needed)
DATABASE_URL=postgresql://username:password@localhost/bidbees

# Optional: Memory server configuration
MEMORY_STORAGE_PATH=/tmp/claude-memory
