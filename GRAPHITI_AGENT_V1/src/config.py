import os
from dataclasses import dataclass
from typing import Optional
from dotenv import load_dotenv

load_dotenv()

@dataclass
class GraphitiConfig:
    """Configuration for Graphiti agent"""
    # Supabase Configuration
    supabase_url: str = os.getenv("SUPABASE_URL", "")
    supabase_key: str = os.getenv("SUPABASE_KEY", "")
    supabase_service_key: str = os.getenv("SUPABASE_SERVICE_KEY", "")
    
    # OpenAI Configuration
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_model: str = os.getenv("OPENAI_MODEL", "gpt-4-turbo-preview")
    
    # Agent Configuration
    agent_name: str = os.getenv("AGENT_NAME", "GraphitiAgent")
    max_search_results: int = int(os.getenv("MAX_SEARCH_RESULTS", "10"))
    enable_parallel_runtime: bool = os.getenv("USE_PARALLEL_RUNTIME", "false").lower() == "true"
    
    # API Configuration
    api_host: str = os.getenv("API_HOST", "0.0.0.0")
    api_port: int = int(os.getenv("API_PORT", "8000"))
    
    def validate(self) -> None:
        """Validate required configuration"""
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY is required")
        if not self.supabase_url:
            raise ValueError("SUPABASE_URL is required")
        if not self.supabase_key:
            raise ValueError("SUPABASE_KEY is required")
        if not self.supabase_service_key:
            raise ValueError("SUPABASE_SERVICE_KEY is required")
