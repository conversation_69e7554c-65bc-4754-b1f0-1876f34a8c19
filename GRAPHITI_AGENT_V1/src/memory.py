import asyncio
import logging
import json
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime
import numpy as np
from sentence_transformers import SentenceTransformer

from supabase import create_client, Client
import asyncpg

from .config import GraphitiConfig

logger = logging.getLogger(__name__)

class SupabaseGraphitiMemory:
    """Supabase-based memory system for the agent (mimicking Graphiti functionality)"""
    
    def __init__(self, config: GraphitiConfig):
        self.config = config
        self.supabase: Optional[Client] = None
        self.db_pool: Optional[asyncpg.Pool] = None
        self.embedder = SentenceTransformer('all-MiniLM-L6-v2')
        
    async def initialize(self) -> None:
        """Initialize Supabase connection and setup database schema"""
        try:
            # Initialize Supabase client
            self.supabase = create_client(
                self.config.supabase_url,
                self.config.supabase_service_key
            )
            
            # Create database connection pool for async operations
            database_url = self.config.supabase_url.replace('https://', 'postgresql://postgres:')
            # Extract project ref from URL for connection
            project_ref = self.config.supabase_url.split('//')[1].split('.')[0]
            
            # Note: You'll need to get the actual database password from Supabase dashboard
            # For now, we'll use the service operations through Supabase client
            
            # Create tables if they don't exist
            await self._create_tables()
            
            logger.info("Supabase Graphiti memory system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase Graphiti: {e}")
            raise
    
    async def _create_tables(self) -> None:
        """Create necessary tables in Supabase"""
        try:
            # Create episodes table
            episodes_table = """
            CREATE TABLE IF NOT EXISTS episodes (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name TEXT NOT NULL,
                content TEXT NOT NULL,
                episode_type TEXT DEFAULT 'conversation',
                timestamp TIMESTAMPTZ DEFAULT NOW(),
                source TEXT DEFAULT 'agent',
                embedding VECTOR(384),
                metadata JSONB DEFAULT '{}'::jsonb,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """
            
            # Create entities table
            entities_table = """
            CREATE TABLE IF NOT EXISTS entities (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name TEXT NOT NULL UNIQUE,
                entity_type TEXT DEFAULT 'general',
                description TEXT,
                properties JSONB DEFAULT '{}'::jsonb,
                embedding VECTOR(384),
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """
            
            # Create relationships table
            relationships_table = """
            CREATE TABLE IF NOT EXISTS relationships (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                source_entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
                target_entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
                relationship_type TEXT NOT NULL,
                fact TEXT NOT NULL,
                confidence FLOAT DEFAULT 1.0,
                episode_id UUID REFERENCES episodes(id) ON DELETE CASCADE,
                embedding VECTOR(384),
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """
            
            # Create indexes
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_episodes_timestamp ON episodes(timestamp);",
                "CREATE INDEX IF NOT EXISTS idx_episodes_type ON episodes(episode_type);",
                "CREATE INDEX IF NOT EXISTS idx_entities_name ON entities(name);",
                "CREATE INDEX IF NOT EXISTS idx_entities_type ON entities(entity_type);",
                "CREATE INDEX IF NOT EXISTS idx_relationships_source ON relationships(source_entity_id);",
                "CREATE INDEX IF NOT EXISTS idx_relationships_target ON relationships(target_entity_id);",
                "CREATE INDEX IF NOT EXISTS idx_relationships_type ON relationships(relationship_type);"
            ]
            
            # Execute table creation using Supabase RPC or direct SQL
            tables = [episodes_table, entities_table, relationships_table] + indexes
            
            for table_sql in tables:
                try:
                    # Use Supabase's RPC to execute SQL
                    result = self.supabase.rpc('exec_sql', {'sql': table_sql}).execute()
                    logger.debug(f"Executed SQL: {table_sql[:50]}...")
                except Exception as e:
                    # If RPC doesn't work, we'll need to create tables manually in Supabase dashboard
                    logger.warning(f"Could not execute SQL directly: {e}")
                    logger.info("Please create the tables manually in your Supabase dashboard")
                    break
            
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            # Continue anyway - tables might already exist
    
    async def add_episode(self, content: str, episode_type: str = "conversation") -> str:
        """Add a new episode to the knowledge graph"""
        if not self.supabase:
            raise RuntimeError("Supabase not initialized")
            
        try:
            episode_id = str(uuid.uuid4())
            
            # Generate embedding for the content
            embedding = self.embedder.encode(content).tolist()
            
            episode_data = {
                "id": episode_id,
                "name": f"{episode_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "content": content,
                "episode_type": episode_type,
                "timestamp": datetime.now().isoformat(),
                "source": self.config.agent_name,
                "embedding": embedding,
                "metadata": {
                    "agent_name": self.config.agent_name,
                    "created_by": "supabase_graphiti_memory"
                }
            }
            
            # Insert episode into Supabase
            result = self.supabase.table("episodes").insert(episode_data).execute()
            
            logger.info(f"Added episode with ID: {episode_id}")
            
            # Extract entities and relationships (simplified)
            await self._extract_and_store_entities(content, episode_id)
            
            return episode_id
            
        except Exception as e:
            logger.error(f"Failed to add episode: {e}")
            raise
    
    async def _extract_and_store_entities(self, content: str, episode_id: str) -> None:
        """Extract entities and relationships from content (simplified implementation)"""
        try:
            # This is a simplified entity extraction
            # In a real implementation, you'd use NLP libraries or LLM-based extraction
            
            # For now, we'll create a simple entity based on the content
            words = content.split()
            if len(words) > 3:
                # Create a simple entity from the first few words
                entity_name = " ".join(words[:3])
                
                # Check if entity already exists
                existing = self.supabase.table("entities").select("id").eq("name", entity_name).execute()
                
                if not existing.data:
                    # Create new entity
                    entity_embedding = self.embedder.encode(entity_name).tolist()
                    entity_data = {
                        "id": str(uuid.uuid4()),
                        "name": entity_name,
                        "entity_type": "extracted",
                        "description": f"Entity extracted from: {content[:100]}...",
                        "embedding": entity_embedding,
                        "properties": {"source_episode": episode_id}
                    }
                    
                    self.supabase.table("entities").insert(entity_data).execute()
                    logger.debug(f"Created entity: {entity_name}")
                    
        except Exception as e:
            logger.error(f"Failed to extract entities: {e}")
    
    async def search_memory(self, query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search the knowledge graph for relevant information"""
        if not self.supabase:
            raise RuntimeError("Supabase not initialized")
            
        limit = limit or self.config.max_search_results
        
        try:
            # Generate embedding for the query
            query_embedding = self.embedder.encode(query).tolist()
            
            # Search episodes using vector similarity
            # Note: This requires pgvector extension in Supabase
            episodes_result = self.supabase.table("episodes").select("*").limit(limit).execute()
            
            # For now, we'll do a simple text search if vector search isn't available
            if not episodes_result.data:
                # Fallback to text search
                episodes_result = self.supabase.table("episodes").select("*").ilike("content", f"%{query}%").limit(limit).execute()
            
            # Search relationships
            relationships_result = self.supabase.table("relationships").select("*, source_entity:entities!source_entity_id(*), target_entity:entities!target_entity_id(*)").ilike("fact", f"%{query}%").limit(limit).execute()
            
            # Format results
            formatted_results = []
            
            # Add episode results
            for episode in episodes_result.data:
                formatted_results.append({
                    "type": "episode",
                    "id": episode["id"],
                    "content": episode["content"],
                    "timestamp": episode["timestamp"],
                    "score": 1.0,  # Placeholder score
                    "source": "episode"
                })
            
            # Add relationship results
            for rel in relationships_result.data:
                formatted_results.append({
                    "type": "relationship",
                    "source": rel.get("source_entity", {}).get("name", "Unknown"),
                    "target": rel.get("target_entity", {}).get("name", "Unknown"),
                    "relationship": rel["relationship_type"],
                    "content": rel["fact"],
                    "timestamp": rel["created_at"],
                    "score": rel.get("confidence", 1.0)
                })
            
            logger.info(f"Found {len(formatted_results)} relevant memories for query: {query}")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search memory: {e}")
            return []
    
    async def get_recent_episodes(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent episodes from memory"""
        if not self.supabase:
            raise RuntimeError("Supabase not initialized")
            
        try:
            result = self.supabase.table("episodes").select("*").order("timestamp", desc=True).limit(limit).execute()
            
            formatted_results = []
            for episode in result.data:
                formatted_results.append({
                    "id": episode["id"],
                    "name": episode["name"],
                    "content": episode["content"],
                    "type": episode["episode_type"],
                    "timestamp": episode["timestamp"],
                    "source": episode["source"]
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to get recent episodes: {e}")
            return []
    
    async def close(self) -> None:
        """Close Supabase connection"""
        if self.db_pool:
            await self.db_pool.close()
        logger.info("Supabase Graphiti memory system closed")
