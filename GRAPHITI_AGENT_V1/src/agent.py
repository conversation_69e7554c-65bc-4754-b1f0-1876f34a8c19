import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

import openai
from openai import Async<PERSON>penAI

from .config import GraphitiConfig
from .memory import SupabaseGraphitiMemory

logger = logging.getLogger(__name__)

class GraphitiAgent:
    """Main Graphiti-powered AI Agent using Supabase"""
    
    def __init__(self, config: GraphitiConfig):
        self.config = config
        self.memory = SupabaseGraphitiMemory(config)
        self.openai_client = AsyncOpenAI(api_key=config.openai_api_key)
        self.conversation_history: List[Dict[str, str]] = []
        
    async def initialize(self) -> None:
        """Initialize the agent and its components"""
        await self.memory.initialize()
        logger.info(f"Agent {self.config.agent_name} initialized successfully")
    
    async def process_message(self, user_message: str, user_id: str = "default") -> str:
        """Process a user message and generate a response"""
        try:
            # Store user message in memory
            await self.memory.add_episode(
                content=f"User ({user_id}): {user_message}",
                episode_type="user_message"
            )
            
            # Search for relevant memories
            relevant_memories = await self.memory.search_memory(user_message)
            
            # Build context from memories
            memory_context = self._build_memory_context(relevant_memories)
            
            # Generate response using OpenAI
            response = await self._generate_response(user_message, memory_context)
            
            # Store agent response in memory
            await self.memory.add_episode(
                content=f"Agent: {response}",
                episode_type="agent_response"
            )
            
            # Update conversation history
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })
            self.conversation_history.append({
                "role": "assistant", 
                "content": response,
                "timestamp": datetime.now().isoformat()
            })
            
            # Keep only recent conversation history
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-20:]
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to process message: {e}")
            return f"I apologize, but I encountered an error processing your message: {str(e)}"
    
    def _build_memory_context(self, memories: List[Dict[str, Any]]) -> str:
        """Build context string from relevant memories"""
        if not memories:
            return "No relevant memories found."
        
        context_parts = ["Relevant memories:"]
        for i, memory in enumerate(memories[:5], 1):  # Limit to top 5 memories
            if memory.get("type") == "episode":
                context_parts.append(f"{i}. Episode: {memory.get('content', 'No content')}")
            elif memory.get("type") == "relationship":
                context_parts.append(f"{i}. {memory.get('source', 'Unknown')} -> {memory.get('target', 'Unknown')}: {memory.get('content', 'No content')}")
            else:
                context_parts.append(f"{i}. {memory.get('content', 'No content')}")
        
        return "\n".join(context_parts)
    
    async def _generate_response(self, user_message: str, memory_context: str) -> str:
        """Generate response using OpenAI with memory context"""
        try:
            # Build conversation messages
            messages = [
                {
                    "role": "system",
                    "content": f"""You are {self.config.agent_name}, a helpful AI assistant with access to a knowledge graph memory system powered by Supabase.

Memory Context:
{memory_context}

Instructions:
- Use the memory context to inform your responses when relevant
- Be conversational and helpful
- If the memory context contains relevant information, incorporate it naturally
- If you don't have relevant information in memory, say so honestly
- Keep responses concise but informative
- Learn from each interaction to build better context for future conversations"""
                }
            ]
            
            # Add recent conversation history
            messages.extend(self.conversation_history[-6:])  # Last 3 exchanges
            
            # Add current user message
            messages.append({"role": "user", "content": user_message})
            
            # Generate response
            response = await self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return "I apologize, but I'm having trouble generating a response right now."
    
    async def get_memory_summary(self) -> Dict[str, Any]:
        """Get a summary of the agent's memory"""
        try:
            recent_episodes = await self.memory.get_recent_episodes()
            return {
                "agent_name": self.config.agent_name,
                "recent_episode_count": len(recent_episodes),
                "conversation_history_length": len(self.conversation_history),
                "status": "active",
                "memory_backend": "supabase",
                "recent_episodes": recent_episodes[:3]  # Show last 3 episodes
            }
        except Exception as e:
            logger.error(f"Failed to get memory summary: {e}")
            return {"error": str(e)}
    
    async def close(self) -> None:
        """Close the agent and cleanup resources"""
        await self.memory.close()
        logger.info(f"Agent {self.config.agent_name} closed")
