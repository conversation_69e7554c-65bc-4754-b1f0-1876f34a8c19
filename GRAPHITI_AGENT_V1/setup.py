#!/usr/bin/env python3
"""
Setup script for Graphiti Agent with Supabase
"""
import os
import sys
import subprocess
import asyncio
from pathlib import Path

def check_python_version():
    """Check if Python version is 3.8 or higher"""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def install_dependencies():
    """Install Python dependencies"""
    print("Installing Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        sys.exit(1)

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("Creating .env file from template...")
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✓ .env file created")
        print("⚠️  Please edit .env file with your actual configuration values")
    elif env_file.exists():
        print("✓ .env file already exists")
    else:
        print("⚠️  No .env.example file found")

def validate_environment():
    """Validate environment variables"""
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = [
        "OPENAI_API_KEY",
        "SUPABASE_URL", 
        "SUPABASE_KEY",
        "SUPABASE_SERVICE_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("⚠️  Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("Please update your .env file with the missing values")
        return False
    
    print("✓ All required environment variables are set")
    return True

def print_setup_instructions():
    """Print setup instructions"""
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    print("\nNext steps:")
    print("1. Set up your Supabase database:")
    print("   - Go to your Supabase project dashboard")
    print("   - Navigate to SQL Editor")
    print("   - Run the SQL commands from 'setup_supabase.sql'")
    print("   - This will create the necessary tables and indexes")
    
    print("\n2. Update your .env file with:")
    print("   - Your OpenAI API key")
    print("   - Your Supabase project URL")
    print("   - Your Supabase anon key")
    print("   - Your Supabase service role key")
    
    print("\n3. Run the agent:")
    print("   python run_local.py")
    
    print("\n4. Test the API:")
    print("   - Health check: http://localhost:8000/health")
    print("   - API docs: http://localhost:8000/docs")
    print("   - Chat endpoint: POST http://localhost:8000/chat")
    
    print("\n5. Example chat request:")
    print("""   curl -X POST http://localhost:8000/chat \\
     -H "Content-Type: application/json" \\
     -d '{"message": "Hello, tell me about yourself", "user_id": "test-user"}'""")
    
    print("\n" + "="*60)

def main():
    """Main setup function"""
    print("Setting up Graphiti Agent with Supabase...")
    print("="*50)
    
    # Check Python version
    check_python_version()
    
    # Install dependencies
    install_dependencies()
    
    # Create .env file
    create_env_file()
    
    # Validate environment (will show warnings if incomplete)
    validate_environment()
    
    # Print setup instructions
    print_setup_instructions()

if __name__ == "__main__":
    main()
