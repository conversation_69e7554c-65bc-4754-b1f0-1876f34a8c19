Perfect! Now I have a good understanding of Graphiti. Let me create a comprehensive guide for setting up your own Graphiti agent that can run both locally and in a cloud Docker container.

## Creating Your Own Graphiti Agent for Local and Cloud Deployment

Based on the Ottomator Graphiti agent repository and the core Graphiti framework, I'll help you create your own agent that can run both locally and in production using Docker containers.

### What is Graphiti?

Graphiti is a framework for building real-time knowledge graphs specifically designed for AI agents. Unlike traditional RAG approaches, Graphiti:
- Continuously integrates user interactions and data into a queryable graph
- Supports incremental updates without complete recomputation
- Provides efficient hybrid retrieval (semantic, keyword, and graph-based)
- Maintains temporal awareness with bi-temporal data models

### Project Structure

Let's create a complete project structure for your Graphiti agent:

```
graphiti-agent/
├── src/
│   ├── __init__.py
│   ├── agent.py              # Main agent logic
│   ├── config.py             # Configuration management
│   ├── memory.py             # Graphiti memory integration
│   └── utils.py              # Helper functions
├── docker/
│   ├── Dockerfile            # Production container
│   ├── docker-compose.yml    # Local development stack
│   └── docker-compose.prod.yml # Production stack
├── scripts/
│   ├── setup.sh              # Local setup script
│   └── deploy.sh             # Deployment script
├── tests/
│   ├── __init__.py
│   ├── test_agent.py
│   └── test_memory.py
├── .env.example              # Environment variables template
├── .gitignore
├── requirements.txt          # Python dependencies
├── README.md
└── run_local.py             # Local development runner
```

### Core Files Implementation

**requirements.txt**
```txt
graphiti-core==0.3.27
fastapi==0.104.1
uvicorn==0.24.0
python-dotenv==1.0.0
pydantic==2.5.0
asyncio==3.4.3
neo4j==5.15.0
openai==1.3.0
python-multipart==0.0.6
```

**src/config.py**
```python
import os
from dataclasses import dataclass
from typing import Optional
from dotenv import load_dotenv

load_dotenv()

@dataclass
class GraphitiConfig:
    """Configuration for Graphiti agent"""
    # Neo4j Configuration
    neo4j_uri: str = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user: str = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password: str = os.getenv("NEO4J_PASSWORD", "password")
    
    # OpenAI Configuration
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_model: str = os.getenv("OPENAI_MODEL", "gpt-4-turbo-preview")
    
    # Agent Configuration
    agent_name: str = os.getenv("AGENT_NAME", "GraphitiAgent")
    max_search_results: int = int(os.getenv("MAX_SEARCH_RESULTS", "10"))
    enable_parallel_runtime: bool = os.getenv("USE_PARALLEL_RUNTIME", "false").lower() == "true"
    
    # API Configuration
    api_host: str = os.getenv("API_HOST", "0.0.0.0")
    api_port: int = int(os.getenv("API_PORT", "8000"))
    
    def validate(self) -> None:
        """Validate required configuration"""
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY is required")
        if not self.neo4j_password:
            raise ValueError("NEO4J_PASSWORD is required")
```

**src/memory.py**
```python
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodicNode
from graphiti_core.edges import Edge

from .config import GraphitiConfig

logger = logging.getLogger(__name__)

class GraphitiMemory:
    """Graphiti-based memory system for the agent"""
    
    def __init__(self, config: GraphitiConfig):
        self.config = config
        self.graphiti: Optional[Graphiti] = None
        
    async def initialize(self) -> None:
        """Initialize Graphiti connection and setup"""
        try:
            self.graphiti = Graphiti(
                uri=self.config.neo4j_uri,
                user=self.config.neo4j_user,
                password=self.config.neo4j_password
            )
            
            # Build indices and constraints
            await self.graphiti.build_indices_and_constraints()
            logger.info("Graphiti memory system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Graphiti: {e}")
            raise
    
    async def add_episode(self, content: str, episode_type: str = "conversation") -> str:
        """Add a new episode to the knowledge graph"""
        if not self.graphiti:
            raise RuntimeError("Graphiti not initialized")
            
        try:
            episode_data = {
                "content": content,
                "type": episode_type,
                "timestamp": datetime.now().isoformat(),
                "source": self.config.agent_name
            }
            
            # Add episode to Graphiti
            episode_ids = await self.graphiti.add_episode(
                name=f"{episode_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                episode_body=content,
                source_description=f"Episode from {self.config.agent_name}",
                reference_time=datetime.now()
            )
            
            logger.info(f"Added episode with ID: {episode_ids[0] if episode_ids else 'Unknown'}")
            return episode_ids[0] if episode_ids else ""
            
        except Exception as e:
            logger.error(f"Failed to add episode: {e}")
            raise
    
    async def search_memory(self, query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search the knowledge graph for relevant information"""
        if not self.graphiti:
            raise RuntimeError("Graphiti not initialized")
            
        limit = limit or self.config.max_search_results
        
        try:
            # Perform hybrid search
            results = await self.graphiti.search(
                query=query,
                limit=limit
            )
            
            # Format results
            formatted_results = []
            for edge in results:
                formatted_results.append({
                    "source": edge.source_node_uuid,
                    "target": edge.target_node_uuid,
                    "relationship": edge.name,
                    "content": getattr(edge, 'fact', ''),
                    "timestamp": getattr(edge, 'created_at', None),
                    "score": getattr(edge, 'score', 0.0)
                })
            
            logger.info(f"Found {len(formatted_results)} relevant memories for query: {query}")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search memory: {e}")
            return []
    
    async def get_recent_episodes(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent episodes from memory"""
        if not self.graphiti:
            raise RuntimeError("Graphiti not initialized")
            
        try:
            # This is a simplified implementation - you might want to implement
            # a more sophisticated recent episodes retrieval
            query = "recent conversations and interactions"
            return await self.search_memory(query, limit)
            
        except Exception as e:
            logger.error(f"Failed to get recent episodes: {e}")
            return []
    
    async def close(self) -> None:
        """Close Graphiti connection"""
        if self.graphiti:
            await self.graphiti.close()
            logger.info("Graphiti memory system closed")
```

**src/agent.py**
```python
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

import openai
from openai import AsyncOpenAI

from .config import GraphitiConfig
from .memory import GraphitiMemory

logger = logging.getLogger(__name__)

class GraphitiAgent:
    """Main Graphiti-powered AI Agent"""
    
    def __init__(self, config: GraphitiConfig):
        self.config = config
        self.memory = GraphitiMemory(config)
        self.openai_client = AsyncOpenAI(api_key=config.openai_api_key)
        self.conversation_history: List[Dict[str, str]] = []
        
    async def initialize(self) -> None:
        """Initialize the agent and its components"""
        await self.memory.initialize()
        logger.info(f"Agent {self.config.agent_name} initialized successfully")
    
    async def process_message(self, user_message: str, user_id: str = "default") -> str:
        """Process a user message and generate a response"""
        try:
            # Store user message in memory
            await self.memory.add_episode(
                content=f"User ({user_id}): {user_message}",
                episode_type="user_message"
            )
            
            # Search for relevant memories
            relevant_memories = await self.memory.search_memory(user_message)
            
            # Build context from memories
            memory_context = self._build_memory_context(relevant_memories)
            
            # Generate response using OpenAI
            response = await self._generate_response(user_message, memory_context)
            
            # Store agent response in memory
            await self.memory.add_episode(
                content=f"Agent: {response}",
                episode_type="agent_response"
            )
            
            # Update conversation history
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })
            self.conversation_history.append({
                "role": "assistant", 
                "content": response,
                "timestamp": datetime.now().isoformat()
            })
            
            # Keep only recent conversation history
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-20:]
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to process message: {e}")
            return f"I apologize, but I encountered an error processing your message: {str(e)}"
    
    def _build_memory_context(self, memories: List[Dict[str, Any]]) -> str:
        """Build context string from relevant memories"""
        if not memories:
            return "No relevant memories found."
        
        context_parts = ["Relevant memories:"]
        for i, memory in enumerate(memories[:5], 1):  # Limit to top 5 memories
            context_parts.append(
                f"{i}. {memory.get('relationship', 'Unknown')}: {memory.get('content', 'No content')}"
            )
        
        return "\n".join(context_parts)
    
    async def _generate_response(self, user_message: str, memory_context: str) -> str:
        """Generate response using OpenAI with memory context"""
        try:
            # Build conversation messages
            messages = [
                {
                    "role": "system",
                    "content": f"""You are {self.config.agent_name}, a helpful AI assistant with access to a knowledge graph memory system.

Memory Context:
{memory_context}

Instructions:
- Use the memory context to inform your responses when relevant
- Be conversational and helpful
- If the memory context contains relevant information, incorporate it naturally
- If you don't have relevant information in memory, say so honestly
- Keep responses concise but informative"""
                }
            ]
            
            # Add recent conversation history
            messages.extend(self.conversation_history[-6:])  # Last 3 exchanges
            
            # Add current user message
            messages.append({"role": "user", "content": user_message})
            
            # Generate response
            response = await self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return "I apologize, but I'm having trouble generating a response right now."
    
    async def get_memory_summary(self) -> Dict[str, Any]:
        """Get a summary of the agent's memory"""
        try:
            recent_episodes = await self.memory.get_recent_episodes()
            return {
                "agent_name": self.config.agent_name,
                "recent_episode_count": len(recent_episodes),
                "conversation_history_length": len(self.conversation_history),
                "status": "active"
            }
        except Exception as e:
            logger.error(f"Failed to get memory summary: {e}")
            return {"error": str(e)}
    
    async def close(self) -> None:
        """Close the agent and cleanup resources"""
        await self.memory.close()
        logger.info(f"Agent {self.config.agent_name} closed")
```

**src/utils.py**
```python
import logging
import sys
from typing import Dict, Any

def setup_logging(level: str = "INFO") -> None:
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('agent.log')
        ]
    )

def validate_environment() -> Dict[str, Any]:
    """Validate environment setup"""
    issues = []
    
    # Check required environment variables
    import os
    required_vars = ["OPENAI_API_KEY", "NEO4J_PASSWORD"]
    for var in required_vars:
        if not os.getenv(var):
            issues.append(f"Missing required environment variable: {var}")
    
    return {
        "valid": len(issues) == 0,
        "issues": issues
    }
```

### Docker Configuration

**docker/Dockerfile**
```dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY run_local.py .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Start the application
CMD ["python", "run_local.py"]
```

**docker/docker-compose.yml** (Local Development)
```yaml
version: '3.8'

services:
  neo4j:
    image: neo4j:5.26-community
    container_name: graphiti-neo4j
    environment:
      NEO4J_AUTH: neo4j/password
      NEO4J_PLUGINS: '["apoc"]'
      NEO4J_apoc_export_file_enabled: 'true'
      NEO4J_apoc_import_file_enabled: 'true'
    ports:
      - "7474:7474"  # Neo4j Browser
      - "7687:7687"  # Bolt protocol
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    networks:
      - graphiti-network

  graphiti-agent:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: graphiti-agent
    environment:
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USER: neo4j
      NEO4J_PASSWORD: password
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      AGENT_NAME: DevGraphitiAgent
      API_HOST: 0.0.0.0
      API_PORT: 8000
    ports:
      - "8000:8000"
    depends_on:
      - neo4j
    networks:
      - graphiti-network
    volumes:
      - ./logs:/app/logs

volumes:
  neo4j_data:
  neo4j_logs:

networks:
  graphiti-network:
    driver: bridge
```

**docker/docker-compose.prod.yml** (Production)
```yaml
version: '3.8'

services:
  neo4j:
    image: neo4j:5.26-enterprise
    container_name: graphiti-neo4j-prod
    environment:
      NEO4J_AUTH: ${NEO4J_USER}/${NEO4J_PASSWORD}
      NEO4J_ACCEPT_LICENSE_AGREEMENT: 'yes'
      NEO4J_PLUGINS: '["apoc"]'
      NEO4J_apoc_export_file_enabled: 'true'
      NEO4J_apoc_import_file_enabled: 'true'
      NEO4J_dbms_memory_heap_initial_size: 1G
      NEO4J_dbms_memory_heap_max_size: 2G
      NEO4J_dbms_memory_pagecache_size: 1G
    ports:
      - "7687:7687"
    volumes:
      - neo4j_data_prod:/data
      - neo4j_logs_prod:/logs
      - ./neo4j-backup:/backup
    networks:
      - graphiti-network
    restart: unless-stopped

  graphiti-agent:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: graphiti-agent-prod
    environment:
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USER: ${NEO4J_USER}
      NEO4J_PASSWORD: ${NEO4J_PASSWORD}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      AGENT_NAME: ProductionGraphitiAgent
      API_HOST: 0.0.0.0
      API_PORT: 8000
      USE_PARALLEL_RUNTIME: 'true'
    ports:
      - "8000:8000"
    depends_on:
      - neo4j
    networks:
      - graphiti-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

volumes:
  neo4j_data_prod:
  neo4j_logs_prod:

networks:
  graphiti-network:
    driver: bridge
```

### Application Runners

**run_local.py**
```python
#!/usr/bin/env python3
"""
Local development runner for Graphiti Agent
"""
import asyncio
import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

from src.config import GraphitiConfig
from src.agent import GraphitiAgent
from src.utils import setup_logging, validate_environment

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global agent instance
agent: GraphitiAgent = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    global agent
    
    # Startup
    logger.info("Starting Graphiti Agent...")
    
    # Validate environment
    env_check = validate_environment()
    if not env_check["valid"]:
        logger.error("Environment validation failed:")
        for issue in env_check["issues"]:
            logger.error(f"  - {issue}")
        raise RuntimeError("Environment validation failed")
    
    # Initialize agent
    config = GraphitiConfig()
    config.validate()
    
    agent = GraphitiAgent(config)
    await agent.initialize()
    
    logger.info("Graphiti Agent started successfully!")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Graphiti Agent...")
    if agent:
        await agent.close()
    logger.info("Shutdown complete.")

# FastAPI app
app = FastAPI(
    title="Graphiti Agent API",
    description="AI Agent powered by Graphiti knowledge graphs",
    version="1.0.0",
    lifespan=lifespan
)

# Request/Response models
class MessageRequest(BaseModel):
    message: str
    user_id: str = "default"

class MessageResponse(BaseModel):
    response: str
    timestamp: str

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "agent": agent.config.agent_name if agent else "not initialized"}

@app.post("/chat", response_model=MessageResponse)
async def chat(request: MessageRequest):
    """Chat with the agent"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    try:
        response = await agent.process_message(request.message, request.user_id)
        from datetime import datetime
        return MessageResponse(
            response=response,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/memory/summary")
async def get_memory_summary():
    """Get agent memory summary"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return await agent.get_memory_summary()

if __name__ == "__main__":
    uvicorn.run(
        "run_local:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
```

### Environment Configuration

**.env.example**
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_secure_password_here

# Agent Configuration
AGENT_NAME=MyGraphitiAgent
MAX_SEARCH_RESULTS=10
USE_PARALLEL_RUNTIME=false

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
```

### Setup Scripts

**scripts/setup.sh**
```bash
#!/bin/bash
set -e

echo "Setting up Graphiti Agent development environment..."

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not installed."
    exit 1
fi

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "Docker is required but not installed."
    exit 1
fi

# Create virtual environment
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Copy environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "Please edit .env file with your configuration"
fi

echo "Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your OpenAI API key and Neo4j password"
echo "2. Run locally: python run_local.py"
echo "3. Or run with Docker: docker-compose -f docker/docker-compose.yml up"
```

**scripts/deploy.sh**
```bash
#!/bin/bash
set -e

echo "Deploying Graphiti Agent to production..."

# Check if required environment variables are set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "Error: OPENAI_API_KEY environment variable is required"
    exit 1
fi

if [ -z "$NEO4J_PASSWORD" ]; then
    echo "Error: NEO4J_PASSWORD environment variable is required"
    exit 1
fi

# Build and deploy with production compose file
echo "Building and starting production containers..."
docker-compose -f docker/docker-compose.prod.yml up --build -d

echo "Deployment complete!"
echo "Agent API available at: http://localhost:8000"
echo "Health check: curl http://localhost:8000/health"
```

### Usage Instructions

#### Local Development Setup

1. **Clone and setup the project:**
```bash
git clone <your-repo-url>
cd graphiti-agent
chmod +x scripts/setup.sh
./scripts/setup.sh
```

2. **Configure environment:**
```bash
# Edit .env file with your API keys
nano .env
```

3. **Run locally with Python:**
```bash
source venv/bin/activate
python run_local.py
```

4. **Or run with Docker Compose:**
```bash
docker-compose -f docker/docker-compose.yml up
```

#### Production Deployment

1. **Set environment variables:**
```bash
export OPENAI_API_KEY="your-key"
export NEO4J_PASSWORD="secure-password"
export NEO4J_USER="neo4j"
```

2. **Deploy to production:**
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

#### Testing the Agent

```bash
# Health check
curl http://localhost:8000/health

# Chat with the agent
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, tell me about yourself", "user_id": "test-user"}'

# Get memory summary
curl http://localhost:8000/memory/summary
```

### Key Features

1. **Real-time Knowledge Graph**: Uses Graphiti to build and maintain a dynamic knowledge graph
2. **Persistent Memory**: Stores conversations and learns from interactions
3. **Hybrid Search**: Combines semantic, keyword, and graph-based search
4. **Docker Ready**: Configured for both local development and production deployment
5. **Scalable**: Neo4j backend can handle large-scale knowledge graphs
6. **API Interface**: RESTful API for easy integration

### Customization Options

- **Custom Entity Types**: Define domain-specific entities in Graphiti
- **Alternative LLMs**: Support for Anthropic, Groq, and other providers
- **Advanced Search**: Configure different search strategies and reranking
- **Memory Policies**: Implement custom memory retention and forgetting strategies

This setup gives you a production-ready Graphiti agent that can learn and remember from conversations while providing intelligent responses based on its accumulated knowledge graph. The agent will continuously build its understanding of topics, relationships, and context through its interactions.