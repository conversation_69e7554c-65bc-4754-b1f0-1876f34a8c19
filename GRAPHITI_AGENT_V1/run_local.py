#!/usr/bin/env python3
"""
Local development runner for Graphiti Agent with Supabase
"""
import asyncio
import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

from src.config import GraphitiConfig
from src.agent import GraphitiAgent
from src.utils import setup_logging, validate_environment

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global agent instance
agent: GraphitiAgent = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    global agent
    
    # Startup
    logger.info("Starting Graphiti Agent with Supabase...")
    
    # Validate environment
    env_check = validate_environment()
    if not env_check["valid"]:
        logger.error("Environment validation failed:")
        for issue in env_check["issues"]:
            logger.error(f"  - {issue}")
        raise RuntimeError("Environment validation failed")
    
    # Initialize agent
    config = GraphitiConfig()
    config.validate()
    
    agent = GraphitiAgent(config)
    await agent.initialize()
    
    logger.info("Graphiti Agent started successfully!")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Graphiti Agent...")
    if agent:
        await agent.close()
    logger.info("Shutdown complete.")

# FastAPI app
app = FastAPI(
    title="Graphiti Agent API (Supabase)",
    description="AI Agent powered by Graphiti-style knowledge graphs using Supabase",
    version="1.0.0",
    lifespan=lifespan
)

# Request/Response models
class MessageRequest(BaseModel):
    message: str
    user_id: str = "default"

class MessageResponse(BaseModel):
    response: str
    timestamp: str

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy", 
        "agent": agent.config.agent_name if agent else "not initialized",
        "backend": "supabase"
    }

@app.post("/chat", response_model=MessageResponse)
async def chat(request: MessageRequest):
    """Chat with the agent"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    try:
        response = await agent.process_message(request.message, request.user_id)
        from datetime import datetime
        return MessageResponse(
            response=response,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/memory/summary")
async def get_memory_summary():
    """Get agent memory summary"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return await agent.get_memory_summary()

@app.get("/")
async def root():
    """Root endpoint with basic info"""
    return {
        "message": "Graphiti Agent API with Supabase",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "chat": "/chat",
            "memory_summary": "/memory/summary",
            "docs": "/docs"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "run_local:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
