-- Supabase Database Schema for Graphiti Agent
-- Run this in your Supabase SQL Editor

-- Enable the pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;

-- Create episodes table
CREATE TABLE IF NOT EXISTS episodes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    content TEXT NOT NULL,
    episode_type TEXT DEFAULT 'conversation',
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    source TEXT DEFAULT 'agent',
    embedding VECTOR(384),
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create entities table
CREATE TABLE IF NOT EXISTS entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    entity_type TEXT DEFAULT 'general',
    description TEXT,
    properties JSONB DEFAULT '{}'::jsonb,
    embedding VECTOR(384),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create relationships table
CREATE TABLE IF NOT EXISTS relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    target_entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    relationship_type TEXT NOT NULL,
    fact TEXT NOT NULL,
    confidence FLOAT DEFAULT 1.0,
    episode_id UUID REFERENCES episodes(id) ON DELETE CASCADE,
    embedding VECTOR(384),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_episodes_timestamp ON episodes(timestamp);
CREATE INDEX IF NOT EXISTS idx_episodes_type ON episodes(episode_type);
CREATE INDEX IF NOT EXISTS idx_episodes_content ON episodes USING gin(to_tsvector('english', content));

CREATE INDEX IF NOT EXISTS idx_entities_name ON entities(name);
CREATE INDEX IF NOT EXISTS idx_entities_type ON entities(entity_type);

CREATE INDEX IF NOT EXISTS idx_relationships_source ON relationships(source_entity_id);
CREATE INDEX IF NOT EXISTS idx_relationships_target ON relationships(target_entity_id);
CREATE INDEX IF NOT EXISTS idx_relationships_type ON relationships(relationship_type);
CREATE INDEX IF NOT EXISTS idx_relationships_fact ON relationships USING gin(to_tsvector('english', fact));

-- Create vector similarity search indexes (if pgvector is available)
CREATE INDEX IF NOT EXISTS idx_episodes_embedding ON episodes USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_entities_embedding ON entities USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_relationships_embedding ON relationships USING ivfflat (embedding vector_cosine_ops);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_episodes_updated_at BEFORE UPDATE ON episodes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_entities_updated_at BEFORE UPDATE ON entities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_relationships_updated_at BEFORE UPDATE ON relationships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create RLS (Row Level Security) policies if needed
-- ALTER TABLE episodes ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE entities ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE relationships ENABLE ROW LEVEL SECURITY;

-- Example RLS policy (uncomment if you want to enable RLS)
-- CREATE POLICY "Users can view their own episodes" ON episodes
--     FOR SELECT USING (auth.uid()::text = (metadata->>'user_id'));

-- Insert some sample data for testing
INSERT INTO entities (name, entity_type, description) VALUES 
    ('GraphitiAgent', 'agent', 'The main AI agent powered by Supabase'),
    ('User', 'person', 'Generic user entity')
ON CONFLICT (name) DO NOTHING;

-- Create a view for easy querying of relationships with entity names
CREATE OR REPLACE VIEW relationship_facts AS
SELECT 
    r.id,
    r.relationship_type,
    r.fact,
    r.confidence,
    r.created_at,
    se.name as source_entity_name,
    se.entity_type as source_entity_type,
    te.name as target_entity_name,
    te.entity_type as target_entity_type,
    e.name as episode_name,
    e.content as episode_content
FROM relationships r
LEFT JOIN entities se ON r.source_entity_id = se.id
LEFT JOIN entities te ON r.target_entity_id = te.id
LEFT JOIN episodes e ON r.episode_id = e.id;

-- Create a function for vector similarity search
CREATE OR REPLACE FUNCTION search_similar_episodes(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.5,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    name text,
    content text,
    episode_type text,
    timestamp timestamptz,
    similarity float
)
LANGUAGE sql
AS $$
    SELECT
        episodes.id,
        episodes.name,
        episodes.content,
        episodes.episode_type,
        episodes.timestamp,
        1 - (episodes.embedding <=> query_embedding) as similarity
    FROM episodes
    WHERE episodes.embedding IS NOT NULL
        AND 1 - (episodes.embedding <=> query_embedding) > match_threshold
    ORDER BY episodes.embedding <=> query_embedding
    LIMIT match_count;
$$;
