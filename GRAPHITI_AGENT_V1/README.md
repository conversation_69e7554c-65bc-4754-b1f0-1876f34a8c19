# Graphiti Agent with Supabase

A Graphiti-style AI agent that uses Supabase (PostgreSQL) as the backend database instead of Neo4j. This agent builds and maintains a knowledge graph from conversations and provides intelligent responses based on accumulated memory.

## Features

- **Real-time Knowledge Graph**: Builds dynamic knowledge graphs from conversations
- **Persistent Memory**: Stores episodes, entities, and relationships in Supabase
- **Vector Search**: Uses embeddings for semantic similarity search
- **RESTful API**: FastAPI-based API for easy integration
- **Supabase Backend**: Uses PostgreSQL with vector extensions for scalability

## Prerequisites

- Python 3.8 or higher
- Supabase project with PostgreSQL database
- OpenAI API key

## Quick Start

### 1. Setup

```bash
# Clone or navigate to the project directory
cd GRAPHITI_AGENT_V1

# Run the setup script
python setup.py
```

### 2. Configure Supabase Database

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and run the SQL commands from `setup_supabase.sql`
4. This creates the necessary tables, indexes, and functions

### 3. Configure Environment

Edit the `.env` file with your actual values:

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview

# Supabase Configuration  
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_role_key

# Agent Configuration
AGENT_NAME=MyGraphitiAgent
MAX_SEARCH_RESULTS=10
```

### 4. Run the Agent

```bash
python run_local.py
```

The API will be available at `http://localhost:8000`

## API Endpoints

### Health Check
```bash
GET /health
```

### Chat with Agent
```bash
POST /chat
Content-Type: application/json

{
    "message": "Hello, tell me about yourself",
    "user_id": "test-user"
}
```

### Get Memory Summary
```bash
GET /memory/summary
```

### API Documentation
Visit `http://localhost:8000/docs` for interactive API documentation.

## Example Usage

```bash
# Health check
curl http://localhost:8000/health

# Chat with the agent
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, I am interested in learning about AI", "user_id": "user123"}'

# Get memory summary
curl http://localhost:8000/memory/summary
```

## Database Schema

The agent uses three main tables in Supabase:

- **episodes**: Stores conversation episodes and interactions
- **entities**: Stores extracted entities (people, concepts, etc.)
- **relationships**: Stores relationships between entities with facts

Each table includes vector embeddings for semantic search capabilities.

## How It Works

1. **Episode Storage**: Each user interaction is stored as an episode
2. **Entity Extraction**: Entities are extracted from conversations (simplified implementation)
3. **Relationship Building**: Relationships between entities are identified and stored
4. **Memory Search**: When responding, the agent searches for relevant memories using vector similarity
5. **Context Building**: Relevant memories are used to build context for the LLM response
6. **Continuous Learning**: Each interaction adds to the knowledge graph

## Customization

### Adding Custom Entity Extraction

Modify `src/memory.py` in the `_extract_and_store_entities` method to implement more sophisticated entity extraction using NLP libraries or LLM-based extraction.

### Changing the LLM Model

Update the `OPENAI_MODEL` in your `.env` file or modify `src/config.py` to use different models.

### Extending the API

Add new endpoints in `run_local.py` to expose additional functionality.

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify your Supabase URL and keys are correct
   - Ensure the database schema has been set up using `setup_supabase.sql`

2. **Vector Search Not Working**
   - Make sure the `pgvector` extension is enabled in Supabase
   - Check that embeddings are being generated and stored

3. **OpenAI API Errors**
   - Verify your OpenAI API key is valid and has sufficient credits
   - Check the model name is correct

### Logs

Check `agent.log` for detailed logging information.

## Development

### Project Structure

```
GRAPHITI_AGENT_V1/
├── src/
│   ├── __init__.py
│   ├── agent.py          # Main agent logic
│   ├── config.py         # Configuration management
│   ├── memory.py         # Supabase memory system
│   └── utils.py          # Helper functions
├── run_local.py          # FastAPI application
├── setup.py              # Setup script
├── setup_supabase.sql    # Database schema
├── requirements.txt      # Python dependencies
├── .env.example          # Environment template
└── README.md
```

### Adding Features

1. **Enhanced Entity Extraction**: Integrate with spaCy or other NLP libraries
2. **Better Relationship Detection**: Use LLM-based relationship extraction
3. **Graph Visualization**: Add endpoints to visualize the knowledge graph
4. **User Management**: Add user-specific knowledge graphs
5. **Export/Import**: Add functionality to export/import knowledge graphs

## License

This project is open source and available under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in `agent.log`
3. Open an issue with detailed information about the problem
