#!/bin/bash

# BidBeez CloudFront Deployment Script
# This script updates microservices to connect to CloudFront backend

set -e

echo "🚀 BidBeez CloudFront Backend Configuration"
echo "==========================================="

# CloudFront URL
CLOUDFRONT_URL="https://d58ser5n68qmv.cloudfront.net"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if running from project root
if [ ! -d "microservices" ] || [ ! -d "client" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

echo ""
echo "📋 Configuration Details:"
echo "- CloudFront URL: $CLOUDFRONT_URL"
echo "- Environment: Production"
echo ""

# Step 1: Update Frontend Configuration
echo "1️⃣ Updating Frontend Configuration..."
if [ -f "client/.env.production" ]; then
    print_status "Frontend production environment file already exists"
else
    print_warning "Frontend production environment file not found, creating from template"
    cp client/.env client/.env.production 2>/dev/null || true
fi

# Update API URL in frontend
if [ -f "client/.env.production" ]; then
    # Check if VITE_API_URL exists
    if grep -q "VITE_API_URL" client/.env.production; then
        sed -i.bak "s|VITE_API_URL=.*|VITE_API_URL=$CLOUDFRONT_URL/api|" client/.env.production
    else
        echo "VITE_API_URL=$CLOUDFRONT_URL/api" >> client/.env.production
    fi
    
    # Check if VITE_MICROSERVICES_URL exists
    if grep -q "VITE_MICROSERVICES_URL" client/.env.production; then
        sed -i.bak "s|VITE_MICROSERVICES_URL=.*|VITE_MICROSERVICES_URL=$CLOUDFRONT_URL|" client/.env.production
    else
        echo "VITE_MICROSERVICES_URL=$CLOUDFRONT_URL" >> client/.env.production
    fi
    
    print_status "Frontend configuration updated"
fi

# Step 2: Update API Gateway Configuration
echo ""
echo "2️⃣ Updating API Gateway Configuration..."
if [ -f "microservices/services/api-gateway/.env.production" ]; then
    print_status "API Gateway production environment file exists"
else
    print_warning "Creating API Gateway production environment from example"
    cp microservices/services/api-gateway/.env.example microservices/services/api-gateway/.env.production 2>/dev/null || true
fi

# Update CORS origin
if [ -f "microservices/services/api-gateway/.env.production" ]; then
    if grep -q "CORS_ORIGIN" microservices/services/api-gateway/.env.production; then
        sed -i.bak "s|CORS_ORIGIN=.*|CORS_ORIGIN=$CLOUDFRONT_URL,https://bidbees.com,http://localhost:3000|" microservices/services/api-gateway/.env.production
    else
        echo "CORS_ORIGIN=$CLOUDFRONT_URL,https://bidbees.com,http://localhost:3000" >> microservices/services/api-gateway/.env.production
    fi
    print_status "API Gateway CORS configuration updated"
fi

# Step 3: Create production environment files for all services
echo ""
echo "3️⃣ Creating production environment files for microservices..."
services=(
    "auth-service"
    "user-service"
    "tender-service"
    "bidding-service"
    "payment-service"
    "notification-service"
    "analytics-service"
    "ml-service"
    "document-service"
    "supplier-service"
    "transport-service"
    "courier-service"
    "bee-tasks-service"
    "queenbee-anchor-service"
    "student-verification-service"
    "map-service"
    "docling-processor"
)

for service in "${services[@]}"; do
    service_path="microservices/services/$service"
    if [ -d "$service_path" ]; then
        if [ -f "$service_path/.env.example" ]; then
            if [ ! -f "$service_path/.env.production" ]; then
                cp "$service_path/.env.example" "$service_path/.env.production"
                # Add CORS_ORIGIN to each service
                echo "CORS_ORIGIN=$CLOUDFRONT_URL" >> "$service_path/.env.production"
                print_status "Created production config for $service"
            else
                # Update existing CORS_ORIGIN
                if grep -q "CORS_ORIGIN" "$service_path/.env.production"; then
                    sed -i.bak "s|CORS_ORIGIN=.*|CORS_ORIGIN=$CLOUDFRONT_URL|" "$service_path/.env.production"
                else
                    echo "CORS_ORIGIN=$CLOUDFRONT_URL" >> "$service_path/.env.production"
                fi
            fi
        fi
    fi
done

# Step 4: Build Frontend for Production
echo ""
echo "4️⃣ Building Frontend for Production..."
cd client
if command -v bun &> /dev/null; then
    print_status "Building with Bun..."
    bun run build
elif command -v npm &> /dev/null; then
    print_status "Building with npm..."
    npm run build
else
    print_error "Neither Bun nor npm found. Please install one of them."
    exit 1
fi
cd ..
print_status "Frontend build completed"

# Step 5: Create deployment checklist
echo ""
echo "5️⃣ Creating deployment checklist..."
cat > CLOUDFRONT_DEPLOYMENT_CHECKLIST.md << EOF
# CloudFront Deployment Checklist

## Pre-Deployment
- [ ] All environment variables are set in production files
- [ ] Database connection strings are configured
- [ ] Redis connection is configured
- [ ] Kafka brokers are configured
- [ ] JWT secrets are set
- [ ] API keys (Stripe, SendGrid, etc.) are configured

## CloudFront Configuration
- [ ] CloudFront distribution is created
- [ ] Origin is set to API Gateway load balancer
- [ ] CORS headers are properly configured
- [ ] Cache behaviors are set appropriately
- [ ] SSL certificate is configured

## Microservices
- [ ] All services have production .env files
- [ ] CORS_ORIGIN includes CloudFront URL
- [ ] Health check endpoints are working
- [ ] Service discovery is configured

## Frontend
- [ ] Production build is created
- [ ] API URLs point to CloudFront
- [ ] Environment variables are set
- [ ] Static assets are uploaded to S3

## Testing
- [ ] API Gateway is accessible via CloudFront
- [ ] Frontend can communicate with backend
- [ ] Authentication flow works
- [ ] WebSocket connections work (if applicable)

## Monitoring
- [ ] CloudWatch alarms are set
- [ ] Logs are being collected
- [ ] Metrics are being tracked
- [ ] Error tracking is enabled
EOF

print_status "Deployment checklist created"

# Step 6: Summary
echo ""
echo "✅ CloudFront Backend Configuration Complete!"
echo "============================================"
echo ""
echo "📋 Next Steps:"
echo "1. Review and update production environment files with actual values"
echo "2. Deploy microservices using Docker Compose or Kubernetes"
echo "3. Upload frontend build to S3 bucket"
echo "4. Configure CloudFront distribution"
echo "5. Test the complete setup"
echo ""
echo "📄 Files Created/Updated:"
echo "- client/.env.production"
echo "- microservices/services/*/.env.production"
echo "- microservices/docker-compose.cloudfront.yml"
echo "- microservices/nginx/nginx.conf"
echo "- CLOUDFRONT_DEPLOYMENT_CHECKLIST.md"
echo ""
echo "🔗 CloudFront URL: $CLOUDFRONT_URL"
echo ""
print_warning "Remember to keep your production secrets secure!"