# 🌐 CloudFront Backend Integration Guide

## Overview
This guide explains how to connect your BidBeez microservices to the CloudFront backend at `https://d58ser5n68qmv.cloudfront.net`.

## 🏗️ Architecture

```
CloudFront (CDN)
    ↓
API Gateway (Load Balancer)
    ↓
Microservices
    ↓
Databases & Cache
```

## 🚀 Quick Start

Run the deployment script:
```bash
./deploy-cloudfront.sh
```

This script will:
1. Update frontend configuration to use CloudFront URL
2. Configure API Gateway CORS settings
3. Create production environment files for all microservices
4. Build the frontend for production
5. Generate a deployment checklist

## 📋 Manual Configuration Steps

### 1. Frontend Configuration

Update `client/.env.production`:
```env
VITE_API_URL=https://d58ser5n68qmv.cloudfront.net/api
VITE_MICROSERVICES_URL=https://d58ser5n68qmv.cloudfront.net
VITE_NEW_ENDPOINTS=auth,users,tenders,payments,courier,analytics,notifications
```

### 2. API Gateway Configuration

Update `microservices/services/api-gateway/.env.production`:
```env
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net,https://bidbees.com,http://localhost:3000
```

### 3. Microservices CORS Configuration

For each microservice, add to `.env.production`:
```env
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
```

## 🐳 Docker Deployment

### Using Docker Compose

```bash
cd microservices
docker-compose -f docker-compose.cloudfront.yml up -d
```

### Environment Variables Required

Create a `.env` file in the microservices directory:
```env
# Database
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DATABASE_URL=****************************************/bidbees

# Redis
REDIS_PASSWORD=your_redis_password

# JWT
JWT_SECRET=your_jwt_secret_min_32_chars
JWT_REFRESH_SECRET=your_jwt_refresh_secret_min_32_chars

# External Services
STRIPE_API_KEY=your_stripe_key
SENDGRID_API_KEY=your_sendgrid_key
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
S3_BUCKET=your_s3_bucket

# CloudFront
CLOUDFRONT_DISTRIBUTION_ID=your_distribution_id
```

## 🔧 CloudFront Configuration

### Origin Settings
- **Origin Domain**: Your API Gateway load balancer URL
- **Origin Protocol**: HTTPS only
- **Origin Path**: Leave empty

### Cache Behaviors
- **Path Pattern**: `/api/*`
- **Viewer Protocol Policy**: Redirect HTTP to HTTPS
- **Allowed HTTP Methods**: GET, HEAD, OPTIONS, PUT, POST, PATCH, DELETE
- **Cache Policy**: Managed-CachingDisabled (for API endpoints)

### CORS Headers
Add these response headers in CloudFront:
- `Access-Control-Allow-Origin`: `https://d58ser5n68qmv.cloudfront.net`
- `Access-Control-Allow-Methods`: `GET, POST, PUT, DELETE, OPTIONS`
- `Access-Control-Allow-Headers`: `Authorization, Content-Type, X-Requested-With`
- `Access-Control-Allow-Credentials`: `true`

## 🧪 Testing the Integration

### 1. Test API Gateway Health
```bash
curl https://d58ser5n68qmv.cloudfront.net/api/health
```

### 2. Test CORS
```javascript
fetch('https://d58ser5n68qmv.cloudfront.net/api/health', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

### 3. Test Authentication
```javascript
fetch('https://d58ser5n68qmv.cloudfront.net/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## 🔍 Troubleshooting

### CORS Issues
1. Check API Gateway CORS configuration
2. Verify CloudFront is forwarding headers
3. Check browser console for specific CORS errors

### Connection Issues
1. Verify CloudFront distribution is deployed
2. Check API Gateway is running
3. Verify security groups allow traffic

### Authentication Issues
1. Check JWT secrets match across services
2. Verify Redis is accessible
3. Check token expiration settings

## 📊 Monitoring

### CloudWatch Metrics
- API Gateway request count
- Error rates
- Latency metrics
- Cache hit ratio

### Logs
- API Gateway logs: `/logs/api-gateway.log`
- Service logs: `/logs/[service-name].log`
- CloudFront logs: Available in S3

## 🚨 Security Considerations

1. **HTTPS Only**: Ensure all traffic uses HTTPS
2. **API Keys**: Keep all API keys secure
3. **CORS**: Only allow trusted origins
4. **Rate Limiting**: Configure appropriate limits
5. **WAF**: Consider using AWS WAF for additional protection

## 📝 Deployment Checklist

- [ ] All environment variables configured
- [ ] Frontend built for production
- [ ] Microservices deployed
- [ ] CloudFront distribution configured
- [ ] SSL certificates valid
- [ ] Health checks passing
- [ ] CORS working correctly
- [ ] Authentication flow tested
- [ ] Monitoring enabled
- [ ] Backup strategy in place

## 🆘 Support

For issues or questions:
1. Check the logs first
2. Review this guide
3. Check the deployment checklist
4. Contact the DevOps team

---

**Last Updated**: January 2025
**CloudFront URL**: https://d58ser5n68qmv.cloudfront.net