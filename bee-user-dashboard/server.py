#!/usr/bin/env python3
"""
BEE User Dashboard Server
Serves the BEE compliance dashboard for companies
"""

import json
from datetime import datetime, timedelta
from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Serve static files
@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    return send_from_directory('.', filename)

# BEE Scorecard API endpoints
@app.route('/api/bee/scorecard')
def get_bee_scorecard():
    """Get complete BEE scorecard"""
    scorecard = {
        "companyInfo": {
            "name": "BuildTech Solutions (Pty) Ltd",
            "registrationNumber": "2019/123456/07",
            "beeLevel": 2,
            "overallScore": 92.5,
            "verificationDate": "2024-01-15",
            "expiryDate": "2024-12-31",
            "verificationAgency": "Empowerdex Verification Agency"
        },
        "elements": [
            {
                "id": "ownership",
                "title": "Ownership",
                "subtitle": "Black ownership and control",
                "currentScore": 18.5,
                "maxScore": 25,
                "percentage": 74,
                "weightingPoints": 25,
                "complianceTarget": 25,
                "details": [
                    {"label": "Black shareholding", "value": "68%", "target": "25%"},
                    {"label": "Black voting rights", "value": "65%", "target": "25%"},
                    {"label": "Black economic interest", "value": "70%", "target": "25%"},
                    {"label": "Black women ownership", "value": "35%", "target": "10%"}
                ]
            },
            {
                "id": "management",
                "title": "Management Control",
                "subtitle": "Black representation in management",
                "currentScore": 8.2,
                "maxScore": 15,
                "percentage": 55,
                "weightingPoints": 15,
                "complianceTarget": 40,
                "details": [
                    {"label": "Board representation", "value": "60%", "target": "50%"},
                    {"label": "Executive management", "value": "50%", "target": "40%"},
                    {"label": "Senior management", "value": "55%", "target": "40%"},
                    {"label": "Middle management", "value": "65%", "target": "40%"}
                ]
            },
            {
                "id": "skills",
                "title": "Skills Development",
                "subtitle": "Investment in employee development",
                "currentScore": 19.8,
                "maxScore": 20,
                "percentage": 99,
                "weightingPoints": 20,
                "complianceTarget": 6,
                "details": [
                    {"label": "Skills development spend", "value": "6.2% of payroll", "target": "6% of payroll"},
                    {"label": "Black employees trained", "value": "85%", "target": "85%"},
                    {"label": "Black disabled trained", "value": "12%", "target": "4%"},
                    {"label": "Training programs", "value": "24 active", "target": "Ongoing"}
                ]
            },
            {
                "id": "employment",
                "title": "Employment Equity",
                "subtitle": "Workplace transformation",
                "currentScore": 12.5,
                "maxScore": 15,
                "percentage": 83,
                "weightingPoints": 15,
                "complianceTarget": 75,
                "details": [
                    {"label": "Black employees", "value": "78%", "target": "75%"},
                    {"label": "Black women employees", "value": "42%", "target": "20%"},
                    {"label": "Black disabled employees", "value": "8%", "target": "2%"},
                    {"label": "Black youth employees", "value": "25%", "target": "15%"}
                ]
            },
            {
                "id": "procurement",
                "title": "Preferential Procurement",
                "subtitle": "Supplier development and procurement",
                "currentScore": 22.8,
                "maxScore": 25,
                "percentage": 91,
                "weightingPoints": 25,
                "complianceTarget": 80,
                "details": [
                    {"label": "BEE procurement spend", "value": "R2.8M (85%)", "target": "80%"},
                    {"label": "QSE procurement", "value": "R1.2M (36%)", "target": "15%"},
                    {"label": "EME procurement", "value": "R800K (24%)", "target": "15%"},
                    {"label": "Black women suppliers", "value": "R650K (20%)", "target": "12%"}
                ]
            },
            {
                "id": "enterprise",
                "title": "Enterprise Development",
                "subtitle": "Supporting black enterprises",
                "currentScore": 4.8,
                "maxScore": 5,
                "percentage": 96,
                "weightingPoints": 5,
                "complianceTarget": 1,
                "details": [
                    {"label": "ED contributions", "value": "R450K (1.2% of revenue)", "target": "1% of revenue"},
                    {"label": "Beneficiary enterprises", "value": "8 companies", "target": "Ongoing"},
                    {"label": "Black women enterprises", "value": "5 companies", "target": "40%"},
                    {"label": "Rural enterprises", "value": "3 companies", "target": "25%"}
                ]
            },
            {
                "id": "supplier",
                "title": "Supplier Development",
                "subtitle": "Developing supplier capabilities",
                "currentScore": 4.9,
                "maxScore": 5,
                "percentage": 98,
                "weightingPoints": 5,
                "complianceTarget": 2,
                "details": [
                    {"label": "SD contributions", "value": "R380K (1.0% of revenue)", "target": "2% of procurement"},
                    {"label": "Supplier programs", "value": "6 active", "target": "Ongoing"},
                    {"label": "Mentorship programs", "value": "4 suppliers", "target": "Ongoing"},
                    {"label": "Capacity building", "value": "R180K invested", "target": "Ongoing"}
                ]
            },
            {
                "id": "socioeconomic",
                "title": "Socio-Economic Development",
                "subtitle": "Community development initiatives",
                "currentScore": 4.7,
                "maxScore": 5,
                "percentage": 94,
                "weightingPoints": 5,
                "complianceTarget": 1,
                "details": [
                    {"label": "SED contributions", "value": "R420K (1.1% of revenue)", "target": "1% of revenue"},
                    {"label": "Beneficiary communities", "value": "12 communities", "target": "Ongoing"},
                    {"label": "Education programs", "value": "R180K", "target": "40%"},
                    {"label": "Healthcare initiatives", "value": "R120K", "target": "30%"}
                ]
            }
        ]
    }
    return jsonify(scorecard)

@app.route('/api/bee/compliance')
def get_compliance_status():
    """Get compliance status for various requirements"""
    compliance = [
        {
            "id": "bee_certificate",
            "title": "BEE Certificate",
            "status": "compliant",
            "details": "Valid Level 2 BEE Certificate issued by accredited verification agency.",
            "expiryDate": "2024-12-31",
            "issueDate": "2024-01-15",
            "agency": "Empowerdex Verification Agency",
            "certificateNumber": "EMP-***********",
            "actions": ["View Certificate", "Download PDF", "Request Renewal"]
        },
        {
            "id": "tax_compliance",
            "title": "Tax Compliance Status",
            "status": "compliant",
            "details": "Tax affairs are in order with SARS. All returns submitted and payments up to date.",
            "expiryDate": "2024-06-30",
            "issueDate": "2024-01-01",
            "agency": "South African Revenue Service",
            "certificateNumber": "TCC-2024-567890",
            "actions": ["View Status", "Download Certificate", "Check Updates"]
        },
        {
            "id": "cipc_compliance",
            "title": "CIPC Annual Returns",
            "status": "warning",
            "details": "Annual return due for submission within 30 days.",
            "expiryDate": "2024-03-15",
            "issueDate": "2023-03-15",
            "agency": "Companies and Intellectual Property Commission",
            "certificateNumber": "AR-2023-789012",
            "actions": ["Submit Return", "View Details", "Pay Fees"]
        },
        {
            "id": "uif_compliance",
            "title": "UIF Compliance",
            "status": "compliant",
            "details": "All UIF contributions are up to date and compliant.",
            "expiryDate": "2024-12-31",
            "issueDate": "2024-01-01",
            "agency": "Department of Employment and Labour",
            "certificateNumber": "UIF-2024-345678",
            "actions": ["View Statement", "Download Report", "Update Details"]
        },
        {
            "id": "coida_compliance",
            "title": "COIDA Registration",
            "status": "compliant",
            "details": "Registered with Department of Labour for compensation insurance.",
            "expiryDate": "2025-03-31",
            "issueDate": "2024-04-01",
            "agency": "Department of Employment and Labour",
            "certificateNumber": "COIDA-2024-901234",
            "actions": ["View Registration", "Update Details", "Renew Registration"]
        },
        {
            "id": "skills_levy",
            "title": "Skills Development Levy",
            "status": "compliant",
            "details": "SDL payments are current and training reports submitted.",
            "expiryDate": "2024-12-31",
            "issueDate": "2024-01-01",
            "agency": "SETA - Construction Education and Training Authority",
            "certificateNumber": "SDL-2024-456789",
            "actions": ["View Payments", "Submit Report", "Download Statement"]
        }
    ]
    return jsonify(compliance)

@app.route('/api/bee/certificates')
def get_certificates():
    """Get all certificates"""
    certificates = [
        {
            "id": "bee_cert_2024",
            "title": "BEE Certificate 2024",
            "type": "BEE Verification",
            "issueDate": "2024-01-15",
            "expiryDate": "2024-12-31",
            "level": "Level 2",
            "score": 92.5,
            "agency": "Empowerdex Verification Agency",
            "certificateNumber": "EMP-***********",
            "status": "active",
            "fileSize": "1.2 MB",
            "downloadUrl": "/downloads/bee_certificate_2024.pdf"
        },
        {
            "id": "tax_cert_2024",
            "title": "Tax Clearance Certificate",
            "type": "Tax Compliance",
            "issueDate": "2024-01-01",
            "expiryDate": "2024-06-30",
            "level": "Compliant",
            "agency": "South African Revenue Service",
            "certificateNumber": "TCC-2024-567890",
            "status": "active",
            "fileSize": "0.8 MB",
            "downloadUrl": "/downloads/tax_clearance_2024.pdf"
        },
        {
            "id": "cidb_cert",
            "title": "CIDB Registration Certificate",
            "type": "Construction Registration",
            "issueDate": "2023-04-01",
            "expiryDate": "2025-03-31",
            "level": "Grade 9",
            "agency": "Construction Industry Development Board",
            "certificateNumber": "CIDB-2023-789012",
            "status": "active",
            "fileSize": "1.5 MB",
            "downloadUrl": "/downloads/cidb_certificate.pdf"
        }
    ]
    return jsonify(certificates)

@app.route('/api/bee/reports')
def get_reports():
    """Get all BEE reports"""
    reports = [
        {
            "id": "annual_bee_2024",
            "title": "Annual BEE Report 2024",
            "type": "BEE Compliance",
            "date": "2024-02-15",
            "size": "2.4 MB",
            "pages": 45,
            "status": "final",
            "description": "Comprehensive annual BEE compliance report including all scorecard elements.",
            "downloadUrl": "/downloads/annual_bee_report_2024.pdf"
        },
        {
            "id": "quarterly_q1_2024",
            "title": "Q1 2024 Compliance Report",
            "type": "Quarterly Review",
            "date": "2024-04-01",
            "size": "1.8 MB",
            "pages": 28,
            "status": "draft",
            "description": "First quarter compliance review and progress assessment.",
            "downloadUrl": "/downloads/q1_2024_report.pdf"
        },
        {
            "id": "skills_development_2024",
            "title": "Skills Development Report 2024",
            "type": "Training & Development",
            "date": "2024-01-30",
            "size": "3.2 MB",
            "pages": 52,
            "status": "final",
            "description": "Detailed skills development initiatives and training outcomes.",
            "downloadUrl": "/downloads/skills_development_2024.pdf"
        },
        {
            "id": "procurement_analysis_2024",
            "title": "Procurement Analysis 2024",
            "type": "Supplier Analysis",
            "date": "2024-02-28",
            "size": "2.1 MB",
            "pages": 35,
            "status": "final",
            "description": "Analysis of preferential procurement and supplier development.",
            "downloadUrl": "/downloads/procurement_analysis_2024.pdf"
        }
    ]
    return jsonify(reports)

@app.route('/api/bee/statistics')
def get_bee_statistics():
    """Get BEE dashboard statistics"""
    stats = {
        "overview": {
            "beeLevel": 2,
            "overallScore": 92.5,
            "blackOwnership": 68,
            "skillsDevelopment": 78,
            "preferentialProcurement": 2800000,
            "enterpriseDevelopment": 450000
        },
        "trends": {
            "scoreImprovement": 3.2,
            "ownershipIncrease": 5,
            "skillsImprovement": 12,
            "procurementIncrease": 15
        },
        "compliance": {
            "totalRequirements": 6,
            "compliantItems": 5,
            "warningItems": 1,
            "nonCompliantItems": 0,
            "complianceRate": 83.3
        },
        "certificates": {
            "total": 3,
            "active": 3,
            "expiringSoon": 1,
            "expired": 0
        }
    }
    return jsonify(stats)

@app.route('/api/bee/recommendations')
def get_recommendations():
    """Get improvement recommendations"""
    recommendations = [
        {
            "element": "Management Control",
            "priority": "high",
            "recommendation": "Increase black representation at executive level to improve management control score.",
            "impact": "Could improve overall score by 2-3 points",
            "timeline": "6-12 months",
            "actions": [
                "Recruit black executives for key positions",
                "Implement succession planning program",
                "Provide leadership development training"
            ]
        },
        {
            "element": "CIPC Compliance",
            "priority": "urgent",
            "recommendation": "Submit annual return immediately to avoid penalties.",
            "impact": "Maintain compliance status",
            "timeline": "Immediate",
            "actions": [
                "Prepare annual return documents",
                "Submit return online",
                "Pay required fees"
            ]
        }
    ]
    return jsonify(recommendations)

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "bee-user-dashboard",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    })

if __name__ == '__main__':
    port = 3024  # Fixed port to avoid conflicts
    print(f"🏆 Starting BEE User Dashboard on port {port}")
    print(f"🌐 Frontend: http://localhost:{port}")
    print(f"📊 API: http://localhost:{port}/api/bee/scorecard")
    
    app.run(host='0.0.0.0', port=port, debug=True)
