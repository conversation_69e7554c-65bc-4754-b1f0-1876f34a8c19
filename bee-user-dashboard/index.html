<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏆 BEE Compliance Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #16a34a;
            --primary-dark: #15803d;
            --secondary: #f59e0b;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #3b82f6;
            --dark: #1f2937;
            --light: #f9fafb;
            --bee-gold: #fbbf24;
            --bee-green: #16a34a;
            --bee-red: #dc2626;
            --bee-blue: #2563eb;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--primary);
        }

        .logo i {
            font-size: 2.5rem;
            background: linear-gradient(135deg, var(--bee-green), var(--bee-gold));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .company-profile {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .company-info {
            text-align: right;
        }

        .company-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .company-details {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin-top: 2px;
        }

        .bee-level {
            background: linear-gradient(135deg, var(--bee-green), var(--bee-gold));
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: var(--shadow-lg);
        }

        .company-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--bee-green), var(--bee-gold));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.5rem;
            box-shadow: var(--shadow-lg);
        }

        /* Navigation */
        .nav-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            padding: 10px;
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
        }

        .nav-tab {
            padding: 15px 30px;
            border: none;
            background: transparent;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            color: var(--gray-600);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95rem;
        }

        .nav-tab:hover {
            background: var(--gray-100);
            color: var(--gray-800);
            transform: translateY(-2px);
        }

        .nav-tab.active {
            background: var(--primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, var(--bee-green), var(--bee-gold));
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .stat-icon.level { background: linear-gradient(135deg, var(--bee-green), #059669); }
        .stat-icon.score { background: linear-gradient(135deg, var(--bee-gold), #d97706); }
        .stat-icon.ownership { background: linear-gradient(135deg, var(--bee-blue), #1d4ed8); }
        .stat-icon.skills { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
        .stat-icon.procurement { background: linear-gradient(135deg, #ec4899, #db2777); }
        .stat-icon.enterprise { background: linear-gradient(135deg, #06b6d4, #0891b2); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--gray-800);
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .stat-progress {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .stat-progress-fill {
            height: 100%;
            background: linear-gradient(135deg, var(--bee-green), var(--bee-gold));
            transition: width 0.8s ease;
            border-radius: 4px;
        }

        .stat-change {
            font-size: 0.875rem;
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-block;
        }

        .stat-change.positive {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .stat-change.negative {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }

        .stat-change.neutral {
            background: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* BEE Scorecard */
        .scorecard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .scorecard-element {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .scorecard-element:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .element-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }

        .element-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 5px;
        }

        .element-subtitle {
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .element-score {
            font-size: 2rem;
            font-weight: 800;
            color: var(--bee-green);
            text-align: right;
        }

        .element-details {
            margin-top: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: var(--gray-700);
        }

        .detail-value {
            font-weight: 600;
            color: var(--gray-800);
        }

        /* Compliance Status */
        .compliance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }

        .compliance-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .compliance-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .compliance-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .compliance-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .compliance-status {
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-compliant {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        .status-non-compliant {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }

        .compliance-details {
            font-size: 0.875rem;
            color: var(--gray-600);
            line-height: 1.6;
        }

        .expiry-date {
            margin-top: 15px;
            padding: 10px 15px;
            background: var(--gray-50);
            border-radius: 10px;
            font-size: 0.875rem;
            color: var(--gray-700);
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background: transparent;
            color: var(--gray-600);
            border: 2px solid var(--gray-300);
        }

        .btn-outline:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .company-profile {
                flex-direction: column;
                gap: 15px;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .scorecard-grid {
                grid-template-columns: 1fr;
            }

            .compliance-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }
        }

        /* Loading Animation */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid var(--gray-200);
            border-top: 5px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, var(--bee-green), var(--bee-gold));
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 1.75rem;
            cursor: pointer;
            box-shadow: var(--shadow-xl);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-award"></i>
                    <span>BEE Compliance Hub</span>
                </div>
                <div class="company-profile">
                    <div class="company-info">
                        <div class="company-name">BuildTech Solutions (Pty) Ltd</div>
                        <div class="company-details">Registration: 2019/123456/07 • CIPC Compliant</div>
                        <div class="bee-level">BBBEE Level 2</div>
                    </div>
                    <div class="company-avatar">BT</div>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showSection('overview')">
                <i class="fas fa-chart-pie"></i>
                Overview
            </button>
            <button class="nav-tab" onclick="showSection('scorecard')">
                <i class="fas fa-clipboard-list"></i>
                BEE Scorecard
            </button>
            <button class="nav-tab" onclick="showSection('compliance')">
                <i class="fas fa-shield-alt"></i>
                Compliance Status
            </button>
            <button class="nav-tab" onclick="showSection('certificates')">
                <i class="fas fa-certificate"></i>
                Certificates
            </button>
            <button class="nav-tab" onclick="showSection('reports')">
                <i class="fas fa-file-alt"></i>
                Reports
            </button>
        </nav>

        <!-- Overview Section -->
        <section id="overview" class="content-section active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-value">Level 2</div>
                            <div class="stat-label">BEE Contributor Level</div>
                            <div class="stat-progress">
                                <div class="stat-progress-fill" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="stat-icon level">
                            <i class="fas fa-trophy"></i>
                        </div>
                    </div>
                    <div class="stat-change positive">↗ Maintained from last year</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-value">92.5</div>
                            <div class="stat-label">Overall BEE Score</div>
                            <div class="stat-progress">
                                <div class="stat-progress-fill" style="width: 92.5%"></div>
                            </div>
                        </div>
                        <div class="stat-icon score">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <div class="stat-change positive">↗ +3.2 points this year</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-value">68%</div>
                            <div class="stat-label">Black Ownership</div>
                            <div class="stat-progress">
                                <div class="stat-progress-fill" style="width: 68%"></div>
                            </div>
                        </div>
                        <div class="stat-icon ownership">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-change positive">↗ +5% increase</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-value">78%</div>
                            <div class="stat-label">Skills Development</div>
                            <div class="stat-progress">
                                <div class="stat-progress-fill" style="width: 78%"></div>
                            </div>
                        </div>
                        <div class="stat-icon skills">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                    </div>
                    <div class="stat-change positive">↗ +12% improvement</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-value">R2.8M</div>
                            <div class="stat-label">Preferential Procurement</div>
                            <div class="stat-progress">
                                <div class="stat-progress-fill" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="stat-icon procurement">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                    <div class="stat-change positive">↗ +15% from target</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-value">R450K</div>
                            <div class="stat-label">Enterprise Development</div>
                            <div class="stat-progress">
                                <div class="stat-progress-fill" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="stat-icon enterprise">
                            <i class="fas fa-rocket"></i>
                        </div>
                    </div>
                    <div class="stat-change positive">↗ Exceeded target</div>
                </div>
            </div>
        </section>

        <!-- BEE Scorecard Section -->
        <section id="scorecard" class="content-section">
            <div class="scorecard-grid" id="scorecardGrid">
                <!-- Scorecard elements will be loaded here -->
            </div>
        </section>

        <!-- Compliance Status Section -->
        <section id="compliance" class="content-section">
            <div class="compliance-grid" id="complianceGrid">
                <!-- Compliance items will be loaded here -->
            </div>
        </section>

        <!-- Certificates Section -->
        <section id="certificates" class="content-section">
            <div class="compliance-grid" id="certificatesGrid">
                <!-- Certificates will be loaded here -->
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="content-section">
            <div class="compliance-grid" id="reportsGrid">
                <!-- Reports will be loaded here -->
            </div>
        </section>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" onclick="generateReport()">
        <i class="fas fa-download"></i>
    </button>

    <script src="app.js"></script>
</body>
</html>
