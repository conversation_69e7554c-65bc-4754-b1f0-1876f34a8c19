// BEE User Dashboard - JavaScript functionality

// Mock data for BEE scorecard elements
const mockScorecardElements = [
    {
        id: 'ownership',
        title: 'Ownership',
        subtitle: 'Black ownership and control',
        currentScore: 18.5,
        maxScore: 25,
        percentage: 74,
        details: [
            { label: 'Black shareholding', value: '68%' },
            { label: 'Black voting rights', value: '65%' },
            { label: 'Black economic interest', value: '70%' },
            { label: 'Black women ownership', value: '35%' }
        ]
    },
    {
        id: 'management',
        title: 'Management Control',
        subtitle: 'Black representation in management',
        currentScore: 8.2,
        maxScore: 15,
        percentage: 55,
        details: [
            { label: 'Board representation', value: '60%' },
            { label: 'Executive management', value: '50%' },
            { label: 'Senior management', value: '55%' },
            { label: 'Middle management', value: '65%' }
        ]
    },
    {
        id: 'skills',
        title: 'Skills Development',
        subtitle: 'Investment in employee development',
        currentScore: 19.8,
        maxScore: 20,
        percentage: 99,
        details: [
            { label: 'Skills development spend', value: '6.2% of payroll' },
            { label: 'Black employees trained', value: '85%' },
            { label: 'Black disabled trained', value: '12%' },
            { label: 'Training programs', value: '24 active' }
        ]
    },
    {
        id: 'employment',
        title: 'Employment Equity',
        subtitle: 'Workplace transformation',
        currentScore: 12.5,
        maxScore: 15,
        percentage: 83,
        details: [
            { label: 'Black employees', value: '78%' },
            { label: 'Black women employees', value: '42%' },
            { label: 'Black disabled employees', value: '8%' },
            { label: 'Black youth employees', value: '25%' }
        ]
    },
    {
        id: 'procurement',
        title: 'Preferential Procurement',
        subtitle: 'Supplier development and procurement',
        currentScore: 22.8,
        maxScore: 25,
        percentage: 91,
        details: [
            { label: 'BEE procurement spend', value: 'R2.8M (85%)' },
            { label: 'QSE procurement', value: 'R1.2M (36%)' },
            { label: 'EME procurement', value: 'R800K (24%)' },
            { label: 'Black women suppliers', value: 'R650K (20%)' }
        ]
    },
    {
        id: 'enterprise',
        title: 'Enterprise Development',
        subtitle: 'Supporting black enterprises',
        currentScore: 4.8,
        maxScore: 5,
        percentage: 96,
        details: [
            { label: 'ED contributions', value: 'R450K (1.2% of revenue)' },
            { label: 'Beneficiary enterprises', value: '8 companies' },
            { label: 'Black women enterprises', value: '5 companies' },
            { label: 'Rural enterprises', value: '3 companies' }
        ]
    }
];

const mockComplianceItems = [
    {
        id: 'bee_certificate',
        title: 'BEE Certificate',
        status: 'compliant',
        details: 'Valid Level 2 BEE Certificate issued by accredited verification agency.',
        expiryDate: '2024-12-31',
        actions: ['View Certificate', 'Download PDF']
    },
    {
        id: 'tax_compliance',
        title: 'Tax Compliance Status',
        status: 'compliant',
        details: 'Tax affairs are in order with SARS. All returns submitted and payments up to date.',
        expiryDate: '2024-06-30',
        actions: ['View Status', 'Download Certificate']
    },
    {
        id: 'cipc_compliance',
        title: 'CIPC Annual Returns',
        status: 'warning',
        details: 'Annual return due for submission within 30 days.',
        expiryDate: '2024-03-15',
        actions: ['Submit Return', 'View Details']
    },
    {
        id: 'uif_compliance',
        title: 'UIF Compliance',
        status: 'compliant',
        details: 'All UIF contributions are up to date and compliant.',
        expiryDate: '2024-12-31',
        actions: ['View Statement', 'Download Report']
    }
];

const mockCertificates = [
    {
        id: 'bee_cert_2024',
        title: 'BEE Certificate 2024',
        type: 'BEE Verification',
        issueDate: '2024-01-15',
        expiryDate: '2024-12-31',
        level: 'Level 2',
        agency: 'Empowerdex Verification Agency',
        status: 'active'
    },
    {
        id: 'tax_cert_2024',
        title: 'Tax Clearance Certificate',
        type: 'Tax Compliance',
        issueDate: '2024-01-01',
        expiryDate: '2024-06-30',
        level: 'Compliant',
        agency: 'South African Revenue Service',
        status: 'active'
    }
];

const mockReports = [
    {
        id: 'annual_bee_2024',
        title: 'Annual BEE Report 2024',
        type: 'BEE Compliance',
        date: '2024-02-15',
        size: '2.4 MB',
        pages: 45,
        status: 'final'
    },
    {
        id: 'quarterly_q1_2024',
        title: 'Q1 2024 Compliance Report',
        type: 'Quarterly Review',
        date: '2024-04-01',
        size: '1.8 MB',
        pages: 28,
        status: 'draft'
    }
];

// Application state
let currentSection = 'overview';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🏆 BEE Compliance Dashboard initialized');
    loadScorecardElements();
    loadComplianceItems();
    loadCertificates();
    loadReports();
    startRealTimeUpdates();
});

// Navigation functionality
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from all nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionName).classList.add('active');
    
    // Add active class to clicked tab
    event.target.classList.add('active');
    
    currentSection = sectionName;
    
    // Load section-specific data
    switch(sectionName) {
        case 'scorecard':
            loadScorecardElements();
            break;
        case 'compliance':
            loadComplianceItems();
            break;
        case 'certificates':
            loadCertificates();
            break;
        case 'reports':
            loadReports();
            break;
    }
}

// Load BEE scorecard elements
function loadScorecardElements() {
    const scorecardGrid = document.getElementById('scorecardGrid');
    if (!scorecardGrid) return;
    
    const elementsHTML = mockScorecardElements.map(element => createScorecardElement(element)).join('');
    scorecardGrid.innerHTML = elementsHTML;
}

function createScorecardElement(element) {
    const percentage = Math.round((element.currentScore / element.maxScore) * 100);
    
    return `
        <div class="scorecard-element">
            <div class="element-header">
                <div>
                    <div class="element-title">${element.title}</div>
                    <div class="element-subtitle">${element.subtitle}</div>
                </div>
                <div class="element-score">${element.currentScore}/${element.maxScore}</div>
            </div>
            
            <div class="stat-progress">
                <div class="stat-progress-fill" style="width: ${percentage}%"></div>
            </div>
            
            <div class="element-details">
                ${element.details.map(detail => `
                    <div class="detail-row">
                        <span class="detail-label">${detail.label}</span>
                        <span class="detail-value">${detail.value}</span>
                    </div>
                `).join('')}
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="viewElementDetails('${element.id}')">
                    <i class="fas fa-eye"></i>
                    View Details
                </button>
                <button class="btn btn-outline" onclick="improveElement('${element.id}')">
                    <i class="fas fa-chart-line"></i>
                    Improve Score
                </button>
            </div>
        </div>
    `;
}

// Load compliance items
function loadComplianceItems() {
    const complianceGrid = document.getElementById('complianceGrid');
    if (!complianceGrid) return;
    
    const itemsHTML = mockComplianceItems.map(item => createComplianceCard(item)).join('');
    complianceGrid.innerHTML = itemsHTML;
}

function createComplianceCard(item) {
    const statusClass = `status-${item.status.replace('_', '-')}`;
    const statusIcon = item.status === 'compliant' ? '✅' : item.status === 'warning' ? '⚠️' : '❌';
    
    return `
        <div class="compliance-card">
            <div class="compliance-header">
                <div class="compliance-title">${item.title}</div>
                <div class="compliance-status ${statusClass}">
                    ${statusIcon} ${item.status}
                </div>
            </div>
            
            <div class="compliance-details">${item.details}</div>
            
            <div class="expiry-date">
                <i class="fas fa-calendar"></i>
                Expires: ${formatDate(item.expiryDate)}
            </div>
            
            <div class="action-buttons">
                ${item.actions.map(action => `
                    <button class="btn btn-outline" onclick="performAction('${item.id}', '${action}')">
                        <i class="fas fa-${action.includes('Download') ? 'download' : action.includes('View') ? 'eye' : 'edit'}"></i>
                        ${action}
                    </button>
                `).join('')}
            </div>
        </div>
    `;
}

// Load certificates
function loadCertificates() {
    const certificatesGrid = document.getElementById('certificatesGrid');
    if (!certificatesGrid) return;
    
    const certificatesHTML = mockCertificates.map(cert => createCertificateCard(cert)).join('');
    certificatesGrid.innerHTML = certificatesHTML;
}

function createCertificateCard(cert) {
    return `
        <div class="compliance-card">
            <div class="compliance-header">
                <div class="compliance-title">${cert.title}</div>
                <div class="compliance-status status-compliant">
                    ✅ ${cert.status}
                </div>
            </div>
            
            <div class="compliance-details">
                <strong>Type:</strong> ${cert.type}<br>
                <strong>Level:</strong> ${cert.level}<br>
                <strong>Issued by:</strong> ${cert.agency}
            </div>
            
            <div class="expiry-date">
                <i class="fas fa-calendar"></i>
                Valid until: ${formatDate(cert.expiryDate)}
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="viewCertificate('${cert.id}')">
                    <i class="fas fa-eye"></i>
                    View Certificate
                </button>
                <button class="btn btn-outline" onclick="downloadCertificate('${cert.id}')">
                    <i class="fas fa-download"></i>
                    Download PDF
                </button>
            </div>
        </div>
    `;
}

// Load reports
function loadReports() {
    const reportsGrid = document.getElementById('reportsGrid');
    if (!reportsGrid) return;
    
    const reportsHTML = mockReports.map(report => createReportCard(report)).join('');
    reportsGrid.innerHTML = reportsHTML;
}

function createReportCard(report) {
    const statusClass = report.status === 'final' ? 'status-compliant' : 'status-warning';
    const statusIcon = report.status === 'final' ? '✅' : '📝';
    
    return `
        <div class="compliance-card">
            <div class="compliance-header">
                <div class="compliance-title">${report.title}</div>
                <div class="compliance-status ${statusClass}">
                    ${statusIcon} ${report.status}
                </div>
            </div>
            
            <div class="compliance-details">
                <strong>Type:</strong> ${report.type}<br>
                <strong>Size:</strong> ${report.size} (${report.pages} pages)<br>
                <strong>Generated:</strong> ${formatDate(report.date)}
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="viewReport('${report.id}')">
                    <i class="fas fa-eye"></i>
                    View Report
                </button>
                <button class="btn btn-outline" onclick="downloadReport('${report.id}')">
                    <i class="fas fa-download"></i>
                    Download
                </button>
            </div>
        </div>
    `;
}

// Action handlers
function viewElementDetails(elementId) {
    showNotification(`Viewing details for ${elementId}`, 'info');
}

function improveElement(elementId) {
    showNotification(`Opening improvement recommendations for ${elementId}`, 'info');
}

function performAction(itemId, action) {
    showNotification(`${action} for ${itemId}`, 'success');
}

function viewCertificate(certId) {
    showNotification(`Opening certificate ${certId}`, 'info');
}

function downloadCertificate(certId) {
    showNotification(`Downloading certificate ${certId}`, 'success');
}

function viewReport(reportId) {
    showNotification(`Opening report ${reportId}`, 'info');
}

function downloadReport(reportId) {
    showNotification(`Downloading report ${reportId}`, 'success');
}

function generateReport() {
    showNotification('Generating comprehensive BEE report...', 'info');
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'var(--success)' : type === 'error' ? 'var(--danger)' : type === 'warning' ? 'var(--warning)' : 'var(--info)'};
        color: white;
        padding: 15px 20px;
        border-radius: 15px;
        box-shadow: var(--shadow-xl);
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 350px;
        font-weight: 500;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Real-time updates
function startRealTimeUpdates() {
    // Simulate real-time updates every 30 seconds
    setInterval(() => {
        if (currentSection === 'overview') {
            // Update progress bars with slight variations
            document.querySelectorAll('.stat-progress-fill').forEach(bar => {
                const currentWidth = parseFloat(bar.style.width);
                const variation = (Math.random() - 0.5) * 2; // ±1%
                const newWidth = Math.max(0, Math.min(100, currentWidth + variation));
                bar.style.width = `${newWidth}%`;
            });
        }
    }, 30000);
}
