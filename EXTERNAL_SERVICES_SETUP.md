# 🔑 External Services Configuration Guide

This guide will help you obtain and configure the required API keys and credentials for BidBeez.

## 📋 Required Services

### 1. PayFast (Payment Processing)

**What you need:**
- `PAYFAST_MERCHANT_ID` - Your PayFast merchant ID
- `PAYFAST_MERCHANT_KEY` - Your PayFast merchant key

**How to get them:**
1. Go to [PayFast Dashboard](https://www.payfast.co.za)
2. Sign in or create an account
3. Navigate to **My Account → Merchant Settings**
4. Copy your **Merchant ID** and **Merchant Key**

**Update in .env:**
```bash
PAYFAST_MERCHANT_ID=your_merchant_id_here
PAYFAST_MERCHANT_KEY=your_merchant_key_here
```

### 2. SendGrid (Email Service)

**What you need:**
- `SENDGRID_API_KEY` - API key for sending emails

**How to get it:**
1. Go to [SendGrid](https://sendgrid.com)
2. Sign in or create an account
3. Navigate to **Settings → API Keys**
4. Click **Create API Key**
5. Name it "BidBeez Production"
6. Select **Full Access**
7. Copy the API key (starts with `SG.`)

**Update in .env:**
```bash
SENDGRID_API_KEY=SG.your_actual_api_key_here
```

### 3. AWS (Cloud Services)

**What you need:**
- `AWS_ACCESS_KEY_ID` - IAM user access key
- `AWS_SECRET_ACCESS_KEY` - IAM user secret key
- `S3_BUCKET` - S3 bucket name for documents

**How to get them:**
1. Go to [AWS Console](https://console.aws.amazon.com)
2. Navigate to **IAM → Users**
3. Create new user "bidbees-production"
4. Attach policies:
   - `AmazonS3FullAccess` (or create custom policy)
   - `CloudFrontFullAccess` (if managing CloudFront)
5. Create access key:
   - Click on user → **Security credentials**
   - Click **Create access key**
   - Select **Application running outside AWS**
   - Copy both keys

**Create S3 Bucket:**
1. Go to **S3 → Create bucket**
2. Name: `bidbees-documents-production`
3. Region: Same as your application
4. Enable versioning
5. Configure CORS:
```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
        "AllowedOrigins": ["https://d58ser5n68qmv.cloudfront.net"],
        "ExposeHeaders": ["ETag"]
    }
]
```

**Update in .env:**
```bash
AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE
AWS_SECRET_ACCESS_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
S3_BUCKET=bidbees-documents-production
```

### 4. CloudFront

**What you need:**
- `CLOUDFRONT_DISTRIBUTION_ID` - Your distribution ID

**How to get it:**
1. Go to **CloudFront → Distributions**
2. Find your distribution (d58ser5n68qmv.cloudfront.net)
3. Copy the **Distribution ID** (e.g., E1QXVP5K2EXAMPLE)

**Update in .env:**
```bash
CLOUDFRONT_DISTRIBUTION_ID=E1QXVP5K2EXAMPLE
```

## 📋 Optional Services

### 5. Sentry (Error Tracking)

**How to get DSN:**
1. Go to [Sentry](https://sentry.io)
2. Create new project → Node.js
3. Copy the DSN from project settings

**Update in .env:**
```bash
SENTRY_DSN=https://<EMAIL>/1234567
```

### 6. Mapbox (Maps)

**How to get token:**
1. Go to [Mapbox](https://www.mapbox.com)
2. Sign in or create account
3. Go to **Account → Tokens**
4. Copy default public token or create new one

**Update in .env:**
```bash
MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoiYmlkYmVleiIsImV4YW1wbGUifQ.example
```

### 7. OpenAI (AI Features)

**How to get API key:**
1. Go to [OpenAI Platform](https://platform.openai.com)
2. Navigate to **API keys**
3. Create new secret key
4. Copy the key (starts with `sk-`)

**Update in .env:**
```bash
OPENAI_API_KEY=sk-your_actual_openai_key_here
```

## 🔧 Quick Configuration Script

After obtaining all credentials, you can use this script to update your .env file:

```bash
#!/bin/bash
# update-credentials.sh

ENV_FILE="microservices/.env"

# Function to update a variable in .env
update_env_var() {
    local key=$1
    local value=$2
    if grep -q "^$key=" "$ENV_FILE"; then
        sed -i.bak "s|^$key=.*|$key=$value|" "$ENV_FILE"
    else
        echo "$key=$value" >> "$ENV_FILE"
    fi
}

# Update your credentials here
update_env_var "PAYFAST_MERCHANT_ID" "your_merchant_id"
update_env_var "PAYFAST_MERCHANT_KEY" "your_merchant_key"
update_env_var "SENDGRID_API_KEY" "SG.your_actual_key"
update_env_var "AWS_ACCESS_KEY_ID" "your_actual_key"
update_env_var "AWS_SECRET_ACCESS_KEY" "your_actual_secret"
update_env_var "S3_BUCKET" "your_bucket_name"
update_env_var "CLOUDFRONT_DISTRIBUTION_ID" "your_distribution_id"

echo "✅ Credentials updated successfully!"
```

## 🔒 Security Best Practices

1. **Never commit credentials to Git**
   - Add `.env` to `.gitignore`
   - Use `.env.example` for templates

2. **Use different credentials for environments**
   - Separate keys for development/staging/production
   - Limit permissions per environment

3. **Rotate credentials regularly**
   - Set calendar reminders
   - Update all services when rotating

4. **Use AWS Secrets Manager for production**
   ```bash
   aws secretsmanager create-secret \
     --name bidbees/production/env \
     --secret-string file://microservices/.env
   ```

5. **Monitor API usage**
   - Set up alerts for unusual activity
   - Review logs regularly

## ✅ Verification Steps

After configuring all credentials:

1. **Test PayFast:**
   - Use PayFast sandbox environment for testing
   - Verify merchant ID and key

2. **Test SendGrid:**
   ```bash
   curl -X POST https://api.sendgrid.com/v3/mail/send \
     -H "Authorization: Bearer SG.your_key" \
     -H "Content-Type: application/json" \
     -d '{"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>"},"subject":"Test","content":[{"type":"text/plain","value":"Test"}]}'
   ```

3. **Test AWS S3:**
   ```bash
   aws s3 ls s3://your-bucket-name --region us-east-1
   ```

## 📞 Support

If you encounter issues:
1. Check service documentation
2. Verify API key permissions
3. Check service status pages
4. Review error logs

---

**Remember:** Keep all production credentials secure and never share them publicly!