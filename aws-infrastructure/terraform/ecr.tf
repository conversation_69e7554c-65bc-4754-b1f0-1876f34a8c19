# ECR Repositories for BidBees Microservices

# ECR Repositories for each microservice
resource "aws_ecr_repository" "api_gateway" {
  name                 = "${var.project_name}/api-gateway"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-api-gateway-ecr"
  }
}

resource "aws_ecr_repository" "auth_service" {
  name                 = "${var.project_name}/auth-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-auth-service-ecr"
  }
}

resource "aws_ecr_repository" "user_service" {
  name                 = "${var.project_name}/user-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-user-service-ecr"
  }
}

resource "aws_ecr_repository" "tender_service" {
  name                 = "${var.project_name}/tender-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-tender-service-ecr"
  }
}

resource "aws_ecr_repository" "bidding_service" {
  name                 = "${var.project_name}/bidding-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-bidding-service-ecr"
  }
}

resource "aws_ecr_repository" "payment_service" {
  name                 = "${var.project_name}/payment-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-payment-service-ecr"
  }
}

resource "aws_ecr_repository" "courier_service" {
  name                 = "${var.project_name}/courier-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-courier-service-ecr"
  }
}

resource "aws_ecr_repository" "supplier_service" {
  name                 = "${var.project_name}/supplier-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-supplier-service-ecr"
  }
}

resource "aws_ecr_repository" "transport_service" {
  name                 = "${var.project_name}/transport-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-transport-service-ecr"
  }
}

resource "aws_ecr_repository" "ml_service" {
  name                 = "${var.project_name}/ml-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-ml-service-ecr"
  }
}

resource "aws_ecr_repository" "docling_processor" {
  name                 = "${var.project_name}/docling-processor"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-docling-processor-ecr"
  }
}

resource "aws_ecr_repository" "queenbee_service" {
  name                 = "${var.project_name}/queenbee-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-queenbee-service-ecr"
  }
}

resource "aws_ecr_repository" "bee_tasks_service" {
  name                 = "${var.project_name}/bee-tasks-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-bee-tasks-service-ecr"
  }
}

resource "aws_ecr_repository" "map_service" {
  name                 = "${var.project_name}/map-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-map-service-ecr"
  }
}

resource "aws_ecr_repository" "notification_service" {
  name                 = "${var.project_name}/notification-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-notification-service-ecr"
  }
}

resource "aws_ecr_repository" "document_service" {
  name                 = "${var.project_name}/document-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-document-service-ecr"
  }
}

resource "aws_ecr_repository" "analytics_service" {
  name                 = "${var.project_name}/analytics-service"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = {
    Name = "${var.project_name}-analytics-service-ecr"
  }
}

# ECR Lifecycle Policies
resource "aws_ecr_lifecycle_policy" "main" {
  for_each = {
    api_gateway         = aws_ecr_repository.api_gateway.name
    auth_service        = aws_ecr_repository.auth_service.name
    user_service        = aws_ecr_repository.user_service.name
    tender_service      = aws_ecr_repository.tender_service.name
    bidding_service     = aws_ecr_repository.bidding_service.name
    payment_service     = aws_ecr_repository.payment_service.name
    courier_service     = aws_ecr_repository.courier_service.name
    supplier_service    = aws_ecr_repository.supplier_service.name
    transport_service   = aws_ecr_repository.transport_service.name
    ml_service          = aws_ecr_repository.ml_service.name
    docling_processor   = aws_ecr_repository.docling_processor.name
    queenbee_service    = aws_ecr_repository.queenbee_service.name
    bee_tasks_service   = aws_ecr_repository.bee_tasks_service.name
    map_service         = aws_ecr_repository.map_service.name
    notification_service = aws_ecr_repository.notification_service.name
    document_service    = aws_ecr_repository.document_service.name
    analytics_service   = aws_ecr_repository.analytics_service.name
  }
  
  repository = each.value
  
  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 10 images"
        selection = {
          tagStatus     = "tagged"
          tagPrefixList = ["v"]
          countType     = "imageCountMoreThan"
          countNumber   = 10
        }
        action = {
          type = "expire"
        }
      },
      {
        rulePriority = 2
        description  = "Delete untagged images older than 1 day"
        selection = {
          tagStatus   = "untagged"
          countType   = "sinceImagePushed"
          countUnit   = "days"
          countNumber = 1
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# Outputs
output "ecr_repositories" {
  value = {
    api_gateway         = aws_ecr_repository.api_gateway.repository_url
    auth_service        = aws_ecr_repository.auth_service.repository_url
    user_service        = aws_ecr_repository.user_service.repository_url
    tender_service      = aws_ecr_repository.tender_service.repository_url
    bidding_service     = aws_ecr_repository.bidding_service.repository_url
    payment_service     = aws_ecr_repository.payment_service.repository_url
    courier_service     = aws_ecr_repository.courier_service.repository_url
    supplier_service    = aws_ecr_repository.supplier_service.repository_url
    transport_service   = aws_ecr_repository.transport_service.repository_url
    ml_service          = aws_ecr_repository.ml_service.repository_url
    docling_processor   = aws_ecr_repository.docling_processor.repository_url
    queenbee_service    = aws_ecr_repository.queenbee_service.repository_url
    bee_tasks_service   = aws_ecr_repository.bee_tasks_service.repository_url
    map_service         = aws_ecr_repository.map_service.repository_url
    notification_service = aws_ecr_repository.notification_service.repository_url
    document_service    = aws_ecr_repository.document_service.repository_url
    analytics_service   = aws_ecr_repository.analytics_service.repository_url
  }
}