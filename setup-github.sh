#!/bin/bash

# BidBees GitHub Setup Script
# This script helps prepare your project for GitHub deployment

echo "🚀 BidBees GitHub Setup Script"
echo "=============================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "\n${YELLOW}Checking prerequisites...${NC}"

if ! command_exists git; then
    echo -e "${RED}❌ Git is not installed${NC}"
    exit 1
fi

if ! command_exists bun && ! command_exists npm; then
    echo -e "${RED}❌ Neither Bun nor npm is installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Check git status
echo -e "\n${YELLOW}Checking git status...${NC}"
if [ -d .git ]; then
    echo -e "${GREEN}✅ Git repository initialized${NC}"
    
    # Show current branch
    CURRENT_BRANCH=$(git branch --show-current)
    echo -e "Current branch: ${GREEN}$CURRENT_BRANCH${NC}"
    
    # Show remote
    REMOTE_URL=$(git remote get-url origin 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo -e "Remote URL: ${GREEN}$REMOTE_URL${NC}"
    else
        echo -e "${RED}❌ No remote configured${NC}"
    fi
    
    # Check for uncommitted changes
    if ! git diff-index --quiet HEAD --; then
        echo -e "${YELLOW}⚠️  You have uncommitted changes${NC}"
    else
        echo -e "${GREEN}✅ Working directory clean${NC}"
    fi
else
    echo -e "${RED}❌ Not a git repository${NC}"
    echo "Run: git init"
    exit 1
fi

# Create necessary directories
echo -e "\n${YELLOW}Ensuring directory structure...${NC}"
mkdir -p .github/workflows
mkdir -p docs
mkdir -p scripts

echo -e "${GREEN}✅ Directory structure ready${NC}"

# Check for required files
echo -e "\n${YELLOW}Checking required files...${NC}"
REQUIRED_FILES=(
    ".github/workflows/enhanced-ci-cd.yml"
    "README.md"
    "package.json"
    ".gitignore"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file exists${NC}"
    else
        echo -e "${YELLOW}⚠️  $file missing${NC}"
    fi
done

# Create .gitignore if it doesn't exist
if [ ! -f .gitignore ]; then
    echo -e "\n${YELLOW}Creating .gitignore...${NC}"
    cat > .gitignore << 'EOF'
# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output/

# Production
build/
dist/
out/

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
.cache/

# Bun
bun.lockb
.bun/

# Docker
*.pid

# AWS
.aws/
*.pem

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Grunt intermediate storage
.grunt/

# Bower dependency directory
bower_components/

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache/
.parcel-cache/

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt/
dist/

# Gatsby files
.cache/
public/

# vuepress build output
.vuepress/dist/

# vuepress v2.x temp and cache directory
.temp/

# Docusaurus cache and generated files
.docusaurus/

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test/

# yarn v2
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Custom
*.backup
backup/
temp/
tmp/
EOF
    echo -e "${GREEN}✅ .gitignore created${NC}"
fi

# GitHub Actions secrets template
echo -e "\n${YELLOW}Creating GitHub secrets template...${NC}"
cat > .github/SECRETS_TEMPLATE.md << 'EOF'
# GitHub Secrets Configuration

Copy and set these secrets in your GitHub repository settings:
Settings > Secrets and variables > Actions > New repository secret

## Required Secrets

### AWS Credentials
- `AWS_ACCESS_KEY_ID`: Your AWS access key
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
- `AWS_ACCOUNT_ID`: Your AWS account ID (12 digits)
- `AWS_REGION`: AWS region (e.g., us-east-1)

### Database
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `MONGODB_URI`: MongoDB connection string (if using)

### Authentication
- `JWT_SECRET`: Random string for JWT signing
- `SESSION_SECRET`: Random string for session encryption

### External Services
- `SENTRY_DSN`: Sentry error tracking DSN
- `STRIPE_API_KEY`: Stripe payment API key
- `SENDGRID_API_KEY`: SendGrid email API key

### Docker Registry (if using Docker Hub)
- `DOCKER_USERNAME`: Docker Hub username
- `DOCKER_PASSWORD`: Docker Hub password

## Environment Variables

Create these environments in Settings > Environments:

### staging
- Protection rules: None
- Secrets: Same as above but with staging values

### production
- Protection rules: Required reviewers
- Secrets: Same as above but with production values
EOF

echo -e "${GREEN}✅ Secrets template created at .github/SECRETS_TEMPLATE.md${NC}"

# Create deployment checklist
echo -e "\n${YELLOW}Creating deployment checklist...${NC}"
cat > DEPLOYMENT_CHECKLIST.md << 'EOF'
# 🚀 BidBees Deployment Checklist

## Pre-Deployment

- [ ] All tests passing locally
- [ ] Code reviewed and approved
- [ ] Database migrations prepared
- [ ] Environment variables documented
- [ ] Secrets configured in GitHub
- [ ] AWS credentials set up
- [ ] Docker images building successfully

## GitHub Setup

- [ ] Repository created/connected
- [ ] Branch protection rules configured
- [ ] GitHub Actions enabled
- [ ] Secrets configured
- [ ] Environments created (staging, production)
- [ ] Webhooks configured (if needed)

## Infrastructure

- [ ] AWS account set up
- [ ] VPC configured
- [ ] Security groups defined
- [ ] RDS instances created
- [ ] Redis cluster deployed
- [ ] S3 buckets created
- [ ] CloudFront distribution set up
- [ ] Route 53 DNS configured

## Monitoring

- [ ] CloudWatch dashboards created
- [ ] Alarms configured
- [ ] Sentry project created
- [ ] Log aggregation set up
- [ ] Performance baselines established

## Security

- [ ] SSL certificates configured
- [ ] WAF rules defined
- [ ] Security scanning enabled
- [ ] Dependency scanning active
- [ ] Access controls reviewed

## Post-Deployment

- [ ] Smoke tests passed
- [ ] Performance validated
- [ ] Monitoring active
- [ ] Documentation updated
- [ ] Team notified
- [ ] Rollback plan tested
EOF

echo -e "${GREEN}✅ Deployment checklist created${NC}"

# Summary
echo -e "\n${GREEN}========================================${NC}"
echo -e "${GREEN}✅ GitHub setup preparation complete!${NC}"
echo -e "${GREEN}========================================${NC}"

echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Review and commit any changes:"
echo "   git add ."
echo "   git commit -m \"feat: prepare for GitHub CI/CD deployment\""
echo "   git push origin main"
echo ""
echo "2. Configure GitHub secrets:"
echo "   See .github/SECRETS_TEMPLATE.md"
echo ""
echo "3. Set up GitHub environments:"
echo "   - staging"
echo "   - production"
echo ""
echo "4. Review deployment checklist:"
echo "   See DEPLOYMENT_CHECKLIST.md"
echo ""
echo "5. Trigger your first deployment:"
echo "   - Push to main branch for production"
echo "   - Push to develop branch for staging"
echo "   - Or use manual workflow dispatch"

echo -e "\n${GREEN}Good luck with your deployment! 🚀${NC}"