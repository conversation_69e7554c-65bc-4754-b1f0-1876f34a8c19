#!/usr/bin/env python3
"""
BeeHive Admin Dashboard Server
Ultra-modern fleet management dashboard for Bee task runners
"""

import json
import os
from datetime import datetime, timedelta
from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Supabase configuration
SUPABASE_URL = os.getenv('SUPABASE_URL', 'https://uvksgkpxeyyssvdsxbts.supabase.co')
SUPABASE_KEY = os.getenv('SUPABASE_KEY', 'your-supabase-key')

# Serve static files
@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    return send_from_directory('.', filename)

# Fleet Overview API
@app.route('/api/fleet/overview')
def get_fleet_overview():
    """Get comprehensive fleet overview statistics"""
    overview = {
        "active_bees": 247,
        "available_bees": 89,
        "busy_bees": 158,
        "offline_bees": 23,
        "total_registered": 270,
        "tasks_today": {
            "total": 1847,
            "completed": 1698,
            "in_progress": 149,
            "pending": 0,
            "completion_rate": 92.0
        },
        "revenue_today": {
            "total": 284000,
            "target": 250000,
            "percentage_of_target": 113.6,
            "average_per_task": 167.3
        },
        "performance": {
            "average_rating": 4.8,
            "on_time_delivery": 96.2,
            "customer_satisfaction": 4.7,
            "efficiency_score": 94.5
        },
        "geographic_distribution": {
            "johannesburg": 156,
            "pretoria": 45,
            "cape_town": 28,
            "durban": 18
        },
        "real_time_metrics": {
            "active_routes": 149,
            "average_speed": 45.2,
            "fuel_efficiency": 87.3,
            "safety_incidents": 0
        }
    }
    return jsonify(overview)

# Active Bees API
@app.route('/api/bees/active')
def get_active_bees():
    """Get all active bees with real-time data"""
    bees = [
        {
            "id": "bee_001",
            "user_id": "user_123",
            "full_name": "Thabo Mthembu",
            "phone": "+27 82 123 4567",
            "transport_mode": "motorcycle",
            "current_location": {
                "latitude": -26.2041,
                "longitude": 28.0473,
                "address": "Johannesburg CBD, Gauteng",
                "last_updated": datetime.now().isoformat()
            },
            "status": "active",
            "current_task": {
                "id": "task_001",
                "title": "Document Delivery",
                "category": "document_delivery",
                "progress": 65,
                "eta_minutes": 12
            },
            "performance": {
                "rating": 4.8,
                "total_ratings": 247,
                "today_earnings": 2847,
                "tasks_completed_today": 12,
                "efficiency_score": 94,
                "on_time_percentage": 98
            },
            "vehicle_info": {
                "make": "Honda",
                "model": "CB125F",
                "license_plate": "GP123ABC",
                "fuel_level": 85,
                "maintenance_due": False
            },
            "safety": {
                "helmet_detected": True,
                "speed_compliance": True,
                "last_safety_check": "2024-02-15T08:00:00Z"
            }
        },
        {
            "id": "bee_002",
            "user_id": "user_124",
            "full_name": "Sarah Ndlovu",
            "phone": "+27 83 456 7890",
            "transport_mode": "bicycle",
            "current_location": {
                "latitude": -26.1448,
                "longitude": 28.0436,
                "address": "Rosebank, Johannesburg",
                "last_updated": datetime.now().isoformat()
            },
            "status": "active",
            "current_task": {
                "id": "task_002",
                "title": "Site Inspection",
                "category": "site_inspection",
                "progress": 30,
                "eta_minutes": 25
            },
            "performance": {
                "rating": 4.9,
                "total_ratings": 189,
                "today_earnings": 3120,
                "tasks_completed_today": 15,
                "efficiency_score": 97,
                "on_time_percentage": 99
            },
            "vehicle_info": {
                "make": "Trek",
                "model": "FX 3",
                "license_plate": None,
                "battery_level": 78,
                "maintenance_due": False
            },
            "safety": {
                "helmet_detected": True,
                "speed_compliance": True,
                "last_safety_check": "2024-02-15T07:30:00Z"
            }
        },
        {
            "id": "bee_003",
            "user_id": "user_125",
            "full_name": "John Molefe",
            "phone": "+27 84 789 0123",
            "transport_mode": "car",
            "current_location": {
                "latitude": -26.1076,
                "longitude": 28.0567,
                "address": "Sandton, Johannesburg",
                "last_updated": datetime.now().isoformat()
            },
            "status": "available",
            "current_task": None,
            "performance": {
                "rating": 4.7,
                "total_ratings": 156,
                "today_earnings": 2340,
                "tasks_completed_today": 9,
                "efficiency_score": 89,
                "on_time_percentage": 95
            },
            "vehicle_info": {
                "make": "Toyota",
                "model": "Corolla",
                "license_plate": "GP456DEF",
                "fuel_level": 72,
                "maintenance_due": True
            },
            "safety": {
                "helmet_detected": False,
                "speed_compliance": True,
                "last_safety_check": "2024-02-14T16:00:00Z"
            }
        }
    ]
    return jsonify(bees)

# Task Management API
@app.route('/api/tasks/overview')
def get_tasks_overview():
    """Get task management overview"""
    tasks = {
        "summary": {
            "total_today": 1847,
            "completed": 1698,
            "in_progress": 149,
            "pending_assignment": 0,
            "cancelled": 12,
            "completion_rate": 92.0
        },
        "by_category": {
            "document_delivery": 856,
            "site_inspection": 423,
            "equipment_transport": 298,
            "courier_service": 270
        },
        "by_priority": {
            "urgent": 234,
            "high": 567,
            "medium": 789,
            "low": 257
        },
        "by_location": {
            "johannesburg_cbd": 456,
            "sandton": 234,
            "rosebank": 189,
            "midrand": 167,
            "pretoria": 145,
            "other": 656
        },
        "revenue_breakdown": {
            "document_delivery": 142800,
            "site_inspection": 84600,
            "equipment_transport": 35700,
            "courier_service": 20900
        },
        "performance_metrics": {
            "average_completion_time": 28.5,
            "average_distance": 12.3,
            "customer_satisfaction": 4.7,
            "repeat_customer_rate": 78.2
        }
    }
    return jsonify(tasks)

# AI Insights API
@app.route('/api/ai/fleet-insights')
def get_fleet_ai_insights():
    """Get AI-powered fleet management insights"""
    insights = {
        "fleet_efficiency": {
            "current_score": 94,
            "trend": "increasing",
            "improvement_this_week": 5,
            "benchmark_comparison": "15% above industry average",
            "recommendations": [
                "Maintain current optimization strategies",
                "Consider expanding fleet in high-demand areas",
                "Implement predictive maintenance for vehicles"
            ]
        },
        "demand_prediction": {
            "next_hour": {
                "expected_tasks": 89,
                "confidence": 0.92,
                "peak_areas": ["Sandton CBD", "Rosebank", "Midrand"]
            },
            "next_day": {
                "expected_tasks": 2300,
                "confidence": 0.87,
                "weather_impact": "Minimal - clear skies predicted"
            },
            "next_week": {
                "expected_tasks": 14500,
                "confidence": 0.78,
                "seasonal_factors": "End of month tender deadlines"
            }
        },
        "optimization_opportunities": {
            "route_optimization": {
                "potential_savings": "18% reduction in travel time",
                "affected_bees": 67,
                "implementation_priority": "high"
            },
            "load_balancing": {
                "underutilized_bees": 23,
                "overworked_bees": 8,
                "rebalancing_recommendation": "Redistribute 15 tasks"
            },
            "pricing_optimization": {
                "revenue_increase_potential": "12%",
                "dynamic_pricing_zones": ["Sandton", "Rosebank"],
                "implementation_timeline": "2 weeks"
            }
        },
        "risk_assessment": {
            "safety_score": 98,
            "high_risk_routes": [],
            "weather_alerts": [],
            "traffic_incidents": 0,
            "maintenance_alerts": 3
        },
        "performance_insights": {
            "top_performers": [
                {"bee_id": "bee_002", "name": "Sarah Ndlovu", "efficiency": 97},
                {"bee_id": "bee_001", "name": "Thabo Mthembu", "efficiency": 94},
                {"bee_id": "bee_003", "name": "John Molefe", "efficiency": 89}
            ],
            "improvement_needed": [
                {"bee_id": "bee_015", "name": "Mike Wilson", "efficiency": 67, "issues": ["Late deliveries", "Poor communication"]}
            ],
            "training_recommendations": [
                "Customer service training for 12 bees",
                "Route optimization workshop for new bees",
                "Safety refresher course for 5 bees"
            ]
        }
    }
    return jsonify(insights)

# Analytics API
@app.route('/api/analytics/dashboard')
def get_analytics_dashboard():
    """Get comprehensive analytics for dashboard"""
    analytics = {
        "revenue_trends": {
            "daily": [284000, 267000, 298000, 245000, 312000, 289000, 276000],
            "weekly": [1890000, 2100000, 1950000, 2250000],
            "monthly": [8400000, 9200000, 8800000, 9600000],
            "growth_rate": {
                "daily": 6.2,
                "weekly": 15.4,
                "monthly": 9.1
            }
        },
        "task_completion_trends": {
            "hourly_distribution": [45, 67, 89, 123, 156, 189, 234, 267, 298, 245, 189, 156, 123, 89, 67, 45, 34, 23, 12, 8, 5, 3, 2, 1],
            "category_performance": {
                "document_delivery": 96.5,
                "site_inspection": 94.2,
                "equipment_transport": 91.8,
                "courier_service": 98.1
            },
            "geographic_performance": {
                "johannesburg": 95.2,
                "pretoria": 93.8,
                "cape_town": 97.1,
                "durban": 94.5
            }
        },
        "bee_performance_metrics": {
            "average_rating": 4.8,
            "rating_distribution": {
                "5_star": 67,
                "4_star": 28,
                "3_star": 4,
                "2_star": 1,
                "1_star": 0
            },
            "efficiency_distribution": {
                "90_100": 45,
                "80_89": 32,
                "70_79": 18,
                "60_69": 4,
                "below_60": 1
            }
        },
        "customer_insights": {
            "satisfaction_score": 4.7,
            "repeat_rate": 78.2,
            "complaint_rate": 1.8,
            "top_feedback_themes": [
                "Fast delivery",
                "Professional service",
                "Good communication",
                "Reliable timing"
            ]
        }
    }
    return jsonify(analytics)

# Fleet Control API
@app.route('/api/fleet/broadcast', methods=['POST'])
def broadcast_to_fleet():
    """Broadcast message to all or selected bees"""
    data = request.get_json()
    
    response = {
        "success": True,
        "message": "Broadcast sent successfully",
        "recipients": data.get('recipients', 'all'),
        "message_content": data.get('message', ''),
        "delivery_status": "sent",
        "timestamp": datetime.now().isoformat()
    }
    return jsonify(response)

@app.route('/api/fleet/emergency-stop', methods=['POST'])
def emergency_stop():
    """Emergency stop for all fleet operations"""
    response = {
        "success": True,
        "message": "Emergency stop activated",
        "affected_bees": 247,
        "timestamp": datetime.now().isoformat(),
        "estimated_resume_time": (datetime.now() + timedelta(minutes=30)).isoformat()
    }
    return jsonify(response)

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "bee-admin-dashboard",
        "timestamp": datetime.now().isoformat(),
        "version": "2.1.0",
        "fleet_status": "operational"
    })

if __name__ == '__main__':
    port = 3026  # Fixed port to avoid conflicts
    print(f"🐝 Starting BeeHive Admin Dashboard on port {port}")
    print(f"🌐 Frontend: http://localhost:{port}")
    print(f"📊 Fleet API: http://localhost:{port}/api/fleet/overview")
    print(f"🤖 AI Insights: http://localhost:{port}/api/ai/fleet-insights")
    
    app.run(host='0.0.0.0', port=port, debug=True)
