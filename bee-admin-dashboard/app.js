// BeeHive Admin Dashboard - Ultra Modern JavaScript with AI Fleet Management

// Global variables
let fleetMap;
let beeMarkers = [];
let taskMarkers = [];
let currentSection = 'overview';
let aiInsightsInterval;

// Mock data for fleet management
const mockFleetData = {
    activeBees: 247,
    tasksToday: 1847,
    revenueToday: 284000,
    avgRating: 4.8,
    totalEarnings: 2847000,
    completionRate: 92,
    onlinePercentage: 85
};

const mockActiveBees = [
    {
        id: 'bee_001',
        name: 'Thab<PERSON>hembu',
        location: { lat: -26.2041, lng: 28.0473 },
        status: 'active',
        currentTask: 'Document Delivery',
        rating: 4.8,
        todayEarnings: 2847,
        tasksCompleted: 12,
        efficiency: 94
    },
    {
        id: 'bee_002',
        name: '<PERSON>',
        location: { lat: -26.1448, lng: 28.0436 },
        status: 'active',
        currentTask: 'Site Inspection',
        rating: 4.9,
        todayEarnings: 3120,
        tasksCompleted: 15,
        efficiency: 97
    },
    {
        id: 'bee_003',
        name: '<PERSON>',
        location: { lat: -26.1076, lng: 28.0567 },
        status: 'available',
        currentTask: null,
        rating: 4.7,
        todayEarnings: 2340,
        tasksCompleted: 9,
        efficiency: 89
    },
    {
        id: 'bee_004',
        name: 'Nomsa Khumalo',
        location: { lat: -25.9953, lng: 28.1294 },
        status: 'active',
        currentTask: 'Equipment Transport',
        rating: 4.6,
        todayEarnings: 2680,
        tasksCompleted: 11,
        efficiency: 91
    }
];

const mockAIInsights = [
    {
        title: 'Fleet Efficiency',
        value: '94%',
        trend: 'up',
        change: '+5%',
        description: 'Overall fleet performance is excellent',
        recommendation: 'Maintain current optimization strategies'
    },
    {
        title: 'Peak Demand Zones',
        value: 'Sandton CBD',
        trend: 'up',
        change: '+23%',
        description: 'Highest task density area',
        recommendation: 'Deploy 3 more bees to this zone'
    },
    {
        title: 'Revenue Optimization',
        value: 'R284K',
        trend: 'up',
        change: '+18%',
        description: 'Daily revenue target exceeded',
        recommendation: 'Focus on high-value task categories'
    },
    {
        title: 'Predictive Analytics',
        value: '2.3K tasks',
        trend: 'up',
        change: '+12%',
        description: 'Expected tasks for tomorrow',
        recommendation: 'Prepare 15 additional bees'
    }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🐝 BeeHive Admin Dashboard initialized');
    initializeFleetMap();
    loadAIInsights();
    animateStats();
    startRealTimeUpdates();
});

// Initialize fleet map
function initializeFleetMap() {
    fleetMap = L.map('fleetMap').setView([-26.2041, 28.0473], 11);
    
    // Add modern tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19
    }).addTo(fleetMap);
    
    // Load bee markers
    loadBeeMarkers();
}

// Load bee markers on fleet map
function loadBeeMarkers() {
    // Clear existing markers
    beeMarkers.forEach(marker => fleetMap.removeLayer(marker));
    beeMarkers = [];
    
    mockActiveBees.forEach(bee => {
        const statusColor = bee.status === 'active' ? '#10b981' : bee.status === 'available' ? '#3b82f6' : '#f59e0b';
        
        const beeIcon = L.divIcon({
            className: 'bee-fleet-marker',
            html: `<div style="background: ${statusColor}; width: 25px; height: 25px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; box-shadow: 0 4px 12px rgba(0,0,0,0.3); border: 3px solid white; font-size: 12px;"><i class="fas fa-bee"></i></div>`,
            iconSize: [31, 31],
            iconAnchor: [15, 15]
        });
        
        const marker = L.marker([bee.location.lat, bee.location.lng], { icon: beeIcon })
            .addTo(fleetMap)
            .bindPopup(`
                <div style="min-width: 200px; padding: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                        <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #fbbf24, #f97316); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">${bee.name.split(' ').map(n => n[0]).join('')}</div>
                        <div>
                            <strong>${bee.name}</strong><br>
                            <span style="color: ${statusColor}; font-weight: 600;">● ${bee.status}</span>
                        </div>
                    </div>
                    <div style="background: #f8fafc; padding: 10px; border-radius: 8px; margin-bottom: 10px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>Rating:</span>
                            <span style="font-weight: 600;">${bee.rating}★</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>Today's Earnings:</span>
                            <span style="font-weight: 600; color: #10b981;">R${bee.todayEarnings}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>Tasks Completed:</span>
                            <span style="font-weight: 600;">${bee.tasksCompleted}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>Efficiency:</span>
                            <span style="font-weight: 600; color: #6366f1;">${bee.efficiency}%</span>
                        </div>
                    </div>
                    ${bee.currentTask ? `<div style="background: #eff6ff; padding: 8px; border-radius: 6px; margin-bottom: 10px; font-size: 0.9em;"><strong>Current Task:</strong> ${bee.currentTask}</div>` : ''}
                    <div style="display: flex; gap: 8px;">
                        <button onclick="contactBee('${bee.id}')" style="flex: 1; background: #6366f1; color: white; border: none; padding: 8px; border-radius: 6px; cursor: pointer; font-weight: 600;">Contact</button>
                        <button onclick="viewBeeDetails('${bee.id}')" style="flex: 1; background: transparent; color: #6366f1; border: 2px solid #6366f1; padding: 8px; border-radius: 6px; cursor: pointer; font-weight: 600;">Details</button>
                    </div>
                </div>
            `);
        
        beeMarkers.push(marker);
    });
}

// Load AI insights
function loadAIInsights() {
    const aiGrid = document.getElementById('aiInsightsGrid');
    if (!aiGrid) return;
    
    const insightsHTML = mockAIInsights.map(insight => `
        <div class="ai-insight-card" style="background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); border-radius: 16px; padding: 20px; border: 1px solid rgba(255, 255, 255, 0.3); transition: all 0.3s ease;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <div style="font-size: 0.875rem; color: #64748b; font-weight: 600;">${insight.title}</div>
                <div style="font-size: 0.75rem; font-weight: 600; padding: 4px 8px; border-radius: 12px; background: ${insight.trend === 'up' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)'}; color: ${insight.trend === 'up' ? '#10b981' : '#ef4444'};">
                    ${insight.trend === 'up' ? '↗' : '↘'} ${insight.change}
                </div>
            </div>
            <div style="font-size: 1.5rem; font-weight: 700; color: #6366f1; margin-bottom: 8px;">${insight.value}</div>
            <div style="font-size: 0.875rem; color: #64748b; margin-bottom: 10px;">${insight.description}</div>
            <div style="font-size: 0.75rem; color: #059669; background: rgba(16, 185, 129, 0.1); padding: 6px 10px; border-radius: 8px; font-weight: 600;">
                💡 ${insight.recommendation}
            </div>
        </div>
    `).join('');
    
    aiGrid.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            ${insightsHTML}
        </div>
    `;
}

// Animate statistics
function animateStats() {
    const progressBars = document.querySelectorAll('.stat-progress-fill');
    progressBars.forEach(bar => {
        const targetWidth = bar.getAttribute('data-width');
        if (targetWidth) {
            setTimeout(() => {
                bar.style.width = `${targetWidth}%`;
            }, 500);
        }
    });
}

// Navigation functionality
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from all nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected section
    const section = document.getElementById(sectionName);
    if (section) {
        section.classList.add('active');
    }
    
    // Add active class to clicked tab
    event.target.classList.add('active');
    
    currentSection = sectionName;
}

// Map control functions
function centerFleetMap() {
    fleetMap.setView([-26.2041, 28.0473], 11);
    showNotification('Fleet map centered', 'info');
}

function toggleHeatmap() {
    showNotification('Heatmap view toggled', 'info');
    // In real implementation, this would toggle heatmap overlay
}

function filterActiveBees() {
    showNotification('Filtering active bees only', 'info');
    // In real implementation, this would filter markers
}

// Bee management functions
function contactBee(beeId) {
    const bee = mockActiveBees.find(b => b.id === beeId);
    if (bee) {
        showNotification(`Contacting ${bee.name}...`, 'info');
    }
}

function viewBeeDetails(beeId) {
    const bee = mockActiveBees.find(b => b.id === beeId);
    if (bee) {
        showNotification(`Opening details for ${bee.name}`, 'info');
    }
}

// Real-time updates
function startRealTimeUpdates() {
    // Update AI insights every 2 minutes
    aiInsightsInterval = setInterval(() => {
        loadAIInsights();
    }, 120000);
    
    // Update bee locations every 30 seconds
    setInterval(() => {
        updateBeeLocations();
    }, 30000);
    
    // Update stats every minute
    setInterval(() => {
        updateFleetStats();
    }, 60000);
}

function updateBeeLocations() {
    // Simulate slight location changes for active bees
    mockActiveBees.forEach(bee => {
        if (bee.status === 'active') {
            const variation = 0.002;
            bee.location.lat += (Math.random() - 0.5) * variation;
            bee.location.lng += (Math.random() - 0.5) * variation;
        }
    });
    
    loadBeeMarkers();
}

function updateFleetStats() {
    // Simulate real-time stats updates
    const progressBars = document.querySelectorAll('.stat-progress-fill');
    progressBars.forEach(bar => {
        const currentWidth = parseFloat(bar.style.width);
        const variation = (Math.random() - 0.5) * 3; // ±1.5%
        const newWidth = Math.max(0, Math.min(100, currentWidth + variation));
        bar.style.width = `${newWidth}%`;
    });
}

// Utility functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#6366f1'};
        color: white;
        padding: 15px 25px;
        border-radius: 16px;
        box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        max-width: 350px;
        font-weight: 600;
        backdrop-filter: blur(20px);
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (aiInsightsInterval) {
        clearInterval(aiInsightsInterval);
    }
});
