<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐝 BeeHive Admin - Ultra Modern Control Center</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #f59e0b;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #3b82f6;
            --dark: #0f172a;
            --light: #f8fafc;
            --bee-yellow: #fbbf24;
            --bee-orange: #f97316;
            --bee-purple: #8b5cf6;
            --bee-green: #22c55e;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Ultra Modern Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--bee-yellow), var(--bee-orange), var(--primary), var(--bee-purple));
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.75rem;
            font-weight: 900;
            color: var(--primary);
        }

        .logo i {
            font-size: 2.5rem;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: buzz 2s ease-in-out infinite;
        }

        @keyframes buzz {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px) rotate(-1deg); }
            75% { transform: translateX(2px) rotate(1deg); }
        }

        .admin-profile {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .admin-info {
            text-align: right;
        }

        .admin-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .admin-role {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin-top: 2px;
        }

        .admin-badge {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            color: white;
            padding: 6px 16px;
            border-radius: 25px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: var(--shadow-lg);
            margin-top: 5px;
        }

        .admin-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--bee-purple));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.5rem;
            box-shadow: var(--shadow-xl);
            position: relative;
        }

        .admin-avatar::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary), var(--bee-purple), var(--bee-yellow), var(--bee-orange));
            border-radius: 50%;
            z-index: -1;
            animation: rotate 4s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* AI Command Center */
        .ai-command-center {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(139, 92, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .ai-command-center::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--bee-purple), var(--primary));
        }

        .ai-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
        }

        .ai-title-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .ai-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--bee-purple), var(--primary));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(139, 92, 246, 0.5); }
            to { box-shadow: 0 0 30px rgba(139, 92, 246, 0.8); }
        }

        .ai-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .ai-subtitle {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .ai-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .ai-status i {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        /* Navigation Styles */
        .nav-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            padding: 8px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            overflow-x: auto;
        }

        .nav-tab {
            padding: 15px 30px;
            border: none;
            background: transparent;
            border-radius: 16px;
            cursor: pointer;
            font-weight: 600;
            color: var(--gray-600);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95rem;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .nav-tab:hover {
            background: var(--gray-100);
            color: var(--gray-800);
            transform: translateY(-2px);
        }

        .nav-tab.active {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .stat-icon.bees { background: linear-gradient(135deg, var(--bee-green), #059669); }
        .stat-icon.tasks { background: linear-gradient(135deg, var(--primary), var(--primary-dark)); }
        .stat-icon.revenue { background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange)); }
        .stat-icon.rating { background: linear-gradient(135deg, var(--info), #1d4ed8); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--gray-800);
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .stat-progress {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .stat-progress-fill {
            height: 100%;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 4px;
            width: 0%;
        }

        .stat-change {
            font-size: 0.875rem;
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .stat-change.positive {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success);
        }

        /* Map Card */
        .map-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .map-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, var(--info), var(--primary));
        }

        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .map-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .map-controls {
            display: flex;
            gap: 10px;
        }

        .map-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .map-btn-primary {
            background: var(--primary);
            color: white;
        }

        .map-btn-outline {
            background: transparent;
            color: var(--gray-600);
            border: 2px solid var(--gray-300);
        }

        .map-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        #fleetMap {
            height: 500px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        /* Placeholder Content */
        .placeholder-content {
            text-align: center;
            padding: 80px 20px;
            color: var(--gray-500);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            border: 2px dashed var(--gray-300);
        }

        .placeholder-content i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: var(--gray-400);
        }

        .placeholder-content h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--gray-700);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .admin-profile {
                flex-direction: column;
                gap: 15px;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .map-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .map-controls {
                justify-content: center;
            }

            #fleetMap {
                height: 350px;
            }
        }

        /* Navigation Styles */
        .nav-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            padding: 8px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            overflow-x: auto;
        }

        .nav-tab {
            padding: 15px 30px;
            border: none;
            background: transparent;
            border-radius: 16px;
            cursor: pointer;
            font-weight: 600;
            color: var(--gray-600);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95rem;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .nav-tab:hover::before {
            left: 100%;
        }

        .nav-tab:hover {
            background: var(--gray-100);
            color: var(--gray-800);
            transform: translateY(-2px);
        }

        .nav-tab.active {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .stat-icon.bees { background: linear-gradient(135deg, var(--bee-green), #059669); }
        .stat-icon.tasks { background: linear-gradient(135deg, var(--primary), var(--primary-dark)); }
        .stat-icon.revenue { background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange)); }
        .stat-icon.rating { background: linear-gradient(135deg, var(--info), #1d4ed8); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--gray-800);
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .stat-progress {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .stat-progress-fill {
            height: 100%;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 4px;
            width: 0%;
        }

        .stat-change {
            font-size: 0.875rem;
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .stat-change.positive {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success);
        }

        /* Map Card */
        .map-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .map-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, var(--info), var(--primary));
        }

        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .map-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .map-controls {
            display: flex;
            gap: 10px;
        }

        .map-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .map-btn-primary {
            background: var(--primary);
            color: white;
        }

        .map-btn-outline {
            background: transparent;
            color: var(--gray-600);
            border: 2px solid var(--gray-300);
        }

        .map-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        #fleetMap {
            height: 500px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        /* Placeholder Content */
        .placeholder-content {
            text-align: center;
            padding: 80px 20px;
            color: var(--gray-500);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            border: 2px dashed var(--gray-300);
        }

        .placeholder-content i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: var(--gray-400);
        }

        .placeholder-content h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--gray-700);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .admin-profile {
                flex-direction: column;
                gap: 15px;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .map-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .map-controls {
                justify-content: center;
            }

            #fleetMap {
                height: 350px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Ultra Modern Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-crown"></i>
                    <span>BeeHive Admin</span>
                </div>
                <div class="admin-profile">
                    <div class="admin-info">
                        <div class="admin-name">Sarah Johnson</div>
                        <div class="admin-role">System Administrator</div>
                        <div class="admin-badge">Super Admin</div>
                    </div>
                    <div class="admin-avatar">SJ</div>
                </div>
            </div>
        </header>

        <!-- AI Command Center -->
        <section class="ai-command-center">
            <div class="ai-header">
                <div class="ai-title-section">
                    <div class="ai-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div>
                        <div class="ai-title">AI Command Center</div>
                        <div class="ai-subtitle">Real-time fleet management with machine learning insights</div>
                    </div>
                </div>
                <div class="ai-status">
                    <i class="fas fa-circle"></i>
                    AI Systems Online
                </div>
            </div>
            <div id="aiInsightsGrid">
                <!-- AI insights will be loaded here -->
            </div>
        </section>

        <!-- Ultra Modern Navigation -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showSection('overview')">
                <i class="fas fa-chart-pie"></i>
                Fleet Overview
            </button>
            <button class="nav-tab" onclick="showSection('bees')">
                <i class="fas fa-users"></i>
                Active Bees
            </button>
            <button class="nav-tab" onclick="showSection('tasks')">
                <i class="fas fa-tasks"></i>
                Task Management
            </button>
            <button class="nav-tab" onclick="showSection('analytics')">
                <i class="fas fa-chart-line"></i>
                Analytics
            </button>
            <button class="nav-tab" onclick="showSection('performance')">
                <i class="fas fa-trophy"></i>
                Performance
            </button>
            <button class="nav-tab" onclick="showSection('settings')">
                <i class="fas fa-cog"></i>
                Settings
            </button>
        </nav>

        <!-- Content Sections -->
        <main>
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <!-- Fleet Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-value">247</div>
                                <div class="stat-label">Active Bees</div>
                                <div class="stat-progress">
                                    <div class="stat-progress-fill" data-width="85"></div>
                                </div>
                            </div>
                            <div class="stat-icon bees">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12 from yesterday
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-value">1,847</div>
                                <div class="stat-label">Tasks Today</div>
                                <div class="stat-progress">
                                    <div class="stat-progress-fill" data-width="92"></div>
                                </div>
                            </div>
                            <div class="stat-icon tasks">
                                <i class="fas fa-clipboard-check"></i>
                            </div>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +23% completion rate
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-value">R284K</div>
                                <div class="stat-label">Revenue Today</div>
                                <div class="stat-progress">
                                    <div class="stat-progress-fill" data-width="78"></div>
                                </div>
                            </div>
                            <div class="stat-icon revenue">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +18% from target
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-value">4.8</div>
                                <div class="stat-label">Avg Rating</div>
                                <div class="stat-progress">
                                    <div class="stat-progress-fill" data-width="96"></div>
                                </div>
                            </div>
                            <div class="stat-icon rating">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +0.3 this month
                        </div>
                    </div>
                </div>

                <!-- Fleet Map -->
                <div class="map-card">
                    <div class="map-header">
                        <div class="map-title">🗺️ Live Fleet Tracking</div>
                        <div class="map-controls">
                            <button class="map-btn map-btn-primary" onclick="centerFleetMap()">
                                <i class="fas fa-crosshairs"></i>
                                Center Fleet
                            </button>
                            <button class="map-btn map-btn-outline" onclick="toggleHeatmap()">
                                <i class="fas fa-fire"></i>
                                Heatmap
                            </button>
                            <button class="map-btn map-btn-outline" onclick="filterActiveBees()">
                                <i class="fas fa-filter"></i>
                                Active Only
                            </button>
                        </div>
                    </div>
                    <div id="fleetMap"></div>
                </div>
            </section>

            <!-- Other sections placeholders -->
            <section id="bees" class="content-section">
                <div class="placeholder-content">
                    <i class="fas fa-users"></i>
                    <h3>Active Bees Management</h3>
                    <p>Bee fleet management interface will be displayed here</p>
                </div>
            </section>

            <section id="tasks" class="content-section">
                <div class="placeholder-content">
                    <i class="fas fa-tasks"></i>
                    <h3>Task Management</h3>
                    <p>Task assignment and monitoring interface will be displayed here</p>
                </div>
            </section>

            <section id="analytics" class="content-section">
                <div class="placeholder-content">
                    <i class="fas fa-chart-line"></i>
                    <h3>Advanced Analytics</h3>
                    <p>Detailed analytics and reporting will be displayed here</p>
                </div>
            </section>

            <section id="performance" class="content-section">
                <div class="placeholder-content">
                    <i class="fas fa-trophy"></i>
                    <h3>Performance Dashboard</h3>
                    <p>Performance metrics and leaderboards will be displayed here</p>
                </div>
            </section>

            <section id="settings" class="content-section">
                <div class="placeholder-content">
                    <i class="fas fa-cog"></i>
                    <h3>System Settings</h3>
                    <p>Administrative settings and configuration will be displayed here</p>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="app.js"></script>
</body>
</html>