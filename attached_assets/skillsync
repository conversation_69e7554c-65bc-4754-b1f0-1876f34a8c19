
A SKILLSYNC UPGRADE
prince sibanda <<EMAIL>>	Wed, Jun 11, 2025 at 12:25 PM
To: <EMAIL>, <EMAIL>
from django_f import FSMField, transition
from django.utils import timezone
from ..models.verification import SkillProviderVerification
from ..models.profile import Qualification
from ..integrations_service import external_verification
import logging
import requests
from rest_framework.exceptions import APIException

logger = logging.getLogger(__name__)

class VerificationWorkflow:
def __init__(self, verification: SkillProviderVerification):
self.verification = verification

@transition(
field='status',
source='pending',
target='in_progress',
conditions=[lambda self: self.verification.verified_by is not None or self.verification.verification_type == 'qualification']
)
async def start_verification(self, tenant_id: int):
"""Initiate verification, including external API calls."""
logger.info(f"Starting verification {self.verification.id} for tenant {self.tenant.id}}")
try:
if self.verification.verification_type == 'qualification':
qualification = Qualification.objects.get(id=self.qualification_id)
# Integrate with SAQA API
result = await self.verify_with_saqa(qualification)
self.verification.notes = result.get('notes', '')
return result.get('success', False)
return True
except Exception as e:
logger.error(f"Verification failed for {self.verification.id}: {str(e)}")
self.verification.notes = f"Error: {str(e)}"
self.verification.status = 'failed'
self.verification.save()
raise APIException(f"Verification failed: {str(e)}")

async def verify_with_saqa(self, qualification: Qualification) -> Dict[str, Any]:
"""Verify qualification with SAQA API."""
try:
api_key = settings.SAQA_API_KEY
response = await requests.post(
"https://api.saqa.org.za/verify",
headers={"Authorization": f"Bearer {api_key}"},
json={
"qualification_name": qualification.name,
"issuing_authority": qualification.issuing_authority,
"document_id": qualification.verification_document.name if qualification.verification_document else None
}
)
response.raise_for_status()
result = response.json()
success = result.get('verified', False)
notes = result.get('message', '')

if success:
qualification.is_verified = True
qualification.save()

# Log audit
AuditLog.objects.create(
tenant_id=qualification.tenant_id,
action='verify_qualification',
model_name='Qualification',
object_id=str(qualification.id),
details={'result': result, 'success': success},
timestamp=timezone.now()
)

logger.info(f"SAQA verification for qualification {qualification.id}: {success}")
return {'success': success, 'notes': notes}
except requests.exceptions.RequestException as e:
logger.error(f"SAQA API call failed for qualification {qualification.id}: {str(e)}")
return {'success': False, 'notes': str(e)}

@transition(field='status', source='in_progress', target='completed')
def complete_verification(self):
logger.info(f"Completing verification {self.verification.id}")
self.verification.verification_date = timezone.now()
self.verification.save()
profile = self.verification.skill_provider_profile
if all(v.status == 'completed' for v in profile.verifications.all()):
profile.verification_status = 'verified'
profile.save()




@primary_agent.tool
async def recruit_skillsync_providers(self, ctx: RunContext[AgentDependencies],
tender_id: str, doclin_result: Dict[str, Any],
pitfalls: List[Pitfall]) -> ModuleRecruitment:
"""Recruit skill providers via SkillSync, with automated verification."""
try:
tender_fields = doclin_result['tender_fields']
requirements = tender_fields.get('requirements', [])
briefing_sessions = tender_fields.get('briefing_details', {}).get('sessions', [])

criteria = []
for req in requirements:
if any(keyword in req.lower() for keyword in ['qualification', 'skill', 'certification', 'engineer']):
criteria.append(req)
for pitfall in pitfalls:
if 'qualification' in pitfall.description.lower() or 'skill' in pitfall.description.lower():
criteria.extend(pitfall.related_requirements)
for session in briefing_sessions:
if quals := session.get('qualifications'):
criteria.append(quals)

db = ctx.deps.mongodb_client[self.config['databases']['mongodb']['database']]
providers = await db['skillsync_profiles'].find({
'tenant_id': ctx.deps.config['tenant_id'],
'verification_status': 'verified',
'skills': {'$in': [re.compile(c, re.IGNORECASE) for c in criteria]}
}).to_list(None)

gaps = [c for c in criteria if not any(c.lower() in p['skills'].lower() for p in providers)]

if gaps:
search_result = await self.search_internet(ctx, f"skilled workers South Africa {', '.join(gaps)}", count=5)
new_users = [
{
'id': f"new_{hash(r['url'])}",
'user_id': f"user_{hash(r['url'])}",
'tenant_id': ctx.deps.config['tenant_id'],
'bio': r['description'][:500],
'skills': [gaps[0]],
'qualifications': [{'name': gaps[0], 'issuing_authority': 'Unknown', 'is_verified': False}],
'verification_status': 'pending',
'rating': None,
'num_reviews': 0,
'availability': True
}
for r in search_result.results if any(g.lower() in r['description'].lower() for g in gaps)
]

if new_users:
await db['skillsync_profiles'].insert_many(new_users)
for user in new_users:
# Trigger SAQA verification
await db['skillsync_verifications'].insert_one({
'skill_provider_id': user['id'],
'qualification_id': user['qualifications'][0]['id'],
'verification_type': 'qualification',
'status': 'pending',
'notes': f"Auto-recruited for tender {tender_id}",
'tenant_id': ctx.deps.config['tenant_id']
})
# Notify provider
await self._send_flutter_notification(
ctx.deps,
'provider_device_token', # Placeholder
f"Verify Your {gaps[0]} Qualification",
"Please upload verification documents for your qualification to join tender opportunities."
)
providers.extend(new_users)

return ModuleRecruitment(
module="SkillSync",
recruited_users=[
{'id': p['id'], 'name': p['user_id'], 'skills': p['skills'], 'verification_status': p['verification_status']}
for p in providers
],
gaps=gaps
)
except Exception as e:
self.logger.error(f"SkillSync recruitment failed: {e}")
raise




from transformers import pipeline
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

class SkillSearchService:
def __init__(self, tenant_id: str):
self.tenant_id = tenant_id
self.nlp_model = pipeline("feature-extraction", model="distilbert-base-uncased")

def compute_similarity(self, text1: str, text2: str) -> float:
"""Compute semantic similarity between two texts."""
cache_key = f"similarity_{self.tenant_id}_{hash(text1)}_{hash(text2)}"
similarity = cache.get(cache_key)
if similarity is not None:
return similarity

try:
# Extract embeddings
emb1 = self.nlp_model(text1, return_tensors="pt")[0].mean(dim=1).numpy()
emb2 = self.nlp_model(text2, return_tensors="pt")[0].mean(dim=1).numpy()
# Compute cosine similarity
similarity = float(np.dot(emb1, emb2.T) / (np.linalg.norm(emb1) * np.linalg.norm(emb2)))
cache.set(cache_key, similarity, 3600)
logger.info(f"Computed similarity for {text1[:50]} and {text2[:50]}: {similarity}")
return similarity
except Exception as e:
logger.error(f"Similarity computation failed: {str(e)}")
return 0.0

async def match_skills(self, requirements: List[str], providers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
"""Match tender requirements to provider skills using NLP."""
matched_providers = []
for provider in providers:
provider_score = 0.0
for req in requirements:
for skill in provider.get('skills', []):
score = self.compute_similarity(req, skill)
provider_score = max(provider_score, score)
for qual in provider.get('qualifications', []):
score = self.compute_similarity(req, qual.get('name', ''))
provider_score = max(provider_score, score)
if provider_score > 0.7: # Threshold for match
matched_providers.append({
**provider,
'match_score': provider_score
})
return sorted(matched_providers, key=lambda x: x['match_score'], reverse=True)




@primary_agent.tool
async def recruit_skillsync_providers(self, ctx: RunContext[AgentDependencies],
tender_id: str, doclin_result: Dict[str, Any],
pitfalls: List[Pitfall]) -> ModuleRecruitment:
try:
tender_fields = doclin_result['tender_fields']
requirements = tender_fields.get('requirements', [])
briefing_sessions = tender_fields.get('briefing_details', {}).get('sessions', [])

criteria = []
for req in requirements:
if any(keyword in req.lower() for keyword in ['qualification', 'skill', 'certification', 'engineer']):
criteria.append(req)
for pitfall in pitfalls:
if 'qualification' in pitfall.description.lower() or 'skill' in pitfall.description.lower():
criteria.extend(pitfall.related_requirements)
for session in briefing_sessions:
if quals := session.get('qualifications'):
criteria.append(quals)

db = ctx.deps.mongodb_client[self.config['databases']['mongodb']['database']]
providers = await db['skillsync_profiles'].find({
'tenant_id': ctx.deps.config['tenant_id'],
'verification_status': {'$in': ['verified', 'pending']}
}).to_list(None)

# Use NLP for dynamic matching
search_service = SkillSearchService(ctx.deps.config['tenant_id'])
matched_providers = await search_service.match_skills(criteria, providers)

gaps = [c for c in criteria if not any(
search_service.compute_similarity(c, p.get('skills', [''])[0]) > 0.7 or
search_service.compute_similarity(c, p.get('qualifications', [{}])[0].get('name', '')) > 0.7
for p in matched_providers
)]

if gaps:
search_result = await self.search_internet(ctx, f"skilled workers South Africa {', '.join(gaps)}", count=5)
new_users = [
{
'id': f"new_{hash(r['url'])}",
'user_id': f"user_{hash(r['url'])}",
'tenant_id': ctx.deps.config['tenant_id'],
'bio': r['description'][:500],
'skills': [gaps[0]],
'qualifications': [{'name': gaps[0], 'issuing_authority': 'Unknown', 'is_verified': False}],
'verification_status': 'pending',
'rating': None,
'num_reviews': 0,
'availability': True
}
for r in search_result.results if any(g.lower() in r['description'].lower() for g in gaps)
]

if new_users:
await db['skillsync_profiles'].insert_many(new_users)
for user in new_users:
await db['skillsync_verifications'].insert_one({
'skill_provider_id': user['id'],
'qualification_id': user['qualifications'][0]['id'],
'verification_type': 'qualification',
'status': 'pending',
'notes': f"Auto-recruited for tender {tender_id}",
'tenant_id': ctx.deps.config['tenant_id']
})
matched_providers.extend(new_users)

return ModuleRecruitment(
module="SkillSync",
recruited_users=[
{'id': p['id'], 'name': p['user_id'], 'skills': p['skills'], 'match_score': p.get('match_score', 0.0)}
for p in matched_providers
],
gaps=gaps
)
except Exception as e:
self.logger.error(f"SkillSync recruitment failed: {e}")
raise




from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.exceptions import APIException
from ..models.license import License
from ..serializers.license import LicenseSerializer, CreateLicenseSerializer
from ..security.permissions import IsBidderOrReadOnly, IsTenantMember
from ..services.smart_contracts import deploy_license_contract
from django.contrib.auth import get_user_model
import logging

logger = logging.getLogger(__name__)

User = get_user_model()

class LicenseViewSet(viewsets.ModelViewSet):
queryset = License.objects.all()
serializer_class = LicenseSerializer
permission_classes = [IsTenantMember, IsBidderOrReadOnly]

def get_queryset(self):
return License.objects.filter(tenant=self.request.tenant)

@action(detail=False, methods=['post'])
async def request_license(self, request):
serializer = CreateLicenseSerializer(data=request.data, context={'request': request})
if serializer.is_valid():
try:
license = serializer.save(
skill_provider_id=request.data['skill_provider'],
licensing_bidder=request.user,
tenant=request.tenant,
)
# Deploy smart contract
contract_id = await deploy_license_contract(license)
license.smart_contract_id = contract_id
license.status = 'pending_payment'
license.save()

# Log audit
AuditLog.objects.create(
tenant=request.tenant,
user=request.user,
action='create_license',
model_name='License',
object_id=str(license.id),
details={'smart_contract_id': contract_id},
timestamp=timezone.now()
)

logger.info(f"License {license.id} created with smart contract {contract_id}")
return Response(LicenseSerializer(license).data, status=status.HTTP_201_CREATED)
except Exception as e:
logger.error(f"License creation failed: {str(e)}")
raise APIException(f"Failed to create license: {str(e)}")
logger.warning(f"Invalid license request: {serializer.errors}")
return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




@primary_agent.tool
async def monitor_license_contract(self, ctx: RunContext[AgentDependencies],
license: License) -> Dict[str, Any]:
"""Monitor smart contract deployment for a license."""
try:
db = ctx.deps.mongodb_client[self.config['databases']['mongodb']['database']]
if not license.smart_contract_id:
raise ValueError(f"No smart contract for license {license.id}")

# Placeholder: Check blockchain transaction status
w3 = Web3(Web3.HTTPProvider(ctx.deps.config['integrations']['blockchain']['provider_url']))
receipt = w3.eth.get_transaction_receipt(license.smart_contract_id)

status = 'deployed' if receipt and receipt.status == 1 else 'pending'

await db['skillsync_licenses'].update_one(
{'_id': license.id},
{'$set': {'status': 'active' if status == 'deployed' else 'pending_payment'}}
)

if status == 'deployed':
await self._send_flutter_notification(
ctx.deps,
'bidder_device_token',
f"License Contract Deployed for Tender {license.tender_id}",
f"Smart contract {license.smart_contract_id} is active."
)

return {'status': status, 'smart_contract_id': license.smart_contract_id}
except Exception as e:
self.logger.error(f"Contract monitoring failed: {e}")
raise




from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from ..models.profile import SkillProviderProfile
from ..models.license import License
from ..security.permissions import IsTenantAdmin
from django.db.models import Count, Avg
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

class AnalyticsViewSet(viewsets.ViewSet):
permission_classes = [IsTenantAdmin]

@action(detail=False, methods=['get'])
async def provider_stats(self, request):
"""Provide analytics for SkillSync providers."""
tenant = request.tenant
try:
# Availability
availability = await SkillProviderProfile.objects.filter(tenant=tenant).aggregate(
available=Count('id', filter=Q(availability=True)),
unavailable=Count('id', filter=Q(availability=False))
)

# Verification Status
verification_stats = await SkillProviderProfile.objects.filter(tenant=tenant).values('verification_status').annotate(count=Count('id'))

# Pricing Trends (last 30 days)
licenses = await License.objects.filter(
tenant=tenant,
created_at__gte=timezone.now() - timedelta(days=30)
).aggregate(avg_fee=Avg('license_fee'))

return Response({
'availability': {
'labels': ['Available', 'Unavailable'],
'counts': [availability['available'], availability['unavailable']]
},
'verification': {
'labels': [item['verification_status'] for item in verification_stats],
'counts': [item['count'] for item in verification_stats]
},
'pricing_trends': {
'average_license_fee': licenses['avg_fee'] or 0.0
}
})
except Exception as e:
logger.error(f"Analytics query failed: {e}")
raise APIException(f"Failed to retrieve analytics: {str(e)}")





import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class AnalyticsDashboardScreen extends StatefulWidget {
@override
_AnalyticsDashboardScreenState createState() => _AnalyticsDashboardScreenState();
}

class _AnalyticsDashboardScreenState extends State<AnalyticsDashboardScreen> {
Map<String, dynamic> analyticsData = {};

@override
void initState() {
super.initState();
fetchAnalytics();
}

Future<void> fetchAnalytics() async {
final response = await http.get(Uri.parse('https://api.bidbeez.com/skillsync/analytics/provider_stats'));
if (response.statusCode == 200) {
setState(() {
analyticsData = jsonDecode(response.body);
});
}
}

@override
Widget build(BuildContext context) {
final availability = analyticsData['availability'] ?? {};
final verification = analyticsData['verification'] ?? {};
return Scaffold(
appBar: AppBar(title: Text('SkillSync Analytics')),
body: ListView(
children: [
Padding(
padding: EdgeInsets.all(16.0),
child: Text('Provider Availability', style: TextStyle(fontWeight: FontWeight.bold)),
),
// Chart.js widget (simulated)
Container(
height: 200,
child: Text('Bar Chart: ${availability['labels']?.join(', ')} - ${availability['counts']?.join(', ')}'),
),
Padding(
padding: EdgeInsets.all(16.0),
child: Text('Verification Status', style: TextStyle(fontWeight: FontWeight.bold)),
),
Container(
height: 200,
child: Text('Pie Chart: ${verification['labels']?.join(', ')} - ${verification['counts']?.join(', ')}'),
),
Padding(
padding: EdgeInsets.all(16.0),
child: Text('Average License Fee: R${analyticsData['pricing_trends']?['average_license_fee']?.toStringAsFixed(2) ?? 'N/A'}'),
),
],
),
);
}
}




AGENT UPDATE


async def notify_bidder(self, ctx: RunContext[AgentDependencies], tender_id: str, doc_metadata: DocumentMetadata):
try:
tender_data = doc_metadata.tender_data
pitfalls = doc_metadata.pitfalls or []
success_prediction = doc_metadata.success_prediction
skillsync_recruitment = next((r for r in doc_metadata.module_recruitments if r.module == "SkillSync"), None)

# Fetch analytics
async with httpx.AsyncClient() as client:
response = await client.get('https://api.bidbeez.com/skillsync/analytics/provider_stats')
analytics = response.json() if response.status_code == 200 else {}

subject = f"Tender Analysis: {tender_data.title} ({tender_id})"
message = f"""
<html>
<body>
<h2>Tender Analysis for {tender_data.title}</h2>
<h3>Success Prediction</h3>
<p>Success Rate: {success_prediction.success_rate:.1f}% (Confidence: {success_prediction.confidence:.2f})</p>
<p><b>Recommendations:</b></p>
<ul>
{''.join(f'<li>{r}</li>' for r in success_prediction.recommendations)}
</ul>
<h3>Critical Pitfalls</h3>
<ul>
{''.join(f'<li><b>{p.name}</b>: {p.description} (Severity: {p.severity})<br>Mitigation: {p.mitigation}</li>'
for p in pitfalls if p.severity in ['critical', 'high'])}
</ul>
<h3>SkillSync Recruitment</h3>
<p>{len(skillsync_recruitment.recruited_users if skillsync_recruitment else 0)} providers recruited.</p>
<ul>
{''.join(f'<li>{u['name']} (Skills: {', '.join(u['skills'])})</li>'
for u in skillsync_recruitment.recruited_users if skillsync_recruitment)}
</ul>
<h3>SkillSync Analytics</h3>
<p>Available Providers: {analytics.get('availability', {}).get('counts', [0])[0]}</p>
<p>Average License Fee: R{analytics.get('pricing_trends', {}).get('average_license_fee', 0.0):.2f}</p>
<p>View full analytics in the Bidbeez app.</p>
</body>
</html>
"""

await self._send_email('<EMAIL>', subject, message)
await self._send_flutter_notification(ctx.deps, 'bidder_device_token', subject,
f"New tender analysis: {tender_data.title}. Check SkillSync analytics in app.")

except Exception as e:
self.logger.error(f"Bidder notification failed: {e}")
raise



SKILLSYNC RECRUITMENT AGENT


recruitment_agent:
mongodb:
url: "mongodb://localhost:27017"
database: "bidbeez"
collections:
recruitment_logs: "recruitment_logs"
skillsync_profiles: "skillsync_users"
skillsync_verifications: "skillsync_verifications"
skillsync_licenses: "licenses"
audit_logs: "audit_logs"
redis:
url: "redis://localhost:6379"
external_sources:
indeed:
api_key: "${INDEED_API_KEY}"
linkedin:
api_key: "${LINKEDIN_API_KEY}"
saqa:
api_key: "${SAQA_API_KEY}"
nlp:
model: "distilbert-base-uncased"
blockchain:
provider_url: "${BLOCKCHAIN_PROVIDER_URL}"
contract_abi: "${LICENSE_CONTRACT_ABI}"
wallet: "${BLOCKCHAIN_WALLET}"
notifications:
firebase_key: "${FIREBASE_API_KEY}"
websocket:
host: "localhost"
port: 8765
tenant_id: "${TENANT_ID}"




import asyncio
import logging
import yaml
from datetime import datetime
from typing import List, Dict, Any
from pydantic import BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.models import OpenAIModel
import motor.motor_asyncio
import redis.asyncio as redis
from .sourcing import SkillSourcingService
from .matching import SkillMatchingService
from .verification import VerificationService
from .onboarding import OnboardingService
from .blockchain import BlockchainService
from .analytics import RecruitmentAnalyticsService
from .notifications import NotificationService

logger = logging.getLogger(__name__)

class AgentDependencies(BaseModel):
mongodb_client: motor.motor_asyncio.AsyncIOMotorClient
redis_client: redis.Redis
config: Dict[str, Any]

class RecruitmentAgent:
def __init__(self, config_path: str = "recruitment_agent/config.yaml"):
self.config = self._load_config(config_path)
self.dependencies = self._initialize_dependencies()

self.agent = Agent(
model=OpenAIModel("gpt-4-turbo-preview"),
deps_type=AgentDependencies,
result_type=Dict[str, Any],
system_prompt="""You are a recruitment agent for Bidbeez's SkillSync module,
specializing in sourcing, verifying, and onboarding skill providers
for tenders. Use QueenBee insights to align recruitment with tender
requirements, automate verifications, deploy smart contracts, and
provide analytics. Ensure compliance with South African regulations."""
)

self.sourcing = SkillSourcingService(self.config)
self.matching = SkillMatchingService(self.config)
self.verification = VerificationService(self.config)
self.onboarding = OnboardingService(self.config)
self.blockchain = BlockchainService(self.config)
self.analytics = RecruitmentAnalyticsService(self.config)
self.notifications = NotificationService(self.config)

def _load_config(self, config_path: str) -> Dict[str, Any]:
with open(config_path, 'r') as f:
return yaml.safe_load(f)

def _initialize_dependencies(self) -> AgentDependencies:
mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(self.config['recruitment_agent']['mongodb']['url'])
redis_client = redis.from_url(self.config['recruitment_agent']['redis']['url'])
return AgentDependencies(
mongodb_client=mongodb_client,
redis_client=redis_client,
config=self.config
)

@agent.tool
async def recruit_for_tender(self, ctx: RunContext[AgentDependencies],
tender_id: str, requirements: List[str],
pitfalls: List[Dict[str, Any]]) -> Dict[str, Any]:
"""Recruit skill providers for a tender based on requirements and pitfalls."""
try:
db = ctx.deps.mongodb_client[self.config['recruitment_agent']['mongodb']['database']]

# Fetch QueenBee insights
insights = await db['queenbee_insights'].find({'tender_id': tender_id}).to_list(None)
parsed_requirements = await db['parsed_requirements'].find({'tender_id': tender_id}).to_list(None)
criteria = requirements + [r['text'] for r in parsed_requirements] + [
p['related_requirements'] for p in pitfalls if 'qualification' in p['description'].lower()
]

# Source candidates
candidates = await self.sourcing.source_candidates(criteria)

# Match candidates
matched_providers = await self.matching.match_skills(criteria, candidates)

# Verify qualifications
verified_providers = []
for provider in matched_providers:
verification_result = await self.verification.verify_provider(provider)
if verification_result['success']:
verified_providers.append(provider)

# Onboard providers
onboarded_providers = []
for provider in verified_providers:
onboard_result = await self.onboarding.onboard_provider(provider, tender_id)
if onboard_result['success']:
# Deploy smart contract
license = onboard_result['license']
contract_id = await self.blockchain.deploy_license_contract(license)
license['smart_contract_id'] = contract_id
await db['skillsync_licenses'].update_one(
{'_id': license['id']},
{'$set': {'smart_contract_id': contract_id, 'status': 'active'}}
)
onboarded_providers.append(provider)

# Update analytics
await self.analytics.update_metrics(tender_id, onboarded_providers, criteria)

# Notify stakeholders
await self.notifications.notify_bidder(tender_id, onboarded_providers)
await self.notifications.notify_providers(onboarded_providers)

# Log recruitment
await db['recruitment_logs'].insert_one({
'tender_id': tender_id,
'providers': [p['id'] for p in onboarded_providers],
'criteria': criteria,
'timestamp': datetime.now()
})

return {
'status': 'success',
'recruited_providers': onboarded_providers,
'gaps': [c for c in criteria if not any(c in p['skills'] for p in onboarded_providers)]
}
except Exception as e:
logger.error(f"Recruitment failed for tender {tender_id}: {e}")
raise



from transformers import pipeline
import numpy as np
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class SkillMatchingService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.nlp_model = pipeline("feature-extraction", model=config['nlp']['model'])

async def match_skills(self, criteria: List[str], candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
"""Match candidates to criteria using NLP."""
try:
matched_providers = []
for candidate in candidates:
score = 0.0
for criterion in criteria:
for skill in candidate.get('skills', []):
emb1 = self.nlp_model(criterion, return_tensors="pt")[0].mean(dim=1).numpy()
emb2 = self.nlp_model(skill, return_tensors="pt")[0].mean(dim=1).numpy()
sim = float(np.dot(emb1, emb2.T) / (np.linalg.norm(emb1) * np.linalg.norm(emb2)))
score = max(score, sim)
for qual in candidate.get('qualifications', []):
emb1 = self.nlp_model(criterion, return_tensors="pt")[0].mean(dim=1).numpy()
emb2 = self.nlp_model(qual.get('name', ''), return_tensors="pt")[0].mean(dim=1).numpy()
sim = float(np.dot(emb1, emb2.T) / (np.linalg.norm(emb1) * np.linalg.norm(emb2)))
score = max(score, sim)
if score > 0.7:
candidate['match_score'] = score
matched_providers.append(candidate)

logger.info(f"Matched {len(matched_providers)} providers for criteria: {criteria}")
return sorted(matched_providers, key=lambda x: x['match_score'], reverse=True)
except Exception as e:
logger.error(f"Skill matching failed: {e}")
raise




import httpx
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class SkillSourcingService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def source_candidates(self, criteria: List[str]) -> List[Dict[str, Any]]:
"""Source candidates from external platforms and internal database."""
try:
candidates = []

# Query Indeed
indeed_response = await self.http_client.get(
"https://api.indeed.com/v2/search",
params={"q": ", ".join(criteria), "key": self.config['external_sources']['indeed']['api_key']}
)
candidates.extend([
{
'id': f"indeed_{hash(job['id'])}",
'user_id': f"user_{hash(job['id'])}",
'skills': job['title'].split(),
'qualifications': [{'name': job['description'][:200], 'issuing_authority': 'Unknown'}],
'source': 'indeed'
}
for job in indeed_response.json().get('jobs', [])
])

# Query internal database
db = motor.motor_asyncio.AsyncIOMotorClient(self.config['mongodb']['url'])[self.config['mongodb']['database']]
internal_providers = await db['skillsync_profiles'].find({
'tenant_id': self.config['tenant_id'],
'skills': {'$in': [re.compile(c, re.IGNORECASE) for c in criteria]}
}).to_list(None)
candidates.extend(internal_providers)

logger.info(f"Sourced {len(candidates)} candidates for criteria: {criteria}")
return candidates
except Exception as e:
logger.error(f"Sourcing failed: {e}")
raise




import httpx
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class VerificationService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def verify_provider(self, provider: Dict[str, Any]) -> Dict[str, Any]:
"""Verify provider qualifications with SAQA."""
try:
db = motor.motor_asyncio.AsyncIOMotorClient(self.config['mongodb']['url'])[self.config['mongodb']['database']]
for qual in provider.get('qualifications', []):
response = await self.http_client.post(
"https://api.saqa.org.za/verify",
headers={"Authorization": f"Bearer {self.config['external_sources']['saqa']['api_key']}"},
json={
"qualification_name": qual['name'],
"issuing_authority": qual.get('issuing_authority', 'Unknown')
}
)
result = response.json()
if result.get('verified', False):
qual['is_verified'] = True
await db['skillsync_verifications'].insert_one({
'skill_provider_id': provider['id'],
'qualification_id': qual['id'],
'verification_type': 'qualification',
'status': 'completed',
'notes': result.get('message', ''),
'tenant_id': self.config['tenant_id']
})

provider['verification_status'] = 'verified' if all(q.get('is_verified', False) for q in provider.get('qualifications', [])) else 'pending'
await db['skillsync_profiles'].update_one(
{'_id': provider['id']},
{'$set': {'verification_status': provider['verification_status']}}
)

logger.info(f"Verified provider {provider['id']}: {provider['verification_status']}")
return {'success': provider['verification_status'] == 'verified', 'provider': provider}
except Exception as e:
logger.error(f"Verification failed for provider {provider['id']}: {e}")
return {'success': False, 'error': str(e)}




import logging
from typing import Dict, Any
from skillsync.services.pricing import PricingService

logger = logging.getLogger(__name__)

class OnboardingService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.pricing = PricingService(config['tenant_id'])

async def onboard_provider(self, provider: Dict[str, Any], tender_id: str) -> Dict[str, Any]:
"""Onboard a provider, set pricing, and create license."""
try:
db = motor.motor_asyncio.AsyncIOMotorClient(self.config['mongodb']['url'])[self.config['mongodb']['database']]

# Set pricing
pricing_result = await self.pricing.suggest_hourly_rate(provider)
provider['hourly_rate'] = pricing_result['rate']
await db['skillsync_profiles'].update_one(
{'_id': provider['id']},
{'$set': {'hourly_rate': provider['hourly_rate']}}
)

# Create license
license = {
'id': f"license_{hash(provider['id'])}",
'skill_provider_id': provider['id'],
'tender_id': tender_id,
'licensing_bidder_id': None, # Placeholder
'start_date': datetime.now(),
'license_fee': await self.pricing.suggest_license_fee(provider),
'status': 'pending_payment',
'tenant_id': self.config['tenant_id']
}
await db['skillsync_licenses'].insert_one(license)

# Create QueenBee prompt for XP
await db['queenbee_prompts'].insert_one({
'tender_id': tender_id,
'message': f"Provider {provider['id']} onboarded for tender {tender_id}",
'trigger_event': 'provider_onboarded',
'created_at': datetime.now(),
'resolved': False,
'tenant_id': self.config['tenant_id']
})

logger.info(f"Onboarded provider {provider['id']} for tender {tender_id}")
return {'success': True, 'provider': provider, 'license': license}
except Exception as e:
logger.error(f"Onboarding failed for provider {provider['id']}: {e}")
return {'success': False, 'error': str(e)}




from web3 import Web3
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class BlockchainService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.w3 = Web3(Web3.HTTPProvider(config['blockchain']['provider_url']))

async def deploy_license_contract(self, license: Dict[str, Any]) -> str:
"""Deploy smart contract for a license."""
try:
contract = self.w3.eth.contract(
abi=self.config['blockchain']['contract_abi'],
bytecode=self.config['blockchain']['contract_bytecode']
)
tx = contract.constructor(
license_id=license['id'],
provider=license['skill_provider_id'],
bidder=license['licensing_bidder_id'] or "0x0",
fee=int(license['license_fee'] * 100)
).transact({'from': self.config['blockchain']['wallet']})
receipt = self.w3.eth.wait_for_transaction_receipt(tx)

logger.info(f"Deployed smart contract for license {license['id']}: {receipt.contractAddress}")
return receipt.contractAddress
except Exception as e:
logger.error(f"Smart contract deployment failed for license {license['id']}: {e}")
raise





import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class RecruitmentAnalyticsService:
def __init__(self, config: Dict[str, Any]):
self.config = config

async def update_metrics(self, tender_id: str, providers: List[Dict[str, Any]], criteria: List[str]):
"""Update recruitment metrics."""
try:
db = motor.motor_asyncio.AsyncIOMotorClient(self.config['mongodb']['url'])[self.config['mongodb']['database']]
await db['recruitment_logs'].insert_one({
'tender_id': tender_id,
'provider_count': len(providers),
'criteria_count': len(criteria),
'gaps': [c for c in criteria if not any(c in p['skills'] for p in providers)],
'timestamp': datetime.now(),
'tenant_id': self.config['tenant_id']
})

logger.info(f"Updated recruitment metrics for tender {tender_id}")
except Exception as e:
logger.error(f"Analytics update failed: {e}")
raise




import httpx
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class NotificationService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def notify_bidder(self, tender_id: str, providers: List[Dict[str, Any]]):
"""Notify bidder of recruited providers."""
try:
payload = {
"to": "bidder_device_token",
"notification": {
"title": f"SkillSync Recruitment for Tender {tender_id}",
"body": f"{len(providers)} providers recruited."
}
}
await self.http_client.post(
"https://fcm.googleapis.com/fcm/send",
headers={"Authorization": f"key={self.config['notifications']['firebase_key']}"},
json=payload
)
logger.info(f"Notified bidder for tender {tender_id}")
except Exception as e:
logger.error(f"Bidder notification failed: {e}")
raise

async def notify_providers(self, providers: List[Dict[str, Any]]):
"""Notify providers of onboarding."""
try:
for provider in providers:
payload = {
"to": f"provider_{provider['id']}_device_token",
"notification": {
"title": "Join Bidbeez SkillSync",
"body": "You’ve been recruited for a tender opportunity. Complete your profile."
}
}
await self.http_client.post(
"https://fcm.googleapis.com/fcm/send",
headers={"Authorization": f"key={self.config['notifications']['firebase_key']}"},
json=payload
)
logger.info(f"Notified {len(providers)} providers")
except Exception as e:
logger.error(f"Provider notification failed: {e}")
raise




recruitment_agent/
├── __init__.py
├── config.yaml # Configuration for APIs, NLP, blockchain
├── agent.py # Core agent orchestration
├── sourcing.py # External and internal candidate sourcing
├── matching.py # NLP-based skill matching
├── verification.py # SAQA and other API verifications
├── onboarding.py # Pricing, licensing, and onboarding
├── blockchain.py # Smart contract deployment
├── analytics.py # Recruitment metrics and dashboards
├── notifications.py # Email, Flutter, WebSocket notifications
├── compliance.py # B-BBEE and regulatory compliance
├── tasks.py # Celery tasks for async processing
├── retry.py # Retry logic for API calls
├── tests/
│ ├── test_sourcing.py
│ ├── test_matching.py
│ ├── test_verification.py
│ ├── test_onboarding.py
│ ├── test_compliance.py
│ └── test_analytics.py
├── logging_config.py # Structured logging
└── utils.py # Helper functions (e.g., B-BBEE scoring)






recruitment_agent:
mongodb:
url: "mongodb://localhost:27017"
database: "bidbeez"
collections:
recruitment_logs: "recruitment_logs"
skillsync_profiles: "skillsync_users"
skillsync_verifications: "skillsync_verifications"
skillsync_licenses: "licenses"
audit_logs: "audit_logs"
queenbee_insights: "queenbee_insights"
parsed_requirements: "parsed_requirements"
queenbee_prompts: "queenbee_prompts"
redis:
url: "redis://localhost:6379"
external_sources:
indeed:
api_key: "${INDEED_API_KEY}"
linkedin:
api_key: "${LINKEDIN_API_KEY}"
saqa:
api_key: "${SAQA_API_KEY}"
nlp:
model: "distilbert-base-uncased"
blockchain:
provider_url: "${BLOCKCHAIN_PROVIDER_URL}"
contract_abi: "${LICENSE_CONTRACT_ABI}"
wallet: "${BLOCKCHAIN_WALLET}"
notifications:
firebase_key: "${FIREBASE_API_KEY}"
websocket:
host: "localhost"
port: 8765
compliance:
bbbee:
required_level: 2
weight: 0.3
retry:
max_attempts: 3
backoff_factor: 2
tenant_id: "${TENANT_ID}"


import asyncio
import logging
import yaml
from datetime import datetime
from typing import List, Dict, Any
from pydantic import BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.models import OpenAIModel
import motor.motor_asyncio
import redis.asyncio as redis
from .sourcing import SkillSourcingService
from .matching import SkillMatchingService
from .verification import VerificationService
from .onboarding import OnboardingService
from .blockchain import BlockchainService
from .analytics import RecruitmentAnalyticsService
from .notifications import NotificationService
from .compliance import ComplianceService
from .retry import retry_async
from .utils import calculate_bbbee_score

logger = logging.getLogger(__name__)

class AgentDependencies(BaseModel):
mongodb_client: motor.motor_asyncio.AsyncIOMotorClient
redis_client: redis.Redis
config: Dict[str, Any]

class RecruitmentAgent:
def __init__(self, config_path: str = "recruitment_agent/config.yaml"):
self.config = self._load_config(config_path)
self.dependencies = self._initialize_dependencies()

self.agent = Agent(
model=OpenAIModel("gpt-4-turbo-preview"),
deps_type=AgentDependencies,
result_type=Dict[str, Any],
system_prompt="""You are a recruitment agent for Bidbeez's SkillSync module,
specializing in sourcing, verifying, and onboarding skill providers
for tenders. Use QueenBee insights to prioritize recruitment, ensure
B-BBEE compliance, automate SAQA verifications, deploy smart contracts,
and provide analytics. Deliver efficient, compliant, and transparent
recruitment aligned with South African tender requirements."""
)

self.sourcing = SkillSourcingService(self.config)
self.matching = SkillMatchingService(self.config)
self.verification = VerificationService(self.config)
self.onboarding = OnboardingService(self.config)
self.blockchain = BlockchainService(self.config)
self.analytics = RecruitmentAnalyticsService(self.config)
self.notifications = NotificationService(self.config)
self.compliance = ComplianceService(self.config)

def _load_config(self, config_path: str) -> Dict[str, Any]:
with open(config_path, 'r') as f:
return yaml.safe_load(f)

def _initialize_dependencies(self) -> AgentDependencies:
mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(self.config['recruitment_agent']['mongodb']['url'])
redis_client = redis.from_url(self.config['recruitment_agent']['redis']['url'])
return AgentDependencies(
mongodb_client=mongodb_client,
redis_client=redis_client,
config=self.config
)

@agent.tool
async def recruit_for_tender(self, ctx: RunContext[AgentDependencies],
tender_id: str, requirements: List[str],
pitfalls: List[Dict[str, Any]]) -> Dict[str, Any]:
"""Recruit skill providers for a tender, using QueenBee insights."""
try:
db = ctx.deps.mongodb_client[self.config['recruitment_agent']['mongodb']['database']]

# Fetch QueenBee insights and requirements
insights = await db['queenbee_insights'].find({'tender_id': tender_id}).to_list(None)
parsed_requirements = await db['parsed_requirements'].find({'tender_id': tender_id}).to_list(None)
criteria = requirements + [r['text'] for r in parsed_requirements]
for insight in insights:
if 'skill' in insight['key'].lower() or 'qualification' in insight['key'].lower():
criteria.append(insight['value'])
for pitfall in pitfalls:
if 'qualification' in pitfall['description'].lower():
criteria.extend(pitfall['related_requirements'])

# Source candidates
candidates = await retry_async(
self.sourcing.source_candidates, criteria,
max_attempts=self.config['retry']['max_attempts'],
backoff_factor=self.config['retry']['backoff_factor']
)

# Match candidates
matched_providers = await self.matching.match_skills(criteria, candidates)

# Check B-BBEE compliance
compliant_providers = []
for provider in matched_providers:
compliance_score = await self.compliance.evaluate_bbbee(provider)
if compliance_score >= self.config['compliance']['bbbee']['required_level']:
provider['bbbee_score'] = compliance_score
compliant_providers.append(provider)

# Verify qualifications
verified_providers = []
for provider in compliant_providers:
verification_result = await retry_async(
self.verification.verify_provider, provider,
max_attempts=self.config['retry']['max_attempts'],
backoff_factor=self.config['retry']['backoff_factor']
)
if verification_result['success']:
verified_providers.append(verification_result['provider'])

# Onboard providers
onboarded_providers = []
for provider in verified_providers:
onboard_result = await self.onboarding.onboard_provider(provider, tender_id)
if onboard_result['success']:
license = onboard_result['license']
# Deploy smart contract
contract_id = await self.blockchain.deploy_license_contract(license)
license['smart_contract_id'] = contract_id
await db['skillsync_licenses'].update_one(
{'_id': license['id']},
{'$set': {'smart_contract_id': contract_id, 'status': 'active'}}
)
# Anchor on blockchain
await db['tender_anchor_status'].update_one(
{'tender_id': tender_id},
{'$set': {'polygon_hash': contract_id, 'anchored_at': datetime.now()}},
upsert=True
)
onboarded_providers.append(provider)

# Create QueenBee prompt and XP trigger
if onboarded_providers:
prompt_id = f"prompt_{tender_id}_{hash(str(criteria))}"
await




import asyncio
import logging
import yaml
from datetime import datetime
from typing import List, Dict, Any
from pydantic import BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.models import OpenAIModel
import motor.motor_asyncio
import redis.asyncio as redis
from .sourcing import SkillSourcingService
from .matching import SkillMatchingService
from .verification import VerificationService
from .onboarding import OnboardingService
from .blockchain import BlockchainService
from .analytics import RecruitmentAnalyticsService
from .notifications import NotificationService
from .compliance import ComplianceService
from .retry import retry_async
from .utils import calculate_bbbee_score

logger = logging.getLogger(__name__)

class AgentDependencies(BaseModel):
mongodb_client: motor.motor_asyncio.AsyncIOMotorClient
redis_client: redis.Redis
config: Dict[str, Any]

class RecruitmentAgent:
def __init__(self, config_path: str = "recruitment_agent/config.yaml"):
self.config = self._load_config(config_path)
self.dependencies = self._initialize_dependencies()

self.agent = Agent(
model=OpenAIModel("gpt-4-turbo-preview"),
deps_type=AgentDependencies,
result_type=Dict[str, Any],
system_prompt="""You are a recruitment agent for Bidbeez's SkillSync module,
specializing in sourcing, verifying, and onboarding skill providers
for tenders. Use QueenBee insights to prioritize recruitment, ensure
B-BBEE compliance, automate SAQA verifications, deploy smart contracts,
and provide analytics. Deliver efficient, compliant, and transparent
recruitment aligned with South African tender requirements."""
)

self.sourcing = SkillSourcingService(self.config)
self.matching = SkillMatchingService(self.config)
self.verification = VerificationService(self.config)
self.onboarding = OnboardingService(self.config)
self.blockchain = BlockchainService(self.config)
self.analytics = RecruitmentAnalyticsService(self.config)
self.notifications = NotificationService(self.config)
self.compliance = ComplianceService(self.config)

def _load_config(self, config_path: str) -> Dict[str, Any]:
with open(config_path, 'r') as f:
return yaml.safe_load(f)

def _initialize_dependencies(self) -> AgentDependencies:
mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(self.config['recruitment_agent']['mongodb']['url'])
redis_client = redis.from_url(self.config['recruitment_agent']['redis']['url'])
return AgentDependencies(
mongodb_client=mongodb_client,
redis_client=redis_client,
config=self.config
)

@agent.tool
async def recruit_for_tender(self, ctx: RunContext[AgentDependencies],
tender_id: str, requirements: List[str],
pitfalls: List[Dict[str, Any]]) -> Dict[str, Any]:
"""Recruit skill providers for a tender, using QueenBee insights."""
try:
db = ctx.deps.mongodb_client[self.config['recruitment_agent']['mongodb']['database']]

# Fetch QueenBee insights and requirements
insights = await db['queenbee_insights'].find({'tender_id': tender_id}).to_list(None)
parsed_requirements = await db['parsed_requirements'].find({'tender_id': tender_id}).to_list(None)
criteria = requirements + [r['text'] for r in parsed_requirements]
for insight in insights:
if 'skill' in insight['key'].lower() or 'qualification' in insight['key'].lower():
criteria.append(insight['value'])
for pitfall in pitfalls:
if 'qualification' in pitfall['description'].lower():
criteria.extend(pitfall['related_requirements'])

# Source candidates
candidates = await retry_async(
self.sourcing.source_candidates, criteria,
max_attempts=self.config['retry']['max_attempts'],
backoff_factor=self.config['retry']['backoff_factor']
)

# Match candidates
matched_providers = await self.matching.match_skills(criteria, candidates)

# Check B-BBEE compliance
compliant_providers = []
for provider in matched_providers:
compliance_score = await self.compliance.evaluate_bbbee(provider)
if compliance_score >= self.config['compliance']['bbbee']['required_level']:
provider['bbbee_score'] = compliance_score
compliant_providers.append(provider)

# Verify qualifications
verified_providers = []
for provider in compliant_providers:
verification_result = await retry_async(
self.verification.verify_provider, provider,
max_attempts=self.config['retry']['max_attempts'],
backoff_factor=self.config['retry']['backoff_factor']
)
if verification_result['success']:
verified_providers.append(verification_result['provider'])

# Onboard providers
onboarded_providers = []
for provider in verified_providers:
onboard_result = await self.onboarding.onboard_provider(provider, tender_id)
if onboard_result['success']:
license = onboard_result['license']
# Deploy smart contract
contract_id = await self.blockchain.deploy_license_contract(license)
license['smart_contract_id'] = contract_id
await db['skillsync_licenses'].update_one(
{'_id': license['id']},
{'$set': {'smart_contract_id': contract_id, 'status': 'active'}}
)
# Anchor on blockchain
await db['tender_anchor_status'].update_one(
{'tender_id': tender_id},
{'$set': {'polygon_hash': contract_id, 'anchored_at': datetime.now()}},
upsert=True
)
onboarded_providers.append(provider)

# Create QueenBee prompt and XP trigger
if onboarded_providers:
prompt_id = f"prompt_{tender_id}_{hash(str(criteria))}"
await db['queenbee_prompts'].insert_one({
'tender_id': tender_id,
'message': f"Recruited {len(onboarded_providers)} providers for tender {tender_id}",
'trigger_event': 'provider_onboarded',
'created_at': datetime.now(),
'resolved': False,
'tenant_id': self.config['tenant_id']
})
for provider in onboarded_providers:
await db['prompt_xp_triggers'].insert_one({
'prompt_id': prompt_id,
'user_id': provider['user_id'],
'xp_awarded': 50,
'reason': 'Completed SkillSync onboarding',
'awarded_at': datetime.now()
})

# Update analytics
await self.analytics.update_metrics(tender_id, onboarded_providers, criteria)

# Notify stakeholders
await self.notifications.notify_bidder(tender_id, onboarded_providers)
await self.notifications.notify_providers(onboarded_providers)

# Log recruitment
await db['recruitment_logs'].insert_one({
'tender_id': tender_id,
'providers': [p['id'] for p in onboarded_providers],
'criteria': criteria,
'gaps': [c for c in criteria if not any(c.lower() in p['skills'].lower() for p in onboarded_providers)],
'timestamp': datetime.now(),
'tenant_id': self.config['tenant_id']
})

# Create fallback BeeTask for gaps
gaps = [c for c in criteria if not any(c.lower() in p['skills'].lower() for p in onboarded_providers)]
if gaps:
task_id = f"task_{tender_id}_{hash(str(gaps))}"
await db['auto_created_bee_tasks'].insert_one({
'tender_id': tender_id,
'task_id': task_id,
'description': f"Manually recruit providers for: {', '.join(gaps)}",
'triggered_by': 'recruitment_gap',
'urgency_level': 'high',
'created_at': datetime.now(),
'tenant_id': self.config['tenant_id']
})

return {
'status': 'success',
'recruited_providers': onboarded_providers,
'gaps': gaps
}
except Exception as e:
logger.error(f"Recruitment failed for tender {tender_id}: {e}")
await db['recruitment_logs'].insert_one({
'tender_id': tender_id,
'status': 'failed',
'error': str(e),
'timestamp': datetime.now(),
'tenant_id': self.config['tenant_id']
})
raise




import logging
from typing import Dict, Any
from .utils import calculate_bbbee_score

logger = logging.getLogger(__name__)

class ComplianceService:
def __init__(self, config: Dict[str, Any]):
self.config = config

async def evaluate_bbbee(self, provider: Dict[str, Any]) -> float:
"""Evaluate provider’s B-BBEE compliance."""
try:
# Placeholder: Query B-BBEE status from profile or external API
bbbee_level = provider.get('bbbee_level', 4) # Default to Level 4
score = calculate_bbbee_score(bbbee_level)

logger.info(f"B-BBEE score for provider {provider['id']}: {score}")
return score
except Exception as e:
logger.error(f"B-BBEE evaluation failed for provider {provider['id']}: {e}")
return 0.0



import asyncio
import logging
from typing import Callable, Any

logger = logging.getLogger(__name__)

async def retry_async(func: Callable, *args, max_attempts: int = 3, backoff_factor: float = 2, **kwargs) -> Any:
"""Retry an async function with exponential backoff."""
attempt = 1
while attempt <= max_attempts:
try:
return await func(*args, **kwargs)
except Exception as e:
if attempt == max_attempts:
logger.error(f"Failed after {max_attempts} attempts: {e}")
raise
wait = backoff_factor ** attempt
logger.warning(f"Attempt {attempt} failed: {e}. Retrying in {wait}s...")
await asyncio.sleep(wait)
attempt += 1





def calculate_bbbee_score(level: int) -> float:
"""Calculate B-BBEE compliance score (1-8 scale)."""
score_map = {1: 1.0, 2: 0.9, 3: 0.8, 4: 0.7, 5: 0.6, 6: 0.5, 7: 0.4, 8: 0.3}
return score_map.get(level, 0.0)



import 'package:flutter/material.dart';
import 'package:web_socket_channel/io.dart';
import 'dart:convert';

class BidderDashboardScreen extends StatefulWidget {
@override
_BidderDashboardScreenState createState() => _BidderDashboardScreenState();
}

class _BidderDashboardScreenState extends State<BidderDashboardScreen> {
final channel = IOWebSocketChannel.connect('ws://localhost:8765');
Map<String, dynamic> tenderData = {};

@override
void initState() {
super.initState();
channel.stream.listen((data) {
setState(() {
tenderData = jsonDecode(data);
});
});
}

@override
Widget build(BuildContext context) {
final pitfalls = tenderData['pitfalls'] ?? [];
final successPrediction = tenderData['success_prediction'] ?? {};
final skillsyncRecruitment = (tenderData['module_recruitments'] ?? []).firstWhere(
(r) => r['module'] == 'SkillSync', orElse: () => {'recruited_users': [], 'gaps': []}
);

return Scaffold(
appBar: AppBar(title: Text('Tender Dashboard')),
body: ListView(
children: [
ListTile(
title: Text('Success Rate: ${successPrediction['success_rate']?.toStringAsFixed(1) ?? 'N/A'}%'),
subtitle: Text('Recommendations: ${(successPrediction['recommendations'] ?? []).join(', ')}'),
),
Padding(
padding: EdgeInsets.all(16.0),
child: Text('Critical Pitfalls', style: TextStyle(fontWeight: FontWeight.bold)),
),
...pitfalls.where((p) => ['critical', 'high'].contains(p['severity'])).map((p) => ListTile(
title: Text('${p['name']}: ${p['description']}'),
subtitle: Text('Mitigation: ${p['mitigation']}'),
)),
Padding(
padding: EdgeInsets.all(16.0),
child: Text('SkillSync Recruitment', style: TextStyle(fontWeight: FontWeight.bold)),
),
...skillsyncRecruitment['recruited_users'].map((u) => ListTile(
title: Text('${u['name']} (Skills: ${u['skills'].join(', ')})'),
subtitle: Text('B-BBEE Score: ${u['bbbee_score']?.toStringAsFixed(2) ?? 'N/A'}'),
)),
if (skillsyncRecruitment['gaps'].isNotEmpty)
ListTile(
title: Text('Recruitment Gaps: ${skillsyncRecruitment['gaps'].join(', ')}'),
subtitle: Text('BeeTask created for manual recruitment'),
),
],
),
);
}

@override
void dispose() {
channel.sink.close();
super.dispose();
}
}



import logging
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from django.core.cache import cache
from typing import List, Dict, Any
import motor.motor_asyncio
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class PredictiveRecruitmentService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.model = None
self.label_encoder = LabelEncoder()
self.db = motor.motor_asyncio.AsyncIOMotorClient(config['recruitment_agent']['mongodb']['url'])[
config['recruitment_agent']['mongodb']['database']
]

async def train_model(self):
"""Train a model to predict skill demand based on QueenBee insights."""
try:
# Fetch historical insights and requirements (last 6 months)
insights = await self.db['queenbee_insights'].find({
'generated_at': {'$gte': datetime.now() - timedelta(days=180)},
'tenant_id': self.config['recruitment_agent']['tenant_id']
}).to_list(None)
requirements = await self.db['parsed_requirements'].find({
'tender_id': {'$in': [i['tender_id'] for i in insights]},
'tenant_id': self.config['recruitment_agent']['tenant_id']
}).to_list(None)

# Prepare features: tender volume, skill mentions, urgency
X, y = [], []
for insight in insights:
skill_mentions = sum(1 for r in requirements if any(
keyword in r['text'].lower() for keyword in ['electrician', 'engineer', 'plumber']
))
features = [
len([i for i in insights if i['tender_id'] == insight['tender_id']]), # Tender volume
skill_mentions, # Skill mentions
insight['confidence'], # Insight confidence
1 if 'urgent' in insight['value'].lower() else 0 # Urgency flag
]
X.append(features)
# Target: Primary skill category
skill = next((r['text'] for r in requirements if 'skill' in r['text'].lower()), 'other')
y.append(skill.split()[0] if skill != 'other' else 'other')

if not X or not y:
logger.warning("Insufficient data to train model")
return

# Encode labels
y_encoded = self.label_encoder.fit_transform(y)
self.model = RandomForestClassifier(n_estimators=100, random_state=42)
self.model.fit(X, y_encoded)

# Cache model
cache_key = f"predictive_model_{self.config['recruitment_agent']['tenant_id']}"
cache.set(cache_key, self.model, 86400) # Cache for 1 day
logger.info(f"Trained predictive recruitment model for tenant {self.config['recruitment_agent']['tenant_id']}")
except Exception as e:
logger.error(f"Model training failed: {e}")
raise

async def predict_skill_demand(self, tender_id: str) -> List[str]:
"""Predict skill demand for a tender."""
try:
if not self.model:
self.model = cache.get(f"predictive_model_{self.config['recruitment_agent']['tenant_id']}")
if not self.model:
await self.train_model()

insights = await self.db['queenbee_insights'].find({'tender_id': tender_id}).to_list(None)
requirements = await self.db['parsed_requirements'].find({'tender_id': tender_id}).to_list(None)

features = [
len(insights), # Tender volume
sum(1 for r in requirements if any(
keyword in r['text'].lower() for keyword in ['electrician', 'engineer', 'plumber']
)), # Skill mentions
sum(i['confidence'] for i in insights) / len(insights) if insights else 0.0, # Avg confidence
1 if any('urgent' in i['value'].lower() for i in insights) else 0 # Urgency
]

prediction = self.model.predict([features])[0]
skill = self.label_encoder.inverse_transform([prediction])[0]

logger.info(f"Predicted skill demand for tender {tender_id}: {skill}")
return [skill] if skill != 'other' else []
except Exception as e:
logger.error(f"Skill demand prediction failed for tender {tender_id}: {e}")
return []

async def trigger_proactive_recruitment(self, tender_id: str):
"""Trigger recruitment based on predicted demand."""
skills = await self.predict_skill_demand(tender_id)
if skills:
# Trigger recruitment via agent
return await self.agent.run(
f"Recruit providers for tender {tender_id} with predicted skills: {skills}",
deps=self.dependencies,
tools=[self.recruit_for_tender],
parameters={'tender_id': tender_id, 'requirements': skills, 'pitfalls': []}
)
return {'status': 'skipped', 'message': 'No predicted skills'}




import logging
import httpx
from typing import Dict, Any
from urllib.parse import urlencode

logger = logging.getLogger(__name__)

class OAuthService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def get_linkedin_auth_url(self, redirect_uri: str) -> str:
"""Generate LinkedIn OAuth authorization URL."""
params = {
'response_type': 'code',
'client_id': self.config['recruitment_agent']['external_sources']['linkedin']['client_id'],
'redirect_uri': redirect_uri,
'scope': 'r_liteprofile r_emailaddress',
'state': 'random_state' # CSRF protection
}
return f"https://www.linkedin.com/oauth/v2/authorization?{urlencode(params)}"

async def get_linkedin_access_token(self, code: str, redirect_uri: str) -> str:
"""Exchange authorization code for access token."""
try:
response = await self.http_client.post(
"https://www.linkedin.com/oauth/v2/accessToken",
data={
'grant_type': 'authorization_code',
'code': code,
'redirect_uri': redirect_uri,
'client_id': self.config['recruitment_agent']['external_sources']['linkedin']['client_id'],
'client_secret': self.config['recruitment_agent']['external_sources']['linkedin']['client_secret']
}
)
response.raise_for_status()
return response.json()['access_token']
except Exception as e:
logger.error(f"LinkedIn access token retrieval failed: {e}")
raise

async def fetch_linkedin_profile(self, access_token: str) -> Dict[str, Any]:
"""Fetch LinkedIn profile data."""
try:
response = await self.http_client.get(
"https://api.linkedin.com/v2/me?projection=(id,firstName,lastName,profilePicture,skills,educations)",
headers={"Authorization": f"Bearer {access_token}"}
)
response.raise_for_status()
profile = response.json()

# Map to SkillProviderProfile
return {
'user_id': profile['id'],
'name': f"{profile.get('firstName', '')} {profile.get('lastName', '')}",
'skills': [s['name'] for s in profile.get('skills', {}).get('elements', [])],
'qualifications': [
{
'name': e['degreeName'],
'issuing_authority': e['schoolName'],
'is_verified': False
}
for e in profile.get('educations', {}).get('elements', [])
],
'bio': profile.get('summary', '')
}
except Exception as e:
logger.error(f"LinkedIn profile fetch failed: {e}")
raise




async def onboard_provider(self, provider: Dict[str, Any], tender_id: str) -> Dict[str, Any]:
try:
db = motor.motor_asyncio.AsyncIOMotorClient(self.config['mongodb']['url'])[self.config['mongodb']['database']]

# Check for LinkedIn OAuth
if provider.get('linkedin_code'):
oauth = OAuthService(self.config)
access_token = await oauth.get_linkedin_access_token(
provider['linkedin_code'],
self.config['recruitment_agent']['external_sources']['linkedin']['redirect_uri']
)
linkedin_profile = await oauth.fetch_linkedin_profile(access_token)
provider.update(linkedin_profile)

# Log OAuth action
await db['audit_logs'].insert_one({
'tenant_id': self.config['tenant_id'],
'action': 'linkedin_oauth',
'model_name': 'SkillProviderProfile',
'object_id': provider['id'],
'details': {'user_id': provider['user_id']},
'timestamp': datetime.now()
})

# Set pricing
pricing_result = await self.pricing.suggest_hourly_rate(provider)
provider['hourly_rate'] = pricing_result['rate']

# Update or insert profile
await db['skillsync_profiles'].update_one(
{'_id': provider['id']},
{'$set': provider},
upsert=True
)

# Create license
license = {
'id': f"license_{hash(provider['id'])}",
'skill_provider_id': provider['id'],
'tender_id': tender_id,
'licensing_bidder_id': None,
'start_date': datetime.now(),
'license_fee': await self.pricing.suggest_license_fee(provider),
'status': 'pending_payment',
'tenant_id': self.config['tenant_id']
}
await db['skillsync_licenses'].insert_one(license)

logger.info(f"Onboarded provider {provider['id']} for tender {tender_id}")
return {'success': True, 'provider': provider, 'license': license}
except Exception as e:
logger.error(f"Onboarding failed for provider {provider['id']}: {e}")
return {'success': False, 'error': str(e)}





external_sources:
linkedin:
client_id: "${LINKEDIN_CLIENT_ID}"
client_secret: "${LINKEDIN_CLIENT_SECRET}"
redirect_uri: "https://bidbeez.com/auth/linkedin/callback"


import logging
import httpx
from typing import Dict, Any
from .utils import calculate_bbbee_score

logger = logging.getLogger(__name__)

class ComplianceService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def verify_bbbee(self, provider: Dict[str, Any]) -> Dict[str, Any]:
"""Verify provider’s B-BBEE status with external agency."""
try:
db = motor.motor_asyncio.AsyncIOMotorClient(self.config['recruitment_agent']['mongodb']['url'])[
self.config['recruitment_agent']['mongodb']['database']
]
response = await self.http_client.post(
"https://api.bbbee-agency.co.za/verify", # Placeholder API
headers={"Authorization": f"Bearer {self.config['recruitment_agent']['external_sources']['bbbee']['api_key']}"},
json={
"company_name": provider.get('name', ''),
"registration_number": provider.get('registration_number', '')
}
)
response.raise_for_status()
result = response.json()
bbbee_level = result.get('level', 4) # Default to Level 4
provider['bbbee_level'] = bbbee_level
provider['bbbee_verified'] = result.get('verified', False)

# Log verification
await db['audit_logs'].insert_one({
'tenant_id': self.config['recruitment_agent']['tenant_id'],
'action': 'verify_bbbee',
'model_name': 'SkillProviderProfile',
'object_id': provider['id'],
'details': {'level': bbbee_level, 'verified': provider['bbbee_verified']},
'timestamp': datetime.now()
})

logger.info(f"B-BBEE verified for provider {provider['id']}: Level {bbbee_level}")
return provider
except Exception as e:
logger.error(f"B-BBEE verification failed for provider {provider['id']}: {e}")
provider['bbbee_level'] = 4
provider['bbbee_verified'] = False
return provider

async def evaluate_bbbee(self, provider: Dict[str, Any]) -> float:
"""Evaluate provider’s B-BBEE compliance."""
try:
provider = await self.verify_bbbee(provider)
score = calculate_bbbee_score(provider['bbbee_level'])
provider['bbbee_score'] = score

if score < self.config['recruitment_agent']['compliance']['bbbee']['required_level']:
logger.warning(f"Provider {provider['id']} does not meet B-BBEE requirement: {score}")

return score
except Exception as e:
logger.error(f"B-BBEE evaluation failed for provider {provider['id']}: {e}")
return 0.0






import logging
import httpx
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class NotificationService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def notify_providers(self, providers: List[Dict[str, Any]]):
"""Notify providers via push and voice alerts."""
try:
db = motor.motor_asyncio.AsyncIOMotorClient(self.config['recruitment_agent']['mongodb']['url'])[
self.config['recruitment_agent']['mongodb']['database']
]
for provider in providers:
# Push notification
push_payload = {
"to": f"provider_{provider['id']}_device_token",
"notification": {
"title": "Join Bidbeez SkillSync",
"body": "You’ve been recruited for a tender opportunity. Complete your profile."
}
}
await self.http_client.post(
"https://fcm.googleapis.com/fcm/send",
headers={"Authorization": f"key={self.config['recruitment_agent']['notifications']['firebase_key']}"},
json=push_payload
)

# Voice notification via Grok
voice_payload = {
"to": f"provider_{provider['id']}_device_token",
"data": {
"type": "voice_alert",
"message": "Please upload your CIDB certificate to join tender opportunities.",
"grok_voice_mode": True # Trigger Grok voice mode
}
}
await self.http_client.post(
"https://fcm.googleapis.com/fcm/send",
headers={"Authorization": f"key={self.config['recruitment_agent']['notifications']['firebase_key']}"},
json=voice_payload
)

# Log notification
await db['audit_logs'].insert_one({
'tenant_id': self.config['recruitment_agent']['tenant_id'],
'action': 'send_voice_notification',
'model_name': 'SkillProviderProfile',
'object_id': provider['id'],
'details': {'message': 'Upload CIDB certificate'},
'timestamp': datetime.now()
})

logger.info(f"Notified {len(providers)} providers")
except Exception as e:
logger.error(f"Provider notification failed: {e}")
raise






import logging
from typing import List, Dict, Any
from datetime import datetime, timedelta
import pandas as pd
from statsmodels.tsa.arima.model import ARIMA
import motor.motor_asyncio

logger = logging.getLogger(__name__)

class RecruitmentAnalyticsService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.db = motor.motor_asyncio.AsyncIOMotorClient(self.config['recruitment_agent']['mongodb']['url'])[
self.config['recruitment_agent']['mongodb']['database']
]

async def predict_skill_gaps(self, horizon_days: int = 30) -> Dict[str, Any]:
"""Predict future skill gaps using time-series analysis."""
try:
# Fetch historical recruitment logs
logs = await self.db['recruitment_logs'].find({
'timestamp': {'$gte': datetime.now() - timedelta(days=180)},
'tenant_id': self.config['recruitment_agent']['tenant_id']
}).to_list(None)

# Aggregate skill gaps by date
df = pd.DataFrame([
{'date': l['timestamp'].date(), 'gap': g}
for l in logs for g in l.get('gaps', [])
])
if df.empty:
return {'gaps': [], 'forecast': {}}

# Count gaps per day
gap_counts = df.groupby(['date', 'gap']).size().unstack(fill_value=0)
gap_counts = gap_counts.resample('D').sum().fillna(0)

# Forecast for each skill
forecast = {}
for skill in gap_counts.columns:
model = ARIMA(gap_counts[skill], order=(1, 1, 1))
fit = model.fit()
future_counts = fit.forecast(steps=horizon_days)
forecast[skill] = future_counts.tolist()

# Identify significant gaps
predicted_gaps = [skill for skill, counts in forecast.items() if sum(counts) > 0.5]

# Create QueenBee prompt for predicted gaps
if predicted_gaps:
await self.db['queenbee_prompts'].insert_one({
'tender_id': None, # Global prompt
'message': f"Predicted skill gaps: {', '.join(predicted_gaps)} in next {horizon_days} days",
'trigger_event': 'predicted_skill_gap',
'created_at': datetime.now(),
'resolved': False,
'tenant_id': self.config['recruitment_agent']['tenant_id']
})

logger.info(f"Predicted skill gaps: {predicted_gaps}")
return {
'gaps': predicted_gaps,
'forecast': {k: v for k, v in forecast.items()}
}
except Exception as e:
logger.error(f"Skill gap prediction failed: {e}")
return {'gaps': [], 'forecast': {}}

async def update_metrics(self, tender_id: str, providers: List[Dict[str, Any]], criteria: List[str]):
"""Update recruitment metrics."""
try:
gaps = [c for c in criteria if not any(c.lower() in p['skills'].lower() for p in providers)]
await self.db['recruitment_logs'].insert_one({
'tender_id': tender_id,
'provider_count': len(providers),
'criteria_count': len(criteria),
'gaps': gaps,
'timestamp': datetime.now(),
'tenant_id': self.config['recruitment_agent']['tenant_id']
})

# Generate forecast
forecast = await self.predict_skill_gaps()
await self.db['recruitment_logs'].update_one(
{'tender_id': tender_id, 'timestamp': {'$gte': datetime.now()}},
{'$set': {'predicted_gaps': forecast['gaps']}}
)

logger.info(f"Updated recruitment metrics for tender {tender_id}")
except Exception as e:
logger.error(f"Analytics update failed: {e}")
raise




import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class AnalyticsDashboardScreen extends StatefulWidget {
@override
_AnalyticsDashboardScreenState createState() => _AnalyticsDashboardScreenState();
}

class _AnalyticsDashboardScreenState extends State<AnalyticsDashboardScreen> {
Map<String, dynamic> analyticsData = {};

@override
void initState() {
super.initState();
fetchAnalytics();
}

Future<void> fetchAnalytics() async {
final response = await http.get(Uri.parse('https://api.bidbeez.com/recruitment/analytics/skill_gaps'));
if (response.statusCode == 200) {
setState(() {
analyticsData = jsonDecode(response.body);
});
}
}

@override
Widget build(BuildContext context) {
final predictedGaps = analyticsData['gaps'] ?? [];
return Scaffold(
appBar: AppBar(title: Text('Recruitment Analytics')),
body: ListView(
children: [
Padding(
padding: EdgeInsets.all(16.0),
child: Text('Predicted Skill Gaps (Next 30 Days)', style: TextStyle(fontWeight: FontWeight.bold)),
),
...predictedGaps.map((gap) => ListTile(
title: Text(gap),
subtitle: Text('Proactive recruitment triggered'),
)),
// Chart.js widget for forecast (simulated)
Container(
height: 200,
child: Text('Line Chart: Future skill gap trends'),
),
],
),
);
}
}

