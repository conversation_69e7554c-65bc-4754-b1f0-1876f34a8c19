This module enables the verification and classification of **Student Bees** for sensitive tasks like **tender document filling**, using manual uploads, admin approval, and future integration potential with universities.

---

## 1. Backend Folder Structure

```
backend/
└── domains/
└── beeverify/
├── models.py # StudentVerificationProfile
├── views.py # Upload, check status
├── serializers.py # For mobile app form
├── urls.py # API routes
├── admin.py # Verification & approval interface
```

---

## 2. Model: `StudentVerificationProfile`

```python
# beeverify/models.py

from django.db import models
from django.conf import settings

class StudentVerificationProfile(models.Model):
bee = models.OneToOneField('bee.BeeProfile', on_delete=models.CASCADE, related_name='student_verification')
institution_name = models.CharField(max_length=100)
student_number = models.CharField(max_length=50)
proof_of_registration = models.FileField(upload_to='student_proofs/')
verified = models.BooleanField(default=False)
submitted_at = models.DateTimeField(auto_now_add=True)
verified_at = models.DateTimeField(null=True, blank=True)

def __str__(self):
return f"{self.bee.full_name} - {self.institution_name}"
```

---

## 3. Serializers

```python
# beeverify/serializers.py

from rest_framework import serializers
from .models import StudentVerificationProfile

class StudentVerificationSerializer(serializers.ModelSerializer):
class Meta:
model = StudentVerificationProfile
fields = '__all__'
read_only_fields = ('verified', 'verified_at')
```

---

## 4. Views

```python
# beeverify/views.py

from rest_framework.decorators import api_view
from rest_framework.response import Response
from .models import StudentVerificationProfile
from .serializers import StudentVerificationSerializer

@api_view(['POST'])
def upload_verification(request):
serializer = StudentVerificationSerializer(data=request.data)
if serializer.is_valid():
serializer.save()
return Response({'status': 'submitted'})
return Response(serializer.errors, status=400)
```

---

## 5. Admin Panel

```python
# beeverify/admin.py

from django.contrib import admin
from .models import StudentVerificationProfile

@admin.register(StudentVerificationProfile)
class StudentVerificationAdmin(admin.ModelAdmin):
list_display = ('bee', 'institution_name', 'student_number', 'verified')
list_filter = ('institution_name', 'verified')
actions = ['mark_as_verified']

def mark_as_verified(self, request, queryset):
queryset.update(verified=True)
```

---

## 6. Task Routing Logic (Queen Bee)

```python
# queenbee/services.py

if task.type == 'TenderFormFilling':
candidates = BeeProfile.objects.filter(
is_verified=True,
student_verification__verified=True
)
```

---

## 7. Database Tables

```sql
-- Table for student verification

CREATE TABLE StudentVerificationProfile (
id SERIAL PRIMARY KEY,
bee_id INTEGER UNIQUE NOT NULL REFERENCES bee_beeprofile(id),
institution_name VARCHAR(100),
student_number VARCHAR(50),
proof_of_registration VARCHAR(200),
verified BOOLEAN DEFAULT FALSE,
submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
verified_at TIMESTAMP
);
```

---

## 8. Future Expansion Ideas

- Partner dashboards for universities
- OAuth login with student portals
- OCR parsing of registration PDFs for auto-flagging errors
- Time-bound verification expiry

---

**Secure, verified Student Bees ensure trusted tender handling.**





Flutter front end

# Bidbeez Platform – Flutter Frontend (Student Bee Verification Form)

This document outlines the complete Flutter screen for student Bees to upload proof of enrollment and apply for document handling tasks.

---

## Folder Structure

```
lib/
└── screens/
└── beeverify/
├── student_verification_form.dart # Main form screen
```

---

## 1. Screen: student_verification_form.dart

```dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';

class StudentVerificationForm extends StatefulWidget {
@override
_StudentVerificationFormState createState() => _StudentVerificationFormState();
}

class _StudentVerificationFormState extends State<StudentVerificationForm> {
final _formKey = GlobalKey<FormState>();
String _institutionName = '';
String _studentNumber = '';
File? _proofFile;

Future<void> _pickFile() async {
FilePickerResult? result = await FilePicker.platform.pickFiles(type: FileType.custom, allowedExtensions: ['pdf', 'jpg', 'png']);
if (result != null) {
setState(() {
_proofFile = File(result.files.single.path!);
});
}
}

Future<void> _submit() async {
if (_formKey.currentState!.validate() && _proofFile != null) {
var request = http.MultipartRequest(
'POST',
Uri.parse('https://yourapi.com/beeverify/upload'),
);
request.fields['institution_name'] = _institutionName;
request.fields['student_number'] = _studentNumber;
request.files.add(await http.MultipartFile.fromPath('proof_of_registration', _proofFile!.path));

var response = await request.send();
if (response.statusCode == 200) {
ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Submitted Successfully')));
} else {
ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Upload failed')));
}
}
}

@override
Widget build(BuildContext context) {
return Scaffold(
appBar: AppBar(title: Text('Student Bee Verification')),
body: Padding(
padding: const EdgeInsets.all(16.0),
child: Form(
key: _formKey,
child: Column(children: [
TextFormField(
decoration: InputDecoration(labelText: 'Institution Name'),
validator: (value) => value == null || value.isEmpty ? 'Required' : null,
onChanged: (val) => _institutionName = val,
),
TextFormField(
decoration: InputDecoration(labelText: 'Student Number'),
validator: (value) => value == null || value.isEmpty ? 'Required' : null,
onChanged: (val) => _studentNumber = val,
),
SizedBox(height: 16),
ElevatedButton(
onPressed: _pickFile,
child: Text('Upload Proof of Registration'),
),
if (_proofFile != null)
Text('Selected: ${basename(_proofFile!.path)}'),
Spacer(),
ElevatedButton(
onPressed: _submit,
child: Text('Submit for Verification'),
)
]),
),
),
);
}
}
```

---

## 2. Dependencies

```yaml
dependencies:
file_picker: ^5.2.6
http: ^0.14.0
path: ^1.8.2
```

---

## 3. Backend API Required

POST to `/beeverify/upload/` with:
- `institution_name`
- `student_number`
- `proof_of_registration` (file)

---

**This screen empowers student Bees to get verified for trusted document tasks.**

