-- Table: tenants
-- Description: Stores tenant information for multi-tenancy.
CREATE TABLE tenants (
tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
name VARCHAR(100) NOT NULL UNIQUE,
configuration JSONB DEFAULT '{}', -- Tenant-specific settings (e.g., B-BBEE level)
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT chk_name CHECK (name <> '')
);

-- Table: recruitment_logs
-- Description: Tracks recruitment activities for tenders.
CREATE TABLE recruitment_logs (
log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
tender_id VARCHAR(50), -- References <PERSON>rse<PERSON> (assumed external)
provider_count INTEGER DEFAULT 0,
criteria_count INTEGER DEFAULT 0,
gaps JSONB DEFAULT '[]', -- Array of unmatched skills
predicted_gaps JSONB DEFAULT '[]', -- From predictive recruitment
status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failed', 'pending')),
error_message TEXT,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_recruitment_logs_tenant_tender (tenant_id, tender_id),
INDEX idx_recruitment_logs_status (status)
);

-- Table: skill_provider_profiles
-- Description: Stores skill provider profiles for relational queries.
CREATE TABLE skill_provider_profiles (
provider_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
user_id VARCHAR(50) NOT NULL, -- References auth_user (assumed external)
bio TEXT,
skills JSONB DEFAULT '[]', -- Array of skills (e.g., ["Electrician", "CIDB Grade 5"])
verification_status VARCHAR(20) NOT NULL CHECK (verification_status IN ('pending', 'verified', 'rejected')),
rating NUMERIC(3, 2),
num_reviews INTEGER DEFAULT 0,
availability BOOLEAN DEFAULT TRUE,
hourly_rate NUMERIC(10, 2),
buyout_price NUMERIC(15, 2),
bbbee_level INTEGER CHECK (bbbee_level BETWEEN 1 AND 8),
bbbee_score NUMERIC(3, 2),
linkedin_id VARCHAR(50), -- For OAuth integration
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT chk_positive_rates CHECK (hourly_rate >= 0 AND buyout_price >= 0),
UNIQUE (tenant_id, user_id),
INDEX idx_skill_provider_profiles_tenant_status (tenant_id, verification_status),
INDEX idx_skill_provider_profiles_bbbee (tenant_id, bbbee_level)
);

-- Table: qualifications
-- Description: Stores provider qualifications with SAQA verification.
CREATE TABLE qualifications (
qualification_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
provider_id UUID NOT NULL REFERENCES skill_provider_profiles(provider_id) ON DELETE CASCADE,
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
name VARCHAR(200) NOT NULL,
issuing_authority VARCHAR(200),
verification_document VARCHAR(500), -- File path or URL
is_verified BOOLEAN DEFAULT FALSE,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_qualifications_provider (provider_id),
INDEX idx_qualifications_verified (tenant_id, is_verified)
);

-- Table: licenses
-- Description: Manages skill licenses with smart contract IDs.
CREATE TABLE licenses (
license_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
provider_id UUID NOT NULL REFERENCES skill_provider_profiles(provider_id) ON DELETE CASCADE,
tender_id VARCHAR(50), -- References ParsedTender
licensing_bidder_id VARCHAR(50), -- References auth_user
start_date DATE NOT NULL,
end_date DATE,
license_fee NUMERIC(10, 2) NOT NULL CHECK (license_fee >= 0),
status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'expired', 'pending_payment', 'cancelled')),
smart_contract_id VARCHAR(256), -- Blockchain contract address
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT chk_dates CHECK (end_date IS NULL OR end_date > start_date),
INDEX idx_licenses_provider (provider_id),
INDEX idx_licenses_tender (tenant_id, tender_id)
);

-- Table: verifications
-- Description: Records qualification and B-BBEE verification attempts.
CREATE TABLE verifications (
verification_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
provider_id UUID NOT NULL REFERENCES skill_provider_profiles(provider_id) ON DELETE CASCADE,
qualification_id UUID REFERENCES qualifications(qualification_id) ON DELETE SET NULL,
verification_type VARCHAR(100) NOT NULL CHECK (verification_type IN ('qualification', 'bbbee')),
status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
notes TEXT,
verified_by VARCHAR(50), -- References auth_user
verification_date TIMESTAMP WITH TIME ZONE,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_verifications_provider (provider_id),
INDEX idx_verifications_type (tenantProcessed, Query id=0 id, verification_type)
);

-- Table: bbbee_compliance_verifications
-- Description: Stores B-BBEE verification results.
CREATE TABLE bbbee_compliance_verifications (
bbbee_verification_id VARCHAR(50) PRIMARY KEY, -- External API reference
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
provider_id UUID NOT NULL REFERENCES skill_provider_profiles(provider_id) ON DELETE CASCADE,
company_name VARCHAR(255),
registration_number VARCHAR(50),
bbbee_level INTEGER CHECK (bbbee_level BETWEEN 1 AND 8),
verified BOOLEAN DEFAULT FALSE,
verification_date TIMESTAMP WITH TIME ZONE,
notes TEXT,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_bbbee_verifications_provider (provider_id)
);

-- Table: predictive_recruitment
-- Description: Stores skill demand predictions from QueenBee insights.
CREATE TABLE predictive_recruitment (
prediction_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
skill VARCHAR(100) NOT NULL,
demand_score NUMERIC(5, 2) CHECK (demand_score BETWEEN 0 AND 1),
forecast_horizon INTEGER NOT NULL CHECK (forecast_horizon > 0), -- Days
predicted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_predictive_recruitment_tenant_skill (tenant_id, skill)
);

-- Table: recruitment_analytics
-- Description: Aggregates metrics for forecasting and dashboards.
CREATE TABLE recruitment_analytics (
analytics_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
tender_id VARCHAR(50),
provider_count INTEGER DEFAULT 0,
gap_count INTEGER DEFAULT 0,
success_rate NUMERIC(5, 2) CHECK (success_rate BETWEEN 0 AND 100), -- % of criteria met
average_bbbee_score NUMERIC(3, 2),
average_match_score NUMERIC(3, 2),
period_start DATE NOT NULL,
period_end DATE NOT NULL,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT chk_period CHECK (period_end > period_start),
INDEX idx_recruitment_analytics_tenant_period (tenant_id, period_start, period_end)
);

-- Table: audit_logs
-- Description: Logs all agent actions for compliance.
CREATE TABLE audit_logs (
log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
user_id VARCHAR(50), -- References auth_user
action VARCHAR(100) NOT NULL, -- e.g., 'linkedin_oauth', 'verify_bbbee'
model_name VARCHAR(100) NOT NULL,
object_id VARCHAR(50),
details JSONB DEFAULT '{}',
timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_audit_logs_tenant_action (tenant_id, action),
INDEX idx_audit_logs_timestamp (timestamp)
);

-- Table: queenbee_prompts
-- Description: Mirrors QueenBeePrompt for recruitment triggers and XP awards.
CREATE TABLE queenbee_prompts (
prompt_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
tender_id VARCHAR(50), -- References ParsedTender
message TEXT NOT NULL,
trigger_event VARCHAR(100) NOT NULL, -- e.g., 'provider_onboarded'
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
resolved BOOLEAN DEFAULT FALSE,
resolved_at TIMESTAMP WITH TIME ZONE,
INDEX idx_queenbee_prompts_tenant_tender (tenant_id, tender_id),
INDEX idx_queenbee_prompts_trigger (trigger_event)
);

