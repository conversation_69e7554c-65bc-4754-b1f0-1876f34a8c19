Bidbeez/
├── backend/
│ └── services/
│ ├── toolhire/ # Django-based toolhire microservice
│ │ ├── api/
│ │ │ └── v1/
│ │ │ ├── endpoints/
│ │ │ │ ├── p2p_listings.py
│ │ │ │ ├── traditional_listings.py
│ │ │ │ ├── bookings.py
│ │ │ │ └── search.py
│ │ │ ├── serializers/
│ │ │ │ ├── p2p_serializers.py
│ │ │ │ ├── traditional_serializers.py
│ │ │ │ └── booking_serializers.py
│ │ │ └── urls.py
│ │ ├── circuit_breakers/ # Resilience patterns
│ │ │ ├── circuit_breaker.py
│ │ │ └── retry_policies.py
│ │ ├── clients/ # Bidbeez service clients
│ │ │ ├── __init__.py
│ │ │ ├── base_client.py
│ │ │ ├── auth_client.py
│ │ │ ├── escrow_client.py
│ │ │ ├── blockchain_client.py
│ │ │ ├── queenbee_client.py
│ │ │ └── compliance_client.py
│ │ ├── config/
│ │ │ ├── __init__.py
│ │ │ ├── settings/
│ │ │ │ ├── base.py
│ │ │ │ ├── local.py
│ │ │ │ └── production.py
│ │ │ └── wsgi.py
│ │ ├── core/
│ │ │ ├── permissions.py
│ │ │ ├── structured_logging.py
│ │ │ └── tenant_context.py # Multi-tenancy RLS
│ │ ├── models/
│ │ │ ├── __init__.py
│ │ │ ├── tool_category.py
│ │ │ ├── p2p_listing.py
│ │ │ ├── traditional_listing.py
│ │ │ ├── booking.py
│ │ │ └── tenant_whitelabel.py
│ │ ├── services/
│ │ │ ├── booking_orchestration_service.py
│ │ │ ├── p2p_service.py
│ │ │ ├── geosearch_service.py
│ │ │ ├── insurance_service.py
│ │ │ ├── analytics_service.py # HiveMind AI integration
│ │ │ └── whitelabel_service.py
│ │ ├── tasks/
│ │ │ ├── update_ratings_task.py
│ │ │ └── analytics_tasks.py
│ │ ├── tests/
│ │ │ ├── unit/
│ │ │ │ ├── test_p2p_listings.py
│ │ │ │ └── test_booking_orchestration.py
│ │ │ └── integration/
│ │ │ ├── test_geosearch.py
│ │ │ └── test_booking_flow.py
│ │ ├── Dockerfile
│ │ ├── entrypoint.sh
│ │ ├── manage.py
│ │ ├── requirements.txt
│ │ └── .env.example
│ └── hivemind/ # HiveMind AI service
│ ├── api/
│ │ └── v1/
│ │ ├── endpoints/
│ │ │ └── analytics.py
│ │ └── urls.py
│ ├── models/
│ │ └── optimization_task.py
│ ├── services/
│ │ └── genetic_optimizer.py
│ ├── tasks/
│ │ └── async_optimization.py
│ ├── Dockerfile
│ └── manage.py
├── devops/
│ ├── kubernetes/
│ │ ├── toolhire/
│ │ │ ├── base/
│ │ │ │ ├── deployment.yaml
│ │ │ │ ├── service.yaml
│ │ │ │ └── kustomization.yaml
│ │ │ ├── overlays/
│ │ │ │ ├── staging/
│ │ │ │ │ ├── configmap.yaml
│ │ │ │ │ └── kustomization.yaml
│ │ │ │ └── production/
│ │ │ │ ├── configmap.yaml
│ │ │ │ ├── sealed-secret.yaml
│ │ │ │ └── kustomization.yaml
│ │ └── hivemind/
│ │ ├── base/
│ │ │ ├── deployment.yaml
│ │ │ └── kustomization.yaml
│ │ └── overlays/
│ │ └── production/
│ │ └── kustomization.yaml
│ ├── cicd/
│ │ ├── toolhire-pipeline.yml
│ │ └── hivemind-pipeline.yml
│ └── monitoring/
│ ├── grafana/
│ │ └── dashboards/
│ │ ├── toolhire_health.json
│ │ └── hivemind_performance.json
│ └── prometheus/
│ └── alerts.yml
└── standalone_app_source/ # Bidbeez Field standalone app
├── backend/
│ ├── local_runner.py
│ ├── local_db_adapter.py
│ ├── config_importer.py # Auto-discover Bidbeez endpoints
│ └── sync_engine/
│ ├── cloud_sync.py
│ ├── conflict_resolver.py
│ └── sync_monitor.py
├── frontend/
│ └── src/
│ ├── pages/
│ │ ├── P2P/
│ │ │ ├── ListNewTool.jsx
│ │ │ └── MyToolListings.jsx
│ │ ├── Booking/
│ │ │ └── P2PBookingFlow.jsx
│ │ └── Analytics/
│ │ └── OpportunityDashboard.jsx
│ ├── themes/
│ │ └── useTheme.js
│ ├── services/
│ │ └── sdk/
│ │ ├── bookingService.js
│ │ ├── p2pService.js
│ │ └── analyticsService.js
│ ├── components/
│ │ ├── OfflineIndicator.jsx
│ │ └── DeploymentSwitch.jsx
│ └── service-worker.js
├── standalone_builder/
│ ├── packagers/
│ │ ├── pyinstaller/
│ │ │ └── spec_files/
│ │ │ ├── win.spec
│ │ │ ├── mac.spec
│ │ │ └── linux.spec
│ │ ├── docker/
│ │ │ └── Dockerfile.standalone
│ │ └── electron/
│ │ ├── main.js
│ │ └── package.json
│ ├── config_templates/
│ │ ├── small_business.yml
│ │ └── individual_contractor.yml
│ └── build_scripts/
│ └── package_standalone.py
├── devops/
│ ├── kubernetes/
│ │ └── standalone-deployment.yaml
│ ├── cicd/
│ │ └── standalone-pipeline.yml
│ └── monitoring/
│ └── grafana/
│ └── standalone_health.json
└── docs/
├── STANDALONE_GUIDE.md
├── API_INTEGRATION_GUIDE.md
└── ADR/
├── 001_multi_tenancy.md
├── 002_hivemind_integration.md
└── 003_whitelabeling.md


# Bidbeez/backend/services/toolhire/config/settings/base.py
import environ
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent.parent
env = environ.Env(DEBUG=(bool, False))
environ.Env.read_env(BASE_DIR / '.env')

SECRET_KEY = env('SECRET_KEY')
DEBUG = env('DEBUG')
ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=['.bidbeez.com'])

INSTALLED_APPS = [
'django.contrib.admin',
'django.contrib.auth',
'django.contrib.contenttypes',
'django.contrib.sessions',
'django.contrib.messages',
'django.contrib.staticfiles',
'django.contrib.gis',
'rest_framework',
'toolhire.models',
'tenants', # Multi-tenancy support
]

MIDDLEWARE = [
'django.middleware.security.SecurityMiddleware',
'django.contrib.sessions.middleware.SessionMiddleware',
'toolhire.core.tenant_context.TenantMiddleware', # RLS middleware
'django.middleware.common.CommonMiddleware',
'django.middleware.csrf.CsrfViewMiddleware',
'django.contrib.auth.middleware.AuthenticationMiddleware',
'django.contrib.messages.middleware.MessageMiddleware',
]

DATABASES = {
'default': env.db('DATABASE_URL', default='postgis://user:pass@localhost/toolhire')
}

REST_FRAMEWORK = {
'DEFAULT_PERMISSION_CLASSES': ['rest_framework.permissions.IsAuthenticated'],
'DEFAULT_AUTHENTICATION_CLASSES': [
'rest_framework.authentication.JWTAuthentication',
],
}

LOGGING = {
'version': 1,
'disable_existing_loggers': False,
'formatters': {
'json': {'()': 'toolhire.core.structured_logging.JSONLogFormatter'},
},
'handlers': {
'console': {'class': 'logging.StreamHandler', 'formatter': 'json'},
},
'root': {'handlers': ['console'], 'level': 'INFO'},
}


# Bidbeez/backend/services/toolhire/config/settings/production.py
from .base import *

DEBUG = False
ALLOWED_HOSTS = ['api.bidbeez.com', 'toolhire.bidbeez.com']

SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Sentry for error tracking
import sentry_sdk
sentry_sdk.init(
dsn=env('SENTRY_DSN'),
traces_sample_rate=1.0,
environment='production'
)


# Bidbeez/backend/services/toolhire/core/structured_logging.py
import json
import logging

class JSONLogFormatter(logging.Formatter):
def format(self, record):
log_entry = {
'timestamp': self.formatTime(record, '%Y-%m-%dT%H:%M:%S%z'),
'level': record.levelname,
'message': record.getMessage(),
'module': record.module,
'tenant_id': getattr(record, 'tenant_id', None)
}
return json.dumps(log_entry)



# Bidbeez/backend/services/toolhire/core/tenant_context.py
from django.db import connection
from django.conf import settings

class TenantMiddleware:
def __init__(self, get_response):
self.get_response = get_response

def __call__(self, request):
tenant_id = request.headers.get('X-Tenant-ID') or request.user.tenant_id
request.tenant = tenant_id
with connection.cursor() as cursor:
cursor.execute(f"SET app.current_tenant = '{tenant_id}'")
response = self.get_response(request)
return response


# Bidbeez/backend/services/toolhire/models/p2p_listing.py
from django.db import models
from django.contrib.gis.db.models import PointField
from django.conf import settings

class P2PListing(models.Model):
owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE)
title = models.CharField(max_length=120)
description = models.TextField(blank=True)
daily_rate_zar = models.DecimalField(max_digits=10, decimal_places=2)
location = PointField()
is_available = models.BooleanField(default=True)
photo_urls = models.JSONField(default=list)
rating = models.DecimalField(max_digits=3, decimal_places=2, default=5.0)
hires_completed = models.PositiveIntegerField(default=0)
estimated_value = models.DecimalField(max_digits=10, decimal_places=2) # For insurance
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

class Meta:
indexes = [models.Index(fields=['location', 'tenant'])]


# Bidbeez/backend/services/toolhire/models/booking.py
from django.db import models
from django.contrib.gis.db.models import PointField
from django.conf import settings

class Booking(models.Model):
renter = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='rentals')
tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE)
listing = models.ForeignKey('P2PListing', on_delete=models.CASCADE, null=True, blank=True)
traditional_listing = models.ForeignKey('TraditionalListing', on_delete=models.CASCADE, null=True, blank=True)
status = models.CharField(max_length=50, default='PENDING_CONFIRMATION')
total_cost = models.DecimalField(max_digits=10, decimal_places=2)
security_deposit = models.DecimalField(max_digits=10, decimal_places=2)
pickup_location = PointField(null=True, blank=True)
start_date = models.DateTimeField()
end_date = models.DateTimeField()
audit_log = models.JSONField(default=list)
insurance_policy = models.JSONField(default=dict) # Stores insurance details
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

# Bidbeez/backend/services/toolhire/models/tenant_whitelabel.py
from django.db import models

class TenantTheme(models.Model):
tenant = models.OneToOneField('tenants.Tenant', on_delete=models.CASCADE)
primary_color = models.CharField(max_length=7, default='#000000')
secondary_color = models.CharField(max_length=7, default='#FFFFFF')
logo_url = models.URLField(max_length=500)
custom_domain = models.CharField(max_length=255, unique=True, null=True, blank=True)
is_active = models.BooleanField(default=True)


# Bidbeez/backend/services/toolhire/services/booking_orchestration_service.py
from transitions import Machine
from datetime import datetime
from ..models import Booking
from ..clients import escrow_client, blockchain_client, queenbee_client
from ..circuit_breakers.circuit_breaker import ToolHireCircuitBreaker

class BookingStateEngine:
STATES = [
'PENDING_CONFIRMATION', 'AWAITING_PAYMENT', 'AWAITING_PICKUP',
'IN_PROGRESS', 'AWAITING_RETURN', 'PENDING_COMPLETION',
'COMPLETED', 'CANCELLED', 'DISPUTED'
]

def __init__(self, booking_instance: Booking):
self.booking = booking_instance
self.breaker = ToolHireCircuitBreaker()
self.machine = Machine(model=self, states=self.STATES, initial=self.booking.status, after_state_change=self.save_state)
self.machine.add_transition('confirm_booking', 'PENDING_CONFIRMATION', 'AWAITING_PAYMENT')
self.machine.add_transition('secure_funds', 'AWAITING_PAYMENT', 'AWAITING_PICKUP', before='_secure_funds_in_escrow')
self.machine.add_transition('confirm_pickup', 'AWAITING_PICKUP', 'IN_PROGRESS', before='_log_pickup_on_blockchain')
self.machine.add_transition('initiate_return', 'IN_PROGRESS', 'AWAITING_RETURN')
self.machine.add_transition('confirm_return', 'AWAITING_RETURN', 'PENDING_COMPLETION', before='_log_return_on_blockchain')
self.machine.add_transition('complete_rental', 'PENDING_COMPLETION', 'COMPLETED', after='_release_escrow_and_notify_hivemind')
self.machine.add_transition('cancel', '*', 'CANCELLED')
self.machine.add_transition('raise_dispute', '*', 'DISPUTED')

def save_state(self):
self.booking.status = self.state
log_entry = {'state': self.state, 'timestamp': datetime.utcnow().isoformat()}
self.booking.audit_log.append(log_entry)
self.booking.save()

def _secure_funds_in_escrow(self):
try:
self.breaker.execute(
escrow_client.secure_funds,
booking_id=self.booking.id,
renter_id=self.booking.renter.id,
amount=self.booking.total_cost,
deposit=self.booking.security_deposit
)
except Exception as e:
self.booking.status = 'DISPUTED'
self.booking.save()
raise RuntimeError(f"Escrow failed: {e}")

def _log_pickup_on_blockchain(self, pickup_photos: list):
try:
self.breaker.execute(
blockchain_client.log_event,
booking_id=self.booking.id,
event_type="PICKUP",
metadata={"photos": pickup_photos, "gps_location": self.booking.pickup_location.coords}
)
except Exception as e:
self.booking.status = 'DISPUTED'
self.booking.save()
raise RuntimeError(f"Blockchain pickup log failed: {e}")

def _log_return_on_blockchain(self, return_photos: list):
try:
self.breaker.execute(
blockchain_client.log_event,
booking_id=self.booking.id,
event_type="RETURN",
metadata={"photos": return_photos}
)
except Exception as e:
self.booking.status = 'DISPUTED'
self.booking.save()
raise RuntimeError(f"Blockchain return log failed: {e}")

def _release_escrow_and_notify_hivemind(self):
try:
self.breaker.execute(escrow_client.release_payment, booking_id=self.booking.id)
self.breaker.execute(queenbee_client.report_completed_rental, booking_details=self.booking.to_dict())
except Exception as e:
self.booking.status = 'DISPUTED'
self.booking.save()
raise RuntimeError(f"Escrow release or HiveMind notification failed: {e}")


# Bidbeez/backend/services/toolhire/services/insurance_service.py
from ..clients.base_client import BaseAPIClient
from ..circuit_breakers.circuit_breaker import ToolHireCircuitBreaker

class InsurtechPartnerClient(BaseAPIClient):
BASE_URL = "https://api.insurtech.co.za/v1/"

def get_per_rental_quote(self, tool_value: int, rental_duration_days: int) -> dict:
payload = {
"asset_value_zar": tool_value,
"coverage_days": rental_duration_days,
"category": "construction_tool"
}
return self.post("quotes", data=payload)

class InsuranceService:
def __init__(self):
self.client = InsurtechPartnerClient()
self.breaker = ToolHireCircuitBreaker()

def calculate_rental_insurance(self, booking) -> dict:
try:
tool_value = booking.listing.estimated_value if booking.listing else booking.traditional_listing.estimated_value
duration = (booking.end_date - booking.start_date).days
quote = self.breaker.execute(self.client.get_per_rental_quote, tool_value, duration)
return {
"is_insurable": True,
"premium_zar": quote['premium'],
"policy_id": quote['policy_id'],
"provider": "InsurtechPartner"
}
except Exception as e:
print(f"Insurance quote failed for booking {booking.id}: {e}")
return {"is_insurable": False, "premium_zar": 0}


# Bidbeez/backend/services/toolhire/services/whitelabel_service.py
from ..models.tenant_whitelabel import TenantTheme

class WhitelabelService:
def get_tenant_theme(self, tenant_id: str) -> dict:
try:
theme = TenantTheme.objects.get(tenant__id=tenant_id)
return {
'primary_color': theme.primary_color,
'secondary_color': theme.secondary_color,
'logo_url': theme.logo_url,
'custom_domain': theme.custom_domain
}
except TenantTheme.DoesNotExist:
return {}



# Bidbeez/backend/services/toolhire/services/geosearch_service.py
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from ..models import P2PListing, TraditionalListing

class GeoSearchService:
@staticmethod
def find_nearby_tools(latitude: float, longitude: float, radius_km: int, category: str = None, tenant_id: str = None):
user_location = Point(longitude, latitude, srid=4326)
p2p_results = P2PListing.objects.filter(
is_available=True,
location__distance_lte=(user_location, D(km=radius_km)),
tenant_id=tenant_id
)
if category:
p2p_results = p2p_results.filter(category__name=category)

traditional_results = TraditionalListing.objects.filter(
is_available=True,
depot_location__distance_lte=(user_location, D(km=radius_km)),
tenant_id=tenant_id
)
if category:
traditional_results = traditional_results.filter(category__name=category)

combined_results = [
{"type": "P2P", "id": l.id, "title": l.title, "daily_rate": l.daily_rate_zar, "owner_rating": l.rating}
for l in p2p_results
] + [
{"type": "Company", "id": l.id, "title": l.title, "daily_rate": l.daily_rate_zar, "company_name": l.company.name}
for l in traditional_results
]
return sorted(combined_results, key=lambda x: x['daily_rate'])



# Bidbeez/backend/services/toolhire/services/analytics_service.py
from ..clients.queenbee_client import queenbee_client
from ..models import Booking, P2PListing

class AnalyticsService:
def analyze_opportunities(self, latitude: float, longitude: float, tenant_id: str):
bookings = Booking.objects.filter(status='COMPLETED', tenant_id=tenant_id)
listings = P2PListing.objects.filter(is_available=True, tenant_id=tenant_id)
data = {
'bookings': [b.to_dict() for b in bookings],
'listings': [{'id': l.id, 'location': l.location.coords} for l in listings],
'user_location': {'lat': latitude, 'lon': longitude}
}
return queenbee_client.analyze_opportunities(data)

def predict_demand(self, zipcode: str, month: int, tenant_id: str):
return queenbee_client.predict_demand(zipcode=zipcode, month=month, tenant_id=tenant_id)



# Bidbeez/backend/services/toolhire/api/v1/endpoints/p2p_listings.py
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from ..serializers.p2p_serializers import P2PListingSerializer
from ...models import P2PListing
from ...core.permissions import IsOwner, IsTenantMember

class P2PListingViewSet(viewsets.ModelViewSet):
queryset = P2PListing.objects.all()
serializer_class = P2PListingSerializer
permission_classes = [IsAuthenticated, IsOwner, IsTenantMember]

def perform_create(self, serializer):
serializer.save(owner=self.request.user, tenant=self.request.tenant)



# Bidbeez/backend/services/toolhire/api/v1/serializers/p2p_serializers.py
from rest_framework import serializers
from ...models import P2PListing

class P2PListingSerializer(serializers.ModelSerializer):
class Meta:
model = P2PListing
fields = ['id', 'title', 'description', 'daily_rate_zar', 'location', 'is_available', 'photo_urls', 'rating', 'hires_completed', 'estimated_value']



# Bidbeez/backend/services/toolhire/circuit_breakers/circuit_breaker.py
from pybreaker import CircuitBreaker

class ToolHireCircuitBreaker:
def __init__(self, failure_threshold=5, recovery_timeout=30):
self.breaker = CircuitBreaker(fail_max=failure_threshold, reset_timeout=recovery_timeout)

def execute(self, func, *args, **kwargs):
try:
return self.breaker.call(func, *args, **kwargs)
except Exception as e:
print(f"Circuit breaker tripped: {e}")
raise



# standalone_app_source/backend/local_runner.py
from .local_db_adapter import LocalDBAdapter
from .sync_engine.cloud_sync import CloudSyncEngine
from .config_importer import discover_bidbeez_endpoints

def main():
config = discover_bidbeez_endpoints()
db = LocalDBAdapter()
sync_engine = CloudSyncEngine(config)
try:
sync_engine.sync_all()
except Exception as e:
print(f"Sync failed: {e}")
db.queue_for_retry()

if __name__ == "__main__":
main()


# standalone_app_source/backend/local_db_adapter.py
from pysqlcipher3 import dbapi2 as sqlite3
from datetime import datetime

class LocalDBAdapter:
def __init__(self):
self.conn = sqlite3.connect('toolhire_local.db')
self.conn.execute('PRAGMA key="your-encryption-key"')
self._init_db()

def _init_db(self):
cursor = self.conn.cursor()
cursor.execute('''
CREATE TABLE IF NOT EXISTS listings (
local_id INTEGER PRIMARY KEY,
cloud_id INTEGER,
title TEXT,
is_synced BOOLEAN,
created_at TIMESTAMP
)
''')
cursor.execute('''
CREATE TABLE IF NOT EXISTS bookings (
local_id INTEGER PRIMARY KEY,
cloud_id INTEGER,
status TEXT,
is_synced BOOLEAN,
created_at TIMESTAMP
)
''')
self.conn.commit()

def get_unsynced_listings(self):
cursor = self.conn.cursor()
cursor.execute('SELECT * FROM listings WHERE is_synced = 0')
return [{'local_id': row[0], 'title': row[2], 'is_synced': row[3]} for row in cursor.fetchall()]

def get_unsynced_bookings(self):
cursor = self.conn.cursor()
cursor.execute('SELECT * FROM bookings WHERE is_synced = 0')
return [{'local_id': row[0], 'status': row[2], 'is_synced': row[3]} for row in cursor.fetchall()]

def mark_listing_as_synced(self, local_id, cloud_id):
cursor = self.conn.cursor()
cursor.execute('UPDATE listings SET is_synced = 1, cloud_id = ? WHERE local_id = ?', (cloud_id, local_id))
self.conn.commit()

def mark_booking_as_synced(self, local_id, cloud_id):
cursor = self.conn.cursor()
cursor.execute('UPDATE bookings SET is_synced = 1, cloud_id = ? WHERE local_id = ?', (cloud_id, local_id))
self.conn.commit()



# standalone_app_source/backend/sync_engine/cloud_sync.py
import requests
from ..local_db_adapter import LocalDBAdapter
from ..circuit_breakers.circuit_breaker import ToolHireCircuitBreaker

BASE_API_URL = "https://api.bidbeez.com/toolhire/v1/"
AUTH_TOKEN = "user-specific-jwt-token"

class CloudSyncEngine:
def __init__(self, config):
self.db = LocalDBAdapter()
self.headers = {"Authorization": f"Bearer {AUTH_TOKEN}"}
self.breaker = ToolHireCircuitBreaker()
self.base_url = config.get('BASE_API_URL', BASE_API_URL)

def sync_all(self):
self.sync_new_p2p_listings()
self.sync_new_bookings()

def sync_new_p2p_listings(self):
unsynced_listings = self.db.get_unsynced_listings()
for listing_data in unsynced_listings:
try:
cloud_payload = listing_data.copy()
del cloud_payload['local_id']
del cloud_payload['is_synced']
response = self.breaker.execute(
requests.post,
f"{self.base_url}/p2p-listings/",
headers=self.headers,
json=cloud_payload
)
response.raise_for_status()
cloud_id = response.json().get('id')
self.db.mark_listing_as_synced(listing_data['local_id'], cloud_id)
except Exception as e:
print(f"Error syncing listing {listing_data['title']}: {e}")
self.db.queue_for_retry(listing_data['local_id'])



// standalone_app_source/frontend/src/pages/Booking/P2PBookingFlow.jsx
import React, { useState, useEffect } from 'react';
import { useBookingSDK, useAuth } from '../../services/sdk';

export default function P2PBookingFlow({ bookingId }) {
const [booking, setBooking] = useState(null);
const { currentUser } = useAuth();
const bookingService = useBookingSDK();

useEffect(() => {
bookingService.getBookingDetails(bookingId).then(setBooking).catch(console.error);
}, [bookingId, bookingService]);

const handleConfirmPickup = async () => {
try {
const photos = await takePhotos();
await bookingService.confirmPickup(bookingId, photos);
setBooking(prev => ({ ...prev, status: 'IN_PROGRESS' }));
} catch (e) {
console.error("Pickup confirmation failed:", e);
}
};

const handleConfirmReturn = async () => {
try {
const photos = await takePhotos();
await bookingService.confirmReturn(bookingId, photos);
setBooking(prev => ({ ...prev, status: 'PENDING_COMPLETION' }));
} catch (e) {
console.error("Return confirmation failed:", e);
}
};

const handleCompleteRental = async () => {
try {
await bookingService.completeRental(bookingId);
setBooking(prev => ({ ...prev, status: 'COMPLETED' }));
} catch (e) {
console.error("Rental completion failed:", e);
}
};

if (!booking) {
return <div>Loading Booking...</div>;
}

return (
<div className="booking-flow-card">
<h2>Booking Status: {booking.status.replace('_', ' ')}</h2>
{currentUser.id === booking.renterId && (
<>
{booking.status === 'AWAITING_PICKUP' && (
<button onClick={handleConfirmPickup}>Confirm Tool Pickup</button>
)}
{booking.status === 'AWAITING_RETURN' && (
<p>Please return the tool to the owner.</p>
)}
</>
)}
{currentUser.id === booking.ownerId && (
<>
{booking.status === 'AWAITING_RETURN' && (
<button onClick={handleConfirmReturn}>Confirm Tool has been Returned</button>
)}
</>
)}
{booking.status === 'PENDING_COMPLETION' && (
<div>
<p>Tool returned. Confirm to release payment.</p>
<button onClick={handleCompleteRental}>Finalize & Complete Rental</button>
</div>
)}
{booking.status === 'COMPLETED' && (
<div>
<h3>Rental Complete!</h3>
<p>Payment processed. Please rate your experience.</p>
</div>
)}
</div>
);
}


// standalone_app_source/frontend/src/themes/useTheme.js
import { useEffect } from 'react';
import { useTenantSDK } from '../services/sdk';

export default function useTheme() {
const { currentTenant } = useTenantSDK();

useEffect(() => {
if (currentTenant?.theme) {
const root = document.documentElement;
root.style.setProperty('--primary-color', currentTenant.theme.primary_color);
root.style.setProperty('--secondary-color', currentTenant.theme.secondary_color);
root.style.setProperty('--logo-url', `url(${currentTenant.theme.logo_url})`);
}
}, [currentTenant]);
}


# devops/cicd/toolhire-pipeline.yml
name: ToolHire CI/CD

on:
push:
branches: [ "main" ]
pull_request:
branches: [ "main" ]

jobs:
test-and-build:
runs-on: ubuntu-latest
steps:
- uses: actions/checkout@v3
- uses: actions/setup-python@v4
with:
python-version: '3.9'
- run: pip install -r Bidbeez/backend/services/toolhire/requirements.txt
- run: |
cd Bidbeez/backend/services/toolhire/
python manage.py test
- uses: anchore/scan-action@v3
with:
path: "${{ github.workspace }}/Bidbeez/backend/services/toolhire/"
fail-build: true
severity-cutoff: 'high'
- uses: docker/build-push-action@v4
if: github.ref == 'refs/heads/main'
with:
context: ./Bidbeez/backend/services/toolhire
push: true
tags: your-registry/toolhire:latest

deploy-to-production:
needs: test-and-build
if: github.ref == 'refs/heads/main'
runs-on: ubuntu-latest
steps:
- uses: actions/checkout@v3
with:
repository: 'YourOrg/bidbeez-infra'
path: 'bidbeez-infra'
- run: |
cd bidbeez-infra/kubernetes/toolhire/overlays/production
kubectl apply -k .



# devops/kubernetes/toolhire/base/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
name: toolhire
spec:
replicas: 3
selector:
matchLabels:
app: toolhire
template:
metadata:
labels:
app: toolhire
spec:
containers:
- name: toolhire
image: your-registry/toolhire:latest
ports:
- containerPort: 8000
envFrom:
- configMapRef:
name: toolhire-config
- secretRef:
name: toolhire-secrets



# Bidbeez/backend/services/toolhire/Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
ENTRYPOINT ["./entrypoint.sh"]
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "toolhire.config.wsgi"]


# Bidbeez/backend/services/toolhire/entrypoint.sh
#!/bin/bash
python manage.py migrate
exec "$@"


# Bidbeez/backend/services/toolhire/requirements.txt
django==4.2
djangorestframework==3.14
django-environ==0.10
django.contrib.gis
psycopg2-binary
transitions==0.9
pybreaker==1.1
requests==2.31
celery==5.3
sentry-sdk==1.40
pysqlcipher3


// standalone_app_source/frontend/src/pages/Analytics/OpportunityDashboard.jsx
import React from 'react';
import { useAnalyticsService } from '../../services/sdk';
import { Map, HeatmapLayer } from 'react-leaflet';

export default function OpportunityDashboard() {
const { opportunities } = useAnalyticsService();

return (
<div>
<h3>Market Opportunities</h3>
<Map center={[-26.2041, 28.0473]} zoom={6}>
<HeatmapLayer
points={opportunities.map(o => [o.lat, o.lon, o.demand])}
longitudeExtractor={p => p[1]}
latitudeExtractor={p => p[0]}
intensityExtractor={p => p[2]}
/>
</Map>
</div>
);
}


# Bidbeez/backend/services/toolhire/api/v1/endpoints/traditional_listings.py
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from ..serializers.traditional_serializers import TraditionalListingSerializer
from ...models import TraditionalListing
from ...core.permissions import IsTenantMember

class TraditionalListingViewSet(viewsets.ModelViewSet):
"""
API endpoint for managing traditional tool listings (B2B).
"""
queryset = TraditionalListing.objects.all()
serializer_class = TraditionalListingSerializer
permission_classes = [IsAuthenticated, IsTenantMember]

def perform_create(self, serializer):
serializer.save(company=self.request.user.company, tenant=self.request.tenant)



# Bidbeez/backend/services/toolhire/api/v1/serializers/traditional_serializers.py
from rest_framework import serializers
from ...models import TraditionalListing

class TraditionalListingSerializer(serializers.ModelSerializer):
class Meta:
model = TraditionalListing
fields = ['id', 'title', 'daily_rate_zar', 'depot_location', 'is_available', 'category', 'company']


# Bidbeez/backend/services/toolhire/models/traditional_listing.py
from django.db import models
from django.contrib.gis.db.models import PointField

class TraditionalListing(models.Model):
"""
Represents a tool listed by a formal hire company.
"""
company = models.ForeignKey('auth.Company', on_delete=models.CASCADE)
tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE)
title = models.CharField(max_length=120)
daily_rate_zar = models.DecimalField(max_digits=10, decimal_places=2)
depot_location = PointField()
is_available = models.BooleanField(default=True)
category = models.ForeignKey('ToolCategory', on_delete=models.SET_NULL, null=True)
estimated_value = models.DecimalField(max_digits=10, decimal_places=2) # For insurance
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return f'"{self.title}" by {self.company.name}'


# Bidbeez/backend/services/toolhire/models/tool_category.py
from django.db import models

class ToolCategory(models.Model):
"""
Represents a category of tools (e.g., Power Tools, Gardening).
"""
name = models.CharField(max_length=100, unique=True)
description = models.TextField(blank=True)
created_at = models.DateTimeField(auto_now_add=True)

def __str__(self):
return self.name


# Bidbeez/backend/services/hivemind/services/genetic_optimizer.py
import numpy as np
from ..models.optimization_task import OptimizationTask

class GeneticOptimizer:
"""
Implements a genetic algorithm for optimizing tool rental strategies.
"""
def __init__(self, population_size=50, mutation_rate=0.1):
self.population_size = population_size
self.mutation_rate = mutation_rate

def optimize_rental_strategy(self, task: OptimizationTask):
"""
Optimizes pricing and availability for a tenant's tool listings.
"""
population = self._initialize_population(task)
for generation in range(task.max_iterations):
fitness_scores = self._evaluate_fitness(population, task)
parents = self._select_parents(population, fitness_scores)
offspring = self._crossover(parents)
offspring = self._mutate(offspring)
population = self._replace_population(population, offspring, fitness_scores)
best_solution = population[np.argmax(fitness_scores)]
return self._format_solution(best_solution, task)

def _initialize_population(self, task):
return np.random.rand(self.population_size, task.parameter_count)

def _evaluate_fitness(self, population, task):
# Placeholder: Evaluate based on revenue, demand, and risk
return np.array([task.evaluate_solution(individual) for individual in population])

def _select_parents(self, population, fitness_scores):
indices = np.random.choice(range(self.population_size), size=self.population_size, p=fitness_scores/fitness_scores.sum())
return population[indices]

def _crossover(self, parents):
offspring = parents.copy()
for i in range(0, len(parents), 2):
if np.random.rand() < 0.8:
crossover_point = np.random.randint(1, parents.shape[1])
offspring[i, crossover_point:] = parents[i+1, crossover_point:]
offspring[i+1, crossover_point:] = parents[i, crossover_point:]
return offspring

def _mutate(self, offspring):
mask = np.random.rand(*offspring.shape) < self.mutation_rate
offspring[mask] += np.random.normal(0, 0.1, size=mask.sum())
return np.clip(offspring, 0, 1)

def _replace_population(self, population, offspring, fitness_scores):
worst_indices = np.argsort(fitness_scores)[:len(offspring)]
population[worst_indices] = offspring
return population

def _format_solution(self, solution, task):
return {'parameters': solution.tolist(), 'task_id': task.id}


# Bidbeez/backend/services/hivemind/tasks/async_optimization.py
from celery import shared_task
from ..services.genetic_optimizer import GeneticOptimizer
from ..models.optimization_task import OptimizationTask

@shared_task
def run_optimization_task(task_id):
"""
Runs a genetic optimization task asynchronously.
"""
task = OptimizationTask.objects.get(id=task_id)
optimizer = GeneticOptimizer()
result = optimizer.optimize_rental_strategy(task)
task.result = result
task.status = 'COMPLETED'
task.save()


# Bidbeez/backend/services/hivemind/models/optimization_task.py
from django.db import models

class OptimizationTask(models.Model):
"""
Represents an AI optimization task for HiveMind.
"""
tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE)
task_type = models.CharField(max_length=50) # e.g., 'pricing', 'availability'
parameters = models.JSONField() # Input parameters
result = models.JSONField(default=dict) # Optimization output
status = models.CharField(max_length=20, default='PENDING')
max_iterations = models.IntegerField(default=100)
parameter_count = models.IntegerField() # Number of parameters to optimize
created_at = models.DateTimeField(auto_now_add=True)

def evaluate_solution(self, solution):
# Placeholder: Compute fitness based on task_type
return np.random.rand() # Replace with actual logic



# Bidbeez/backend/services/toolhire/tasks/analytics_tasks.py
from celery import shared_task
from ..services.analytics_service import AnalyticsService

@shared_task
def run_opportunity_analysis(latitude: float, longitude: float, tenant_id: str):
"""
Runs opportunity analysis asynchronously for a tenant.
"""
analytics = AnalyticsService()
result = analytics.analyze_opportunities(latitude, longitude, tenant_id)
# Store result in a tenant-specific analytics cache or DB
return result


// standalone_app_source/frontend/src/services/secure_storage.js
import * as Keychain from 'react-native-keychain'; // For mobile, or equivalent for web

class SecureStorage {
static async save(key, value) {
try {
await Keychain.setGenericPassword(key, value);
return true;
} catch (e) {
console.error('Secure storage save failed:', e);
return false;
}
}

static async get(key) {
try {
const credentials = await Keychain.getGenericPassword();
if (credentials && credentials.username === key) {
return credentials.password;
}
return null;
} catch (e) {
console.error('Secure storage get failed:', e);
return null;
}
}

static async remove(key) {
try {
await Keychain.resetGenericPassword();
return true;
} catch (e) {
console.error('Secure storage remove failed:', e);
return false;
}
}
}

export default SecureStorage;


# Bidbeez/backend/services/toolhire/tests/unit/test_p2p_listings.py
from django.test import TestCase
from ..models import P2PListing
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point

class P2PListingTests(TestCase):
def setUp(self):
User = get_user_model()
self.user = User.objects.create_user(username='testuser', password='testpass')
self.tenant = Tenant.objects.create(id='tenant1')
self.listing = P2PListing.objects.create(
owner=self.user,
tenant=self.tenant,
title='Drill',
daily_rate_zar=50.00,
location=Point(28.0473, -26.2041)
)

def test_listing_creation(self):
self.assertEqual(self.listing.title, 'Drill')
self.assertEqual(self.listing.daily_rate_zar, 50.00)
self.assertTrue(self.listing.is_available)

# Bidbeez/backend/services/toolhire/tests/unit/test_booking_orchestration.py
from django.test import TestCase
from ..models import Booking
from ..services.booking_orchestration_service import BookingStateEngine
from django.contrib.auth import get_user_model

class BookingOrchestrationTests(TestCase):
def setUp(self):
User = get_user_model()
self.user = User.objects.create_user(username='testuser', password='testpass')
self.tenant = Tenant.objects.create(id='tenant1')
self.booking = Booking.objects.create(
renter=self.user,
tenant=self.tenant,
total_cost=100.00,
security_deposit=50.00
)
self.engine = BookingStateEngine(self.booking)

def test_confirm_booking(self):
self.engine.confirm_booking()
self.assertEqual(self.booking.status, 'AWAITING_PAYMENT')


# Bidbeez/backend/services/toolhire/tests/integration/test_geosearch.py
from django.test import TestCase
from ..services.geosearch_service import GeoSearchService
from ..models import P2PListing
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point

class GeoSearchTests(TestCase):
def setUp(self):
User = get_user_model()
self.user = User.objects.create_user(username='testuser', password='testpass')
self.tenant = Tenant.objects.create(id='tenant1')
P2PListing.objects.create(
owner=self.user,
tenant=self.tenant,
title='Drill',
daily_rate_zar=50.00,
location=Point(28.0473, -26.2041)
)

def test_find_nearby_tools(self):
results = GeoSearchService.find_nearby_tools(-26.2041, 28.0473, 10, tenant_id='tenant1')
self.assertEqual(len(results), 1)
self.assertEqual(results[0]['title'], 'Drill')


# Bidbeez/backend/services/toolhire/tests/integration/test_booking_flow.py
from django.test import TestCase
from ..models import Booking, P2PListing
from ..services.booking_orchestration_service import BookingStateEngine
from django.contrib.auth import get_user_model

class BookingFlowTests(TestCase):
def setUp(self):
User = get_user_model()
self.user = User.objects.create_user(username='testuser', password='testpass')
self.tenant = Tenant.objects.create(id='tenant1')
self.listing = P2PListing.objects.create(
owner=self.user,
tenant=self.tenant,
title='Drill',
daily_rate_zar=50.00
)
self.booking = Booking.objects.create(
renter=self.user,
tenant=self.tenant,
listing=self.listing,
total_cost=100.00,
security_deposit=50.00
)
self.engine = BookingStateEngine(self.booking)

def test_full_booking_flow(self):
self.engine.confirm_booking()
self.engine.secure_funds()
self.engine.confirm_pickup(photos=['photo1.jpg'])
self.engine.initiate_return()
self.engine.confirm_return(photos=['photo2.jpg'])
self.engine.complete_rental()
self.assertEqual(self.booking.status, 'COMPLETED')



# Bidbeez/backend/services/toolhire/services/p2p_service.py
from ..models import P2PListing
from ..clients.compliance_client import compliance_client
from ..circuit_breakers.circuit_breaker import ToolHireCircuitBreaker

class P2PService:
def __init__(self):
self.breaker = ToolHireCircuitBreaker()

def verify_user_for_p2p(self, user_id: str, tenant_id: str) -> bool:
"""
Verifies a user is eligible to list tools via KYC/FICA checks.
"""
try:
result = self.breaker.execute(
compliance_client.verify_user,
user_id=user_id,
tenant_id=tenant_id,
compliance_type='FICA'
)
return result['is_compliant']
except Exception as e:
print(f"KYC verification failed for user {user_id}: {e}")
return False

def create_p2p_listing(self, user, tenant, data):
"""
Creates a P2P listing after verification.
"""
if not self.verify_user_for_p2p(user.id, tenant.id):
raise ValueError("User not compliant for P2P listing")
return P2PListing.objects.create(
owner=user,
tenant=tenant,
title=data['title'],
daily_rate_zar=data['daily_rate_zar'],
location=data['location'],
estimated_value=data['estimated_value']
)


# Bidbeez/backend/services/toolhire/tasks/update_ratings_task.py
from celery import shared_task
from ..models import P2PListing, Booking

@shared_task
def update_p2p_ratings(listing_id: str):
"""
Updates the rating for a P2P listing based on completed bookings.
"""
listing = P2PListing.objects.get(id=listing_id)
bookings = Booking.objects.filter(listing=listing, status='COMPLETED')
if bookings.exists():
ratings = [b.rating for b in bookings if b.rating]
if ratings:
listing.rating = sum(ratings) / len(ratings)
listing.hires_completed = bookings.count()
listing.save()


# Bidbeez/backend/services/toolhire/clients/auth_client.py
from .base_client import BaseAPIClient
from ..circuit_breakers.circuit_breaker import ToolHireCircuitBreaker

class AuthClient(BaseAPIClient):
BASE_URL = "https://api.bidbeez.com/auth/v1/"

def __init__(self):
super().__init__()
self.breaker = ToolHireCircuitBreaker()

def validate_user(self, user_id: str, token: str) -> bool:
try:
response = self.breaker.execute(
self.get,
f"users/{user_id}/validate",
headers={"Authorization": f"Bearer {token}"}
)
return response['is_valid']
except Exception as e:
print(f"User validation failed: {e}")
return False


# Bidbeez/backend/services/toolhire/clients/blockchain_client.py
from .base_client import BaseAPIClient
from ..circuit_breakers.circuit_breaker import ToolHireCircuitBreaker

class BlockchainClient(BaseAPIClient):
BASE_URL = "https://api.bidbeez.com/blockchain/v1/"

def __init__(self):
super().__init__()
self.breaker = ToolHireCircuitBreaker()

def log_event(self, booking_id: str, event_type: str, metadata: dict):
try:
response = self.breaker.execute(
self.post,
"events",
data={
"booking_id": booking_id,
"event_type": event_type,
"metadata": metadata
}
)
return response['transaction_id']
except Exception as e:
print(f"Blockchain event logging failed: {e}")
raise



// standalone_app_source/frontend/src/pages/P2P/MyToolListings.jsx
import React, { useState, useEffect } from 'react';
import { useP2PService } from '../../services/sdk';

export default function MyToolListings() {
const [listings, setListings] = useState([]);
const p2pService = useP2PService();

useEffect(() => {
p2pService.getMyListings().then(setListings).catch(console.error);
}, [p2pService]);

const handleToggleAvailability = async (listingId, isAvailable) => {
try {
await p2pService.updateListing(listingId, { is_available: isAvailable });
setListings(prev =>
prev.map(l => l.id === listingId ? { ...l, is_available: isAvailable } : l)
);
} catch (e) {
console.error('Update failed:', e);
}
};

return (
<div>
<h2>My Tool Listings</h2>
{listings.length === 0 ? (
<p>No listings found.</p>
) : (
<ul>
{listings.map(listing => (
<li key={listing.id}>
<h3>{listing.title}</h3>
<p>Rate: ZAR {listing.daily_rate_zar}/day</p>
<p>Status: {listing.is_available ? 'Available' : 'Unavailable'}</p>
<button
onClick={() => handleToggleAvailability(listing.id, !listing.is_available)}
>
Toggle Availability
</button>
</li>
))}
</ul>
)}
</div>
);
}



# standalone_app_source/backend/config_importer.py
import zeroconf
import requests

def discover_bidbeez_endpoints():
"""
Auto-discovers Bidbeez service endpoints via DNS-SD or cloud probe.
"""
try:
services = zeroconf.ServiceBrowser.find('_bidbeez._tcp.local.')
return {
'BASE_API_URL': f"http://{services['toolhire']}/v1",
'AUTH_ENDPOINT': f"http://{services['auth']}/v1",
'HIVEMIND_ENDPOINT': f"http://{services['hivemind']}/v1"
}
except Exception:
response = requests.post(
'https://discovery.bidbeez.com/probe',
data={'version': '1.0'}
)
return response.json()

# Bidbeez/backend/services/toolhire/tasks/analytics_tasks.py
from celery import shared_task
from ..services.analytics_service import AnalyticsService

@shared_task
def run_opportunity_analysis(latitude: float, longitude: float, tenant_id: str):
"""
Runs opportunity analysis asynchronously for a tenant.
"""
analytics = AnalyticsService()
result = analytics.analyze_opportunities(latitude, longitude, tenant_id)
# Store result in a tenant-specific analytics cache or DB
return result


# Bidbeez/backend/services/toolhire/tasks/analytics_tasks.py
import logging
from celery import Celery
from celery.exceptions import MaxRetriesExceededError
from django.core.cache import cache
from datetime import datetime
from prometheus_client import Counter, Histogram
from ..services.analytics_service import AnalyticsService
from ..models import Booking, P2PListing, TraditionalListing, TenantAnalytics
from ..clients.queenbee_client import queenbee_client
from ..circuit_breakers.circuit_breaker import ToolHireCircuitBreaker

# Configure logging with tenant context
logger = logging.getLogger(__name__)

# Prometheus metrics
ANALYTICS_TASK_DURATION = Histogram(
'analytics_task_duration_seconds',
'Duration of analytics tasks',
['task_name'],
buckets=[10, 30, 60, 120, 300]
)
ANALYTICS_TASK_ERRORS = Counter(
'analytics_task_errors_total',
'Total errors in analytics tasks',
['task_name']
)

# Celery configuration (assumes Redis backend)
celery_app = Celery('toolhire', broker='redis://localhost:6379/0/', backend='redis://localhost:6379/1')

@shared_task(bind=True, max_retries=3, retry_backoff=30, retry_jitter=True)
def run_opportunity_analysis(self, task_id: str, latitude: float, longitude: float, radius_km: float=5, tenant_id: str):
"""
Analyzes geographical opportunities for tool rentals near a given location.
Stores results in TenantAnalytics for tenant-specific dashboards.
"""
task_name = 'opportunity_analysis'
with ANALYTICS_TASK_DURATION.labels(task_name).time():
try:
logger = logging.LoggerAdapter(logger, {'tenant_id': tenant_id, 'task_id': 'task_id})
logger.info(f"Starting opportunity analysis for tenant {tenant_id} at ({latitude}, {longitude})")

analytics = AnalyticsService()
breaker = ToolHireCircuitBreaker()

# Fetch data with RLS
bookings = Booking.objects.filter(tenant_id=tenant_id, status='COMPLETED')
listings = list(P2PListing.objects.filter(tenant_id=tenant_id, is_available=True)) + \
list(TraditionalListing.objects.filter(
tenant_id=tenant_id, is_available=True))

# Prepare data for HiveMind
data = {
'bookings': [b.to_dict() for b in bookings],
'listings': [{'id': l.id, 'location': l.location.coords, 'type': 'P2P' if isinstance(l, P2PListing) else 'Company'} for l in listings],
'user_location': {'lat': latitude, 'lon': longitude},
'radius_km': radius_km,
'currency': 'ZAR'
}

# Call HiveMind with circuit breaker
result = breaker.execute(
queenbee_client.analyze_opportunities,
data=data,
tenant_id=tenant_id
)

# Store result in tenant-specific cache and DB
cache_key = f'analytics_opportunity_{tenant_id}_{task_id}'
cache.set(cache_key, result, timeout=3600) # 1 hour
TenantAnalytics.objects.create(
tenant_id=tenant_id,
task_id=task_id,
task_type='OPPORTUNITY_ANALYSIS',
result=result,
created_at=datetime.utcnow()
)

logger.info(f"Opportunity analysis completed for tenant {tenant_id}")
return result

except Exception as e:
ANALYTICS_TASK_ERRORS.labels(task_name).inc()
logger.error(f"Opportunity analysis failed: {e}")
try:
self.retry(countdown=30)
except MaxRetriesExceededError:
logger.error(f"Max retries exceeded for task {task_id}")
return {'error': str(e), 'status': 'FAILED'}

@shared_task(bind=True, max_retries=3, retry_backoff=30, retry_jitter=True)
def run_demand_forecast(self, task_id: str, zipcode: str, month: int, tenant_id: str):
"""
Forecasts rental demand for a given zipcode and month using HiveMind AI.
"""
task_name = 'demand_forecast'
with ANALYTICS_TASK_DURATION.labels(task_name).time():
try:
logger = logging.LoggerAdapter(logger, {'tenant_id': tenant_id, 'task_id': task_id})
logger.info(f"Starting demand forecast for tenant {tenant_id}, zipcode {zipcode}, month {month}")

analytics = AnalyticsService()
breaker = ToolHireCircuitBreaker()

result = breaker.execute(
queenbee_client.predict_demand,
zipcode=zipcode,
month=month,
tenant_id=tenant_id,
currency='ZAR'
)

cache_key = f'analytics_demand_{tenant_id}_{task_id}'
cache.set(cache_key, result, timeout=3600)
TenantAnalytics.objects.create(
tenant_id=tenant_id,
task_id=task_id,
task_type='DEMAND_FORECAST',
result=result,
created_at=datetime.utcnow()
)

logger.info(f"Demand forecast completed for tenant {tenant_id}")
return result

except Exception as e:
ANALYTICS_TASK_ERRORS.labels(task_name).inc()
logger.error(f"Demand forecast failed: {e}")
try:
self.retry(countdown=30)
except MaxRetriesExceededError:
logger.error(f"Max retries exceeded for task {task_id}")
return {'error': str(e), 'status': 'FAILED'}

@shared_task(bind=True, max_retries=3, retry_backoff=30, retry_jitter=True)
def run_supplier_risk_scoring(self, task_id: str, tenant_id: str):
"""
Calculates risk scores for P2P and B2B suppliers based on booking history and KYC.
"""
task_name = 'supplier_risk_scoring'
with ANALYTICS_TASK_DURATION.labels(task_name).time():
try:
logger = logging.LoggerAdapter(logger, {'tenant_id': tenant_id, 'task_id': task_id})
logger.info(f"Starting supplier risk scoring for tenant {tenant_id}")

analytics = AnalyticsService()
breaker = ToolHireCircuitBreaker()

p2p_listings = P2PListing.objects.filter(tenant_id=tenant_id)
traditional_listings = TraditionalListing.objects.filter(tenant_id=tenant_id)
scores = []

for listing in list(p2p_listings) + list(traditional_listings):
bookings = Booking.objects.filter(
tenant_id=tenant_id,
listing=listing if isinstance(listing, P2PListing) else None,
traditional_listing=listing if isinstance(listing, TraditionalListing) else None
)
default_rate = sum(1 for b in bookings if b.status == 'DISPUTED') / max(bookings.count(), 1)
kyc_score = breaker.execute(
queenbee_client.get_kyc_score,
user_id=listing.owner.id if isinstance(listing, P2PListing) else listing.company.id,
tenant_id=tenant_id
).get('kyc_score', 0.5)
risk_score = 0.7 * kyc_score + 0.3 * default_rate
scores.append({
'listing_id': listing.id,
'listing_type': 'P2P' if isinstance(listing, P2PListing) else 'Company',
'risk_score': risk_score
})

result = {'scores': scores}
cache_key = f'analytics_risk_{tenant_id}_{task_id}'
cache.set(cache_key, result, timeout=3600)
TenantAnalytics.objects.create(
tenant_id=tenant_id,
task_id=task_id,
task_type='SUPPLIER_RISK_SCORING',
result=result,
created_at=datetime.utcnow()
)

logger.info(f"Supplier risk scoring completed for tenant {tenant_id}")
return result

except Exception as e:
ANALYTICS_TASK_ERRORS.labels(task_name).inc()
logger.error(f"Supplier risk scoring failed: {e}")
try:
self.retry(countdown=30)
except MaxRetriesExceededError:
logger.error(f"Max retries exceeded for task {task_id}")
return {'error': str(e), 'status': 'FAILED'}

@shared_task(bind=True, max_retries=3, retry_backoff=30, retry_jitter=True)
def run_tenant_benchmarking(self, task_id: str, tenant_id: str):
"""
Benchmarks tenant performance against industry averages.
"""
task_name = 'tenant_benchmarking'
with ANALYTICS_TASK_DURATION.labels(task_name).time():
try:
logger = logging.LoggerAdapter(logger, {'tenant_id': tenant_id, 'task_id': task_id})
logger.info(f"Starting tenant benchmarking for tenant {tenant_id}")

analytics = AnalyticsService()
breaker = ToolHireCircuitBreaker()

# Calculate tenant metrics
bookings = Booking.objects.filter(tenant_id=tenant_id, status='COMPLETED')
total_revenue = sum(b.total_cost for b in bookings)
booking_count = bookings.count()
avg_revenue_per_booking = total_revenue / max(booking_count, 1)

# Fetch industry benchmarks from HiveMind
benchmarks = breaker.execute(
queenbee_client.get_industry_benchmarks,
industry='tool_rental',
region='South_Africa',
currency='ZAR'
)

result = {
'tenant_metrics': {
'total_revenue_zar': float(total_revenue),
'booking_count': booking_count,
'avg_revenue_per_booking_zar': float(avg_revenue_per_booking)
},
'industry_benchmarks': benchmarks
}

cache_key = f'analytics_benchmark_{tenant_id}_{task_id}'
cache.set(cache_key, result, timeout=3600)
TenantAnalytics.objects.create(
tenant_id=tenant_id,
task_id=task_id,
task_type='TENANT_BENCHMARKING',
result=result,
created_at=datetime.utcnow()
)

logger.info(f"Tenant benchmarking completed for tenant {tenant_id}")
return result

except Exception as e:
ANALYTICS_TASK_ERRORS.labels(task_name).inc()
logger.error(f"Tenant benchmarking failed: {e}")
try:
self.retry(countdown=30)
except MaxRetriesExceededError:
logger.error(f"Max retries exceeded for task {task_id}")
return {'error': str(e), 'status': 'FAILED'}


# Bidbeez/backend/services/toolhire/models/tenant_analytics.py
from django.db import models

class TenantAnalytics(models.Model):
"""
Stores results of analytics tasks for a tenant.
"""
tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE)
task_id = models.CharField(max_length=50, unique=True)
task_type = models.CharField(max_length=50) # e.g., OPPORTUNITY_ANALYSIS
result = models.JSONField()
created_at = models.DateTimeField()
updated_at = models.DateTimeField(auto_now=True)

class Meta:
indexes = [models.Index(fields=['tenant', 'task_type'])]


# Bidbeez/backend/services/toolhire/services/analytics_service.py
from ..clients.queenbee_client import queenbee_client
from ..models import Booking, P2PListing, TraditionalListing

class AnalyticsService:
def analyze_opportunities(self, latitude: float, longitude: float, tenant_id: str):
bookings = Booking.objects.filter(status='COMPLETED', tenant_id=tenant_id)
listings = list(P2PListing.objects.filter(is_available=True, tenant_id=tenant_id)) + \
list(TraditionalListing.objects.filter(is_available=True, tenant_id=tenant_id))
data = {
'bookings': [b.to_dict() for b in bookings],
'listings': [{'id': l.id, 'location': l.location.coords, 'type': 'P2P' if isinstance(l, P2PListing) else 'Company'} for l in listings],
'user_location': {'lat': latitude, 'lon': longitude}
}
return queenbee_client.analyze_opportunities(data, tenant_id=tenant_id)

def predict_demand(self, zipcode: str, month: int, tenant_id: str):
return queenbee_client.predict_demand(zipcode=zipcode, month=month, tenant_id=tenant_id)



# Bidbeez/backend/services/toolhire/clients/queenbee_client.py
from .base_client import BaseAPIClient

class QueenbeeClient(BaseAPIClient):
BASE_URL = "https://api.bidbeez.com/hivemind/v1/"

def analyze_opportunities(self, data: dict, tenant_id: str):
return self.post("analytics/opportunities", data={**data, 'tenant_id': tenant_id})

def predict_demand(self, zipcode: str, month: int, tenant_id: str, currency: str):
return self.post("analytics/demand", data={
'zipcode': zipcode,
'month': month,
'tenant_id': tenant_id,
'currency': currency
})

def get_kyc_score(self, user_id: str, tenant_id: str):
return self.get(f"compliance/kyc/{user_id}", params={'tenant_id': tenant_id})

def get_industry_benchmarks(self, industry: str, region: str, currency: str):
return self.get("benchmarks", params={
'industry': industry,
'region': region,
'currency': currency
})


# devops/monitoring/grafana/dashboards/toolhire_analytics.json
{
"dashboard": {
"title": "ToolHire Analytics Performance",
"panels": [
{
"title": "Task Duration",
"type": "graph",
"targets": [
{
"expr": "histogram_quantile(0.95, sum(rate(analytics_task_duration_seconds_bucket[5m])) by (le, task_name))",
"legendFormat": "{{task_name}}"
}
]
},
{
"title": "Task Errors",
"type": "graph",
"targets": [
{
"expr": "rate(analytics_task_errors_total[5m])",
"legendFormat": "{{task_name}}"
}
]
}
]
}
}