1. Final Directory Structure
This structure provides a clear separation of concerns, vital for a large-scale application.
bidbeez/
└── contracts/
├── api/
│ ├── __init__.py
│ ├── v1/
│ │ ├── __init__.py
│ │ ├── endpoints/
│ │ │ ├── analysis.py # Handles the async analysis requests
│ │ │ └── collaboration.py # Handles WebSocket and annotations
│ │ └── schemas.py # Pydantic data contracts for the API
│ └── deps.py # FastAPI dependencies (e.g., auth, services)
│
├── core/
│ ├── __init__.py
│ └── config.py # Centralized configuration management
│
├── models/
│ ├── __init__.py
│ └── all_models.py # Django ORM or SQLAlchemy models
│
├── services/
│ ├── __init__.py
│ ├── analysis_service.py # Main orchestration service
│ ├── clustering_service.py # NLP query clustering logic
│ ├── feature_extraction_service.py # (NEW) PDF parsing and feature creation
│ ├── knowledge_graph_service.py# Neo4j integration
│ ├── mitigation_service.py # Generates mitigation plans
│ └── translation_service.py # Handles multilingual translations & QA
│
├── ml/
│ ├── __init__.py
│ ├── explainers.py # SHAP implementation
│ ├── predictors.py # ML model and Rules Engine predictors
│ └── routing.py # The advanced HybridModelRouter
│
├── tasks/
│ ├── __init__.py
│ └── analysis_tasks.py # Celery tasks for background processing
│
├── ui/
│ ├── risk_bar_component.dart # Enhanced for accessibility
│ ├── collaboration/
│ │ └── clause_chat_view.dart # With WebSocket logic and pagination
│ └── onboarding/
│ └── onboarding_wizard.dart # Guides users through features
│
└── tests/
├── __init__.py
├── test_analysis_flow.py # End-to-end test for the async flow
├── test_knowledge_graph.py
└── test_translation_quality.py

2. SQL Schema (contracts_schema.sql)
This robust PostgreSQL schema includes pgvector support and is heavily indexed for performance.
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "vector";

-- =================================================================
-- CORE TABLES
-- =================================================================

CREATE TABLE contracts_contractstandard (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
code VARCHAR(20) UNIQUE NOT NULL,
name VARCHAR(150) NOT NULL,
version VARCHAR(50),
governing_body VARCHAR(255),
is_active BOOLEAN DEFAULT TRUE,
created_at TIMESTAMPTZ DEFAULT NOW(),
updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE contracts_contractclause (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
standard_id UUID NOT NULL REFERENCES contracts_contractstandard(id) ON DELETE CASCADE,
reference_code VARCHAR(50),
title VARCHAR(250) NOT NULL,
description TEXT,
source_page_number INT,
risk_level VARCHAR(10) CHECK (risk_level IN ('Low', 'Medium', 'High', 'Critical')),
embedding public.vector(768), -- For sentence-transformer models
is_flagged BOOLEAN DEFAULT FALSE,
created_at TIMESTAMPTZ DEFAULT NOW(),
updated_at TIMESTAMPTZ DEFAULT NOW(),
UNIQUE(standard_id, reference_code)
);
CREATE INDEX idx_clauses_embedding ON contracts_contractclause USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);
CREATE INDEX idx_clauses_title_trgm ON contracts_contractclause USING gin(title gin_trgm_ops);


CREATE TABLE contracts_translatedclausetext (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
clause_id UUID NOT NULL REFERENCES contracts_contractclause(id) ON DELETE CASCADE,
language_code VARCHAR(10) NOT NULL,
translated_title VARCHAR(250) NOT NULL,
translated_description TEXT NOT NULL,
is_human_verified BOOLEAN DEFAULT FALSE,
translation_quality_score FLOAT, -- e.g., BLEU score
last_updated TIMESTAMPTZ DEFAULT NOW(),
UNIQUE (clause_id, language_code)
);

-- =================================================================
-- ANALYSIS & MITIGATION TABLES
-- =================================================================

CREATE TABLE contracts_contractriskanalysis (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
tender_id UUID NOT NULL UNIQUE,
risk_score FLOAT NOT NULL CHECK (risk_score BETWEEN 0 AND 10.0),
confidence_score FLOAT NOT NULL CHECK (confidence_score BETWEEN 0 AND 1.0),
recommendation VARCHAR(20) NOT NULL,
executive_summary TEXT,
key_risk_factors JSONB, -- [{"feature": "...", "impact": 0.4, "narrative": "...", "source": "SHAP/KG/Rules"}]
analysis_date TIMESTAMPTZ DEFAULT NOW(),
model_version VARCHAR(50) NOT NULL
);

CREATE TABLE contracts_mitigationplan (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
analysis_id UUID NOT NULL REFERENCES contracts_contractriskanalysis(id) ON DELETE CASCADE,
plan_data JSONB NOT NULL, -- [{"risk_factor": "...", "actions": ["..."], "owner_id": "uuid", "status": "Pending"}]
status VARCHAR(20) DEFAULT 'Draft',
created_by_id UUID NOT NULL,
created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =================================================================
-- COLLABORATION & NLP TABLES
-- =================================================================

CREATE TABLE contracts_clauseannotation (
id BIGSERIAL PRIMARY KEY,
clause_id UUID NOT NULL REFERENCES contracts_contractclause(id),
tender_id UUID NOT NULL,
user_id UUID NOT NULL,
comment TEXT NOT NULL,
created_at TIMESTAMPTZ DEFAULT NOW()
);
CREATE INDEX idx_annotation_clause_tender ON contracts_clauseannotation(clause_id, tender_id);


CREATE TABLE contracts_clausequerycluster (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
centroid_text TEXT NOT NULL,
keywords TEXT[] DEFAULT '{}',
topic_summary TEXT,
last_occurrence TIMESTAMPTZ DEFAULT NOW()
);

-- =================================================================
-- PERFORMANCE & LOGGING
-- =================================================================

CREATE TABLE contracts_aiperformancemetrics (
id SERIAL PRIMARY KEY,
metric_date DATE NOT NULL UNIQUE,
avg_confidence_score FLOAT,
hybrid_model_fallback_rate FLOAT,
human_override_rate FLOAT,
per_tender_metrics JSONB, -- {"avg_analysis_time_seconds": 120, "high_risk_tender_pct": 15}
per_user_metrics JSONB, -- {"avg_queries_per_user": 2.5, "plans_created": 50}
created_at TIMESTAMPTZ DEFAULT NOW()
);

3. Python Backend (FastAPI, Celery, Services)
contracts/core/config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
# App
PROJECT_NAME: str = "Bidbeez Contracts AI"

# Database
DATABASE_URL: str = "postgresql://user:password@localhost/bidbeez_contracts"

# Redis for Celery
REDIS_URL: str = "redis://localhost:6379/0"

# Knowledge Graph
NEO4J_URI: str = "bolt://localhost:7687"
NEO4J_USER: str = "neo4j"
NEO4J_PASSWORD: str = "password"

# Google Cloud
GOOGLE_APPLICATION_CREDENTIALS: str = "/path/to/your/gcp-credentials.json"

# ML Thresholds
HIGH_VALUE_TENDER_THRESHOLD: float = 1_000_000.00
CONFIDENCE_THRESHOLD_DEFAULT: float = 0.65
CONFIDENCE_THRESHOLD_HIGH_VALUE: float = 0.75

# Supported Languages
SUPPORTED_LANGUAGES: list[str] = ['zu', 'xh', 'af', 'en', 'st', 'tn', 've']

class Config:
case_sensitive = True

settings = Settings()

contracts/tasks/analysis_tasks.py
from celery import Celery
from uuid import UUID
from ..core.config import settings
from ..services.analysis_service import AnalysisService
from ..services.feature_extraction_service import FeatureExtractionService
# Assume services are injectable or instantiated here
import asyncio

celery_app = Celery("contracts_worker", broker=settings.REDIS_URL)

@celery_app.task(name="run_tender_analysis")
def run_tender_analysis(tender_id: str, document_url: str, tender_value: float, sector: str):
"""
Celery task to perform the full analysis workflow asynchronously.
"""
async def main():
feature_service = FeatureExtractionService()
analysis_service = AnalysisService() # In a real app, use a dependency injection container

# 1. Extract features from the document
features = await feature_service.extract_features_from_url(document_url)
features.update({
"tender_value": tender_value,
"sector": sector,
})

# 2. Run the full analysis
analysis_result = await analysis_service.analyze_tender(UUID(tender_id), features)

# 3. Notify user/system of completion (e.g., via webhook, email, or saving to DB)
print(f"Analysis complete for tender {tender_id}")
return analysis_result

return asyncio.run(main())

contracts/services/feature_extraction_service.py
import fitz # PyMuPDF
import requests
from sentence_transformers import SentenceTransformer

class FeatureExtractionService:
def __init__(self):
# This model should be loaded once and kept in memory
self.nlp_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')

async def extract_features_from_url(self, document_url: str) -> dict:
"""Downloads a PDF and extracts text and basic features."""
response = requests.get(document_url, stream=True)
response.raise_for_status()

text_content = ""
with fitz.open(stream=response.content, filetype="pdf") as doc:
for page in doc:
text_content += page.get_text()

lower_text = text_content.lower()

# Simple keyword-based feature extraction (can be expanded with NLP NER)
features = {
"text_length": len(lower_text),
"contains_liquidated_damages": "liquidated damages" in lower_text,
"contains_termination_clause": "termination for default" in lower_text,
"document_embedding": self.nlp_model.encode(text_content[:4096]).tolist() # Truncate for performance
}
return features

contracts/services/translation_service.py
from uuid import UUID
from google.cloud import translate_v2 as translate
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from ..core.config import settings
# from ..models.all_models import ContractClause, TranslatedClauseText (using Django ORM syntax as example)

class TranslationService:
def __init__(self):
self.client = translate.Client()
self.smoother = SmoothingFunction().method1

async def translate_and_assess(self, clause_id: UUID, target_language: str) -> dict:
"""Translates a clause and assesses the quality using BLEU score."""
if target_language not in settings.SUPPORTED_LANGUAGES:
raise ValueError(f"Language '{target_language}' is not supported.")

# clause = await ContractClause.objects.get(id=clause_id) # Async ORM
# For demo, using placeholder text
original_title = "Liquidated Damages"
original_desc = "In the event of a delay, the contractor shall be liable to pay a penalty."

result = self.client.translate(
[original_title, original_desc],
target_language=target_language,
source_language='en'
)

translated_title = result[0]['translatedText']
translated_desc = result[1]['translatedText']

# Calculate BLEU score
reference = [original_desc.split()]
candidate = translated_desc.split()
bleu_score = sentence_bleu(reference, candidate, smoothing_function=self.smoother)

is_human_verified = bleu_score > 0.75 # Auto-verify high-quality translations

# await TranslatedClauseText.objects.create(...)

if not is_human_verified:
# Add to a human verification queue (e.g., Redis list, database table)
print(f"Clause {clause_id} translation to {target_language} needs human verification (BLEU: {bleu_score:.2f})")

return {
"clause_id": clause_id,
"language": target_language,
"translated_title": translated_title,
"translated_description": translated_desc,
"bleu_score": bleu_score,
"is_human_verified": is_human_verified,
}

contracts/api/v1/endpoints/analysis.py
from fastapi import APIRouter, status, BackgroundTasks
from uuid import UUID
from ..schemas import TenderAnalysisRequest, AnalysisQueuedResponse
from ....tasks.analysis_tasks import run_tender_analysis

router = APIRouter()

@router.post(
"/analyze",
response_model=AnalysisQueuedResponse,
status_code=status.HTTP_202_ACCEPTED
)
async def create_tender_analysis(
request: TenderAnalysisRequest,
background_tasks: BackgroundTasks
):
"""
Accepts a tender for analysis. The process runs in the background.
"""
# Use Celery's delay method to queue the task
run_tender_analysis.delay(
tender_id=str(request.tender_id),
document_url=request.document_url,
tender_value=request.tender_value,
sector=request.sector
)

return AnalysisQueuedResponse(
message="Tender analysis has been queued.",
tender_id=request.tender_id
)


contracts/api/v1/endpoints/collaboration.py
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from typing import List
from uuid import UUID
# from ....services.collaboration_service import CollaborationService (service to handle db ops)
# from ..schemas import Annotation

router = APIRouter()

class ConnectionManager:
def __init__(self):
self.active_connections: dict[str, List[WebSocket]] = {}

async def connect(self, websocket: WebSocket, room_id: str):
await websocket.accept()
if room_id not in self.active_connections:
self.active_connections[room_id] = []
self.active_connections[room_id].append(websocket)

def disconnect(self, websocket: WebSocket, room_id: str):
self.active_connections[room_id].remove(websocket)

async def broadcast(self, message: str, room_id: str):
if room_id in self.active_connections:
for connection in self.active_connections[room_id]:
await connection.send_text(message)

manager = ConnectionManager()

@router.websocket("/ws/clauses/{clause_id}/tenders/{tender_id}")
async def websocket_endpoint(websocket: WebSocket, clause_id: UUID, tender_id: UUID):
room_id = f"{tender_id}:{clause_id}"
await manager.connect(websocket, room_id)
try:
while True:
data = await websocket.receive_text()
# Here you would save the annotation to the database
# new_annotation = await collaboration_service.create_annotation(...)
# Then broadcast the new annotation to all clients in the room
await manager.broadcast(data, room_id)
except WebSocketDisconnect:
manager.disconnect(websocket, room_id)

4. Flutter UI
contracts/ui/collaboration/clause_chat_view.dart
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../models/annotation.dart'; // Assuming you have this model
import '../services/api_service.dart'; // Assuming a service to fetch annotations

class ClauseChatView extends StatefulWidget {
final String clauseId;
final String tenderId;
final String wsUrl;

const ClauseChatView({
Key? key,
required this.clauseId,
required this.tenderId,
required this.wsUrl,
}) : super(key: key);

@override
_ClauseChatViewState createState() => _ClauseChatViewState();
}

class _ClauseChatViewState extends State<ClauseChatView> {
static const _pageSize = 20;
final PagingController<int, Annotation> _pagingController = PagingController(firstPageKey: 0);
final TextEditingController _textController = TextEditingController();
WebSocketChannel? _channel;
bool _isConnected = false;
Timer? _reconnectionTimer;

@override
void initState() {
super.initState();
_pagingController.addPageRequestListener((pageKey) {
_fetchPage(pageKey);
});
_connectWebSocket();
}

void _connectWebSocket() {
if (_reconnectionTimer != null) _reconnectionTimer!.cancel();

_channel = WebSocketChannel.connect(Uri.parse(widget.wsUrl));
_channel!.stream.listen(
(message) {
if (!mounted) return;
setState(() => _isConnected = true);
final annotation = Annotation.fromJson(jsonDecode(message));
_pagingController.itemList?.insert(0, annotation);
// ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
_pagingController.notifyListeners();
},
onDone: () {
if (mounted) {
setState(() => _isConnected = false);
_scheduleReconnection();
}
},
onError: (error) {
if (mounted) {
setState(() => _isConnected = false);
_scheduleReconnection();
}
},
);
setState(() => _isConnected = true);
}

void _scheduleReconnection() {
_reconnectionTimer = Timer(const Duration(seconds: 5), _connectWebSocket);
}

Future<void> _fetchPage(int pageKey) async {
try {
final newItems = await ApiService.fetchAnnotations(
tenderId: widget.tenderId,
clauseId: widget.clauseId,
page: pageKey,
pageSize: _pageSize,
);
final isLastPage = newItems.length < _pageSize;
if (isLastPage) {
_pagingController.appendLastPage(newItems);
} else {
final nextPageKey = pageKey + 1;
_pagingController.appendPage(newItems, nextPageKey);
}
} catch (error) {
_pagingController.error = error;
}
}

void _sendMessage() {
if (_textController.text.isNotEmpty && _isConnected) {
final message = {
"user_id": "current_user_uuid", // Get from auth service
"comment": _textController.text,
};
_channel?.sink.add(jsonEncode(message));
_textController.clear();
}
}

@override
Widget build(BuildContext context) {
return Column(
children: [
if (!_isConnected)
Container(
color: Colors.red.shade100,
width: double.infinity,
padding: const EdgeInsets.all(8.0),
child: const Text('Connecting to collaboration service...', textAlign: TextAlign.center),
),
Expanded(
child: PagedListView<int, Annotation>.separated(
pagingController: _pagingController,
reverse: true,
builderDelegate: PagedChildBuilderDelegate<Annotation>(
itemBuilder: (context, item, index) => ListTile(
title: Text(item.comment),
subtitle: Text("by ${item.userId}"), // Fetch user name
),
),
separatorBuilder: (context, index) => const Divider(),
),
),
// Input field area...
],
);
}

@override
void dispose() {
_channel?.sink.close();
_pagingController.dispose();
_reconnectionTimer?.cancel();
super.dispose();
}
}

contracts/ui/onboarding/onboarding_wizard.dart
import 'package:flutter/material.dart';
import '../risk_bar_component.dart'; // Placeholder
import '../collaboration/clause_chat_view.dart'; // Placeholder

class OnboardingWizard extends StatefulWidget {
const OnboardingWizard({Key? key}) : super(key: key);

@override
_OnboardingWizardState createState() => _OnboardingWizardState();
}

class _OnboardingWizardStep {
final String title;
final String description;
final Widget content;

_OnboardingWizardStep({
required this.title,
required this.description,
required this.content,
});
}

class _OnboardingWizardState extends State<OnboardingWizard> {
final PageController _pageController = PageController();
int _currentPage = 0;

late final List<_OnboardingWizardStep> _steps = [
_OnboardingWizardStep(
title: "Understand Risk Instantly",
description: "Our AI analyzes contracts to give you a clear risk score and confidence level.",
content: const RiskBar(riskScore: 7.5, confidence: 0.88)),
_OnboardingWizardStep(
title: "Collaborate in Real-Time",
description: "Discuss specific clauses with your team directly within the tender document.",
content: Container(child: const Center(child: Text("Live Chat Preview")))), // Replace with chat preview
_OnboardingWizardStep(
title: "Build Actionable Plans",
description: "Turn AI-driven insights into concrete mitigation tasks for your team.",
content: Container(child: const Center(child: Text("Mitigation Plan Preview")))),
];

@override
Widget build(BuildContext context) {
return Scaffold(
body: SafeArea(
child: Column(
children: [
Expanded(
child: PageView.builder(
controller: _pageController,
itemCount: _steps.length,
onPageChanged: (page) => setState(() => _currentPage = page),
itemBuilder: (context, index) {
return Padding(
padding: const EdgeInsets.all(24.0),
child: Column(
mainAxisAlignment: MainAxisAlignment.center,
children: [
Text(_steps[index].title, style: Theme.of(context).textTheme.headlineSmall),
const SizedBox(height: 16),
Text(_steps[index].description, textAlign: TextAlign.center),
const SizedBox(height: 32),
_steps[index].content,
],
),
);
},
),
),
// Navigation controls
Padding(
padding: const EdgeInsets.all(16.0),
child: Row(
mainAxisAlignment: MainAxisAlignment.spaceBetween,
children: [
TextButton(onPressed: () {}, child: const Text("Skip")),
ElevatedButton(
onPressed: () {
if (_currentPage < _steps.length - 1) {
_pageController.nextPage(
duration: const Duration(milliseconds: 300),
curve: Curves.easeIn,
);
} else {
// Navigate to home screen
}
},
child: Text(_currentPage < _steps.length - 1 ? "Next" : "Finish"),
),
],
),
),
],
),
),
);
}
}


1. AI Model Auto-Switch
This logic is added to the HybridModelRouter to pre-emptively choose a prediction engine based on tender value, optimizing for cost and speed.
🧠 contracts/ml/routing.py (Modified)
from .predictors import MLPredictor, RulesEnginePredictor
from ..core.config import settings

class HybridModelRouter:
def __init__(self, ml_predictor: MLPredictor, rules_predictor: RulesEnginePredictor):
self.ml_predictor = ml_predictor
self.rules_predictor = rules_predictor

def _get_dynamic_threshold(self, features: dict) -> float:
"""Set a stricter confidence threshold for high-value tenders."""
if features.get('tender_value', 0) > settings.HIGH_VALUE_TENDER_THRESHOLD:
return settings.CONFIDENCE_THRESHOLD_HIGH_VALUE
return settings.CONFIDENCE_THRESHOLD_DEFAULT

async def predict_with_explanation(self, features: dict) -> dict:
"""
NEW: First, decide which model to use based on tender value.
Then, perform the prediction and explanation.
"""
tender_value = features.get('tender_value', 0)

# --- AUTO-SWITCH LOGIC ---
if tender_value < settings.HIGH_VALUE_TENDER_THRESHOLD:
# For low-value tenders, go straight to the fast, cheap rules engine.
rule_pred, rule_explanation = await self.rules_predictor.predict(features)
return {
"prediction": rule_pred,
"confidence": 1.0, # Rules are deterministic
"explanation": rule_explanation,
"method": "RulesEngine (Auto-Switch)",
"dynamic_threshold": self._get_dynamic_threshold(features),
}

# --- ORIGINAL HIGH-VALUE TENDER LOGIC ---
threshold = self._get_dynamic_threshold(features)
ml_pred, ml_confidence, ml_explanation = await self.ml_predictor.predict(features)

if ml_confidence >= threshold:
return {
"prediction": ml_pred,
"confidence": ml_confidence,
"explanation": ml_explanation,
"method": "SHAP_Model",
"dynamic_threshold": threshold,
}

# Fallback for high-value tenders with low confidence
rule_pred, rule_explanation = await self.rules_predictor.predict(features)
return {
"prediction": rule_pred,
"confidence": 1.0,
"explanation": rule_explanation,
"method": "RulesEngine (Fallback)",
"dynamic_threshold": threshold,
}

2. Smart Clause Extraction (LayoutLMv3)
This snippet shows a more advanced FeatureExtractionService using the Hugging Face transformers library to understand document layouts.
🧠 contracts/services/feature_extraction_service.py (Upgraded)
import fitz # PyMuPDF
import requests
from PIL import Image
from transformers import pipeline, AutoProcessor, LayoutLMv3ForTokenClassification
import io

class SmartFeatureExtractionService:
def __init__(self):
# These models are large and should be loaded once as singletons
self.processor = AutoProcessor.from_pretrained("microsoft/layoutlmv3-base", apply_ocr=False)
self.model = LayoutLMv3ForTokenClassification.from_pretrained("microsoft/layoutlmv3-base")
# In a real scenario, you'd use a model fine-tuned on contract document labels
# self.document_classifier = pipeline("document-question-answering", model="impira/layoutlm-document-qa")

async def extract_structured_clauses(self, document_url: str) -> list[dict]:
"""
Downloads a PDF, converts pages to images, and uses LayoutLMv3
to extract structured data from the document layout.
"""
response = requests.get(document_url)
response.raise_for_status()

extracted_clauses = []

with fitz.open(stream=io.BytesIO(response.content), filetype="pdf") as doc:
for page_num, page in enumerate(doc):
pix = page.get_pixmap()
image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

# Use a layout-aware model to find key elements
# Note: This is a simplified example. A real implementation would
# involve more sophisticated token classification logic.
# E.g., model fine-tuned to recognize 'clause_title', 'clause_body' etc.

# For demonstration, we'll use a QA pipeline to find a specific clause
# qa_pipeline = self.document_classifier
# question = "What is the text of the limitation of liability clause?"
# result = qa_pipeline(image, question)
# extracted_clauses.append({"page": page_num + 1, "clause": result})

# Placeholder for complex extraction logic
extracted_clauses.append({
"page": page_num + 1,
"clause_title": "Example: Limitation of Liability (from LayoutLM)",
"clause_text": "The total liability of the Contractor shall not exceed the Contract Price.",
"bounding_box": [100, 250, 500, 350] # [x0, y0, x1, y1]
})

return extracted_clauses

3. Auto-Mitigator & 4. Legal AdvisorBot
These are combined into a new GenerativeService using a RAG pattern.
🧠 contracts/services/generative_service.py (New)
import openai
from ..core.config import settings
# from ..services.vector_search_service import find_similar_clauses # Assume a vector search service

# openai.api_key = settings.OPENAI_API_KEY

class GenerativeService:

async def suggest_mitigation(self, risky_clause_text: str) -> str:
"""Uses RAG to generate a safer, redlined version of a clause."""

# 1. Retrieve similar, pre-approved clauses from vector DB
# similar_clauses = await find_similar_clauses(risky_clause_text, top_k=3)
# approved_examples = "\n".join([c.text for c in similar_clauses])
approved_examples = "Example: The Contractor's total liability for all claims shall be capped at 75% of the contract value." # Placeholder

# 2. Construct a detailed prompt for the LLM
prompt = f"""
You are an expert contract lawyer. A client has a contract with the following risky clause:
---
RISKY CLAUSE: "{risky_clause_text}"
---
Your task is to rewrite this clause to be more balanced and fair, reducing the client's risk.
Use the following examples of approved clauses as a style guide for your suggestion.
---
APPROVED EXAMPLES:
{approved_examples}
---
Provide only the rewritten clause text.
"""

# 3. Call the LLM
# response = await openai.ChatCompletion.acreate( ... )
# suggestion = response.choices[0].message.content
suggestion = "The Contractor's total liability arising from or in connection with the contract shall be limited to the total fees paid under this agreement." # Placeholder response

return suggestion

async def answer_legal_question(self, question: str, clause_context: str) -> dict:
"""Answers a user's question about a clause, citing the source."""
prompt = f"""
You are a helpful AI Legal Assistant. A user has a question about a specific contract clause.
Your task is to answer the question based *only* on the provided text.
Do not provide legal advice. If the answer is not in the text, say so.
---
CONTRACT CLAUSE TEXT: "{clause_context}"
---
USER QUESTION: "{question}"
---
YOUR ANSWER:
"""

# response = await openai.ChatCompletion.acreate( ... )
# answer = response.choices[0].message.content
answer = "Based on the text, it means a penalty will be applied for delays. It does not specify the exact amount." # Placeholder response

return {
"answer": answer,
"source": "Provided Clause Context"
}

🧠 contracts/ui/bots/legal_advisor_bot_view.dart (New UI)
import 'package:flutter/material.dart';

// Assuming Message model: class Message { final String text; final bool isFromUser; final String? source; }

class LegalAdvisorBotView extends StatefulWidget {
final String clauseContext;
const LegalAdvisorBotView({Key? key, required this.clauseContext}) : super(key: key);
@override
_LegalAdvisorBotViewState createState() => _LegalAdvisorBotViewState();
}

class _LegalAdvisorBotViewState extends State<LegalAdvisorBotView> {
final List<Message> _messages = [];
final TextEditingController _controller = TextEditingController();

void _handleSubmitted(String text) {
_controller.clear();
setState(() {
_messages.insert(0, Message(text: text, isFromUser: true));
});
// Call API to get bot response
// final botResponse = await ApiService.askBot(question: text, context: widget.clauseContext);
// setState(() {
// _messages.insert(0, Message(text: botResponse.answer, isFromUser: false, source: "Clause X.Y"));
// });
}

@override
Widget build(BuildContext context) {
return Column(
children: [
Container(
padding: const EdgeInsets.all(8.0),
color: Colors.amber.shade100,
child: const Text(
"AI Assistant: I can answer questions about the provided text. I am not a lawyer and this is not legal advice.",
textAlign: TextAlign.center,
style: TextStyle(fontSize: 12),
),
),
Expanded(
child: ListView.builder(
reverse: true,
itemCount: _messages.length,
itemBuilder: (context, index) {
final message = _messages[index];
return ListTile(
title: Align(
alignment: message.isFromUser ? Alignment.centerRight : Alignment.centerLeft,
child: Container(
padding: const EdgeInsets.all(12),
decoration: BoxDecoration(
color: message.isFromUser ? Colors.blue.shade100 : Colors.grey.shade200,
borderRadius: BorderRadius.circular(16),
),
child: Column(
crossAxisAlignment: CrossAxisAlignment.start,
children: [
Text(message.text),
if (message.source != null) ...[
const SizedBox(height: 8),
Text("Source: ${message.source!}", style: const TextStyle(fontSize: 10, fontStyle: FontStyle.italic)),
]
],
),
),
),
);
},
),
),
// Input field...
],
);
}
}

5. Clause Benchmarking
This new service provides market context for a given clause.
🧠 contracts/services/benchmarking_service.py (New)
import numpy as np

class BenchmarkingService:
async def get_clause_benchmark(self, clause_embedding: list[float]) -> dict:
"""
Finds similar clauses and aggregates data to create a market benchmark.
"""
# 1. Use vector search to find the semantic cluster of this clause
# cluster_id = await find_clause_cluster(clause_embedding)

# 2. Query DB for all clauses and their analyses within this cluster
# analyses = await fetch_analyses_for_cluster(cluster_id)

# 3. Aggregate statistics (using placeholder data)
risk_scores = [6.5, 7.1, 5.8, 8.0, 6.9, 7.3]

market_average_score = np.mean(risk_scores)
percentile = np.searchsorted(sorted(risk_scores), 7.8) / len(risk_scores) * 100

return {
"market_average_risk_score": round(market_average_score, 2),
"current_clause_risk_score": 7.8, # Assuming this is the score of the clause being benchmarked
"percentile_rank": round(percentile), # e.g., 83rd percentile = riskier than 83% of similar clauses
"common_mitigations": [
"Negotiate a liability cap",
"Ensure insurance coverage is adequate"
],
"data_source": "Based on 1,250 Construction Tenders (Gauteng, 2024-2025)"
}

🧠 contracts/ui/benchmarking/clause_benchmark_widget.dart (New UI)
import 'package:flutter/material.dart';

// Assuming BenchmarkData model

class ClauseBenchmarkWidget extends StatelessWidget {
final BenchmarkData data;
const ClauseBenchmarkWidget({Key? key, required this.data}) : super(key: key);

@override
Widget build(BuildContext context) {
return Card(
elevation: 2,
child: Padding(
padding: const EdgeInsets.all(16.0),
child: Column(
crossAxisAlignment: CrossAxisAlignment.start,
children: [
Text("Clause Benchmark", style: Theme.of(context).textTheme.titleLarge),
Text(data.dataSource, style: Theme.of(context).textTheme.bodySmall),
const Divider(height: 24),
Row(
mainAxisAlignment: MainAxisAlignment.spaceAround,
children: [
_buildMetric("Your Clause Risk", data.currentClauseRiskScore.toString()),
_buildMetric("Market Average", data.marketAverageRiskScore.toString()),
_buildMetric("Riskier Than", "${data.percentileRank}% of market"),
],
),
const SizedBox(height: 16),
Text("Common Mitigation Strategies:", style: Theme.of(context).textTheme.titleMedium),
...data.commonMitigations.map((text) => ListTile(
leading: const Icon(Icons.check_circle_outline, color: Colors.green),
title: Text(text),
)),
],
),
),
);
}

Widget _buildMetric(String label, String value) {
return Column(
children: [
Text(value, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
],
);
}
}

6. Clause Negotiation Generator
This advanced feature generates multiple negotiation options for the user.
🧠 contracts/services/generative_service.py (Extended)
class GenerativeService:
# ... existing methods ...

async def generate_counter_proposals(
self,
risky_clause_text: str,
user_goal: str,
benchmark_data: dict
) -> dict:
"""Generates a set of negotiation options based on user goals and market data."""

prompt = f"""
You are an expert contract negotiation strategist. A user wants to negotiate a risky clause.

ORIGINAL RISKY CLAUSE:
"{risky_clause_text}"

USER'S GOAL: "{user_goal}"

MARKET BENCHMARK DATA:
"{benchmark_data}"

TASK:
Generate three distinct counter-proposal options. For each option, provide the rewritten clause text and a brief rationale explaining the strategy.
1. A "Collaborative" option: a soft, reasonable request that is easy for the other party to accept.
2. A "Balanced" option: a fair middle-ground proposal.
3. An "Assertive" option: a strong counter that aggressively favors the user's position.

Format your response as a JSON object with keys "collaborative", "balanced", and "assertive".
Each key should contain an object with "text" and "rationale".
"""

# response = await openai.ChatCompletion.acreate(...)
# result = json.loads(response.choices[0].message.content)

# Placeholder response
result = {
"collaborative": {
"text": "The Contractor's liability shall be discussed in good faith should a claim arise.",
"rationale": "Opens the door for discussion without being confrontational."
},
"balanced": {
"text": "The Contractor's liability shall be limited to the value of the fees paid in the preceding 12 months.",
"rationale": "A common, fair compromise that links liability to revenue."
},
"assertive": {
"text": "The Contractor's total aggregate liability shall be capped at R500,000.",
"rationale": "Sets a firm, non-negotiable monetary cap to minimize exposure."
}
}

return result