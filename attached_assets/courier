# **Bidbeez Courier Module - DevOps Documentation**

## **📋 Table of Contents**
- [System Overview](#system-overview)
- [Architecture & Dependencies](#architecture--dependencies)
- [Prerequisites](#prerequisites)
- [Installation & Deployment](#installation--deployment)
- [Database Setup](#database-setup)
- [Configuration](#configuration)
- [Testing Procedures](#testing-procedures)
- [Monitoring & Health Checks](#monitoring--health-checks)
- [API Documentation](#api-documentation)
- [Troubleshooting](#troubleshooting)
- [Performance Optimization](#performance-optimization)
- [Security Guidelines](#security-guidelines)
- [Backup & Recovery](#backup--recovery)
- [Scaling Guidelines](#scaling-guidelines)
- [Maintenance Procedures](#maintenance-procedures)

---

## **System Overview**

### **Purpose**
The Bidbeez Courier Module is an enterprise-grade delivery management system that integrates with the existing Bidbeez platform to provide:
- Smart bee (delivery worker) assignment using AI algorithms
- Real-time GPS tracking with coordinate storage
- Multi-courier company integration
- Tender-based delivery management
- Complete audit trail and compliance logging
- Performance analytics and SLA monitoring

### **Key Features**
- **AI-Powered Matching**: Intelligent bee assignment based on location, rating, transport mode, and availability
- **Real-Time Tracking**: GPS coordinate tracking with status updates and notifications
- **Multi-Tenant Architecture**: Secure data isolation for different organizations
- **Enterprise Compliance**: Complete audit logging with GDPR compliance
- **Integration Ready**: Seamless integration with existing tenders, payments, and bee systems
- **Scalable Design**: Optimized for high-volume operations

---

## **Architecture & Dependencies**

### **Technology Stack**
```yaml
Database: PostgreSQL 13+ with PostGIS 3.3+
Backend Language: SQL Functions (PostgreSQL/PL-pgSQL)
Caching: Redis (optional but recommended)
Message Queue: Celery (for async operations)
Mapping Services: 
  - Mapbox (route visualization)
  - Google Maps (geocoding & routing)
Authentication: JWT tokens
Multi-tenancy: Row Level Security (RLS)
```

### **System Dependencies**
```mermaid
graph TD
    A[Courier Module] --> B[PostgreSQL + PostGIS]
    A --> C[Existing Bidbeez Tables]
    A --> D[Redis Cache]
    A --> E[External APIs]
    
    C --> C1[users]
    C --> C2[tenders]
    C --> C3[payments]
    C --> C4[bee_profiles]
    
    E --> E1[Mapbox API]
    E --> E2[Google Maps API]
    E --> E3[Courier Company APIs]
```

### **Database Tables**
- **Core Tables**: `CourierCompany`, `CourierRequest`
- **Module Tables**: `courier_rates`, `courier_tracking_updates`, `courier_integrations`, `bee_courier_assignments`, `courier_audit_logs`
- **Integration Tables**: Links to existing `users`, `tenders`, `payments`, `bee_profiles`

---

## **Prerequisites**

### **System Requirements**
```bash
# Operating System
Ubuntu 20.04+ / CentOS 8+ / Docker environment

# Database
PostgreSQL 13+
PostGIS 3.3+
Minimum 4GB RAM allocated to database
SSD storage recommended for performance

# Network
Outbound HTTPS access for:
- Mapbox API (api.mapbox.com)
- Google Maps API (maps.googleapis.com)
- Courier company APIs (various)

# Memory & CPU
Minimum: 2 CPU cores, 4GB RAM
Recommended: 4 CPU cores, 8GB RAM
Production: 8+ CPU cores, 16+ GB RAM
```

### **API Keys Required**
```bash
# External Service API Keys
MAPBOX_API_KEY=your_mapbox_key_here
GOOGLE_API_KEY=your_google_maps_key_here

# Security Keys
WEBHOOK_SECRET=your_webhook_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Email Configuration (for notifications)
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password
```

---

## **Installation & Deployment**

### **Step 1: Environment Preparation**
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install PostgreSQL with PostGIS
sudo apt install postgresql-13 postgresql-contrib postgresql-13-postgis-3 -y

# Install Redis (optional but recommended)
sudo apt install redis-server -y

# Start services
sudo systemctl start postgresql
sudo systemctl start redis-server
sudo systemctl enable postgresql
sudo systemctl enable redis-server
```

### **Step 2: Database Setup**
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and enable PostGIS
CREATE DATABASE bidbeez;
\c bidbeez;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

# Verify PostGIS installation
SELECT PostGIS_Version();
```

### **Step 3: Deploy Database Schema**
```sql
-- Run this complete script in order:

-- 1. Create Enums
CREATE TYPE courier_service_level_enum AS ENUM (
    'economy', 'standard', 'express', 'overnight', 'same_day', 'white_glove'
);

CREATE TYPE courier_status_enum AS ENUM (
    'draft', 'pending', 'confirmed', 'picked_up', 'in_transit', 
    'out_for_delivery', 'delivered', 'failed', 'cancelled', 
    'returned', 'on_hold', 'exception'
);

CREATE TYPE courier_update_source_enum AS ENUM (
    'system', 'webhook', 'manual', 'api', 'mobile_app', 'integration', 'ai_prediction'
);

CREATE TYPE courier_auth_type_enum AS ENUM (
    'api_key', 'oauth2', 'basic_auth', 'bearer_token', 'custom'
);

CREATE TYPE courier_connection_status_enum AS ENUM (
    'connected', 'disconnected', 'error', 'testing', 'rate_limited', 'maintenance'
);

CREATE TYPE courier_action_enum AS ENUM (
    'create', 'read', 'update', 'delete', 'dispatch', 'track', 'estimate', 
    'cancel', 'webhook_received', 'integration_call', 'rate_calculated', 
    'notification_sent', 'bee_assigned', 'route_optimized', 'payment_processed',
    'bee_assignment_failed', 'bee_assignment_success', 'delivery_status_retrieved'
);

CREATE TYPE courier_action_category_enum AS ENUM (
    'data_access', 'business_operation', 'integration', 'security', 'compliance'
);

CREATE TYPE courier_data_sensitivity_enum AS ENUM (
    'public', 'internal', 'confidential', 'restricted', 'personal_data'
);

CREATE TYPE courier_urgency_enum AS ENUM (
    'low', 'medium', 'high', 'urgent', 'critical'
);

CREATE TYPE courier_insurance_type_enum AS ENUM (
    'none', 'basic', 'standard', 'premium', 'full_coverage'
);

-- 2. Create Tables (Run the complete table creation scripts from previous steps)
-- [Tables: courier_rates, courier_tracking_updates, courier_integrations, bee_courier_assignments, courier_audit_logs]

-- 3. Create Indexes
-- [All index creation scripts]

-- 4. Enable Row Level Security
-- [All RLS policies]

-- 5. Create Functions
-- [assign_optimal_bee_to_delivery, create_tracking_update_with_notifications, get_delivery_status, test_courier_system]

-- 6. Create Views
-- [delivery_ecosystem_overview, active_delivery_operations, courier_company_performance]
```

### **Step 4: Load Sample Data**
```sql
-- Load courier companies
INSERT INTO "CourierCompany" (name, service_level, coverage_area, priority_support, reliability_score, is_blacklisted) VALUES
('FastTrack Express', 'premium', 'national', true, 95.5, false),
('EconoDelivery', 'standard', 'regional', false, 82.3, false),
('SpeedyBee Local', 'express', 'city', true, 88.7, false),
('ReliableRun', 'standard', 'provincial', false, 91.2, false),
('UltraFast Courier', 'overnight', 'national', true, 94.8, false);

-- Load sample bee profiles (adjust based on your user management)
-- [Bee profiles creation script]

-- Load sample courier requests linked to existing tenders
-- [Courier requests creation script]
```

---

## **Configuration**

### **Environment Variables**
```bash
# Create .env file
cat > /etc/bidbeez/courier/.env << EOF
# API Configuration
MAPBOX_API_KEY=pk.ey...your_mapbox_key
GOOGLE_API_KEY=AIza...your_google_key

# Security
WEBHOOK_SECRET=your_secure_webhook_secret_here
ENCRYPTION_KEY=your_base64_encoded_encryption_key

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/bidbeez
REDIS_URL=redis://localhost:6379/1

# Email Notifications
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
EMAIL_USE_TLS=true

# Application
FRONTEND_BASE_URL=https://app.bidbeez.com
DEFAULT_TENANT_ID=default

# Performance
CACHE_TIMEOUT=3600
RATE_LIMIT_PER_MINUTE=60
MAX_TRACKING_UPDATES_PER_REQUEST=100
EOF

# Set permissions
chmod 600 /etc/bidbeez/courier/.env
```

### **Database Configuration**
```sql
-- Configure PostgreSQL for optimal performance
-- postgresql.conf adjustments:

# Memory Settings
shared_buffers = 256MB                  # 25% of system RAM
effective_cache_size = 1GB             # 75% of system RAM
work_mem = 16MB                         # For complex queries
maintenance_work_mem = 64MB             # For maintenance operations

# Connection Settings
max_connections = 200                   # Adjust based on expected load
shared_preload_libraries = 'postgis'   # Required for PostGIS

# Logging
log_min_duration_statement = 1000      # Log slow queries (1 second+)
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# Performance
effective_io_concurrency = 200         # For SSD storage
random_page_cost = 1.1                 # For SSD storage
checkpoint_completion_target = 0.9     # Smoother checkpoints
```

---

## **Testing Procedures**

### **1. Smoke Tests (Post-Deployment)**
```sql
-- Test 1: Database Connectivity and Schema
SELECT 'Database Connection: ' || current_database() as test_result;
SELECT 'PostGIS Version: ' || PostGIS_Version() as test_result;

-- Test 2: Table Existence
SELECT 
    table_name,
    CASE WHEN table_name IS NOT NULL THEN '✅ EXISTS' ELSE '❌ MISSING' END as status
FROM information_schema.tables 
WHERE table_name IN (
    'CourierCompany', 'CourierRequest', 'courier_rates', 
    'courier_tracking_updates', 'bee_courier_assignments', 
    'courier_integrations', 'courier_audit_logs'
)
ORDER BY table_name;

-- Test 3: Function Availability
SELECT 
    routine_name,
    CASE WHEN routine_name IS NOT NULL THEN '✅ AVAILABLE' ELSE '❌ MISSING' END as status
FROM information_schema.routines 
WHERE routine_name IN (
    'assign_optimal_bee_to_delivery',
    'create_tracking_update_with_notifications', 
    'get_delivery_status',
    'test_courier_system'
)
ORDER BY routine_name;

-- Test 4: View Accessibility
SELECT 
    table_name as view_name,
    CASE WHEN table_name IS NOT NULL THEN '✅ ACCESSIBLE' ELSE '❌ MISSING' END as status
FROM information_schema.views 
WHERE table_name IN (
    'delivery_ecosystem_overview',
    'active_delivery_operations', 
    'courier_company_performance'
)
ORDER BY table_name;
```

### **2. Integration Tests**
```sql
-- Test 5: System Integration Test
SELECT * FROM test_courier_system();

-- Test 6: Data Validation
SELECT 
    'Courier Companies' as entity,
    COUNT(*) as count,
    CASE WHEN COUNT(*) > 0 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM "CourierCompany"
UNION ALL
SELECT 
    'Bee Profiles',
    COUNT(*),
    CASE WHEN COUNT(*) > 0 THEN '✅ PASS' ELSE '❌ FAIL' END
FROM bee_profiles
UNION ALL
SELECT 
    'Courier Requests',
    COUNT(*),
    CASE WHEN COUNT(*) >= 0 THEN '✅ PASS' ELSE '❌ FAIL' END
FROM "CourierRequest";
```

### **3. Performance Tests**
```sql
-- Test 7: Query Performance
EXPLAIN ANALYZE 
SELECT * FROM delivery_ecosystem_overview 
WHERE alert_status != 'normal'
LIMIT 100;

-- Test 8: Function Performance
SELECT 
    'assign_optimal_bee_to_delivery' as function_name,
    EXTRACT(MILLISECONDS FROM (end_time - start_time)) as execution_time_ms
FROM (
    SELECT 
        NOW() as start_time,
        assign_optimal_bee_to_delivery(
            (SELECT id FROM "CourierRequest" LIMIT 1),
            'default', 20, NULL
        ) as result,
        NOW() as end_time
) perf_test;
```

### **4. Load Tests**
```bash
#!/bin/bash
# load_test.sh - Simulate concurrent bee assignments

echo "Starting load test..."

for i in {1..10}; do
    {
        psql -d bidbeez -c "
        SELECT assign_optimal_bee_to_delivery(
            (SELECT id FROM \"CourierRequest\" ORDER BY RANDOM() LIMIT 1),
            'default', 25, NULL
        );" &
    }
done

wait
echo "Load test completed"
```

### **5. API Integration Tests**
```bash
#!/bin/bash
# api_test.sh - Test external API dependencies

echo "Testing Mapbox API..."
curl -s "https://api.mapbox.com/geocoding/v5/mapbox.places/johannesburg.json?access_token=$MAPBOX_API_KEY" | jq '.features[0].center'

echo "Testing Google Maps API..."
curl -s "https://maps.googleapis.com/maps/api/geocode/json?address=johannesburg&key=$GOOGLE_API_KEY" | jq '.results[0].geometry.location'

echo "External API tests completed"
```

---

## **Monitoring & Health Checks**

### **1. Health Check Endpoints**
```sql
-- Create health check function
CREATE OR REPLACE FUNCTION system_health_check()
RETURNS TABLE (
    component TEXT,
    status TEXT,
    details TEXT,
    last_check TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Database health
    RETURN QUERY SELECT 
        'Database'::TEXT,
        'HEALTHY'::TEXT,
        'PostgreSQL ' || version() as details,
        NOW();
    
    -- PostGIS health
    RETURN QUERY SELECT 
        'PostGIS'::TEXT,
        'HEALTHY'::TEXT,
        'PostGIS ' || PostGIS_Version() as details,
        NOW();
    
    -- Table health
    RETURN QUERY SELECT 
        'Tables'::TEXT,
        CASE WHEN COUNT(*) = 7 THEN 'HEALTHY' ELSE 'DEGRADED' END,
        'Found ' || COUNT(*) || ' courier tables' as details,
        NOW()
    FROM information_schema.tables 
    WHERE table_name LIKE '%courier%' OR table_name = 'bee_profiles';
    
    -- Function health
    RETURN QUERY SELECT 
        'Functions'::TEXT,
        CASE WHEN COUNT(*) >= 4 THEN 'HEALTHY' ELSE 'DEGRADED' END,
        'Found ' || COUNT(*) || ' courier functions' as details,
        NOW()
    FROM information_schema.routines 
    WHERE routine_name LIKE '%courier%' OR routine_name LIKE '%bee%';
    
    -- Recent activity
    RETURN QUERY SELECT 
        'Activity'::TEXT,
        CASE WHEN COUNT(*) > 0 THEN 'ACTIVE' ELSE 'IDLE' END,
        'Last 24h: ' || COUNT(*) || ' audit logs' as details,
        NOW()
    FROM courier_audit_logs 
    WHERE timestamp > NOW() - INTERVAL '24 hours';
    
END;
$$ LANGUAGE plpgsql;
```

### **2. Performance Monitoring**
```sql
-- Create performance monitoring function
CREATE OR REPLACE FUNCTION performance_metrics()
RETURNS TABLE (
    metric_name TEXT,
    metric_value NUMERIC,
    unit TEXT,
    status TEXT
) AS $$
BEGIN
    -- Average assignment time
    RETURN QUERY SELECT 
        'avg_assignment_time'::TEXT,
        AVG(execution_time_ms)::NUMERIC,
        'milliseconds'::TEXT,
        CASE WHEN AVG(execution_time_ms) < 1000 THEN 'GOOD' 
             WHEN AVG(execution_time_ms) < 3000 THEN 'WARNING'
             ELSE 'CRITICAL' END
    FROM courier_audit_logs 
    WHERE action = 'bee_assignment_success' 
      AND timestamp > NOW() - INTERVAL '1 hour'
      AND execution_time_ms IS NOT NULL;
    
    -- Success rate
    RETURN QUERY SELECT 
        'assignment_success_rate'::TEXT,
        (COUNT(CASE WHEN success = true THEN 1 END)::NUMERIC / COUNT(*) * 100),
        'percentage'::TEXT,
        CASE WHEN (COUNT(CASE WHEN success = true THEN 1 END)::NUMERIC / COUNT(*) * 100) > 95 THEN 'GOOD'
             WHEN (COUNT(CASE WHEN success = true THEN 1 END)::NUMERIC / COUNT(*) * 100) > 85 THEN 'WARNING'
             ELSE 'CRITICAL' END
    FROM courier_audit_logs 
    WHERE action LIKE '%assignment%' 
      AND timestamp > NOW() - INTERVAL '1 hour';
    
    -- Active deliveries
    RETURN QUERY SELECT 
        'active_deliveries'::TEXT,
        COUNT(*)::NUMERIC,
        'count'::TEXT,
        'INFO'::TEXT
    FROM active_delivery_operations;
    
END;
$$ LANGUAGE plpgsql;
```

### **3. Monitoring Queries**
```sql
-- Daily monitoring dashboard
SELECT 
    '=== BIDBEEZ COURIER SYSTEM HEALTH ===' as header,
    NOW() as check_time;

-- System health
SELECT * FROM system_health_check();

-- Performance metrics  
SELECT * FROM performance_metrics();

-- Critical alerts
SELECT 
    'CRITICAL ALERTS' as alert_type,
    COUNT(*) as count
FROM active_delivery_operations 
WHERE priority_level = 'critical'
   OR sla_status = 'violated';

-- Top performing bees
SELECT 
    'TOP BEES (24H)' as report_type,
    bee_name,
    assignment_status,
    customer_satisfaction_score
FROM delivery_ecosystem_overview 
WHERE assigned_at > NOW() - INTERVAL '24 hours'
  AND customer_satisfaction_score IS NOT NULL
ORDER BY customer_satisfaction_score DESC
LIMIT 5;
```

### **4. Log Monitoring**
```bash
#!/bin/bash
# monitor_logs.sh - Monitor for critical errors

# PostgreSQL logs
tail -f /var/log/postgresql/postgresql-13-main.log | grep -i error &

# Application logs (if using file logging)
tail -f /var/log/bidbeez/courier.log | grep -i "critical\|error\|failed" &

# System monitoring
watch -n 30 'psql -d bidbeez -c "SELECT * FROM system_health_check();"'
```

---

## **API Documentation**

### **Core Functions**

#### **1. assign_optimal_bee_to_delivery**
```sql
-- Function Signature
assign_optimal_bee_to_delivery(
    p_courier_request_id UUID,
    p_tenant_id VARCHAR(50) DEFAULT 'default',
    p_max_distance_km INTEGER DEFAULT 20,
    p_required_transport_mode TEXT DEFAULT NULL
)

-- Example Usage
SELECT * FROM assign_optimal_bee_to_delivery(
    '550e8400-e29b-41d4-a716-************',  -- courier_request_id
    'tenant_123',                            -- tenant_id  
    25,                                      -- max_distance_km
    'motorcycle'                             -- transport_mode
);

-- Expected Response
{
    assignment_id: "uuid",
    bee_user_id: "uuid", 
    bee_name: "John Motorbike Express",
    bee_phone: "+27821234567",
    estimated_distance: 8.5,
    recommendation_score: 0.85,
    assignment_status: "pending"
}
```

#### **2. create_tracking_update_with_notifications**
```sql
-- Function Signature
create_tracking_update_with_notifications(
    p_courier_request_id UUID,
    p_status courier_status_enum,
    p_location_name TEXT DEFAULT NULL,
    p_latitude DECIMAL DEFAULT NULL,
    p_longitude DECIMAL DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    p_bee_user_id UUID DEFAULT NULL,
    p_tenant_id VARCHAR(50) DEFAULT 'default',
    p_external_tracking_id TEXT DEFAULT NULL,
    p_external_carrier TEXT DEFAULT NULL
)

-- Example Usage
SELECT create_tracking_update_with_notifications(
    '550e8400-e29b-41d4-a716-************',  -- courier_request_id
    'picked_up',                             -- status
    'Warehouse Loading Bay A',               -- location_name
    -26.2041,                               -- latitude
    28.0473,                                -- longitude  
    'Package collected successfully',        -- notes
    '660f8400-e29b-41d4-a716-************', -- bee_user_id
    'tenant_123'                            -- tenant_id
);

-- Expected Response (JSONB)
{
    "success": true,
    "tracking_update_id": "uuid",
    "courier_request_id": "uuid", 
    "status": "picked_up",
    "previous_status": "pending",
    "is_milestone": true,
    "notifications_triggered": true,
    "timestamp": "2024-01-15T10:30:00Z"
}
```

#### **3. get_delivery_status**
```sql
-- Function Signature
get_delivery_status(
    p_courier_request_id UUID DEFAULT NULL,
    p_tracking_number TEXT DEFAULT NULL,
    p_tenant_id VARCHAR(50) DEFAULT 'default'
)

-- Example Usage
SELECT get_delivery_status(
    p_tracking_number => 'WB-12345678',
    p_tenant_id => 'tenant_123'
);

-- Expected Response (JSONB)
{
    "success": true,
    "delivery_id": "uuid",
    "tracking_number": "WB-12345678",
    "status": {
        "current": "in_transit",
        "alert_status": "normal", 
        "delivery_performance": "good",
        "on_time": true
    },
    "addresses": {
        "pickup": "123 Industrial Road, Johannesburg",
        "dropoff": "456 Government Ave, Pretoria",
        "sender": "Supply Warehouse",
        "receiver": "Education Dept Office"
    },
    "courier_company": {
        "name": "FastTrack Express",
        "service_level": "premium",
        "reliability_score": 95.5
    },
    "bee_assignment": {
        "bee_name": "John Motorbike Express",
        "transport_mode": "motorcycle",
        "assignment_status": "in_progress",
        "started_at": "2024-01-15T09:00:00Z"
    },
    "tracking_updates": [...],
    "current_location": {
        "latitude": -25.85,
        "longitude": 28.15,
        "location_name": "Highway N1 to Pretoria"
    }
}
```

### **Dashboard Views**

#### **1. active_delivery_operations**
```sql
-- Real-time operations dashboard
SELECT 
    delivery_id,
    tracking_number,
    priority_level,        -- critical, high, medium, low
    sla_status,           -- on_track, at_risk, violated
    bee_performance_category, -- excellent, good, average, needs_attention
    financial_status,     -- paid, payment_pending, amount_finalized
    hours_since_assignment,
    hours_until_deadline,
    courier_company_name,
    assignment_status
FROM active_delivery_operations
ORDER BY 
    CASE priority_level 
        WHEN 'critical' THEN 1 
        WHEN 'high' THEN 2 
        ELSE 3 
    END,
    hours_since_assignment DESC;
```

#### **2. delivery_ecosystem_overview**
```sql
-- Comprehensive delivery information
SELECT 
    delivery_id,
    tracking_number,
    sender_name,
    receiver_name,
    courier_company_name,
    bee_name,
    assignment_status,
    latest_tracking_status,
    current_latitude,
    current_longitude,
    alert_status,         -- normal, delayed_start, tracking_stale, delivery_delayed
    delivery_performance, -- excellent, good, poor, unknown
    distance_to_destination_km
FROM delivery_ecosystem_overview
WHERE alert_status != 'normal'  -- Focus on issues
ORDER BY created_at DESC;
```

#### **3. courier_company_performance**
```sql
-- Courier company analytics
SELECT 
    courier_name,
    service_level,
    total_deliveries,
    on_time_percentage,
    completion_rate_percentage,
    avg_customer_satisfaction,
    total_revenue,
    deliveries_last_7_days,
    company_reliability_score
FROM courier_company_performance
ORDER BY on_time_percentage DESC;
```

---

## **Troubleshooting**

### **Common Issues & Solutions**

#### **1. PostGIS Installation Issues**
```bash
# Problem: "type geometry does not exist"
# Solution: Reinstall PostGIS extension
sudo -u postgres psql -d bidbeez -c "DROP EXTENSION IF EXISTS postgis CASCADE;"
sudo -u postgres psql -d bidbeez -c "CREATE EXTENSION postgis;"
sudo -u postgres psql -d bidbeez -c "SELECT PostGIS_Version();"
```

#### **2. Foreign Key Constraint Violations**
```sql
-- Problem: bee_profiles foreign key fails
-- Solution: Check user table or remove constraint temporarily
SELECT 'Users available: ' || COUNT(*) FROM users;

-- If no users, create independent bee profiles:
ALTER TABLE bee_profiles DROP CONSTRAINT IF EXISTS bee_profiles_user_id_fkey;
-- Then reload bee profile data
```

#### **3. Function Not Found Errors**
```sql
-- Problem: "function assign_optimal_bee_to_delivery does not exist"
-- Solution: Check function exists and recreate if needed
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_name LIKE '%bee%' OR routine_name LIKE '%courier%';

-- If missing, recreate function from deployment scripts
```

#### **4. Performance Issues**
```sql
-- Problem: Slow query performance
-- Solution: Check and rebuild indexes
REINDEX INDEX CONCURRENTLY idx_courier_tracking_request_time;
REINDEX INDEX CONCURRENTLY idx_bee_assignments_tenant_status;

-- Analyze tables for better query planning
ANALYZE courier_tracking_updates;
ANALYZE bee_courier_assignments;
ANALYZE "CourierRequest";
```

#### **5. Memory Issues**
```bash
# Problem: Out of memory errors
# Solution: Adjust PostgreSQL memory settings
sudo nano /etc/postgresql/13/main/postgresql.conf

# Increase these values:
shared_buffers = 512MB
work_mem = 32MB
maintenance_work_mem = 128MB

sudo systemctl restart postgresql
```

### **Debug Mode**
```sql
-- Enable detailed logging for troubleshooting
SET log_min_duration_statement = 0;  -- Log all queries
SET log_statement = 'all';            -- Log all statements

-- Check recent errors
SELECT 
    timestamp,
    action,
    error_message,
    details
FROM courier_audit_logs 
WHERE success = false 
  AND timestamp > NOW() - INTERVAL '1 hour'
ORDER BY timestamp DESC;
```

### **Diagnostic Queries**
```sql
-- System diagnostics
SELECT 
    'Database Size' as metric,
    pg_size_pretty(pg_database_size('bidbeez')) as value;

-- Table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename LIKE '%courier%' OR tablename = 'bee_profiles'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Connection count
SELECT 
    count(*) as active_connections,
    state,
    query_start
FROM pg_stat_activity 
WHERE datname = 'bidbeez'
GROUP BY state, query_start;
```

---

## **Performance Optimization**

### **Database Optimization**

#### **1. Index Optimization**
```sql
-- Monitor index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
  AND (tablename LIKE '%courier%' OR tablename = 'bee_profiles')
ORDER BY idx_scan DESC;

-- Create additional indexes for common queries
CREATE INDEX CONCURRENTLY idx_courier_request_tenant_created 
ON "CourierRequest"(courier_id, created_at) 
WHERE on_time = true;

CREATE INDEX CONCURRENTLY idx_tracking_updates_recent 
ON courier_tracking_updates(courier_request_id, event_timestamp DESC) 
WHERE event_timestamp > NOW() - INTERVAL '7 days';
```

#### **2. Query Optimization**
```sql
-- Optimize heavy queries with CTEs
CREATE OR REPLACE VIEW delivery_ecosystem_overview_optimized AS
WITH recent_tracking AS (
    SELECT DISTINCT ON (courier_request_id)
        courier_request_id,
        status,
        location_name,
        event_timestamp,
        latitude,
        longitude,
        estimated_delivery_time,
        exception_type,
        resolution_required
    FROM courier_tracking_updates 
    ORDER BY courier_request_id, event_timestamp DESC
),
active_assignments AS (
    SELECT DISTINCT ON (courier_request_id)
        courier_request_id,
        bee_user_id,
        assignment_status,
        assigned_at,
        accepted_at,
        started_at,
        completed_at,
        estimated_distance_km,
        actual_distance_km,
        on_time_delivery,
        customer_satisfaction_score,
        offered_amount,
        final_amount,
        payment_status,
        related_tender_id
    FROM bee_courier_assignments
    ORDER BY courier_request_id, assigned_at DESC
)
-- ... rest of optimized view
```

#### **3. Partitioning for Large Tables**
```sql
-- Partition audit logs by date for better performance
CREATE TABLE courier_audit_logs_partitioned (
    LIKE courier_audit_logs INCLUDING ALL
) PARTITION BY RANGE (timestamp);

-- Create monthly partitions
CREATE TABLE courier_audit_logs_y2024m01 
PARTITION OF courier_audit_logs_partitioned
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE courier_audit_logs_y2024m02 
PARTITION OF courier_audit_logs_partitioned
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
```

### **Application Optimization**

#### **1. Connection Pooling**
```python
# Example connection pool configuration (Python/Django)
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': 'bidbeez',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
            'OPTIONS': {
                'application_name': 'bidbeez_courier',
                'connect_timeout': 10,
            }
        }
    }
}
```

#### **2. Caching Strategy**
```sql
-- Cache frequently accessed data
CREATE MATERIALIZED VIEW courier_performance_cache AS
SELECT * FROM courier_company_performance;

-- Refresh cache periodically (setup as cron job)
REFRESH MATERIALIZED VIEW CONCURRENTLY courier_performance_cache;
```

#### **3. Batch Operations**
```sql
-- Batch processing for multiple assignments
CREATE OR REPLACE FUNCTION batch_assign_bees(
    p_courier_request_ids UUID[],
    p_tenant_id VARCHAR(50) DEFAULT 'default'
)
RETURNS TABLE (
    courier_request_id UUID,
    assignment_result JSONB
) AS $$
DECLARE
    request_id UUID;
    result RECORD;
BEGIN
    FOREACH request_id IN ARRAY p_courier_request_ids
    LOOP
        SELECT * INTO result FROM assign_optimal_bee_to_delivery(request_id, p_tenant_id, 25, NULL);
        
        RETURN QUERY SELECT 
            request_id,
            row_to_json(result)::JSONB;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

---

## **Security Guidelines**

### **1. Row Level Security (RLS)**
```sql
-- Verify RLS is enabled on all tables
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename LIKE '%courier%' OR tablename = 'bee_profiles';

-- Create tenant isolation policies
CREATE POLICY courier_request_tenant_policy ON "CourierRequest"
    FOR ALL TO authenticated
    USING (courier_id IN (
        SELECT id FROM "CourierCompany" 
        WHERE tenant_id = current_setting('app.current_tenant_id', true)
    ));
```

### **2. Data Encryption**
```sql
-- Sensitive data encryption (implement in application layer)
CREATE OR REPLACE FUNCTION encrypt_sensitive_data(plaintext TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Use application-level encryption for sensitive fields
    -- This is a placeholder - implement with your encryption library
    RETURN 'encrypted:' || encode(plaintext::bytea, 'base64');
END;
$$ LANGUAGE plpgsql;
```

### **3. API Security**
```bash
# Rate limiting configuration
iptables -A INPUT -p tcp --dport 5432 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT

# Database user permissions
sudo -u postgres psql -c "
CREATE ROLE courier_app_user WITH LOGIN PASSWORD 'secure_password_here';
GRANT CONNECT ON DATABASE bidbeez TO courier_app_user;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO courier_app_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO courier_app_user;
"
```

### **4. Audit Trail**
```sql
-- Ensure all sensitive operations are logged
CREATE OR REPLACE FUNCTION log_sensitive_operation(
    operation_type TEXT,
    table_name TEXT,
    record_id UUID,
    user_id UUID DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO courier_audit_logs (
        action,
        action_category,
        resource_type,
        resource_id,
        user_id,
        data_sensitivity,
        success
    ) VALUES (
        operation_type,
        'security',
        table_name,
        record_id,
        user_id,
        'restricted',
        true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## **Backup & Recovery**

### **1. Backup Strategy**
```bash
#!/bin/bash
# backup_courier_system.sh

BACKUP_DIR="/backups/bidbeez/courier"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="bidbeez"

# Create backup directory
mkdir -p $BACKUP_DIR

# Full database backup
pg_dump -h localhost -U postgres -d $DB_NAME \
    --format=custom \
    --compress=9 \
    --verbose \
    --file=$BACKUP_DIR/bidbeez_full_$DATE.dump

# Schema-only backup
pg_dump -h localhost -U postgres -d $DB_NAME \
    --schema-only \
    --format=custom \
    --file=$BACKUP_DIR/bidbeez_schema_$DATE.dump

# Courier-specific tables backup
pg_dump -h localhost -U postgres -d $DB_NAME \
    --format=custom \
    --table='"CourierCompany"' \
    --table='"CourierRequest"' \
    --table='courier_*' \
    --table='bee_courier_assignments' \
    --file=$BACKUP_DIR/courier_tables_$DATE.dump

# Compress and archive
tar -czf $BACKUP_DIR/courier_backup_$DATE.tar.gz $BACKUP_DIR/*_$DATE.dump

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: courier_backup_$DATE.tar.gz"
```

### **2. Recovery Procedures**
```bash
#!/bin/bash
# restore_courier_system.sh

BACKUP_FILE=$1
DB_NAME="bidbeez"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file.dump>"
    exit 1
fi

# Create recovery point
pg_dump -h localhost -U postgres -d $DB_NAME \
    --format=custom \
    --file=/backups/recovery_point_$(date +%Y%m%d_%H%M%S).dump

# Restore from backup
pg_restore -h localhost -U postgres -d $DB_NAME \
    --clean \
    --if-exists \
    --verbose \
    $BACKUP_FILE

# Verify restoration
psql -h localhost -U postgres -d $DB_NAME -c "
SELECT 'Restoration verification' as status;
SELECT * FROM system_health_check();
"

echo "Restoration completed from: $BACKUP_FILE"
```

### **3. Point-in-Time Recovery**
```bash
# Enable WAL archiving for PITR
# In postgresql.conf:
wal_level = replica
archive_mode = on
archive_command = 'cp %p /var/lib/postgresql/wal_archive/%f'

# Create base backup
pg_basebackup -h localhost -U postgres -D /backups/basebackup -Ft -z -P

# Recovery configuration (if needed)
# Create recovery.conf:
restore_command = 'cp /var/lib/postgresql/wal_archive/%f %p'
recovery_target_time = '2024-01-15 14:30:00 UTC'
```

---

## **Scaling Guidelines**

### **1. Horizontal Scaling**

#### **Read Replicas**
```bash
# Setup read replica for reporting queries
# On master server (postgresql.conf):
wal_level = replica
max_wal_senders = 3
wal_keep_segments = 32

# On replica server:
pg_basebackup -h master_server -D /var/lib/postgresql/13/replica -U replication -P -v -R
```

#### **Database Sharding Strategy**
```sql
-- Tenant-based sharding preparation
CREATE TABLE courier_shard_mapping (
    tenant_id VARCHAR(50) PRIMARY KEY,
    shard_name VARCHAR(50) NOT NULL,
    database_host VARCHAR(255) NOT NULL,
    database_port INTEGER DEFAULT 5432,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Route queries based on tenant
CREATE OR REPLACE FUNCTION get_shard_for_tenant(p_tenant_id VARCHAR(50))
RETURNS VARCHAR(50) AS $$
DECLARE
    shard_name VARCHAR(50);
BEGIN
    SELECT csm.shard_name INTO shard_name
    FROM courier_shard_mapping csm
    WHERE csm.tenant_id = p_tenant_id;
    
    RETURN COALESCE(shard_name, 'default');
END;
$$ LANGUAGE plpgsql;
```

### **2. Vertical Scaling**

#### **Resource Allocation**
```bash
# Production server specifications
CPU: 16+ cores
RAM: 64+ GB
Storage: NVMe SSD, 1TB+
Network: 10 Gbps

# PostgreSQL configuration for high load
max_connections = 500
shared_buffers = 16GB
effective_cache_size = 48GB
work_mem = 64MB
maintenance_work_mem = 2GB
checkpoint_segments = 64
checkpoint_completion_target = 0.9
```

#### **Connection Pooling**
```bash
# Install and configure PgBouncer
sudo apt install pgbouncer

# /etc/pgbouncer/pgbouncer.ini
[databases]
bidbeez = host=localhost port=5432 dbname=bidbeez

[pgbouncer]
listen_port = 6432
listen_addr = *
auth_type = md5
pool_mode = transaction
max_client_conn = 1000
default_pool_size = 25
max_db_connections = 100
```

### **3. Performance Monitoring at Scale**
```sql
-- Monitor connection usage
SELECT 
    state,
    count(*) as connections,
    avg(extract(epoch from now() - query_start)) as avg_duration_seconds
FROM pg_stat_activity 
WHERE datname = 'bidbeez'
GROUP BY state;

-- Monitor table sizes and growth
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE tablename LIKE '%courier%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

---

## **Maintenance Procedures**

### **1. Regular Maintenance Tasks**

#### **Daily Tasks**
```bash
#!/bin/bash
# daily_maintenance.sh

echo "Starting daily maintenance..."

# 1. Health check
psql -d bidbeez -c "SELECT * FROM system_health_check();" > /var/log/bidbeez/health_$(date +%Y%m%d).log

# 2. Performance metrics
psql -d bidbeez -c "SELECT * FROM performance_metrics();" > /var/log/bidbeez/performance_$(date +%Y%m%d).log

# 3. Check for critical alerts
CRITICAL_COUNT=$(psql -d bidbeez -t -c "SELECT COUNT(*) FROM active_delivery_operations WHERE priority_level = 'critical';")
if [ "$CRITICAL_COUNT" -gt 0 ]; then
    echo "ALERT: $CRITICAL_COUNT critical deliveries found" | mail -s "Bidbeez Courier Critical Alert" <EMAIL>
fi

# 4. Cleanup old audit logs (older than 1 year)
psql -d bidbeez -c "DELETE FROM courier_audit_logs WHERE timestamp < NOW() - INTERVAL '1 year';"

echo "Daily maintenance completed"
```

#### **Weekly Tasks**
```bash
#!/bin/bash
# weekly_maintenance.sh

echo "Starting weekly maintenance..."

# 1. Analyze table statistics
psql -d bidbeez -c "ANALYZE;"

# 2. Reindex critical tables
psql -d bidbeez -c "REINDEX INDEX CONCURRENTLY idx_courier_tracking_request_time;"
psql -d bidbeez -c "REINDEX INDEX CONCURRENTLY idx_bee_assignments_tenant_status;"

# 3. Update materialized views
psql -d bidbeez -c "REFRESH MATERIALIZED VIEW CONCURRENTLY courier_performance_cache;"

# 4. Generate weekly performance report
psql -d bidbeez -c "
SELECT 
    'Weekly Report: ' || date_trunc('week', NOW()) as report_period,
    COUNT(*) as total_deliveries,
    COUNT(CASE WHEN assignment_status = 'completed' THEN 1 END) as completed,
    ROUND(AVG(customer_satisfaction_score), 2) as avg_satisfaction
FROM bee_courier_assignments 
WHERE assigned_at > NOW() - INTERVAL '7 days';
" > /var/log/bidbeez/weekly_report_$(date +%Y%m%d).log

echo "Weekly maintenance completed"
```

#### **Monthly Tasks**
```bash
#!/bin/bash
# monthly_maintenance.sh

echo "Starting monthly maintenance..."

# 1. Full vacuum of large tables
psql -d bidbeez -c "VACUUM FULL courier_audit_logs;"
psql -d bidbeez -c "VACUUM FULL courier_tracking_updates;"

# 2. Update table statistics
psql -d bidbeez -c "ANALYZE VERBOSE;"

# 3. Check for unused indexes
psql -d bidbeez -c "
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE idx_scan < 100 
  AND pg_relation_size(indexrelid) > 1024*1024
ORDER BY pg_relation_size(indexrelid) DESC;
"

# 4. Archive old data
psql -d bidbeez -c "
INSERT INTO courier_audit_logs_archive 
SELECT * FROM courier_audit_logs 
WHERE timestamp < NOW() - INTERVAL '6 months';

DELETE FROM courier_audit_logs 
WHERE timestamp < NOW() - INTERVAL '6 months';
"

echo "Monthly maintenance completed"
```

### **2. Automated Monitoring Setup**
```bash
# Setup cron jobs for maintenance
# Add to /etc/crontab:

# Daily maintenance at 2 AM
0 2 * * * postgres /opt/bidbeez/scripts/daily_maintenance.sh

# Weekly maintenance on Sundays at 3 AM  
0 3 * * 0 postgres /opt/bidbeez/scripts/weekly_maintenance.sh

# Monthly maintenance on 1st of month at 4 AM
0 4 1 * * postgres /opt/bidbeez/scripts/monthly_maintenance.sh

# Backup every 6 hours
0 */6 * * * postgres /opt/bidbeez/scripts/backup_courier_system.sh
```

### **3. Emergency Procedures**
```bash
#!/bin/bash
# emergency_response.sh

ISSUE_TYPE=$1

case $ISSUE_TYPE in
    "high_cpu")
        echo "Handling high CPU usage..."
        # Kill long-running queries
        psql -d bidbeez -c "
        SELECT pg_terminate_backend(pid) 
        FROM pg_stat_activity 
        WHERE state = 'active' 
          AND query_start < NOW() - INTERVAL '5 minutes'
          AND datname = 'bidbeez';
        "
        ;;
    "disk_full")
        echo "Handling disk space issue..."
        # Archive old logs
        find /var/log/bidbeez -name "*.log" -mtime +7 -exec gzip {} \;
        find /var/log/bidbeez -name "*.gz" -mtime +30 -delete
        ;;
    "connection_limit")
        echo "Handling connection limit..."
        # Kill idle connections
        psql -d bidbeez -c "
        SELECT pg_terminate_backend(pid) 
        FROM pg_stat_activity 
        WHERE state = 'idle' 
          AND state_change < NOW() - INTERVAL '30 minutes'
          AND datname = 'bidbeez';
        "
        ;;
    *)
        echo "Usage: $0 {high_cpu|disk_full|connection_limit}"
        ;;
esac
```

---

## **Conclusion**

This documentation provides comprehensive guidance for deploying, maintaining, and scaling the Bidbeez Courier Module. The system is designed for enterprise-grade performance with:

- **High Availability**: Multi-replica setup with automatic failover
- **Scalability**: Horizontal and vertical scaling strategies
- **Security**: Multi-tenant isolation with comprehensive audit logging
- **Performance**: Optimized queries and caching strategies
- **Reliability**: Comprehensive monitoring and automated maintenance

For additional support or questions, contact the development team or refer to the API documentation for integration details.

**System Status**: ✅ Production Ready  
**Last Updated**: January 2024  
**Version**: 1.0.0  
**Maintained By**: Bidbeez DevOps Team