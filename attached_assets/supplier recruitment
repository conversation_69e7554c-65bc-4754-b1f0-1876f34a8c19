
backend/tenderflow/supplier_recruitment_agent/
├── __init__.py
├── config.yaml # Settings for APIs, NLP, blockchain, compliance
├── agent.py # Orchestrates recruitment and selection
├── sourcing.py # Internet sourcing (eTenders, private platforms, CIPC, CSD)
├── pricelist_loader.py # Extracts/validates pricelists
├── historical_boq.py # Precomputes 10-year BOQ analysis
├── rfq_processor.py # Processes RFQs from eTenders and other sources
├── boq_rfq_matcher.py # Cross-references BOQs and RFQs for consistency
├── matching.py # BOQ/RFQ-to-supplier matching with preference-based scoring
├── verification.py # KYC, AML, ESG, B-BBEE verification
├── onboarding.py # Supplier onboarding
├── blockchain.py # Blockchain anchoring
├── analytics.py # Metrics, forecasting, dashboards
├── notifications.py # Email, push, voice, WebSocket notifications
├── compliance.py # Compliance checks
├── predictive_recruitment.py # Predictive supplier demand
├── oauth.py # LinkedIn OAuth
├── retry.py # Retry logic
├── utils.py # Scoring, geo-distance, NLP
├── tender_preferences.py # Manages bidder preferences
├── tasks.py # Celery tasks
├── tests/
│ ├── __init__.py
│ ├── test_sourcing.py
│ ├── test_pricelist_loader.py
│ ├── test_historical_boq.py
│ ├── test_rfq_processor.py
│ ├── test_boq_rfq_matcher.py
│ ├── test_matching.py
│ ├── test_verification.py
│ ├── test_onboarding.py
│ ├── test_compliance.py
│ ├── test_analytics.py
│ ├── test_predictive_recruitment.py
│ ├── test_oauth.py
│ ├── test_notifications.py
│ ├── test_tender_preferences.py
├── logging_config.py # Structured logging
├── README.md # Documentation



import logging
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from django.core.cache import cache
from typing import List, Dict, Any
import motor.motor_asyncio
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class PredictiveRecruitmentService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.model = None
self.label_encoder = LabelEncoder()
self.db = motor.motor_asyncio.AsyncIOMotorClient(
self.config['mongodb']['url']
)[self.config['mongodb']['database']]

async def train_model(self):
try:
boqs = await self.db['historical_boqs'].find({
'created_at': {'$gte': datetime.now() - timedelta(days=3650)},
'tenant_id': self.config['tenant_id']
}).to_list(None)
rfqs = await self.db['rfqs'].find({
'created_at': {'$gte': datetime.now() - timedelta(days=3650)},
'tenant_id': self.config['tenant_id']
}).to_list(None)
insights = await self.db['queenbee_insights'].find({
'tender_id': {'$in': [b['tender_id'] for b in boqs] + [r['tender_id'] for r in rfqs]},
'tenant_id': self.config['tenant_id']
}).to_list(None)

X, y = [], []
for data in boqs + rfqs:
for item in data.get('items', []):
category = item.get('category', 'Other')
insight_count = len([i for i in insights if i['tender_id'] == data['tender_id']])
urgency = 1 if any('urgent' in i.get('value', '').lower() for i in insights) else 0
features = [item['quantity'], insight_count, urgency, data.get('total_value', 0.0)]
X.append(features)
y.append(category)

if not X or not y:
logger.warning("Insufficient data for model training")
return

y_encoded = self.label_encoder.fit_transform(y)
self.model = RandomForestClassifier(n_estimators=100, random_state=42)
self.model.fit(X, y_encoded)
cache.set(f"predictive_supplier_model_{self.config['tenant_id']}", self.model, 86400)
logger.info(f"Trained model for tenant {self.config['tenant_id']}")
except Exception as e:
logger.error(f"Model training failed: {e}")
raise

async def predict_supplier_demand(self, tender_id: str, preferences: Dict[str, float]) -> List[str]:
try:
self.model = cache.get(f"predictive_supplier_model_{self.config['tenant_id']}")
if not self.model:
await self.train_model()

tender = await self.db['parsed_tenders'].find_one({'id': tender_id})
insights = await self.db['queenbee_insights'].find({'tender_id': tender_id}).to_list(None)
data = await self.db['historical_boqs'].find_one({'tender_id': tender_id}) or await self.db['rfqs'].find_one({'tender_id': tender_id})

categories = []
for item in data.get('items', []) if data else []:
features = [
item['quantity'],
len(insights),
1 if any('urgent' in i.get('value', '').lower() for i in insights) else 0,
tender.get('estimated_value', 0.0) if tender else 0.0
]
prediction = self.model.predict([features])[0]
category = self.label_encoder.inverse_transform([prediction])[0]
if category != 'Other':
categories.append(category)

if preferences.get('require_bbbee', False):
public_tender_categories = await self.db['historical_boqs'].distinct(
'category', {'tender_type': 'public', 'tenant_id': self.config['tenant_id']}
)
categories = [c for c in categories if c in public_tender_categories]

logger.info(f"Predicted supplier demand for tender {tender_id}: {categories}")
return list(set(categories))
except Exception as e:
logger.error(f"Demand prediction failed for tender {tender_id}: {e}")
return []




import logging
import httpx
from typing import Dict, Any
from urllib.parse import urlencode

logger = logging.getLogger(__name__)

class OAuthService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def get_linkedin_auth_url(self, redirect_uri: str) -> str:
params = {
'response_type': 'code',
'client_id': self.config['external_sources']['linkedin']['client_id'],
'redirect_uri': redirect_uri,
'scope': 'r_organization_social r_emailaddress',
'state': 'random_state'
}
return f"https://www.linkedin.com/oauth/v2/authorization?{urlencode(params)}"

async def get_linkedin_access_token(self, code: str, redirect_uri: str) -> str:
try:
response = await self.http_client.post(
"https://www.linkedin.com/oauth/v2/accessToken",
data={
'grant_type': 'authorization_code',
'code': code,
'redirect_uri': redirect_uri,
'client_id': self.config['external_sources']['linkedin']['client_id'],
'client_secret': self.config['external_sources']['linkedin']['client_secret']
}
)
response.raise_for_status()
return response.json()['access_token']
except Exception as e:
logger.error(f"LinkedIn access token retrieval failed: {e}")
raise

async def fetch_linkedin_profile(self, access_token: str) -> Dict[str, Any]:
try:
response = await self.http_client.get(
"https://api.linkedin.com/v2/organizations?projection=(id,name,localizedName,locations,industries)",
headers={"Authorization": f"Bearer {access_token}"}
)
response.raise_for_status()
profile = response.json()
return {
'company_name': profile['localizedName'],
'registration_number': profile.get('id', ''),
'address_line_1': profile['locations'][0]['address']['line1'] if profile.get('locations') else '',
'city': profile['locations'][0]['address']['city'] if profile.get('locations') else '',
'category': profile['industries'][0]['localizedName'] if profile.get('industries') else 'Unknown',
'email': profile.get('email', '')
}
except Exception as e:
logger.error(f"LinkedIn profile fetch failed: {e}")
raise




import logging
import httpx
from typing import Dict, Any
from .utils import calculate_bbbee_score

logger = logging.getLogger(__name__)

class ComplianceService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def verify_bbbee(self, supplier: Dict[str, Any], preferences: Dict[str, float]) -> Dict[str, Any]:
require_bbbee = preferences.get('require_bbbee', False)
if not require_bbbee:
supplier['bbbee_level'] = None
supplier['bbbee_verified'] = False
return supplier

try:
db = motor.motor_asyncio.AsyncIOMotorClient(
self.config['mongodb']['url']
)[self.config['mongodb']['database']]
response = await self.http_client.post(
"https://api.bbbee-agency.co.za/verify",
headers={"Authorization": f"Bearer {self.config['external_sources']['bbbee']['api_key']}"},
json={
"company_name": supplier.get('company_name', ''),
"registration_number": supplier.get('registration_number', '')
}
)
response.raise_for_status()
result = response.json()
supplier['bbbee_level'] = result.get('level', 4)
supplier['bbbee_verified'] = result.get('verified', False)

await db['audit_logs'].insert_one({
'tenant_id': self.config['tenant_id'],
'action': 'verify_bbbee',
'model_name': 'SupplierProfile',
'object_id': supplier['id'],
'details': {'level': supplier['bbbee_level'], 'verified': supplier['bbbee_verified']},
'timestamp': datetime.now()
})
logger.info(f"B-BBEE verified for supplier {supplier['id']}: Level {supplier['bbbee_level']}")
return supplier
except Exception as e:
logger.error(f"B-BBEE verification failed for supplier {supplier['id']}: {e}")
supplier['bbbee_level'] = 4
supplier['bbbee_verified'] = False
return supplier

async def evaluate_compliance(self, supplier: Dict[str, Any], preferences: Dict[str, float]) -> float:
try:
supplier = await self.verify_bbbee(supplier, preferences)
bbbee_score = calculate_bbbee_score(supplier.get('bbbee_level', 4)) if preferences.get('require_bbbee', False) else 1.0
esg_score = supplier.get('esg_score', 0.0) / 100 * preferences.get('esg_weight', 0.05)
return bbbee_score * preferences.get('bbbee_weight', 0.0) + esg_score
except Exception as e:
logger.error(f"Compliance evaluation failed for supplier {supplier['id']}: {e}")
return 0.0




import logging
import httpx
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class NotificationService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.http_client = httpx.AsyncClient()

async def notify_suppliers(self, suppliers: List[Dict[str, Any]], tender_id: str, preferences: Dict[str, float]):
try:
db = motor.motor_asyncio.AsyncIOMotorClient(
self.config['mongodb']['url']
)[self.config['mongodb']['database']]
for supplier in suppliers:
priority = max(preferences, key=lambda k: preferences[k] if k.endswith('_weight') else 0)
message = f"Submit a quote for RFQ in tender {tender_id}. Priority: {priority.replace('_weight', '')}."
push_payload = {
"to": f"supplier_{supplier['id']}_device_token",
"notification": {
"title": f"RFQ Opportunity for Tender {tender_id}",
"body": message
}
}
await self.http_client.post(
"https://fcm.googleapis.com/fcm/send",
headers={"Authorization": f"key={self.config['notifications']['firebase_key']}"},
json=push_payload
)

voice_payload = {
"to": f"supplier_{supplier['id']}_device_token",
"data": {
"type": "voice_alert",
"message": message,
"grok_voice_mode": True
}
}
await self.http_client.post(
"https://fcm.googleapis.com/fcm/send",
headers={"Authorization": f"key={self.config['notifications']['firebase_key']}"},
json=voice_payload
)

await db['audit_logs'].insert_one({
'tenant_id': self.config['tenant_id'],
'action': 'send_voice_notification',
'model_name': 'SupplierProfile',
'object_id': supplier['id'],
'details': {'message': message},
'timestamp': datetime.now()
})
logger.info(f"Notified {len(suppliers)} suppliers for tender {tender_id}")
except Exception as e:
logger.error(f"Supplier notification failed: {e}")
raise




import logging
from typing import List, Dict, Any
from datetime import datetime, timedelta
import pandas as pd
from statsmodels.tsa.arima.model import ARIMA
import motor.motor_asyncio

logger = logging.getLogger(__name__)

class RecruitmentAnalyticsService:
def __init__(self, config: Dict[str, Any]):
self.config = config
self.db = motor.motor_asyncio.AsyncIOMotorClient(
self.config['mongodb']['url']
)[self.config['mongodb']['database']]

async def predict_supplier_gaps(self, horizon_days: int = 30, preferences: Dict[str, float] = None) -> Dict[str, Any]:
try:
require_bbbee = preferences.get('require_bbbee', False) if preferences else False
logs = await self.db['recruitment_logs'].find({
'timestamp': {'$gte': datetime.now() - timedelta(days=3650)},
'tenant_id': self.config['tenant_id'],
'require_bbbee': require_bbbee
}).to_list(None)

df = pd.DataFrame([
{'date': l['timestamp'].date(), 'gap': g}
for l in logs for g in l.get('gaps', [])
])
if df.empty:
return {'gaps': [], 'forecast': {}}

gap_counts = df.groupby(['date', 'gap']).size().unstack(fill_value=0)
gap_counts = gap_counts.resample('D').sum().fillna(0)

forecast = {}
for category in gap_counts.columns:
model = ARIMA(gap_counts[category], order=(1, 1, 1))
fit = model.fit()
future_counts = fit.forecast(steps=horizon_days)
forecast[category] = future_counts.tolist()

predicted_gaps = [cat for cat, counts in forecast.items() if sum(counts) > 0.5]

if predicted_gaps:
await self.db['queenbee_prompts'].insert_one({
'tender_id': None,
'message': f"Predicted supplier gaps: {', '.join(predicted_gaps)} in next {horizon_days} days",
'trigger_event': 'predicted_supplier_gap',
'created_at': datetime.now(),
'resolved': False,
'tenant_id': self.config['tenant_id']
})

logger.info(f"Predicted supplier gaps: {predicted_gaps}")
return {'gaps': predicted_gaps, 'forecast': forecast}
except Exception as e:
logger.error(f"Supplier gap prediction failed: {e}")
return {'gaps': [], 'forecast': {}}

async def update_metrics(self, tender_id: str, suppliers: List[Dict[str, Any]], criteria: List[str], preferences: Dict[str, float]):
try:
gaps = [c for c in criteria if not any(c.lower() in p['category'].lower() for p in suppliers)]
await self.db['recruitment_logs'].insert_one({
'tender_id': tender_id,
'supplier_count': len(suppliers),
'criteria_count': len(criteria),
'gaps': gaps,
'preferences': preferences,
'timestamp': datetime.now(),
'tenant_id': self.config['tenant_id']
})

forecast = await self.predict_supplier_gaps(preferences=preferences)
await self.db['recruitment_logs'].update_one(
{'tender_id': tender_id, 'timestamp': {'$gte': datetime.now()}},
{'$set': {'predicted_gaps': forecast['gaps']}}
)
logger.info(f"Updated metrics for tender {tender_id}")
except Exception as e:
logger.error(f"Metrics update failed: {e}")
raise




import logging
from typing import List, Dict, Any
from .utils import calculate_nlp_similarity

logger = logging.getLogger(__name__)

class BOQRFQMatcherService:
def __init__(self, config: Dict[str, Any]):
self.config = config

async def match_boq_rfq(self, boq_items: List[Dict[str, Any]], rfq_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
"""Match BOQ and RFQ items, resolving conflicts."""
try:
matched_items = []
for boq_item in boq_items:
best_match = None
best_score = 0.0
for rfq_item in rfq_items:
similarity = calculate_nlp_similarity(
boq_item.get('description', ''),
rfq_item.get('description', '')
)
if similarity > best_score and similarity > 0.7:
best_match = rfq_item
best_score = similarity

if best_match:
# Combine BOQ and RFQ data, prioritizing BOQ
matched_item = {
'tender_id': boq_item['tender_id'],
'description': boq_item['description'],
'quantity': boq_item['quantity'],
'category': boq_item.get('category', best_match.get('category', 'Other')),
'delivery_location': best_match.get('delivery_location', boq_item.get('delivery_location', {})),
'require_bbbee': best_match.get('require_bbbee', False),
'source': 'boq_rfq_matched'
}
matched_items.append(matched_item)
else:
matched_items.append(boq_item)

# Add unmatched RFQ items
for rfq_item in rfq_items:
if not any(calculate_nlp_similarity(rfq_item['description'], m['description']) > 0.7 for m in matched_items):
matched_items.append({
'tender_id': rfq_item['tender_id'],
'description': rfq_item['description'],
'quantity': rfq_item['quantity'],
'category': rfq_item.get('category', 'Other'),
'delivery_location': rfq_item.get('delivery_location', {}),
'require_bbbee': rfq_item.get('require_bbbee', False),
'source': 'rfq_only'
})

logger.info(f"Matched {len(matched_items)} items for tender {boq_items[0]['tender_id']}")
return matched_items
except Exception as e:
logger.error(f"BOQ-RFQ matching failed: {e}")
return boq_items




recruitment_agent:
mongodb:
url: "mongodb://localhost:27017"
database: "bidbeez"
collections:
recruitment_logs: "recruitment_logs"
supplier_profiles: "supplier_profiles"
historical_boqs: "historical_boqs"
rfqs: "rfqs"
queenbee_insights: "queenbee_insights"
parsed_requirements: "parsed_requirements"
queenbee_prompts: "queenbee_prompts"
audit_logs: "audit_logs"
tender_configs: "tender_configs"
postgresql:
dsn: "postgresql://user:password@localhost:5432/bidbeez"
redis:
url: "redis://localhost:6379"
external_sources:
brave_search:
api_key: "${BRAVE_API_KEY}"
linkedin:
client_id: "${LINKEDIN_CLIENT_ID}"
client_secret: "${LINKEDIN_CLIENT_SECRET}"
redirect_uri: "https://bidbeez.com/auth/linkedin/callback"
bbbee:
api_key: "${BBBEE_API_KEY}"
etenders:
api_key: "${ETENDERS_API_KEY}"
tenderbulletins:
api_key: "${TENDERBULLETINS_API_KEY}"
nlp:
model: "distilbert-base-uncased"
blockchain:
provider_url: "${BLOCKCHAIN_PROVIDER_URL}"
contract_abi: "${LICENSE_CONTRACT_ABI}"
wallet: "${BLOCKCHAIN_WALLET}"
notifications:
firebase_key: "${FIREBASE_API_KEY}"
websocket:
host: "localhost"
port: 8765
compliance:
bbbee:
required_level: 2
default_weight: 0.25
retry:
max_attempts: 3
backoff_factor: 2
tenant_id: "${TENANT_ID}"






-- Table: tenants
-- Description: Stores tenant information for multi-tenancy.
CREATE TABLE tenants (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
name VARCHAR(100) NOT NULL UNIQUE,
configuration JSONB DEFAULT '{}', -- Tenant settings (e.g., default B-BBEE level)
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT chk_name CHECK (name <> ''),
INDEX idx_tenants_name (name)
);

-- Table: supplier_profiles
-- Description: Stores supplier business profiles for relational queries.
CREATE TABLE supplier_profiles (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
user_id VARCHAR(50) NOT NULL, -- References auth_user
company_name VARCHAR(255) NOT NULL,
registration_number VARCHAR(50) UNIQUE,
email VARCHAR(255) UNIQUE,
address_line_1 VARCHAR(255),
city VARCHAR(100),
province VARCHAR(100),
postal_code VARCHAR(20),
latitude NUMERIC(9, 6),
longitude NUMERIC(9, 6),
bbbee_level INTEGER CHECK (bbbee_level BETWEEN 1 AND 8 OR bbbee_level IS NULL),
bbbee_score NUMERIC(3, 2),
esg_score NUMERIC(5, 2),
category VARCHAR(100), -- eTenders sector (e.g., Information Technology)
verification_status VARCHAR(20) NOT NULL CHECK (verification_status IN ('pending', 'verified', 'rejected')),
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
UNIQUE (tenant_id, user_id),
INDEX idx_supplier_profiles_tenant_category (tenant_id, category),
INDEX idx_supplier_profiles_bbbee (tenant_id, bbbee_level),
INDEX idx_supplier_profiles_location (tenant_id, latitude, longitude)
);

-- Table: supplier_products
-- Description: Stores supplier product details, including pricelists.
CREATE TABLE supplier_products (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
supplier_id UUID NOT NULL REFERENCES supplier_profiles(id) ON DELETE CASCADE,
product_name VARCHAR(255) NOT NULL,
description TEXT,
supplier_sku VARCHAR(100),
unit_price NUMERIC(10, 2) NOT NULL CHECK (unit_price >= 0),
currency CHAR(3) DEFAULT 'ZAR',
minimum_order_quantity INTEGER NOT NULL CHECK (minimum_order_quantity >= 1),
lead_time_days INTEGER,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_supplier_products_supplier (tenant_id, supplier_id),
INDEX idx_supplier_products_category (tenant_id, product_name)
);

-- Table: boq_items
-- Description: Stores BOQ items from tender documents.
CREATE TABLE boq_items (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
tender_id VARCHAR(50) NOT NULL, -- References ParsedTender
item_number VARCHAR(50) NOT NULL,
description TEXT NOT NULL,
quantity INTEGER NOT NULL CHECK (quantity > 0),
specification JSONB,
category VARCHAR(100), -- eTenders sector
delivery_location JSONB, -- {latitude, longitude, address}
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
UNIQUE (tenant_id, tender_id, item_number),
INDEX idx_boq_items_tenant_tender (tenant_id, tender_id),
INDEX idx_boq_items_category (tenant_id, category)
);

-- Table: rfqs
-- Description: Stores RFQ items from eTenders and other sources.
CREATE TABLE rfqs (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
tender_id VARCHAR(50) NOT NULL,
source VARCHAR(100) NOT NULL, -- eTenders, TenderBulletins, etc.
items JSONB NOT NULL, -- Array of {description, quantity, category}
delivery_location JSONB,
require_bbbee BOOLEAN NOT NULL DEFAULT FALSE,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_rfqs_tenant_tender (tenant_id, tender_id),
INDEX idx_rfqs_source (tenant_id, source)
);

-- Table: boq_rfq_matches
-- Description: Correlates BOQ and RFQ items for recruitment.
CREATE TABLE boq_rfq_matches (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
boq_item_id UUID NOT NULL REFERENCES boq_items(id) ON DELETE CASCADE,
rfq_id UUID REFERENCES rfqs(id) ON DELETE SET NULL,
similarity_score NUMERIC(5, 2) NOT NULL CHECK (similarity_score BETWEEN 0 AND 1),
matched_item JSONB, -- Combined BOQ/RFQ item data
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
UNIQUE (tenant_id, boq_item_id, rfq_id),
INDEX idx_boq_rfq_matches_boq (tenant_id, boq_item_id),
INDEX idx_boq_rfq_matches_rfq (tenant_id, rfq_id)
);

-- Table: recruitment_logs
-- Description: Tracks recruitment activities for BOQs and RFQs.
CREATE TABLE recruitment_logs (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
tender_id VARCHAR(50) NOT NULL,
supplier_count INTEGER NOT NULL DEFAULT 0,
criteria_count INTEGER NOT NULL DEFAULT 0,
gaps JSONB DEFAULT '[]', -- Unmatched categories
preferences JSONB, -- Bidder preferences
status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failed', 'pending')),
error_message TEXT,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_recruitment_logs_tenant_tender (tenant_id, tender_id),
INDEX idx_recruitment_logs_status (tenant_id, status)
);

-- Table: verifications
-- Description: Records supplier verifications.
CREATE TABLE verifications (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
supplier_id UUID NOT NULL REFERENCES supplier_profiles(id) ON DELETE CASCADE,
verification_type VARCHAR(100) NOT NULL CHECK (verification_type IN ('kyc', 'aml', 'bbbee', 'esg')),
status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
notes TEXT,
verified_by VARCHAR(50), -- References auth_user
verification_date TIMESTAMP WITH TIME ZONE,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_verifications_supplier (tenant_id, supplier_id),
INDEX idx_verifications_type (tenant_id, verification_type)
);

-- Table: bbbee_compliance_verifications
-- Description: Stores B-BBEE verification results.
CREATE TABLE bbbee_compliance_verifications (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
supplier_id UUID NOT NULL REFERENCES supplier_profiles(id) ON DELETE CASCADE,
bbbee_level INTEGER CHECK (bbbee_level BETWEEN 1 AND 8),
verified BOOLEAN NOT NULL DEFAULT FALSE,
verification_date TIMESTAMP WITH TIME ZONE,
notes TEXT,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_bbbee_verifications_supplier (tenant_id, supplier_id)
);

-- Table: supplier_relevance_scores
-- Description: Stores historical BOQ-based supplier relevance.
CREATE TABLE supplier_relevance_scores (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
supplier_id UUID NOT NULL REFERENCES supplier_profiles(id) ON DELETE CASCADE,
category VARCHAR(100) NOT NULL,
relevance_score NUMERIC(5, 2) NOT NULL CHECK (relevance_score BETWEEN 0 AND 1),
match_count INTEGER NOT NULL DEFAULT 0,
last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
UNIQUE (tenant_id, supplier_id, category),
INDEX idx_relevance_scores_supplier (tenant_id, supplier_id),
INDEX idx_relevance_scores_category (tenant_id, category)
);

-- Table: tender_preferences
-- Description: Manages bidder preferences for supplier selection.
CREATE TABLE tender_preferences (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
tender_id VARCHAR(50) NOT NULL,
bidder_id VARCHAR(50) NOT NULL,
require_bbbee BOOLEAN NOT NULL DEFAULT FALSE,
bbbee_weight NUMERIC(5, 2) DEFAULT 0.25,
location_weight NUMERIC(5, 2) DEFAULT 0.20,
price_weight NUMERIC(5, 2) DEFAULT 0.35,
stock_weight NUMERIC(5, 2) DEFAULT 0.25,
performance_weight NUMERIC(5, 2) DEFAULT 0.10,
esg_weight NUMERIC(5, 2) DEFAULT 0.05,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
UNIQUE (tenant_id, tender_id, bidder_id),
CONSTRAINT chk_weights CHECK (
bbbee_weight + location_weight + price_weight + stock_weight + performance_weight + esg_weight = 1.0
),
INDEX idx_tender_preferences_tenant_tender (tenant_id, tender_id)
);

-- Table: recruitment_analytics
-- Description: Aggregates metrics for forecasting and dashboards.
CREATE TABLE recruitment_analytics (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
tender_id VARCHAR(50),
supplier_count INTEGER NOT NULL DEFAULT 0,
gap_count INTEGER NOT NULL DEFAULT 0,
success_rate NUMERIC(5, 2) CHECK (success_rate BETWEEN 0 AND 100),
average_bbbee_score NUMERIC(3, 2),
average_match_score NUMERIC(3, 2),
period_start DATE NOT NULL,
period_end DATE NOT NULL,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT chk_period CHECK (period_end > period_start),
INDEX idx_recruitment_analytics_tenant_period (tenant_id, period_start, period_end)
);

-- Table: audit_logs
-- Description: Logs agent actions for compliance.
CREATE TABLE audit_logs (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
user_id VARCHAR(50), -- References auth_user
action VARCHAR(100) NOT NULL, -- e.g., 'rfq_processed', 'bbbee_verified'
model_name VARCHAR(100) NOT NULL,
object_id VARCHAR(50),
details JSONB DEFAULT '{}',
timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
INDEX idx_audit_logs_tenant_action (tenant_id, action),
INDEX idx_audit_logs_timestamp (tenant_id, timestamp)
);

-- Table: queenbee_prompts
-- Description: Triggers recruitment events, synced with QueenBeePrompt.
CREATE TABLE queenbee_prompts (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
tender_id VARCHAR(50),
message TEXT NOT NULL,
trigger_event VARCHAR(100) NOT NULL, -- e.g., 'rfq_processed'
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
resolved BOOLEAN NOT NULL DEFAULT FALSE,
resolved_at TIMESTAMP WITH TIME ZONE,
INDEX idx_queenbee_prompts_tenant_tender (tenant_id, tender_id),
INDEX idx_queenbee_prompts_trigger (tenant_id, trigger_event)
);
