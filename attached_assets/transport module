

TRANSPORT MODULE — FINAL BEST UPGRADE (One Shot, Full Replacement)


backend/domains/transport/models.py
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class TransportBooking(models.Model):
SERVICE_CHOICES = [
('uber', 'Uber'),
('bolt', 'Bolt'),
('indrive', 'InDrive'),
]

STATUS_CHOICES = [
('requested', 'Requested'),
('accepted', 'Accepted'),
('in_transit', 'In Transit'),
('completed', 'Completed'),
('cancelled', 'Cancelled'),
('failed', 'Failed'),
]

created_by = models.ForeignKey(User, on_delete=models.CASCADE)
bee_full_name = models.CharField(max_length=255)
service_type = models.CharField(max_length=20, choices=SERVICE_CHOICES)
pickup_address = models.Char<PERSON>ield(max_length=255)
dropoff_address = models.Char<PERSON><PERSON>(max_length=255)
pickup_time = models.DateTimeField()
estimated_fare = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
actual_fare = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
tracking_link = models.URLField(blank=True, null=True)
status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='requested')
created_at = models.DateTimeField(auto_now_add=True)

def __str__(self):
return f"{self.bee_full_name} - {self.service_type} ({self.status})"


backend/domains/transport/serializers.py
from rest_framework import serializers
from .models import TransportBooking

class TransportBookingSerializer(serializers.ModelSerializer):
class Meta:
model = TransportBooking
fields = [
'id', 'created_by', 'bee_full_name', 'service_type',
'pickup_address', 'dropoff_address', 'pickup_time',
'estimated_fare', 'actual_fare', 'tracking_link', 'status', 'created_at'
]
read_only_fields = ['created_by', 'created_at']



backend/domains/transport/views.py
from rest_framework import viewsets, permissions
from .models import TransportBooking
from .serializers import TransportBookingSerializer
from .services import auto_assign_transport_service

class TransportBookingViewSet(viewsets.ModelViewSet):
queryset = TransportBooking.objects.all()
serializer_class = TransportBookingSerializer
permission_classes = [permissions.IsAuthenticated]

def perform_create(self, serializer):
service_type = auto_assign_transport_service()
serializer.save(created_by=self.request.user, service_type=service_type)



backend/domains/transport/services.py

import random

def auto_assign_transport_service():
"""Simulate selecting Uber, Bolt, or InDrive based on availability."""
providers = ['uber', 'bolt', 'indrive']
return random.choice(providers)


backend/domains/transport/admin.py
from django.contrib import admin
from .models import TransportBooking

@admin.register(TransportBooking)
class TransportBookingAdmin(admin.ModelAdmin):
list_display = ('bee_full_name', 'service_type', 'pickup_address', 'dropoff_address', 'status', 'pickup_time', 'created_at')
list_filter = ('service_type', 'status')
search_fields = ('bee_full_name', 'pickup_address', 'dropoff_address')


backend/domains/transport/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import TransportBookingViewSet

router = DefaultRouter()
router.register(r'transport-bookings', TransportBookingViewSet)

urlpatterns = [
path('', include(router.urls)),
]





Migration Commands


After pasting these into your backend/domains/transport/,

run:

python manage.py makemigrations transport
python manage.py migrate transport
