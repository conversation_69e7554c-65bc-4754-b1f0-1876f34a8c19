import re

def extract_site_meetings(text):
    matches = re.findall(r'(site meeting|briefing) (will be held|at) ([\w\s,.-]+) on (\d{1,2} \w+ \d{4})', text, re.IGNORECASE)
    return [{"location": m[2].strip(), "date": m[3].strip()} for m in matches]

def extract_submission_address(text):
    if "submit" in text.lower():
        return re.findall(r'submit.*?to ([\w\s,.-]+)', text, re.IGNORECASE)
    return []
