Project Structure
backend/domains/courier/
├── __init__.py
├── apps.py
├── models/
│   ├── __init__.py
│   ├── courier_company.py
│   ├── courier_request.py
│   ├── courier_rate.py
│   ├── courier_tracking_update.py
│   ├── courier_integration.py
│   └── audit_log.py
├── views/
│   ├── __init__.py
│   ├── dispatch_view.py
│   ├── tracking_view.py
│   ├── estimation_view.py
│   └── admin_views.py
├── services/
│   ├── __init__.py
│   ├── dispatcher.py
│   ├── tracking_service.py
│   ├── estimation_service.py
│   ├── sla_monitoring_service.py
│   ├── route_optimization_service.py
│   ├── notification_service.py
│   └── webhook_service.py
├── utils/
│   ├── __init__.py
│   ├── courier_utils.py
│   ├── mapping_utils.py
│   ├── insurance_calculator.py
│   └── encryption_utils.py
├── middleware/
│   ├── __init__.py
│   └── tenant_middleware.py
├── events/
│   ├── __init__.py
│   ├── courier_request_created_event.py
│   └── tracking_update_event.py
├── tasks/
│   ├── __init__.py
│   ├── celery_tasks.py
│   └── monitoring_tasks.py
├── serializers/
│   ├── __init__.py
│   ├── courier_serializers.py
│   └── tracking_serializers.py
├── tests/
│   ├── __init__.py
│   ├── test_models.py
│   ├── test_views.py
│   ├── test_services.py
│   └── test_utils.py
├── migrations/
│   └── __init__.py
├── admin.py
├── urls.py
├── settings.py
└── requirements.txt

1. Database Models
models/init.py
Copy
from .courier_company import CourierCompany
from .courier_request import CourierRequest
from .courier_rate import CourierRate
from .courier_tracking_update import CourierTrackingUpdate
from .courier_integration import CourierIntegration
from .audit_log import AuditLog

__all__ = [
    'CourierCompany',
    'CourierRequest', 
    'CourierRate',
    'CourierTrackingUpdate',
    'CourierIntegration',
    'AuditLog'
]
models/courier_company.py
Copy
import uuid
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator

class CourierCompany(models.Model):
    """
    Model representing courier companies available on the platform.
    Supports multi-tenant architecture with reliability scoring.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tenant_id = models.CharField(max_length=50, db_index=True)
    name = models.CharField(max_length=100)
    is_national = models.BooleanField(default=False)
    reliability_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    insurance_cap = models.DecimalField(
        max_digits=12, 
        decimal_places=2, 
        null=True, 
        blank=True,
        help_text="Maximum insurable value in ZAR"
    )
    currency = models.CharField(max_length=3, default='ZAR')
    api_integration_active = models.BooleanField(default=False)
    is_blacklisted = models.BooleanField(default=False)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'courier_couriercompany'
        indexes = [
            models.Index(fields=['tenant_id']),
            models.Index(fields=['tenant_id', 'is_blacklisted']),
            models.Index(fields=['reliability_score']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['tenant_id', 'name'], 
                name='unique_courier_per_tenant'
            )
        ]
    
    def __str__(self):
        return f"{self.name} ({self.tenant_id})"
    
    def get_active_integrations(self):
        """Get active integrations for this courier"""
        return self.courier_integrations.filter(
            connection_status='connected'
        )
    
    def calculate_reliability_score(self):
        """Calculate reliability score based on recent performance"""
        # Implementation would analyze recent deliveries
        # This is a placeholder for the actual calculation
        from django.db.models import Avg
        from .courier_request import CourierRequest
        
        recent_requests = CourierRequest.objects.filter(
            courier_company=self,
            status='delivered',
            created_at__gte=timezone.now() - timedelta(days=30)
        )
        
        if not recent_requests.exists():
            return self.reliability_score
        
        # Calculate based on on-time delivery rate
        on_time_count = recent_requests.filter(
            actual_delivery_date__lte=models.F('expected_delivery_date')
        ).count()
        
        total_count = recent_requests.count()
        new_score = (on_time_count / total_count) * 100
        
        # Update the score
        self.reliability_score = new_score
        self.save(update_fields=['reliability_score', 'updated_at'])
        
        return new_score
models/courier_request.py
Copy
import uuid
from django.db import models
from django.contrib.auth.models import User
from django.contrib.gis.db import models as gis_models
from django.core.validators import MinValueValidator
from .courier_company import CourierCompany

class CourierRequest(models.Model):
    """
    Model representing courier shipment requests initiated by users.
    Includes geospatial support for origin/destination tracking.
    """
    
    URGENCY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_transit', 'In Transit'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tenant_id = models.CharField(max_length=50, db_index=True)
    created_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='courier_requests'
    )
    courier_company = models.ForeignKey(
        CourierCompany,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='courier_requests'
    )
    
    # Address information
    origin_address = models.TextField()
    destination_address = models.TextField()
    
    # Geospatial coordinates (PostGIS)
    location_origin = gis_models.PointField(null=True, blank=True, srid=4326)
    location_destination = gis_models.PointField(null=True, blank=True, srid=4326)
    
    # Package details
    weight_kg = models.FloatField(validators=[MinValueValidator(0.1)])
    declared_value = models.DecimalField(
        max_digits=12, 
        decimal_places=2, 
        default=0.00,
        validators=[MinValueValidator(0)]
    )
    urgency = models.CharField(max_length=10, choices=URGENCY_CHOICES, default='medium')
    
    # Status and tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    tracking_number = models.CharField(max_length=50, unique=True, null=True, blank=True)
    
    # Delivery timing
    expected_delivery_date = models.DateTimeField(null=True, blank=True)
    actual_delivery_date = models.DateTimeField(null=True, blank=True)
    
    # Additional information
    delivery_notes = models.TextField(blank=True)
    special_instructions = models.TextField(blank=True)
    
    # Pricing information
    estimated_cost = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        null=True, 
        blank=True
    )
    final_cost = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        null=True, 
        blank=True
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'courier_courierrequest'
        indexes = [
            models.Index(fields=['tenant_id']),
            models.Index(fields=['status']),
            models.Index(fields=['tracking_number']),
            models.Index(fields=['created_by']),
            models.Index(fields=['courier_company']),
            models.Index(fields=['created_at']),
            models.Index(fields=['expected_delivery_date']),
        ]
    
    def __str__(self):
        return f"Request {self.tracking_number or self.id} - {self.status}"
    
    def is_overdue(self):
        """Check if delivery is overdue"""
        if not self.expected_delivery_date:
            return False
        
        from django.utils import timezone
        return (
            self.status not in ['delivered', 'cancelled'] and
            timezone.now() > self.expected_delivery_date
        )
    
    def get_latest_tracking_update(self):
        """Get the most recent tracking update"""
        return self.tracking_updates.order_by('-timestamp').first()
    
    def calculate_distance_km(self):
        """Calculate distance between origin and destination"""
        if not (self.location_origin and self.location_destination):
            return None
        
        from django.contrib.gis.measure import Distance
        distance = self.location_origin.distance(self.location_destination)
        return distance.km
    
    def generate_tracking_number(self):
        """Generate unique tracking number"""
        from ..utils.courier_utils import CourierUtils
        if not self.tracking_number:
            self.tracking_number = CourierUtils.generate_tracking_number()
            self.save(update_fields=['tracking_number'])
        return self.tracking_number
models/courier_rate.py
Copy
import uuid
from django.db import models
from django.core.validators import MinValueValidator
from .courier_company import CourierCompany

class CourierRate(models.Model):
    """
    Model storing pricing rates for courier services.
    Supports zone-based pricing and service levels.
    """
    
    SERVICE_LEVELS = [
        ('standard', 'Standard'),
        ('express', 'Express'),
        ('overnight', 'Overnight'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tenant_id = models.CharField(max_length=50, db_index=True)
    courier = models.ForeignKey(
        CourierCompany,
        on_delete=models.CASCADE,
        related_name='courier_rates'
    )
    
    # Zone information (assuming Zone model exists elsewhere)
    origin_zone = models.CharField(max_length=50, null=True, blank=True)
    destination_zone = models.CharField(max_length=50, null=True, blank=True)
    
    # Weight and pricing
    weight_class = models.CharField(max_length=20)  # e.g., '<1kg', '1-2kg'
    base_price = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    price_per_additional_kg = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=0.00
    )
    
    # Surcharges
    surcharge_urgent = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=0.00
    )
    min_volume_discount = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=0.00
    )
    
    # Validity period
    effective_from = models.DateTimeField()
    effective_to = models.DateTimeField()
    
    # Service details
    service_level = models.CharField(max_length=20, choices=SERVICE_LEVELS)
    currency = models.CharField(max_length=3, default='ZAR')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'courier_courierrate'
        indexes = [
            models.Index(fields=['tenant_id']),
            models.Index(fields=['courier']),
            models.Index(fields=['weight_class']),
            models.Index(fields=['service_level']),
            models.Index(fields=['effective_from', 'effective_to']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(effective_to__gt=models.F('effective_from')),
                name='valid_date_range'
            )
        ]
    
    def __str__(self):
        return f"{self.courier.name} - {self.weight_class} - {self.service_level}"
    
    def is_active(self):
        """Check if rate is currently active"""
        from django.utils import timezone
        now = timezone.now()
        return self.effective_from <= now <= self.effective_to
    
    def calculate_total_cost(self, weight_kg, urgency='medium', declared_value=0):
        """Calculate total cost for given parameters"""
        total_cost = float(self.base_price)
        
        # Add weight-based charges
        if weight_kg > 1.0:  # Assuming base covers first kg
            additional_weight = weight_kg - 1.0
            total_cost += float(self.price_per_additional_kg) * additional_weight
        
        # Add urgency surcharge
        if urgency == 'high':
            total_cost += float(self.surcharge_urgent)
        
        # Add insurance cost (placeholder calculation)
        if declared_value > 0:
            insurance_rate = 0.005  # 0.5% of declared value
            total_cost += float(declared_value) * insurance_rate
        
        return round(total_cost, 2)
models/courier_tracking_update.py
Copy
import uuid
from django.db import models
from django.contrib.gis.db import models as gis_models
from .courier_request import CourierRequest

class CourierTrackingUpdate(models.Model):
    """
    Model storing tracking updates for courier requests.
    Includes geospatial positioning for real-time tracking.
    """
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('picked_up', 'Picked Up'),
        ('in_transit', 'In Transit'),
        ('out_for_delivery', 'Out for Delivery'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('returned', 'Returned'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tenant_id = models.CharField(max_length=50, db_index=True)
    courier_request = models.ForeignKey(
        CourierRequest,
        on_delete=models.CASCADE,
        related_name='tracking_updates'
    )
    
    # Status and location
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    location = models.TextField(null=True, blank=True)  # Human-readable location
    position = gis_models.PointField(null=True, blank=True, srid=4326)  # GPS coordinates
    
    # Timing information
    estimated_delivery_time = models.DateTimeField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Additional details
    notes = models.TextField(blank=True)
    courier_reference = models.CharField(max_length=100, blank=True)
    
    # Source information
    source = models.CharField(
        max_length=20,
        choices=[
            ('system', 'System'),
            ('webhook', 'Webhook'),
            ('manual', 'Manual'),
            ('api', 'API'),
        ],
        default='system'
    )
    
    class Meta:
        db_table = 'courier_couriertrackingupdate'
        indexes = [
            models.Index(fields=['tenant_id']),
            models.Index(fields=['courier_request']),
            models.Index(fields=['status']),
            models.Index(fields=['timestamp']),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"Update for {self.courier_request.tracking_number}: {self.status}"
    
    def get_mapbox_coordinates(self):
        """Return coordinates in Mapbox format [lng, lat]"""
        if not self.position:
            return None
        return [self.position.x, self.position.y]
    
    def save(self, *args, **kwargs):
        """Override save to update courier request status"""
        is_new = self.pk is None
        super().save(*args, **kwargs)
        
        if is_new:
            # Update the parent courier request status
            self.courier_request.status = self.status
            self.courier_request.save(update_fields=['status', 'updated_at'])
            
            # Trigger tracking update event
            from ..events.tracking_update_event import TrackingUpdateEvent
            TrackingUpdateEvent.trigger(self)
models/courier_integration.py
Copy
import uuid
from django.db import models
from encrypted_fields import EncryptedCharField
from .courier_company import CourierCompany

class CourierIntegration(models.Model):
    """
    Model storing API integration details for courier companies.
    Uses encrypted fields for sensitive API credentials.
    """
    
    CONNECTION_STATUS_CHOICES = [
        ('connected', 'Connected'),
        ('disconnected', 'Disconnected'),
        ('error', 'Error'),
        ('testing', 'Testing'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tenant_id = models.CharField(max_length=50, db_index=True)
    courier_company = models.ForeignKey(
        CourierCompany,
        on_delete=models.CASCADE,
        related_name='courier_integrations'
    )
    
    # API Configuration
    api_endpoint = models.URLField()
    api_key = EncryptedCharField(max_length=100)
    api_secret = EncryptedCharField(max_length=100, null=True, blank=True)
    
    # Capabilities
    supports_tracking = models.BooleanField(default=False)
    supports_dispatch = models.BooleanField(default=False)
    supports_estimation = models.BooleanField(default=False)
    supports_webhooks = models.BooleanField(default=False)
    
    # Connection status
    connection_status = models.CharField(
        max_length=20, 
        choices=CONNECTION_STATUS_CHOICES,
        default='disconnected'
    )
    last_test_at = models.DateTimeField(null=True, blank=True)
    last_error = models.TextField(blank=True)
    
    # Webhook configuration
    webhook_url = models.URLField(null=True, blank=True)
    webhook_secret = EncryptedCharField(max_length=100, null=True, blank=True)
    
    # Rate limiting
    rate_limit_per_minute = models.IntegerField(default=60)
    current_usage = models.IntegerField(default=0)
    usage_reset_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'courier_courierintegration'
        indexes = [
            models.Index(fields=['tenant_id']),
            models.Index(fields=['courier_company']),
            models.Index(fields=['connection_status']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['tenant_id', 'courier_company'], 
                name='unique_integration_per_courier'
            )
        ]
    
    def __str__(self):
        return f"Integration: {self.courier_company.name} ({self.connection_status})"
    
    def test_connection(self):
        """Test API connection"""
        from ..services.webhook_service import WebhookService
        
        try:
            # Implement actual API test here
            webhook_service = WebhookService(self)
            result = webhook_service.test_connection()
            
            if result:
                self.connection_status = 'connected'
                self.last_error = ''
            else:
                self.connection_status = 'error'
                self.last_error = 'Connection test failed'
                
        except Exception as e:
            self.connection_status = 'error'
            self.last_error = str(e)
        
        self.last_test_at = timezone.now()
        self.save(update_fields=['connection_status', 'last_test_at', 'last_error'])
        
        return self.connection_status == 'connected'
    
    def is_rate_limited(self):
        """Check if integration is rate limited"""
        from django.utils import timezone
        
        if not self.usage_reset_at:
            return False
        
        if timezone.now() > self.usage_reset_at:
            # Reset usage counter
            self.current_usage = 0
            self.usage_reset_at = timezone.now() + timezone.timedelta(minutes=1)
            self.save(update_fields=['current_usage', 'usage_reset_at'])
            return False
        
        return self.current_usage >= self.rate_limit_per_minute
    
    def increment_usage(self):
        """Increment API usage counter"""
        from django.utils import timezone
        
        if not self.usage_reset_at:
            self.usage_reset_at = timezone.now() + timezone.timedelta(minutes=1)
        
        self.current_usage += 1
        self.save(update_fields=['current_usage', 'usage_reset_at'])
models/audit_log.py
Copy
import uuid
from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey

class AuditLog(models.Model):
    """
    Model storing audit logs for all operations in the courier module.
    Provides comprehensive tracking for compliance and debugging.
    """
    
    ACTION_CHOICES = [
        ('create', 'Create'),
        ('read', 'Read'),
        ('update', 'Update'),
        ('delete', 'Delete'),
        ('dispatch', 'Dispatch'),
        ('track', 'Track'),
        ('estimate', 'Estimate'),
        ('webhook', 'Webhook'),
        ('integration', 'Integration'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tenant_id = models.CharField(max_length=50, null=True, blank=True, db_index=True)
    
    # Action details
    action = models.CharField(max_length=100)
    action_type = models.CharField(max_length=20, choices=ACTION_CHOICES, default='read')
    
    # User information
    user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='courier_audit_logs'
    )
    user_ip = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    # Object reference (generic foreign key)
    content_type = models.ForeignKey(
        ContentType, 
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    object_id = models.CharField(max_length=255, null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Additional details
    details = models.JSONField(null=True, blank=True)
    request_data = models.JSONField(null=True, blank=True)
    response_data = models.JSONField(null=True, blank=True)
    
    # Status and timing
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)
    execution_time_ms = models.IntegerField(null=True, blank=True)
    
    # Metadata
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'courier_auditlog'
        indexes = [
            models.Index(fields=['tenant_id']),
            models.Index(fields=['action']),
            models.Index(fields=['action_type']),
            models.Index(fields=['user']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['success']),
            models.Index(fields=['content_type', 'object_id']),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.action} by {self.user or 'System'} at {self.timestamp}"
    
    @classmethod
    def log_action(cls, action, user=None, tenant_id=None, content_object=None, 
                   details=None, request_data=None, response_data=None, 
                   success=True, error_message='', execution_time_ms=None,
                   user_ip=None, user_agent=''):
        """
        Convenient method to create audit log entries
        """
        action_type = cls._determine_action_type(action)
        
        return cls.objects.create(
            action=action,
            action_type=action_type,
            user=user,
            tenant_id=tenant_id,
            content_object=content_object,
            details=details,
            request_data=request_data,
            response_data=response_data,
            success=success,
            error_message=error_message,
            execution_time_ms=execution_time_ms,
            user_ip=user_ip,
            user_agent=user_agent,
        )
    
    @staticmethod
    def _determine_action_type(action):
        """Determine action type from action string"""
        action_lower = action.lower()
        
        if 'create' in action_lower or 'dispatch' in action_lower:
            return 'create'
        elif 'update' in action_lower or 'modify' in action_lower:
            return 'update'
        elif 'delete' in action_lower or 'remove' in action_lower:
            return 'delete'
        elif 'track' in action_lower:
            return 'track'
        elif 'estimate' in action_lower:
            return 'estimate'
        elif 'webhook' in action_lower:
            return 'webhook'
        elif 'integration' in action_lower:
            return 'integration'
        else:
            return 'read'

2. API Views
views/init.py
Copy
from .dispatch_view import DispatchView
from .tracking_view import TrackingView
from .estimation_view import EstimationView
from .admin_views import CourierCompanyViewSet, CourierRateViewSet

__all__ = [
    'DispatchView',
    'TrackingView', 
    'EstimationView',
    'CourierCompanyViewSet',
    'CourierRateViewSet'
]
views/dispatch_view.py
Copy
import time
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db import transaction

from ..models import CourierRequest, CourierCompany, AuditLog
from ..serializers.courier_serializers import DispatchRequestSerializer, CourierRequestSerializer
from ..services.dispatcher import Dispatcher
from ..middleware.tenant_middleware import get_current_tenant

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def dispatch_courier(request):
    """
    Dispatch a courier shipment request.
    
    POST /api/courier/dispatch/
    
    Expected payload:
    {
        "courier_company_id": "uuid",
        "origin_address": "123 Main St, Cape Town",
        "destination_address": "456 High St, Johannesburg",
        "weight_kg": 2.5,
        "declared_value": 1000.00,
        "urgency": "high",
        "special_instructions": "Handle with care"
    }
    """
    start_time = time.time()
    tenant_id = get_current_tenant(request)
    
    try:
        # Validate request data
        serializer = DispatchRequestSerializer(data=request.data)
        if not serializer.is_valid():
            AuditLog.log_action(
                action='dispatch_validation_failed',
                user=request.user,
                tenant_id=tenant_id,
                details={'errors': serializer.errors},
                request_data=request.data,
                success=False,
                user_ip=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            return Response(
                {'error': 'Invalid request data', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        
        # Verify courier company exists and is available
        try:
            courier_company = CourierCompany.objects.get(
                id=validated_data['courier_company_id'],
                tenant_id=tenant_id,
                is_blacklisted=False
            )
        except CourierCompany.DoesNotExist:
            return Response(
                {'error': 'Courier company not found or not available'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Use dispatcher service to create and process request
        with transaction.atomic():
            dispatcher = Dispatcher(tenant_id)
            courier_request = dispatcher.dispatch_shipment(
                user=request.user,
                courier_company=courier_company,
                **validated_data
            )
        
        # Prepare response
        response_serializer = CourierRequestSerializer(courier_request)
        execution_time = int((time.time() - start_time) * 1000)
        
        # Log successful dispatch
        AuditLog.log_action(
            action='dispatch_shipment_created',
            user=request.user,
            tenant_id=tenant_id,
            content_object=courier_request,
            request_data=request.data,
            response_data=response_serializer.data,
            execution_time_ms=execution_time,
            user_ip=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        return Response(
            {
                'success': True,
                'message': 'Shipment dispatched successfully',
                'data': response_serializer.data,
                'tracking_number': courier_request.tracking_number
            },
            status=status.HTTP_201_CREATED
        )
        
    except Exception as e:
        execution_time = int((time.time() - start_time) * 1000)
        
        # Log error
        AuditLog.log_action(
            action='dispatch_shipment_failed',
            user=request.user,
            tenant_id=tenant_id,
            request_data=request.data,
            success=False,
            error_message=str(e),
            execution_time_ms=execution_time,
            user_ip=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        return Response(
            {'error': 'Failed to dispatch shipment', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class DispatchView:
    """Class-based view wrapper for dispatch functionality"""
    
    @staticmethod
    def dispatch(request):
        return dispatch_courier(request)
views/tracking_view.py
Copy
import time
import hmac
import hashlib
from django.conf import settings
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from ..models import CourierRequest, CourierTrackingUpdate, AuditLog
from ..serializers.tracking_serializers import TrackingUpdateSerializer, TrackingResponseSerializer
from ..services.tracking_service import TrackingService
from ..middleware.tenant_middleware import get_current_tenant

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_tracking_info(request, tracking_number):
    """
    Get tracking information for a shipment.
    
    GET /api/courier/tracking/{tracking_number}/
    """
    start_time = time.time()
    tenant_id = get_current_tenant(request)
    
    try:
        # Find courier request
        try:
            courier_request = CourierRequest.objects.get(
                tracking_number=tracking_number,
                tenant_id=tenant_id
            )
        except CourierRequest.DoesNotExist:
            return Response(
                {'error': 'Tracking number not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Check if user has access to this request
        if (courier_request.created_by != request.user and 
            not request.user.is_staff):
            return Response(
                {'error': 'Access denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get tracking updates
        tracking_service = TrackingService(tenant_id)
        tracking_data = tracking_service.get_tracking_info(courier_request)
        
        execution_time = int((time.time() - start_time) * 1000)
        
        # Log tracking access
        AuditLog.log_action(
            action='tracking_info_accessed',
            user=request.user,
            tenant_id=tenant_id,
            content_object=courier_request,
            details={'tracking_number': tracking_number},
            execution_time_ms=execution_time,
            user_ip=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        return Response(tracking_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        execution_time = int((time.time() - start_time) * 1000)
        
        AuditLog.log_action(
            action='tracking_info_failed',
            user=request.user,
            tenant_id=tenant_id,
            details={'tracking_number': tracking_number},
            success=False,
            error_message=str(e),
            execution_time_ms=execution_time,
            user_ip=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        return Response(
            {'error': 'Failed to retrieve tracking information'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@csrf_exempt
@api_view(['POST'])
def webhook_tracking_update(request):
    """
    Webhook endpoint for external courier systems to send tracking updates.
    
    POST /api/courier/tracking/
    
    Requires X-Webhook-Signature header with HMAC-SHA256 signature.
    """
    start_time = time.time()
    
    try:
        # Verify webhook signature
        signature = request.META.get('HTTP_X_WEBHOOK_SIGNATURE')
        if not signature:
            return Response(
                {'error': 'Missing webhook signature'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        # Validate signature
        webhook_secret = getattr(settings, 'WEBHOOK_SECRET', '')
        expected_signature = hmac.new(
            webhook_secret.encode('utf-8'),
            request.body,
            hashlib.sha256
        ).hexdigest()
        
        if not hmac.compare_digest(signature, f"sha256={expected_signature}"):
            AuditLog.log_action(
                action='webhook_invalid_signature',
                details={'provided_signature': signature},
                request_data=request.data,
                success=False,
                error_message='Invalid webhook signature',
                user_ip=request.META.get('REMOTE_ADDR')
            )
            return Response(
                {'error': 'Invalid webhook signature'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        # Validate request data
        serializer = TrackingUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {'error': 'Invalid tracking data', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        tenant_id = validated_data.get('tenant_id')
        
        # Find courier request
        try:
            courier_request = CourierRequest.objects.get(
                id=validated_data['courier_request_id'],
                tenant_id=tenant_id
            )
        except CourierRequest.DoesNotExist:
            return Response(
                {'error': 'Courier request not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Process tracking update
        tracking_service = TrackingService(tenant_id)
        tracking_update = tracking_service.process_webhook_update(
            courier_request, validated_data
        )
        
        execution_time = int((time.time() - start_time) * 1000)
        
        # Log successful webhook
        AuditLog.log_action(
            action='webhook_tracking_update_processed',
            tenant_id=tenant_id,
            content_object=tracking_update,
            request_data=request.data,
            execution_time_ms=execution_time,
            user_ip=request.META.get('REMOTE_ADDR')
        )
        
        return Response(
            {'success': True, 'message': 'Tracking update processed'},
            status=status.HTTP_200_OK
        )
        
    except Exception as e:
        execution_time = int((time.time() - start_time) * 1000)
        
        AuditLog.log_action(
            action='webhook_tracking_update_failed',
            request_data=request.data,
            success=False,
            error_message=str(e),
            execution_time_ms=execution_time,
            user_ip=request.META.get('REMOTE_ADDR')
        )
        
        return Response(
            {'error': 'Failed to process tracking update'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class TrackingView:
    """Class-based view wrapper for tracking functionality"""
    
    @staticmethod
    def get_tracking_info(request, tracking_number):
        return get_tracking_info(request, tracking_number)
    
    @staticmethod
    def webhook_update(request):
        return webhook_tracking_update(request)
views/estimation_view.py
Copy
import time
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from ..models import AuditLog
from ..serializers.courier_serializers import EstimationRequestSerializer
from ..services.estimation_service import EstimationService
from ..middleware.tenant_middleware import get_current_tenant

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def estimate_shipment_cost(request):
    """
    Estimate cost and delivery time for a shipment.
    
    POST /api/courier/estimate/
    
    Expected payload:
    {
        "origin_address": "123 Main St, Cape Town",
        "destination_address": "456 High St, Johannesburg", 
        "weight_kg": 2.5,
        "declared_value": 1000.00,
        "urgency": "high",
        "courier_company_id": "uuid" (optional)
    }
    """
    start_time = time.time()
    tenant_id = get_current_tenant(request)
    
    try:
        # Validate request data
        serializer = EstimationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            AuditLog.log_action(
                action='estimation_validation_failed',
                user=request.user,
                tenant_id=tenant_id,
                details={'errors': serializer.errors},
                request_data=request.data,
                success=False,
                user_ip=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            return Response(
                {'error': 'Invalid request data', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        
        # Use estimation service to calculate costs
        estimation_service = EstimationService(tenant_id)
        estimates = estimation_service.calculate_estimates(**validated_data)
        
        execution_time = int((time.time() - start_time) * 1000)
        
        # Log successful estimation
        AuditLog.log_action(
            action='shipment_estimation_calculated',
            user=request.user,
            tenant_id=tenant_id,
            request_data=request.data,
            response_data=estimates,
            execution_time_ms=execution_time,
            user_ip=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        return Response(
            {
                'success': True,
                'estimates': estimates,
                'calculation_time_ms': execution_time
            },
            status=status.HTTP_200_OK
        )
        
    except Exception as e:
        execution_time = int((time.time() - start_time) * 1000)
        
        # Log error
        AuditLog.log_action(
            action='shipment_estimation_failed',
            user=request.user,
            tenant_id=tenant_id,
            request_data=request.data,
            success=False,
            error_message=str(e),
            execution_time_ms=execution_time,
            user_ip=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        return Response(
            {'error': 'Failed to calculate estimates', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class EstimationView:
    """Class-based view wrapper for estimation functionality"""
    
    @staticmethod
    def estimate(request):
        return estimate_shipment_cost(request)

# Bidbeez Courier Module - Complete Backend Implementation (Continued)

## 3. Services Layer

### services/__init__.py
```python
from .dispatcher import Dispatcher
from .tracking_service import TrackingService
from .estimation_service import EstimationService
from .sla_monitoring_service import SLAMonitoringService
from .route_optimization_service import RouteOptimizationService
from .notification_service import NotificationService
from .webhook_service import WebhookService

__all__ = [
    'Dispatcher',
    'TrackingService',
    'EstimationService', 
    'SLAMonitoringService',
    'RouteOptimizationService',
    'NotificationService',
    'WebhookService'
]
```

### services/dispatcher.py
```python
import uuid
from django.utils import timezone
from django.db import transaction
from django.contrib.gis.geos import Point

from ..models import CourierRequest, CourierCompany, AuditLog
from ..utils.courier_utils import CourierUtils
from ..utils.mapping_utils import MappingUtils
from ..events.courier_request_created_event import CourierRequestCreatedEvent

class Dispatcher:
    """
    Service for dispatching courier shipments.
    Handles shipment creation, validation, and initial processing.
    """
    
    def __init__(self, tenant_id):
        self.tenant_id = tenant_id
        self.mapping_utils = MappingUtils()
        
    def dispatch_shipment(self, user, courier_company, origin_address, 
                         destination_address, weight_kg, declared_value=0.00,
                         urgency='medium', special_instructions='', **kwargs):
        """
        Create and dispatch a new courier shipment request.
        
        Args:
            user: User creating the request
            courier_company: CourierCompany instance
            origin_address: Source address string
            destination_address: Destination address string
            weight_kg: Package weight in kilograms
            declared_value: Declared value for insurance
            urgency: Priority level (low, medium, high)
            special_instructions: Additional delivery instructions
            
        Returns:
            CourierRequest: Created courier request instance
        """
        
        with transaction.atomic():
            # Generate tracking number
            tracking_number = CourierUtils.generate_tracking_number()
            
            # Geocode addresses
            origin_coords = self.mapping_utils.geocode_address(origin_address)
            destination_coords = self.mapping_utils.geocode_address(destination_address)
            
            # Create location points if geocoding successful
            location_origin = None
            location_destination = None
            
            if origin_coords:
                location_origin = Point(
                    origin_coords['longitude'], 
                    origin_coords['latitude']
                )
                
            if destination_coords:
                location_destination = Point(
                    destination_coords['longitude'], 
                    destination_coords['latitude']
                )
            
            # Calculate estimated delivery date
            estimated_delivery_date = self._calculate_estimated_delivery(
                courier_company, origin_coords, destination_coords, urgency
            )
            
            # Create courier request
            courier_request = CourierRequest.objects.create(
                tenant_id=self.tenant_id,
                created_by=user,
                courier_company=courier_company,
                origin_address=origin_address,
                destination_address=destination_address,
                location_origin=location_origin,
                location_destination=location_destination,
                weight_kg=weight_kg,
                declared_value=declared_value,
                urgency=urgency,
                tracking_number=tracking_number,
                expected_delivery_date=estimated_delivery_date,
                special_instructions=special_instructions,
                status='pending'
            )
            
            # Calculate estimated cost
            estimated_cost = self._calculate_estimated_cost(
                courier_company, weight_kg, declared_value, urgency,
                origin_coords, destination_coords
            )
            
            if estimated_cost:
                courier_request.estimated_cost = estimated_cost
                courier_request.save(update_fields=['estimated_cost'])
            
            # Log dispatch action
            AuditLog.log_action(
                action='courier_request_dispatched',
                user=user,
                tenant_id=self.tenant_id,
                content_object=courier_request,
                details={
                    'tracking_number': tracking_number,
                    'courier_company': courier_company.name,
                    'estimated_cost': float(estimated_cost) if estimated_cost else None,
                    'urgency': urgency
                }
            )
            
            # Trigger created event for notifications
            CourierRequestCreatedEvent.trigger(courier_request)
            
            # Attempt to send to courier API if integration exists
            self._attempt_courier_api_dispatch(courier_request)
            
            return courier_request
    
    def _calculate_estimated_delivery(self, courier_company, origin_coords, 
                                    destination_coords, urgency):
        """Calculate estimated delivery date based on various factors"""
        from datetime import timedelta
        
        base_hours = 24  # Default 24 hours
        
        # Adjust based on urgency
        urgency_adjustments = {
            'low': 48,
            'medium': 24,
            'high': 12
        }
        base_hours = urgency_adjustments.get(urgency, 24)
        
        # Adjust based on distance if coordinates available
        if origin_coords and destination_coords:
            distance_km = self.mapping_utils.calculate_distance(
                origin_coords, destination_coords
            )
            
            if distance_km > 500:  # Long distance
                base_hours += 24
            elif distance_km > 100:  # Medium distance
                base_hours += 12
        
        # Adjust based on courier reliability
        if courier_company.reliability_score < 70:
            base_hours += 12  # Add buffer for less reliable couriers
        
        return timezone.now() + timedelta(hours=base_hours)
    
    def _calculate_estimated_cost(self, courier_company, weight_kg, declared_value, 
                                urgency, origin_coords, destination_coords):
        """Calculate estimated cost for the shipment"""
        from ..services.estimation_service import EstimationService
        
        try:
            estimation_service = EstimationService(self.tenant_id)
            estimates = estimation_service.calculate_estimates(
                origin_address="",  # We have coords
                destination_address="",
                weight_kg=weight_kg,
                declared_value=declared_value,
                urgency=urgency,
                courier_company_id=courier_company.id,
                origin_coords=origin_coords,
                destination_coords=destination_coords
            )
            
            # Return the estimate for the specified courier
            for estimate in estimates:
                if estimate['courier_id'] == str(courier_company.id):
                    return estimate['total_cost']
                    
        except Exception as e:
            # Log error but don't fail dispatch
            AuditLog.log_action(
                action='cost_estimation_failed',
                tenant_id=self.tenant_id,
                details={'error': str(e)},
                success=False
            )
        
        return None
    
    def _attempt_courier_api_dispatch(self, courier_request):
        """Attempt to send dispatch request to courier API"""
        try:
            integration = courier_request.courier_company.get_active_integrations().first()
            
            if integration and integration.supports_dispatch:
                from ..services.webhook_service import WebhookService
                webhook_service = WebhookService(integration)
                
                # Schedule async dispatch to courier API
                from ..tasks.celery_tasks import dispatch_to_courier_api
                dispatch_to_courier_api.delay(
                    courier_request.id, 
                    integration.id
                )
                
        except Exception as e:
            # Log but don't fail the main dispatch
            AuditLog.log_action(
                action='courier_api_dispatch_failed',
                tenant_id=self.tenant_id,
                content_object=courier_request,
                details={'error': str(e)},
                success=False
            )
```

### services/tracking_service.py
```python
from django.utils import timezone
from django.contrib.gis.geos import Point
from django.db import transaction

from ..models import CourierRequest, CourierTrackingUpdate, AuditLog
from ..utils.mapping_utils import MappingUtils
from ..events.tracking_update_event import TrackingUpdateEvent

class TrackingService:
    """
    Service for managing courier tracking operations.
    Handles tracking updates, location processing, and status management.
    """
    
    def __init__(self, tenant_id):
        self.tenant_id = tenant_id
        self.mapping_utils = MappingUtils()
    
    def get_tracking_info(self, courier_request):
        """
        Get comprehensive tracking information for a courier request.
        
        Args:
            courier_request: CourierRequest instance
            
        Returns:
            dict: Tracking information with updates and map data
        """
        
        # Get all tracking updates
        tracking_updates = courier_request.tracking_updates.all()
        
        # Prepare tracking data
        tracking_data = {
            'tracking_number': courier_request.tracking_number,
            'status': courier_request.status,
            'origin_address': courier_request.origin_address,
            'destination_address': courier_request.destination_address,
            'expected_delivery_date': courier_request.expected_delivery_date,
            'actual_delivery_date': courier_request.actual_delivery_date,
            'courier_company': {
                'name': courier_request.courier_company.name,
                'reliability_score': courier_request.courier_company.reliability_score
            } if courier_request.courier_company else None,
            'package_info': {
                'weight_kg': courier_request.weight_kg,
                'declared_value': courier_request.declared_value,
                'urgency': courier_request.urgency
            },
            'tracking_updates': [],
            'map_data': None,
            'delivery_progress': self._calculate_delivery_progress(courier_request),
            'is_overdue': courier_request.is_overdue()
        }
        
        # Process tracking updates
        for update in tracking_updates:
            update_data = {
                'id': str(update.id),
                'status': update.status,
                'location': update.location,
                'timestamp': update.timestamp,
                'notes': update.notes,
                'coordinates': update.get_mapbox_coordinates(),
                'estimated_delivery_time': update.estimated_delivery_time
            }
            tracking_data['tracking_updates'].append(update_data)
        
        # Generate map data if we have coordinates
        if self._has_location_data(courier_request, tracking_updates):
            tracking_data['map_data'] = self._generate_map_data(
                courier_request, tracking_updates
            )
        
        return tracking_data
    
    def process_webhook_update(self, courier_request, update_data):
        """
        Process tracking update from webhook.
        
        Args:
            courier_request: CourierRequest instance
            update_data: Validated webhook data
            
        Returns:
            CourierTrackingUpdate: Created tracking update
        """
        
        with transaction.atomic():
            # Create position point if coordinates provided
            position = None
            if 'coordinates' in update_data:
                coords = update_data['coordinates']
                position = Point(coords['longitude'], coords['latitude'])
            
            # Create tracking update
            tracking_update = CourierTrackingUpdate.objects.create(
                tenant_id=self.tenant_id,
                courier_request=courier_request,
                status=update_data['status'],
                location=update_data.get('location', ''),
                position=position,
                estimated_delivery_time=update_data.get('estimated_delivery_time'),
                notes=update_data.get('notes', ''),
                courier_reference=update_data.get('courier_reference', ''),
                source='webhook'
            )
            
            # Update delivery date if delivered
            if update_data['status'] == 'delivered':
                courier_request.actual_delivery_date = timezone.now()
                courier_request.save(update_fields=['actual_delivery_date', 'updated_at'])
            
            # Log tracking update
            AuditLog.log_action(
                action='tracking_update_processed',
                tenant_id=self.tenant_id,
                content_object=tracking_update,
                details={
                    'tracking_number': courier_request.tracking_number,
                    'new_status': update_data['status'],
                    'location': update_data.get('location', ''),
                    'source': 'webhook'
                }
            )
            
            # Trigger tracking update event
            TrackingUpdateEvent.trigger(tracking_update)
            
            return tracking_update
    
    def create_manual_update(self, courier_request, status, location='', 
                           coordinates=None, notes='', user=None):
        """
        Create manual tracking update (admin/system generated).
        
        Args:
            courier_request: CourierRequest instance
            status: New status
            location: Human-readable location
            coordinates: Dict with latitude/longitude
            notes: Additional notes
            user: User creating the update
            
        Returns:
            CourierTrackingUpdate: Created tracking update
        """
        
        position = None
        if coordinates:
            position = Point(coordinates['longitude'], coordinates['latitude'])
        
        with transaction.atomic():
            tracking_update = CourierTrackingUpdate.objects.create(
                tenant_id=self.tenant_id,
                courier_request=courier_request,
                status=status,
                location=location,
                position=position,
                notes=notes,
                source='manual'
            )
            
            # Log manual update
            AuditLog.log_action(
                action='manual_tracking_update_created',
                user=user,
                tenant_id=self.tenant_id,
                content_object=tracking_update,
                details={
                    'tracking_number': courier_request.tracking_number,
                    'status': status,
                    'location': location
                }
            )
            
            # Trigger event
            TrackingUpdateEvent.trigger(tracking_update)
            
            return tracking_update
    
    def _calculate_delivery_progress(self, courier_request):
        """Calculate delivery progress percentage"""
        status_progress = {
            'pending': 0,
            'picked_up': 20,
            'in_transit': 60,
            'out_for_delivery': 80,
            'delivered': 100,
            'failed': 0,
            'returned': 0
        }
        
        return status_progress.get(courier_request.status, 0)
    
    def _has_location_data(self, courier_request, tracking_updates):
        """Check if we have sufficient location data for mapping"""
        return (
            courier_request.location_origin or 
            courier_request.location_destination or
            any(update.position for update in tracking_updates)
        )
    
    def _generate_map_data(self, courier_request, tracking_updates):
        """Generate Mapbox-compatible map data"""
        map_data = {
            'type': 'FeatureCollection',
            'features': []
        }
        
        # Add origin point
        if courier_request.location_origin:
            origin_feature = {
                'type': 'Feature',
                'geometry': {
                    'type': 'Point',
                    'coordinates': [
                        courier_request.location_origin.x,
                        courier_request.location_origin.y
                    ]
                },
                'properties': {
                    'type': 'origin',
                    'address': courier_request.origin_address,
                    'icon': 'warehouse'
                }
            }
            map_data['features'].append(origin_feature)
        
        # Add destination point
        if courier_request.location_destination:
            destination_feature = {
                'type': 'Feature',
                'geometry': {
                    'type': 'Point',
                    'coordinates': [
                        courier_request.location_destination.x,
                        courier_request.location_destination.y
                    ]
                },
                'properties': {
                    'type': 'destination',
                    'address': courier_request.destination_address,
                    'icon': 'marker'
                }
            }
            map_data['features'].append(destination_feature)
        
        # Add tracking points
        for update in tracking_updates:
            if update.position:
                tracking_feature = {
                    'type': 'Feature',
                    'geometry': {
                        'type': 'Point',
                        'coordinates': [update.position.x, update.position.y]
                    },
                    'properties': {
                        'type': 'tracking_point',
                        'status': update.status,
                        'timestamp': update.timestamp.isoformat(),
                        'location': update.location,
                        'icon': self._get_status_icon(update.status)
                    }
                }
                map_data['features'].append(tracking_feature)
        
        # Add route if we have origin and destination
        if (courier_request.location_origin and 
            courier_request.location_destination):
            try:
                route = self.mapping_utils.get_route(
                    {
                        'latitude': courier_request.location_origin.y,
                        'longitude': courier_request.location_origin.x
                    },
                    {
                        'latitude': courier_request.location_destination.y,
                        'longitude': courier_request.location_destination.x
                    }
                )
                
                if route:
                    route_feature = {
                        'type': 'Feature',
                        'geometry': route['geometry'],
                        'properties': {
                            'type': 'route',
                            'distance': route['distance'],
                            'duration': route['duration']
                        }
                    }
                    map_data['features'].append(route_feature)
                    
            except Exception as e:
                # Log error but don't fail
                AuditLog.log_action(
                    action='route_generation_failed',
                    tenant_id=self.tenant_id,
                    content_object=courier_request,
                    details={'error': str(e)},
                    success=False
                )
        
        return map_data
    
    def _get_status_icon(self, status):
        """Get Mapbox icon for tracking status"""
        status_icons = {
            'pending': 'circle',
            'picked_up': 'warehouse',
            'in_transit': 'car',
            'out_for_delivery': 'bicycle',
            'delivered': 'marker',
            'failed': 'cross',
            'returned': 'arrow-left'
        }
        return status_icons.get(status, 'circle')
```

### services/estimation_service.py
```python
from decimal import Decimal
from django.utils import timezone
from django.db.models import Q

from ..models import CourierCompany, CourierRate, AuditLog
from ..utils.mapping_utils import MappingUtils
from ..utils.insurance_calculator import InsuranceCalculator

class EstimationService:
    """
    Service for calculating shipment cost and delivery time estimates.
    Supports multiple couriers and dynamic pricing.
    """
    
    def __init__(self, tenant_id):
        self.tenant_id = tenant_id
        self.mapping_utils = MappingUtils()
        self.insurance_calculator = InsuranceCalculator()
    
    def calculate_estimates(self, origin_address, destination_address, weight_kg,
                          declared_value=0, urgency='medium', courier_company_id=None,
                          origin_coords=None, destination_coords=None):
        """
        Calculate cost and delivery estimates for shipment.
        
        Args:
            origin_address: Source address
            destination_address: Destination address
            weight_kg: Package weight
            declared_value: Declared value for insurance
            urgency: Priority level
            courier_company_id: Specific courier (optional)
            origin_coords: Pre-geocoded origin coordinates (optional)
            destination_coords: Pre-geocoded destination coordinates (optional)
            
        Returns:
            list: List of estimates for available couriers
        """
        
        estimates = []
        
        # Geocode addresses if coordinates not provided
        if not origin_coords and origin_address:
            origin_coords = self.mapping_utils.geocode_address(origin_address)
        
        if not destination_coords and destination_address:
            destination_coords = self.mapping_utils.geocode_address(destination_address)
        
        # Calculate distance if coordinates available
        distance_km = None
        if origin_coords and destination_coords:
            distance_km = self.mapping_utils.calculate_distance(
                origin_coords, destination_coords
            )
        
        # Get available couriers
        couriers_query = CourierCompany.objects.filter(
            tenant_id=self.tenant_id,
            is_blacklisted=False
        )
        
        if courier_company_id:
            couriers_query = couriers_query.filter(id=courier_company_id)
        
        couriers = couriers_query.all()
        
        for courier in couriers:
            try:
                estimate = self._calculate_courier_estimate(
                    courier, weight_kg, declared_value, urgency,
                    distance_km, origin_coords, destination_coords
                )
                
                if estimate:
                    estimates.append(estimate)
                    
            except Exception as e:
                # Log error for this courier but continue with others
                AuditLog.log_action(
                    action='courier_estimation_failed',
                    tenant_id=self.tenant_id,
                    details={
                        'courier_id': str(courier.id),
                        'courier_name': courier.name,
                        'error': str(e)
                    },
                    success=False
                )
        
        # Sort estimates by total cost
        estimates.sort(key=lambda x: x['total_cost'])
        
        return estimates
    
    def _calculate_courier_estimate(self, courier, weight_kg, declared_value, 
                                  urgency, distance_km, origin_coords, destination_coords):
        """Calculate estimate for a specific courier"""
        
        # Find applicable rate
        rate = self._find_applicable_rate(courier, weight_kg, urgency)
        
        if not rate:
            # Use fallback calculation if no rate found
            return self._calculate_fallback_estimate(
                courier, weight_kg, declared_value, urgency, distance_km
            )
        
        # Calculate base cost using rate
        total_cost = rate.calculate_total_cost(weight_kg, urgency, declared_value)
        
        # Add distance-based adjustments
        if distance_km:
            distance_multiplier = self._get_distance_multiplier(distance_km)
            total_cost *= distance_multiplier
        
        # Calculate insurance cost
        insurance_cost = self.insurance_calculator.calculate_premium(
            declared_value, courier.insurance_cap
        )
        total_cost += insurance_cost
        
        # Calculate delivery time estimate
        delivery_estimate = self._calculate_delivery_time(
            courier, urgency, distance_km
        )
        
        # Add courier-specific adjustments
        reliability_adjustment = self._get_reliability_adjustment(courier)
        total_cost *= reliability_adjustment
        
        estimate = {
            'courier_id': str(courier.id),
            'courier_name': courier.name,
            'service_level': rate.service_level,
            'base_cost': float(rate.base_price),
            'weight_cost': float(rate.price_per_additional_kg * max(0, weight_kg - 1)),
            'urgency_surcharge': float(rate.surcharge_urgent if urgency == 'high' else 0),
            'insurance_cost': float(insurance_cost),
            'distance_adjustment': float(total_cost - rate.calculate_total_cost(weight_kg, urgency, declared_value) - insurance_cost),
            'total_cost': round(float(total_cost), 2),
            'currency': rate.currency,
            'estimated_delivery_hours': delivery_estimate['hours'],
            'estimated_delivery_date': delivery_estimate['date'].isoformat(),
            'reliability_score': courier.reliability_score,
            'distance_km': distance_km,
            'route_info': self._get_route_info(origin_coords, destination_coords) if origin_coords and destination_coords else None
        }
        
        return estimate
    
    def _find_applicable_rate(self, courier, weight_kg, urgency):
        """Find the most applicable rate for given parameters"""
        
        now = timezone.now()
        
        # Determine weight class
        weight_class = self._determine_weight_class(weight_kg)
        
        # Determine service level based on urgency
        service_level_map = {
            'low': 'standard',
            'medium': 'standard',
            'high': 'express'
        }
        service_level = service_level_map.get(urgency, 'standard')
        
        # Find matching rate
        rate = CourierRate.objects.filter(
            courier=courier,
            weight_class=weight_class,
            service_level=service_level,
            effective_from__lte=now,
            effective_to__gte=now
        ).first()
        
        # Fallback to any available rate for this courier
        if not rate:
            rate = CourierRate.objects.filter(
                courier=courier,
                effective_from__lte=now,
                effective_to__gte=now
            ).first()
        
        return rate
    
    def _determine_weight_class(self, weight_kg):
        """Determine weight class for rate lookup"""
        if weight_kg <= 1:
            return '<1kg'
        elif weight_kg <= 2:
            return '1-2kg'
        elif weight_kg <= 5:
            return '2-5kg'
        elif weight_kg <= 10:
            return '5-10kg'
        else:
            return '>10kg'
    
    def _get_distance_multiplier(self, distance_km):
        """Get distance-based cost multiplier"""
        if distance_km <= 50:
            return 1.0
        elif distance_km <= 200:
            return 1.1
        elif distance_km <= 500:
            return 1.2
        else:
            return 1.3
    
    def _calculate_delivery_time(self, courier, urgency, distance_km):
        """Calculate estimated delivery time"""
        from datetime import timedelta
        
        # Base delivery times by urgency
        base_hours = {
            'low': 48,
            'medium': 24,
            'high': 12
        }
        
        hours = base_hours.get(urgency, 24)
        
        # Adjust for distance
        if distance_km:
            if distance_km > 500:
                hours += 24
            elif distance_km > 200:
                hours += 12
            elif distance_km > 100:
                hours += 6
        
        # Adjust for courier reliability
        if courier.reliability_score < 70:
            hours += 12
        elif courier.reliability_score > 90:
            hours -= 6
        
        # Minimum 2 hours
        hours = max(2, hours)
        
        delivery_date = timezone.now() + timedelta(hours=hours)
        
        return {
            'hours': hours,
            'date': delivery_date
        }
    
    def _get_reliability_adjustment(self, courier):
        """Get cost adjustment based on courier reliability"""
        if courier.reliability_score >= 90:
            return 1.05  # Premium for high reliability
        elif courier.reliability_score >= 80:
            return 1.0
        elif courier.reliability_score >= 70:
            return 0.95
        else:
            return 0.9  # Discount for lower reliability
    
    def _calculate_fallback_estimate(self, courier, weight_kg, declared_value, 
                                   urgency, distance_km):
        """Fallback calculation when no rates are available"""
        
        # Base cost calculation
        base_cost = Decimal('50.00')  # Base ZAR 50
        
        # Weight-based cost
        weight_cost = Decimal(str(weight_kg)) * Decimal('15.00')  # ZAR 15 per kg
        
        # Distance-based cost
        distance_cost = Decimal('0.00')
        if distance_km:
            distance_cost = Decimal(str(distance_km)) * Decimal('2.50')  # ZAR 2.50 per km
        
        # Urgency surcharge
        urgency_surcharge = Decimal('0.00')
        if urgency == 'high':
            urgency_surcharge = Decimal('50.00')
        
        # Insurance
        insurance_cost = self.insurance_calculator.calculate_premium(
            declared_value, courier.insurance_cap
        )
        
        total_cost = base_cost + weight_cost + distance_cost + urgency_surcharge + insurance_cost
        
        # Delivery time
        delivery_estimate = self._calculate_delivery_time(courier, urgency, distance_km)
        
        return {
            'courier_id': str(courier.id),
            'courier_name': courier.name,
            'service_level': 'standard',
            'base_cost': float(base_cost),
            'weight_cost': float(weight_cost),
            'distance_cost': float(distance_cost),
            'urgency_surcharge': float(urgency_surcharge),
            'insurance_cost': float(insurance_cost),
            'total_cost': float(total_cost),
            'currency': 'ZAR',
            'estimated_delivery_hours': delivery_estimate['hours'],
            'estimated_delivery_date': delivery_estimate['date'].isoformat(),
            'reliability_score': courier.reliability_score,
            'distance_km': distance_km,
            'note': 'Estimate calculated using fallback rates'
        }
    
    def _get_route_info(self, origin_coords, destination_coords):
        """Get route information from mapping service"""
        try:
            route = self.mapping_utils.get_route(origin_coords, destination_coords)
            if route:
                return {
                    'distance_km': route['distance'],
                    'duration_minutes': route['duration'],
                    'route_geometry': route['geometry']
                }
        except Exception:
            pass
        
        return None
```

### services/sla_monitoring_service.py
```python
from django.utils import timezone
from django.db.models import Q
from datetime import timedelta

from ..models import CourierRequest, AuditLog
from ..tasks.celery_tasks import send_sla_violation_notification

class SLAMonitoringService:
    """
    Service for monitoring Service Level Agreement compliance.
    Tracks delivery times, identifies violations, and triggers alerts.
    """
    
    def __init__(self, tenant_id=None):
        self.tenant_id = tenant_id
    
    def check_sla_violations(self):
        """
        Check for SLA violations across all active requests.
        
        Returns:
            dict: Summary of violations found
        """
        
        now = timezone.now()
        violations = {
            'overdue_deliveries': [],
            'at_risk_deliveries': [],
            'summary': {
                'total_overdue': 0,
                'total_at_risk': 0,
                'total_checked': 0
            }
        }
        
        # Build query
        query = Q(
            status__in=['pending', 'picked_up', 'in_transit', 'out_for_delivery'],
            expected_delivery_date__isnull=False
        )
        
        if self.tenant_id:
            query &= Q(tenant_id=self.tenant_id)
        
        active_requests = CourierRequest.objects.filter(query)
        violations['summary']['total_checked'] = active_requests.count()
        
        for request in active_requests:
            violation_info = self._check_request_sla(request, now)
            
            if violation_info['is_overdue']:
                violations['overdue_deliveries'].append(violation_info)
                violations['summary']['total_overdue'] += 1
                
                # Trigger notification for overdue
                self._handle_overdue_violation(request, violation_info)
                
            elif violation_info['is_at_risk']:
                violations['at_risk_deliveries'].append(violation_info)
                violations['summary']['total_at_risk'] += 1
                
                # Trigger notification for at-risk
                self._handle_at_risk_violation(request, violation_info)
        
        # Log monitoring activity
        AuditLog.log_action(
            action='sla_monitoring_completed',
            tenant_id=self.tenant_id,
            details=violations['summary']
        )
        
        return violations
    
    def _check_request_sla(self, request, current_time):
        """Check SLA status for a single request"""
        
        expected_delivery = request.expected_delivery_date
        hours_overdue = 0
        hours_until_due = 0
        
        if current_time > expected_delivery:
            # Overdue
            time_diff = current_time - expected_delivery
            hours_overdue = time_diff.total_seconds() / 3600
            is_overdue = True
            is_at_risk = False
        else:
            # Not yet due
            time_diff = expected_delivery - current_time
            hours_until_due = time_diff.total_seconds() / 3600
            is_overdue = False
            # At risk if less than 4 hours remaining
            is_at_risk = hours_until_due <= 4
        
        # Calculate severity
        severity = self._calculate_violation_severity(hours_overdue, hours_until_due)
        
        return {
            'request_id': str(request.id),
            'tracking_number': request.tracking_number,
            'status': request.status,
            'expected_delivery_date': expected_delivery,
            'current_time': current_time,
            'is_overdue': is_overdue,
            'is_at_risk': is_at_risk,
            'hours_overdue': round(hours_overdue, 2),
            'hours_until_due': round(hours_until_due, 2),
            'severity': severity,
            'courier_company': request.courier_company.name if request.courier_company else None,
            'origin_address': request.origin_address,
            'destination_address': request.destination_address,
            'created_by': request.created_by.username if request.created_by else None
        }
    
    def _calculate_violation_severity(self, hours_overdue, hours_until_due):
        """Calculate violation severity level"""
        
        if hours_overdue > 24:
            return 'critical'
        elif hours_overdue > 12:
            return 'high'
        elif hours_overdue > 4:
            return 'medium'
        elif hours_overdue > 0:
            return 'low'
        elif hours_until_due <= 2:
            return 'urgent'
        elif hours_until_due <= 4:
            return 'warning'
        else:
            return 'normal'
    
    def _handle_overdue_violation(self, request, violation_info):
        """Handle overdue delivery violation"""
        
        # Log the violation
        AuditLog.log_action(
            action='sla_violation_overdue',
            tenant_id=request.tenant_id,
            content_object=request,
            details=violation_info
        )
        
        # Schedule notification
        send_sla_violation_notification.delay(
            request.id,
            'overdue',
            violation_info
        )
        
        # Update courier reliability score if severely overdue
        if violation_info['hours_overdue'] > 24 and request.courier_company:
            self._penalize_courier_reliability(
                request.courier_company, 
                violation_info['hours_overdue']
            )
    
    def _handle_at_risk_violation(self, request, violation_info):
        """Handle at-risk delivery warning"""
        
        # Log the at-risk status
        AuditLog.log_action(
            action='sla_violation_at_risk',
            tenant_id=request.tenant_id,
            content_object=request,
            details=violation_info
        )
        
        # Schedule notification (less urgent)
        send_sla_violation_notification.delay(
            request.id,
            'at_risk',
            violation_info
        )
    
    def _penalize_courier_reliability(self, courier_company, hours_overdue):
        """Reduce courier reliability score for severe violations"""
        
        penalty = min(5.0, hours_overdue / 12)  # Max 5 point penalty
        new_score = max(0, courier_company.reliability_score - penalty)
        
        courier_company.reliability_score = new_score
        courier_company.save(update_fields=['reliability_score', 'updated_at'])
        
        AuditLog.log_action(
            action='courier_reliability_penalized',
            tenant_id=courier_company.tenant_id,
            content_object=courier_company,
            details={
                'old_score': courier_company.reliability_score + penalty,
                'new_score': new_score,
                'penalty': penalty,
                'hours_overdue': hours_overdue
            }
        )
    
    def get_sla_performance_report(self, days_back=30):
        """
        Generate SLA performance report for the specified period.
        
        Args:
            days_back: Number of days to analyze
            
        Returns:
            dict: Performance report with metrics
        """
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days_back)
        
        # Build query
        query = Q(
            created_at__gte=start_date,
            created_at__lte=end_date,
            status__in=['delivered', 'failed']
        )
        
        if self.tenant_id:
            query &= Q(tenant_id=self.tenant_id)
        
        completed_requests = CourierRequest.objects.filter(query)
        
        total_requests = completed_requests.count()
        on_time_requests = completed_requests.filter(
            actual_delivery_date__lte=F('expected_delivery_date')
        ).count()
        
        late_requests = total_requests - on_time_requests
        
        # Calculate performance by courier
        courier_performance = {}
        for request in completed_requests:
            if request.courier_company:
                courier_name = request.courier_company.name
                if courier_name not in courier_performance:
                    courier_performance[courier_name] = {
                        'total': 0,
                        'on_time': 0,
                        'late': 0
                    }
                
                courier_performance[courier_name]['total'] += 1
                
                if (request.actual_delivery_date and 
                    request.actual_delivery_date <= request.expected_delivery_date):
                    courier_performance[courier_name]['on_time'] += 1
                else:
                    courier_performance[courier_name]['late'] += 1
        
        # Calculate percentages
        for courier_data in courier_performance.values():
            if courier_data['total'] > 0:
                courier_data['on_time_percentage'] = (
                    courier_data['on_time'] / courier_data['total'] * 100
                )
        
        report = {
            'period': {
                'start_date': start_date,
                'end_date': end_date,
                'days': days_back
            },
            'overall_performance': {
                'total_requests': total_requests,
                'on_time_requests': on_time_requests,
                'late_requests': late_requests,
                'on_time_percentage': (on_time_requests / total_requests * 100) if total_requests > 0 else 0
            },
            'courier_performance': courier_performance
        }
        
        return report
```

### services/notification_service.py
```python
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone

from ..models import AuditLog
from ..utils.mapping_utils import MappingUtils

class NotificationService:
    """
    Service for sending notifications related to courier operations.
    Handles email notifications, SMS (future), and in-app notifications.
    """
    
    def __init__(self, tenant_id=None):
        self.tenant_id = tenant_id
        self.mapping_utils = MappingUtils()
    
    def send_dispatch_confirmation(self, courier_request):
        """Send confirmation email when shipment is dispatched"""
        
        if not courier_request.created_by or not courier_request.created_by.email:
            return False
        
        try:
            # Generate static map if location data available
            map_url = None
            if courier_request.location_origin and courier_request.location_destination:
                map_url = self.mapping_utils.generate_static_map_url(
                    origin={
                        'latitude': courier_request.location_origin.y,
                        'longitude': courier_request.location_origin.x
                    },
                    destination={
                        'latitude': courier_request.location_destination.y,
                        'longitude': courier_request.location_destination.x
                    }
                )
            
            context = {
                'tracking_number': courier_request.tracking_number,
                'origin_address': courier_request.origin_address,
                'destination_address': courier_request.destination_address,
                'expected_delivery_date': courier_request.expected_delivery_date,
                'courier_company': courier_request.courier_company.name if courier_request.courier_company else 'TBD',
                'estimated_cost': courier_request.estimated_cost,
                'weight_kg': courier_request.weight_kg,
                'urgency': courier_request.urgency,
                'map_url': map_url,
                'tracking_url': f"{settings.FRONTEND_BASE_URL}/tracking/{courier_request.tracking_number}"
            }
            
            subject = f"Shipment Dispatched - {courier_request.tracking_number}"
            
            # Render email templates
            html_message = render_to_string('courier/emails/dispatch_confirmation.html', context)
            text_message = render_to_string('courier/emails/dispatch_confirmation.txt', context)
            
            send_mail(
                subject=subject,
                message=text_message,
                html_message=html_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[courier_request.created_by.email],
                fail_silently=False
            )
            
            # Log notification
            AuditLog.log_action(
                action='dispatch_confirmation_sent',
                user=courier_request.created_by,
                tenant_id=self.tenant_id,
                content_object=courier_request,
                details={
                    'recipient': courier_request.created_by.email,
                    'tracking_number': courier_request.tracking_number
                }
            )
            
            return True
            
        except Exception as e:
            # Log error
            AuditLog.log_action(
                action='dispatch_confirmation_failed',
                user=courier_request.created_by,
                tenant_id=self.tenant_id,
                content_object=courier_request,
                details={'error': str(e)},
                success=False
            )
            return False
    
    def send_tracking_update(self, tracking_update):
        """Send notification when tracking status is updated"""
        
        courier_request = tracking_update.courier_request
        
        if not courier_request.created_by or not courier_request.created_by.email:
            return False
        
        # Only send notifications for significant status changes
        significant_statuses = ['picked_up', 'out_for_delivery', 'delivered', 'failed']
        if tracking_update.status not in significant_statuses:
            return False
        
        try:
            # Generate map with current location if available
            map_url = None
            if tracking_update.position:
                map_url = self.mapping_utils.generate_static_map_url(
                    current_location={
                        'latitude': tracking_update.position.y,
                        'longitude': tracking_update.position.x
                    },
                    destination={
                        'latitude': courier_request.location_destination.y,
                        'longitude': courier_request.location_destination.x
                    } if courier_request.location_destination else None
                )
            
            context = {
                'tracking_number': courier_request.tracking_number,
                'status': tracking_update.status,
                'status_display': tracking_update.get_status_display(),
                'location': tracking_update.location,
                'timestamp': tracking_update.timestamp,
                'estimated_delivery_time': tracking_update.estimated_delivery_time,
                'notes': tracking_update.notes,
                'origin_address': courier_request.origin_address,
                'destination_address': courier_request.destination_address,
                'map_url': map_url,
                'tracking_url': f"{settings.FRONTEND_BASE_URL}/tracking/{courier_request.tracking_number}"
            }
            
            subject = f"Tracking Update - {courier_request.tracking_number} - {tracking_update.get_status_display()}"
            
            # Render email templates
            html_message = render_to_string('courier/emails/tracking_update.html', context)
            text_message = render_to_string('courier/emails/tracking_update.txt', context)
            
            send_mail(
                subject=subject,
                message=text_message,
                html_message=html_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[courier_request.created_by.email],
                fail_silently=False
            )
            
            # Log notification
            AuditLog.log_action(
                action='tracking_update_notification_sent',
                user=courier_request.created_by,
                tenant_id=self.tenant_id,
                content_object=tracking_update,
                details={
                    'recipient': courier_request.created_by.email,
                    'tracking_number': courier_request.tracking_number,
                    'status': tracking_update.status
                }
            )
            
            return True
            
        except Exception as e:
            # Log error
            AuditLog.log_action(
                action='tracking_update_notification_failed',
                user=courier_request.created_by,
                tenant_id=self.tenant_id,
                content_object=tracking_update,
                details={'error': str(e)},
                success=False
            )
            return False
    
    def send_sla_violation_alert(self, courier_request, violation_type, violation_info):
        """Send SLA violation alert to administrators"""
        
        # Get admin emails (this would come from settings or admin users)
        admin_emails = getattr(settings, 'COURIER_ADMIN_EMAILS', [])
        if not admin_emails:
            return False
        
        try:
            context = {
                'violation_type': violation_type,
                'violation_info': violation_info,
                'courier_request': courier_request,
                'tracking_url': f"{settings.FRONTEND_BASE_URL}/tracking/{courier_request.tracking_number}",
                'admin_url': f"{settings.FRONTEND_BASE_URL}/admin/courier/requests/{courier_request.id}"
            }
            
            subject = f"SLA Violation Alert - {courier_request.tracking_number} - {violation_type.upper()}"
            
            # Render email templates
            html_message = render_to_string('courier/emails/sla_violation_alert.html', context)
            text_message = render_to_string('courier/emails/sla_violation_alert.txt', context)
            
            send_mail(
                subject=subject,
                message=text_message,
                html_message=html_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=admin_emails,
                fail_silently=False
            )
            
            # Log notification
            AuditLog.log_action(
                action='sla_violation_alert_sent',
                tenant_id=self.tenant_id,
                content_object=courier_request,
                details={
                    'recipients': admin_emails,
                    'violation_type': violation_type,
                    'violation_severity': violation_info.get('severity')
                }
            )
            
            return True
            
        except Exception as e:
            # Log error
            AuditLog.log_action(
                action='sla_violation_alert_failed',
                tenant_id=self.tenant_id,
                content_object=courier_request,
                details={'error': str(e)},
                success=False
            )
            return False
    
    def send_delivery_confirmation(self, courier_request):
        """Send confirmation when package is delivered"""
        
        if not courier_request.created_by or not courier_request.created_by.email:
            return False
        
        try:
            # Calculate delivery performance
            on_time = (
                courier_request.actual_delivery_date <= 
                courier_request.expected_delivery_date
            ) if courier_request.actual_delivery_date and courier_request.expected_delivery_date else None
            
            context = {
                'tracking_number': courier_request.tracking_number,
                'delivery_date': courier_request.actual_delivery_date,
                'expected_delivery_date': courier_request.expected_delivery_date,
                'on_time': on_time,
                'origin_address': courier_request.origin_address,
                'destination_address': courier_request.destination_address,
                'courier_company': courier_request.courier_company.name if courier_request.courier_company else 'N/A',
                'final_cost': courier_request.final_cost,
                'delivery_notes': courier_request.delivery_notes
            }
            
            subject = f"Package Delivered - {courier_request.tracking_number}"
            
            # Render email templates
            html_message = render_to_string('courier/emails/delivery_confirmation.html', context)
            text_message = render_to_string('courier/emails/delivery_confirmation.txt', context)
            
            send_mail(
                subject=subject,
                message=text_message,
                html_message=html_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[courier_request.created_by.email],
                fail_silently=False
            )
            
            # Log notification
            AuditLog.log_action(
                action='delivery_confirmation_sent',
                user=courier_request.created_by,
                tenant_id=self.tenant_id,
                content_object=courier_request,
                details={
                    'recipient': courier_request.created_by.email,
                    'tracking_number': courier_request.tracking_number,
                    'on_time': on_time
                }
            )
            
            return True
            
        except Exception as e:
            # Log error
            AuditLog.log_action(
                action='delivery_confirmation_failed',
                user=courier_request.created_by,
                tenant_id=self.tenant_id,
                content_object=courier_request,
                details={'error': str(e)},
                success=False
            )
            return False
```

Let me continue with the remaining services and components in the next part. We still need to cover:

- RouteOptimizationService
- WebhookService  
- Utils layer
- Middleware
- Events
- Tasks
- Serializers
- Tests
- Configuration files

Should I continue with the next batch of components?)
