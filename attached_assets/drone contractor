	BidBees <<EMAIL>>
DRONE CONTRACTOR (GIG-WORKER)
prince sibanda <<EMAIL>>	<PERSON><PERSON>, May 27, 2025 at 2:00 PM
To: <EMAIL>, <EMAIL>
backend_biddbeez/
├── .env.example # Example environment variables
├── .gitignore
├── manage.py # Django management script
├── requirements.txt # Dependencies (e.g., django, psycopg2, boto3, djangorestframework, celery, redis, django-channels, djoser, drf-yasg)
├── Dockerfile # For containerization
├── docker-compose.yml # For local development setup
├── biddbeez/ # Main Django project
│ ├── __init__.py
│ ├── settings.py # Config: DB, AWS, APIs, CORS, Celery, Channels
│ ├── urls.py # Root URL routing (including API docs)
│ ├── wsgi.py # WSGI entry point
│ └── asgi.py # ASGI entry point (for Django Channels)
├── dronecontractor/ # Core module (consider renaming app for clarity if possible in future)
│ ├── __init__.py
│ ├── admin.py # Admin interface for new/updated models
│ ├── apps.py # App configuration
│ ├── consumers.py # Django Channels consumers (for WebSocket, real-time notifications)
│ ├── routing.py # Django Channels routing configuration
│ ├── migrations/ # Database migrations
│ ├── models/ # Database schema
│ │ ├── __init__.py
│ │ ├── user_related.py # User extensions (BidderProfile, GigWorker)
│ │ ├── skills_certs.py # Skill, Certification, Endorsement models
│ │ ├── categories.py # ServiceCategory, ServiceSubCategory models
│ │ ├── task_related.py # Task, HouseholdRequest, Assignment models
│ │ ├── proof_dispute.py # Proof, ProofAttachment, Dispute, DisputeEvidence models
│ │ ├── financial.py # FeeStructure, TransactionLog, EscrowLog models
│ │ ├── engagement.py # Feedback, Loyalty, Schedule, Package models
│ │ └── utility.py # ContractorPreferences, SignupBonus, Notification models
│ ├── views/ # API endpoints (Consider using ViewSets for brevity)
│ │ ├── __init__.py
│ │ ├── bidder_views.py # Bidder-facing APIs
│ │ ├── contractor_views.py # Contractor-facing APIs
│ │ ├── household_views.py # Household-facing APIs
│ │ ├── common_views.py # Common lookups (categories, skills)
│ │ └── compliance_views.py # Compliance APIs
│ ├── serializers/ # API serialization
│ │ ├── __init__.py
│ │ ├── bidder_serializers.py
│ │ ├── contractor_serializers.py
│ │ ├── household_serializers.py
│ │ └── common_serializers.py
│ ├── services/ # Business logic
│ │ ├── __init__.py
│ │ ├── geo_matching_service.py # Enhanced Geo-matching logic
│ │ ├── payment_service.py # Escrow, Kazang, payout logic
│ │ ├── proof_service.py # Proof validation and blockchain logic
│ │ ├── compliance_service.py # SACAA/PSIRA/HPCSA verification logic
│ │ ├── notification_service.py # Multi-channel notification logic (email, SMS, in-app, WhatsApp)
│ │ ├── analytics_service.py # Category-specific metrics and forecasts
│ │ └── dispute_service.py # Dispute resolution workflow
│ ├── tasks/ # Celery tasks for async operations
│ │ ├── __init__.py
│ │ ├── compliance_tasks.py # Async compliance checks
│ │ ├── notification_tasks.py # Async notifications
│ │ ├── blockchain_tasks.py # Async blockchain proof submission
│ │ └── data_processing_tasks.py # e.g., for analytics, report generation
│ ├── tests/ # Unit and integration tests
│ │ ├── __init__.py
│ │ ├── factories.py # Model factories for testing
│ │ ├── test_models/
│ │ ├── test_serializers/
│ │ ├── test_views/
│ │ └── test_services/
│ ├── search_indexes.py # For Haystack or similar search integration
│ └── urls.py # Module-specific URL routing
├── private_sector_app/ # Private sector-facing app (API wrapper/aggregator if needed)
│ ├── __init__.py
│ ├── urls.py
│ └── views.py
├── household_app/ # Domestic household-facing app (API wrapper/aggregator if needed)
│ ├── __init__.py
│ ├── urls.py
│ └── views.py
├── integrations/ # External API integrations client code
│ ├── __init__.py
│ ├── national_treasury_client.py # National Treasury eTenders API
│ ├── compliance_clients.py # SACAA, PSIRA, HPCSA, ITIL API clients
│ ├── payment_gateway_clients.py # Kazang, other payment gateways
│ ├── blockchain_client.py # SA GovChain (or other) blockchain API client
│ ├── communication_clients.py # Africa's Talking (USSD/SMS/WhatsApp), Email service
│ └── drone_api_clients.py # RocketMine/GLOBHE drone APIs
├── scripts/ # Utility scripts
│ ├── __init__.py
│ ├── seed_master_data.py # Seed categories, subcategories, skills
│ ├── seed_test_data.py # Seed database with extensive test data
│ └── custom_migration_scripts.py # For complex data migrations
└── docs/
└── openapi-schema.yml # OpenAPI (Swagger) schema definition





# dronecontractor/models/user_related.py
from django.contrib.auth.models import User
from django.contrib.gis.db import models as gis_models
from django.db import models
from django.contrib.postgres.fields import ArrayField

# Forward declarations for ForeignKey relationships
class ServiceCategory(models.Model): pass
class ServiceSubCategory(models.Model): pass
class Skill(models.Model): pass
class Certification(models.Model): pass # Detailed certification model

class BidderProfile(models.Model):
USER_TYPE_CHOICES = [('ENTERPRISE', 'Enterprise'), ('HOUSEHOLD', 'Household')]
DASHBOARD_CHOICES = [('STANDARD', 'Standard'), ('ADVANCED', 'Advanced Analytics')]

user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='bidder_profile')
user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='ENTERPRISE')
company_name = models.CharField(max_length=255, null=True, blank=True) # For Enterprise
company_registration_number = models.CharField(max_length=100, null=True, blank=True) # For Enterprise
company_vat_number = models.CharField(max_length=50, null=True, blank=True) # For Enterprise
company_address = models.TextField(null=True, blank=True)
contact_person_name = models.CharField(max_length=255, null=True, blank=True)
primary_service_categories = models.ManyToManyField('ServiceCategory', related_name='primary_bidders')
secondary_service_categories = models.ManyToManyField('ServiceCategory', related_name='secondary_bidders', blank=True)
onboarding_completed = models.BooleanField(default=False)
preferred_dashboard = models.CharField(max_length=50, choices=DASHBOARD_CHOICES, default='STANDARD')
profile_picture = models.ImageField(upload_to='bidder_pics/', null=True, blank=True)
# POPIA consent, etc.
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return f"{self.user.username} - {self.get_user_type_display()}"

class GigWorker(models.Model): # Renamed from DroneContractor internally for clarity
TIER_CHOICES = [
('BASIC', 'Basic (KYC Only)'),
('ADVANCED', 'Advanced (Skills Verified)'),
('BONDED', 'Bonded (Insured + Vetted)'),
('COMMUNITY', 'Community (Low-Cost, KYC Only)'),
]
LICENSE_STATUS_CHOICES = [('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('EXPIRED', 'Expired')]

user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='gig_worker_profile')
tier = models.CharField(max_length=10, choices=TIER_CHOICES, default='BASIC')
bio = models.TextField(null=True, blank=True)
skills = models.ManyToManyField('Skill', related_name='skilled_workers', blank=True)
certifications_documents = models.ManyToManyField('Certification', related_name='certified_workers', blank=True) # Link to structured certs
profile_picture = models.ImageField(upload_to='worker_pics/', null=True, blank=True)
phone_number_verified = models.BooleanField(default=False)
email_verified = models.BooleanField(default=False)
bakkie_available = models.BooleanField(default=False) # Vehicle
current_location = gis_models.PointField(null=True, blank=True, geography=True) # For real-time updates
service_radius_km = models.PositiveIntegerField(default=50, help_text="Preferred service radius in km")
preferred_service_areas = gis_models.MultiPolygonField(null=True, blank=True, help_text="Specific polygons for service areas")
rating_average = models.FloatField(default=5.0)
total_ratings = models.PositiveIntegerField(default=0)
saps_clearance_doc = models.FileField(upload_to='saps_clearances/', null=True, blank=True)
saps_clearance_verified = models.BooleanField(default=False)
saps_clearance_expiry = models.DateField(null=True, blank=True)
psira_license_number = models.CharField(max_length=50, null=True, blank=True) # Keep for quick ref if needed
psira_expiry = models.DateField(null=True, blank=True) # Keep for quick ref
license_status = models.CharField(max_length=20, choices=LICENSE_STATUS_CHOICES, default='PENDING')
drone_task_capable = models.BooleanField(default=False) # If still a specific focus
onboarding_completed = models.BooleanField(default=False)
last_seen = models.DateTimeField(null=True, blank=True)
# POPIA consent, bank details (potentially in a separate secure model)
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return self.user.username

# dronecontractor/models/skills_certs.py
class SkillCategory(models.Model):
name = models.CharField(max_length=100, unique=True)
description = models.TextField(blank=True, null=True)

def __str__(self):
return self.name
class Meta:
verbose_name_plural = "Skill Categories"

class Skill(models.Model):
category = models.ForeignKey(SkillCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='skills')
name = models.CharField(max_length=100, unique=True)
description = models.TextField(blank=True, null=True)
# For skills that Bidbeez can verify (e.g. through internal tests or partner certifications)
is_verifiable = models.BooleanField(default=False)

def __str__(self):
return self.name

class GigWorkerSkillEndorsement(models.Model): # If Bidders can endorse skills
skill = models.ForeignKey(Skill, on_delete=models.CASCADE)
endorsed_by_bidder = models.ForeignKey(BidderProfile, on_delete=models.CASCADE)
gig_worker = models.ForeignKey(GigWorker, on_delete=models.CASCADE)
task = models.ForeignKey('Task', on_delete=models.SET_NULL, null=True) # Task for which skill was endorsed
created_at = models.DateTimeField(auto_now_add=True)

class CertificationType(models.Model): # e.g. SACAA RPL, PSIRA Grade A, ITIL Foundation
name = models.CharField(max_length=255, unique=True)
issuing_body_organisation = models.CharField(max_length=255, null=True, blank=True)
description = models.TextField(blank=True, null=True)
is_api_verifiable = models.BooleanField(default=False) # Can this be checked via an API (SACAA, PSIRA)?
verification_api_endpoint = models.URLField(null=True, blank=True) # For dynamic checks

def __str__(self):
return self.name

class Certification(models.Model): # Detailed certification for a GigWorker
gig_worker = models.ForeignKey(GigWorker, on_delete=models.CASCADE, related_name='detailed_certifications')
certification_type = models.ForeignKey(CertificationType, on_delete=models.PROTECT)
license_id_number = models.CharField(max_length=100)
issued_date = models.DateField(null=True, blank=True)
expiry_date = models.DateField(null=True, blank=True)
document_scan = models.FileField(upload_to='worker_certifications/', null=True, blank=True)
verification_status = models.CharField(max_length=20, choices=GigWorker.LICENSE_STATUS_CHOICES, default='PENDING')
verified_by_admin = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_certs')
last_verified_on = models.DateTimeField(null=True, blank=True)
notes = models.TextField(blank=True, null=True)

def __str__(self):
return f"{self.gig_worker.user.username} - {self.certification_type.name}"

# dronecontractor/models/categories.py
class ServiceCategory(models.Model): # Replaces TASK_CATEGORIES in Task model
code = models.CharField(max_length=30, unique=True, help_text="e.g. CONSTRUCTION, ICT") # Aligned with eTenders if possible
name = models.CharField(max_length=100)
description = models.TextField(blank=True, null=True)
icon_class = models.CharField(max_length=50, blank=True, null=True) # For frontend e.g. 'fas fa-hard-hat'
is_active = models.BooleanField(default=True)

def __str__(self):
return self.name
class Meta:
verbose_name_plural = "Service Categories"

class ServiceSubCategory(models.Model):
service_category = models.ForeignKey(ServiceCategory, on_delete=models.CASCADE, related_name='subcategories')
code = models.CharField(max_length=50, unique=True, help_text="e.g. CIVIL_ENGINEERING, IT_HARDWARE")
name = models.CharField(max_length=100)
description = models.TextField(blank=True, null=True)
is_active = models.BooleanField(default=True)
# Example: required_certifications = models.ManyToManyField(CertificationType) # If subcategories always need specific certs

def __str__(self):
return f"{self.service_category.name} -> {self.name}"
class Meta:
verbose_name_plural = "Service Subcategories"
unique_together = ('service_category', 'name')


# dronecontractor/models/task_related.py
class Task(models.Model):
NOTIFICATION_STATUS_CHOICES = [('PENDING', 'Pending Notif'), ('SENT', 'Sent'), ('VIEWED', 'Viewed'), ('ACCEPTED', 'Accepted by Worker')]
TASK_STATUS_CHOICES = [
('OPEN', 'Open for Applications'),
('ASSIGNED', 'Assigned to Worker'),
('IN_PROGRESS', 'In Progress'),
('PENDING_REVIEW', 'Pending Bidder Review'),
('COMPLETED', 'Completed'),
('CANCELLED_BIDDER', 'Cancelled by Bidder'),
('CANCELLED_WORKER', 'Cancelled by Worker'),
('DISPUTED', 'Disputed'),
('EXPIRED', 'Expired (No applications/assignment)')
]
DRONE_TASK_TYPE_CHOICES = [('SURVEY', 'Aerial Survey'), ('SPRAYING', 'Crop Spraying'), ('SURVEILLANCE', 'Security Surveillance'), ('DELIVERY', 'Drone Delivery'), ('OTHER', 'Other Drone Task')]

bidder = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_tasks') # Links to User directly
tender_reference = models.CharField(max_length=100, null=True, blank=True)
title = models.CharField(max_length=255)
service_category = models.ForeignKey(ServiceCategory, on_delete=models.PROTECT, related_name='tasks')
service_subcategory = models.ForeignKey(ServiceSubCategory, on_delete=models.PROTECT, related_name='tasks')
description = models.TextField()
required_skills = models.ManyToManyField(Skill, blank=True, help_text="Specific skills needed for this task.")
location_point = gis_models.PointField(geography=True, help_text="Precise location for the task.")
location_address_text = models.CharField(max_length=500, null=True, blank=True) # Human-readable address
required_tier = models.CharField(max_length=10, choices=GigWorker.TIER_CHOICES, default='BASIC')
budget_min = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
budget_max = models.DecimalField(max_digits=12, decimal_places=2)
deadline_date = models.DateTimeField()
cpv_code = models.CharField(max_length=20, null=True, blank=True) # Common Procurement Vocabulary
is_drone_task = models.BooleanField(default=False)
drone_task_type = models.CharField(max_length=20, choices=DRONE_TASK_TYPE_CHOICES, null=True, blank=True)
task_status = models.CharField(max_length=20, choices=TASK_STATUS_CHOICES, default='OPEN')
notification_status_to_workers = models.CharField(max_length=20, choices=NOTIFICATION_STATUS_CHOICES, default='PENDING')
# metrics = models.JSONField(default=dict) # Consider a related model if metrics become complex
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return f"{self.title} ({self.service_subcategory.name})"

class TaskAttachment(models.Model):
task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name='attachments')
file = models.FileField(upload_to='task_attachments/')
description = models.CharField(max_length=255, blank=True, null=True)
uploaded_at = models.DateTimeField(auto_now_add=True)

class TaskApplication(models.Model): # When a GigWorker applies for a task
STATUS_CHOICES = [('APPLIED', 'Applied'), ('SHORTLISTED', 'Shortlisted'), ('REJECTED', 'Rejected'), ('HIRED', 'Hired')]
task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name='applications')
gig_worker = models.ForeignKey(GigWorker, on_delete=models.CASCADE, related_name='task_applications')
cover_letter = models.TextField(null=True, blank=True)
proposed_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
estimated_completion_time_hours = models.PositiveIntegerField(null=True, blank=True)
status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='APPLIED')
applied_at = models.DateTimeField(auto_now_add=True)

class Meta:
unique_together = ('task', 'gig_worker') # Worker can only apply once per task

class Assignment(models.Model):
STATUS_CHOICES = [
('PENDING_ACCEPTANCE', 'Pending Worker Acceptance'), # Worker offered the task
('ACCEPTED', 'Accepted by Worker'), # Worker accepted, ready to start
('IN_PROGRESS', 'In Progress'),
('SUBMITTED_PROOF', 'Proof Submitted, Pending Review'),
('COMPLETED', 'Completed & Approved'),
('DISPUTED', 'Disputed'),
('CANCELLED', 'Cancelled')
]
PAYMENT_METHOD_CHOICES = [('DIGITAL_ESCROW', 'Digital Escrow'), ('CASH_KAZANG', 'Cash via Kazang')]

task = models.OneToOneField(Task, on_delete=models.CASCADE, related_name='assignment') # One task, one assignment
assigned_worker = models.ForeignKey(GigWorker, on_delete=models.PROTECT, related_name='assigned_tasks')
agreed_budget = models.DecimalField(max_digits=10, decimal_places=2)
# escrow_held = models.DecimalField(max_digits=10, decimal_places=2) # This might be better tracked in a transaction log
status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='PENDING_ACCEPTANCE')
payment_method_selected = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='DIGITAL_ESCROW')
kazang_payout_location_ref = models.CharField(max_length=100, null=True, blank=True) # If Kazang selected
# bundled_tasks = models.JSONField(default=list) # Reconsider: better to link child tasks if bundling is complex
assigned_at = models.DateTimeField(auto_now_add=True)
expected_start_time = models.DateTimeField(null=True, blank=True)
actual_start_time = models.DateTimeField(null=True, blank=True)
expected_completion_time = models.DateTimeField(null=True, blank=True)
actual_completion_time = models.DateTimeField(null=True, blank=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return f"Assignment for: {self.task.title} to {self.assigned_worker.user.username}"

class HouseholdRequest(models.Model): # Similar structure to Task but simpler for households
# ... (similar fields to Task, but tailored for household needs, potentially without tenders, CPV codes etc.)
# Link to ServiceCategory, ServiceSubCategory
household_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='household_requests')
service_category = models.ForeignKey(ServiceCategory, on_delete=models.PROTECT)
service_subcategory = models.ForeignKey(ServiceSubCategory, on_delete=models.PROTECT)
description = models.TextField()
location_point = gis_models.PointField(geography=True)
location_address_text = models.CharField(max_length=500, null=True, blank=True)
budget = models.DecimalField(max_digits=10, decimal_places=2)
preferred_datetime_start = models.DateTimeField()
preferred_datetime_end = models.DateTimeField(null=True, blank=True)
status = models.CharField(max_length=20, choices=[('OPEN', 'Open'), ('MATCHED', 'Matched'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='OPEN')
assigned_worker = models.ForeignKey(GigWorker, on_delete=models.SET_NULL, null=True, blank=True, related_name='household_gigs')
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)


# dronecontractor/models/proof_dispute.py
class Proof(models.Model):
SUBMISSION_TYPE_CHOICES = [('ONLINE', 'Online'), ('OFFLINE_PENDING_SYNC', 'Offline Pending Sync'), ('OFFLINE_MANUAL_REVIEW', 'Offline Manual Review')]
VERIFICATION_STATUS_CHOICES = [('PENDING', 'Pending Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')]

assignment = models.OneToOneField(Assignment, on_delete=models.CASCADE, related_name='proof_of_completion')
submitted_at = models.DateTimeField(auto_now_add=True)
submission_type = models.CharField(max_length=30, choices=SUBMISSION_TYPE_CHOICES, default='ONLINE')
notes_by_worker = models.TextField(blank=True, null=True)
gps_latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
gps_longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
gps_timestamp = models.DateTimeField(null=True, blank=True)
blockchain_hash = models.CharField(max_length=256, null=True, blank=True) # Hash of key proof elements
blockchain_transaction_id = models.CharField(max_length=256, null=True, blank=True)
verification_status = models.CharField(max_length=20, choices=VERIFICATION_STATUS_CHOICES, default='PENDING')
verified_by_bidder = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_proofs')
verified_at = models.DateTimeField(null=True, blank=True)
bidder_comments = models.TextField(blank=True, null=True) # Comments from bidder during review

class ProofAttachment(models.Model):
proof = models.ForeignKey(Proof, on_delete=models.CASCADE, related_name='attachments')
file = models.FileField(upload_to='proof_attachments/') # Photos, videos, documents
file_type = models.CharField(max_length=20, choices=[('IMAGE', 'Image'), ('VIDEO', 'Video'), ('DOCUMENT', 'Document')])
caption = models.CharField(max_length=255, blank=True, null=True)
uploaded_at = models.DateTimeField(auto_now_add=True)

class Dispute(models.Model):
STATUS_CHOICES = [('OPEN', 'Open'), ('UNDER_REVIEW', 'Under Review'), ('MEDIATION', 'Mediation'), ('RESOLVED_FAVOR_BIDDER', 'Resolved for Bidder'), ('RESOLVED_FAVOR_WORKER', 'Resolved for Worker'), ('RESOLVED_SPLIT', 'Resolved with Split Responsibility'), ('CLOSED', 'Closed')]
RAISED_BY_CHOICES = [('BIDDER', 'Bidder'), ('CONTRACTOR', 'Contractor')]

assignment = models.OneToOneField(Assignment, on_delete=models.CASCADE, related_name='dispute')
raised_by_user_type = models.CharField(max_length=20, choices=RAISED_BY_CHOICES)
raised_by_user = models.ForeignKey(User, on_delete=models.PROTECT, related_name='raised_disputes')
reason_category = models.CharField(max_length=100) # e.g., 'Work not completed', 'Quality unsatisfactory', 'Payment issue'
reason_description = models.TextField()
desired_outcome = models.TextField(blank=True, null=True)
status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='OPEN')
resolution_details = models.TextField(blank=True, null=True)
resolved_by_admin = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_disputes')
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

class DisputeEvidence(models.Model):
dispute = models.ForeignKey(Dispute, on_delete=models.CASCADE, related_name='evidence_files')
uploaded_by_user = models.ForeignKey(User, on_delete=models.PROTECT)
file = models.FileField(upload_to='dispute_evidence/')
description = models.CharField(max_length=255, blank=True, null=True)
uploaded_at = models.DateTimeField(auto_now_add=True)

# dronecontractor/models/financial.py
class FeeStructure(models.Model):
# ... (As before, but ensure it's flexible enough, maybe with valid_from/to dates)
tier = models.CharField(max_length=10, choices=GigWorker.TIER_CHOICES, unique=True)
service_fee_percentage = models.FloatField(help_text="Percentage of task value charged as service fee")
escrow_fee_fixed = models.DecimalField(max_digits=6, decimal_places=2)
low_budget_escrow_fee = models.DecimalField(max_digits=6, decimal_places=2, default=10.00)
# ... other fee fields ...
is_active = models.BooleanField(default=True)
effective_date = models.DateField()

class TransactionLog(models.Model):
TRANSACTION_TYPE_CHOICES = [('ESCROW_FUND', 'Escrow Funding'), ('PAYOUT_WORKER', 'Payout to Worker'), ('REFUND_BIDDER', 'Refund to Bidder'), ('SERVICE_FEE', 'Service Fee Collection'), ('BONUS_PAYMENT', 'Bonus Payment')]
TRANSACTION_STATUS_CHOICES = [('PENDING', 'Pending'), ('SUCCESSFUL', 'Successful'), ('FAILED', 'Failed')]

user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='transactions') # User initiating or receiving
assignment = models.ForeignKey(Assignment, on_delete=models.SET_NULL, null=True, blank=True, related_name='transactions')
transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
amount = models.DecimalField(max_digits=12, decimal_places=2)
currency = models.CharField(max_length=3, default='ZAR')
status = models.CharField(max_length=20, choices=TRANSACTION_STATUS_CHOICES, default='PENDING')
payment_gateway_ref = models.CharField(max_length=255, null=True, blank=True)
notes = models.TextField(null=True, blank=True)
timestamp = models.DateTimeField(auto_now_add=True)


# dronecontractor/models/engagement.py (Feedback, Loyalty, Schedule, Package - largely as before, ensure FKs are correct)
class Feedback(models.Model):
# From User (Bidder/Household) to GigWorker about a Task/Assignment
assignment = models.ForeignKey(Assignment, on_delete=models.CASCADE, related_name='feedback_entries')
rating_overall = models.PositiveSmallIntegerField() # 1-5
rating_communication = models.PositiveSmallIntegerField(null=True, blank=True)
rating_quality = models.PositiveSmallIntegerField(null=True, blank=True)
rating_punctuality = models.PositiveSmallIntegerField(null=True, blank=True)
comments = models.TextField(blank=True, null=True)
is_public = models.BooleanField(default=True) # Whether feedback is visible on worker profile
feedback_by_user = models.ForeignKey(User, on_delete=models.CASCADE)
feedback_for_worker = models.ForeignKey(GigWorker, on_delete=models.CASCADE, related_name='received_feedback')
created_at = models.DateTimeField(auto_now_add=True)
via_ussd = models.BooleanField(default=False)

class Loyalty(models.Model): # For Households mostly
# ...
household_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='loyalty_account')
points = models.IntegerField(default=0)
tier = models.CharField(max_length=50, null=True, blank=True) # e.g., Bronze, Silver, Gold
last_updated = models.DateTimeField(auto_now=True)


# dronecontractor/models/utility.py
class Notification(models.Model):
RECIPIENT_TYPE_CHOICES = [('USER', 'Specific User'), ('GROUP', 'User Group e.g. all Tier X workers')] # Advanced
TYPE_CHOICES = [('NEW_TASK_MATCH', 'New Task Match'), ('APPLICATION_UPDATE', 'Application Update'), ('PAYMENT_CONFIRMATION', 'Payment Confirmation'), ('SYSTEM_ANNOUNCEMENT', 'System Announcement'), ('PROOF_REQUIRED', 'Proof Required'), ('FEEDBACK_REQUEST', 'Feedback Request')]
CHANNEL_CHOICES = [('IN_APP', 'In-App'), ('EMAIL', 'Email'), ('SMS', 'SMS'), ('WHATSAPP', 'WhatsApp')]
STATUS_CHOICES = [('PENDING', 'Pending Send'), ('SENT', 'Sent'), ('DELIVERED', 'Delivered'), ('FAILED', 'Failed'), ('READ', 'Read')]

recipient_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
# recipient_group = models.CharField() # For group notifications
notification_type = models.CharField(max_length=30, choices=TYPE_CHOICES)
message_title = models.CharField(max_length=255, null=True, blank=True)
message_body = models.TextField()
delivery_channels = ArrayField(models.CharField(max_length=10, choices=CHANNEL_CHOICES), default=list)
status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PENDING')
related_task = models.ForeignKey(Task, on_delete=models.SET_NULL, null=True, blank=True)
related_assignment = models.ForeignKey(Assignment, on_delete=models.SET_NULL, null=True, blank=True)
cta_link = models.URLField(null=True, blank=True, help_text="Call to action link for the notification")
created_at = models.DateTimeField(auto_now_add=True)
scheduled_send_time = models.DateTimeField(null=True, blank=True) # For scheduled notifications
sent_at = models.DateTimeField(null=True, blank=True)
read_at = models.DateTimeField(null=True, blank=True)

# ContractorPreferences, SignupBonus models (as before, ensuring FKs to new models like ServiceCategory)
# ...



# DroneContractor Module - Bidbeez Platform

## Overview

The **DroneContractor module** (internally referred to as **GigWorker** for clarity) is a core component of the **Bidbeez platform**, enabling **bidders** (private sector enterprises and domestic households) to outsource tasks aligned with South Africa’s **eTenders classifications** (15 categories, e.g., Construction, ICT, Human Health, General Supplies, Agriculture) to verified gig workers (**GigWorkers**). The module powers two separate apps—**Private Sector App** (for enterprises) and **Household App** (for domestic users)—using a unified **Django/PostgreSQL** backend, while integrating seamlessly with **Bidbeez bidder interfaces** (web dashboards, APIs). It supports private sector tenders (e.g., mining surveys, healthcare delivery) and household needs (e.g., cleaning, catering), addressing affordability, accessibility, reliability, and convenience.

### Key Features
- **eTenders Task Outsourcing**: Supports all eTenders categories/subcategories (e.g., ICT: Telecommunications, General Supplies: White Goods) with tender-specific compliance (CPV codes, `Task.cpv_code`).
- **Tiered Gig Workers**: Four tiers (Basic, Advanced, Bonded, Community) with category-specific access (e.g., Bonded for Human Health, Community for General Supplies).
- **Geo-Matching**: Matches tasks to workers within a configurable radius (`GigWorker.service_radius_km`, PostGIS `location_point`).
- **Compliance**: Integrates SACAA (drone tasks), PSIRA (security), HPCSA (health), and ITIL (ICT) APIs for certification verification (`Certification.verification_status`).
- **Payments**: Digital escrow and cash payouts via Kazang (`Assignment.payment_method_selected`, `kazang_payout_location_ref`).
- **Proof & Blockchain**: Validates task completion with multimedia proof (`Proof`, `ProofAttachment`) and SA GovChain blockchain (`Proof.blockchain_hash`).
- **Rural Accessibility**: USSD/SMS onboarding and offline proof submission (`Proof.submission_type`) for rural workers, supporting 50% rural task share.
- **Household Features**: Simplified requests (`HouseholdRequest`), task scheduling (`Schedule`), service packages (`Package`), and loyalty points (`Loyalty`).
- **Real-Time Notifications**: WebSocket-based updates via Django Channels (`consumers.py`) and multi-channel communication (SMS, WhatsApp, email, `Notification`).
- **Dispute Resolution**: Structured workflow for disputes (`Dispute`, `DisputeEvidence`) with mediation support.
- **Analytics**: Category-specific metrics (`Task.metrics`) and predictive demand forecasts (`services/analytics_service.py`).

### Objectives
- Support South Africa’s R200 billion tender market (National Treasury 2024).
- Enable private sector efficiency (e.g., high-value tenders for mining, healthcare).
- Address domestic household pain points (e.g., affordable cleaning, reliable catering).
- Promote rural inclusion and youth employment (46% unemployment, Stats SA 2024).
- Align with Bidbeez’s “Trusted Gig Layer” vision.

## Project Structure

backend_biddbeez/ ├── .env.example # Environment variables template ├── .gitignore # Git ignore patterns ├── manage.py # Django management script ├── requirements.txt # Dependencies (django, djangorestframework, celery, etc.) ├── Dockerfile # Containerization ├── docker-compose.yml # Local development setup ├── biddbeez/ # Main Django project │ ├── settings.py # Config: DB, AWS, APIs, CORS, Celery, Channels │ ├── urls.py # Root URL routing (includes API docs) │ ├── wsgi.py # WSGI entry point │ └── asgi.py # ASGI entry point (Channels) ├── dronecontractor/ # Core module (GigWorker functionality) │ ├── admin.py # Admin interface │ ├── apps.py # App config │ ├── consumers.py # WebSocket consumers (real-time notifications) │ ├── routing.py # Channels routing │ ├── migrations/ # Database migrations │ ├── models/ # Schema │ │ ├── user_related.py # BidderProfile, GigWorker │ │ ├── skills_certs.py # Skill, Certification models │ │ ├── categories.py # ServiceCategory, ServiceSubCategory │ │ ├── task_related.py # Task, HouseholdRequest, Assignment │ │ ├── proof_dispute.py # Proof, Dispute models │ │ ├── financial.py # FeeStructure, TransactionLog │ │ ├── engagement.py # Feedback, Loyalty, Schedule, Package │ │ └── utility.py # ContractorPreferences, Notification │ ├── views/ # API endpoints (ViewSets) │ │ ├── bidder_views.py # Bidder APIs │ │ ├── contractor_views.py # Contractor APIs │ │ ├── household_views.py # Household APIs │ │ ├── common_views.py # Shared lookups │ │ └── compliance_views.py # Compliance APIs │ ├── serializers/ # API serialization │ ├── services/ # Business logic │ │ ├── geo_matching_service.py │ │ ├── payment_service.py │ │ ├── proof_service.py │ │ ├── compliance_service.py │ │ ├── notification_service.py │ │ ├── analytics_service.py │ │ └── dispute_service.py │ ├── tasks/ # Celery tasks │ │ ├── compliance_tasks.py │ │ ├── notification_tasks.py │ │ ├── blockchain_tasks.py │ │ └── data_processing_tasks.py │ ├── tests/ # Tests │ │ ├── factories.py │ │ ├── test_models/ │ │ ├── test_serializers/ │ │ ├── test_views/ │ │ └── test_services/ │ ├── search_indexes.py # Search integration │ └── urls.py # Module URLs ├── private_sector_app/ # Private sector API wrapper ├── household_app/ # Household API wrapper ├── integrations/ # External APIs │ ├── national_treasury_client.py │ ├── compliance_clients.py │ ├── payment_gateway_clients.py │ ├── blockchain_client.py │ ├── communication_clients.py │ └── drone_api_clients.py ├── scripts/ # Utility scripts │ ├── seed_master_data.py │ ├── seed_test_data.py │ └── custom_migration_scripts.py └── docs/ └── openapi-schema.yml # OpenAPI schema



## Database Schema

The schema (`dronecontractor/models/`) uses **Django** with **PostgreSQL/PostGIS** to support eTenders tasks, gig workers, and household requests. Key models:
- **BidderProfile**: Differentiates enterprise/household users (`user_type`, `company_name`).
- **GigWorker**: Manages tiers, skills, certifications (`tier`, `skills`, `detailed_certifications`).
- **ServiceCategory/SubCategory**: Maps eTenders classifications (`code`, `name`).
- **Task**: Supports tasks with categories, subcategories, and drone tasks (`service_category`, `drone_task_type`).
- **HouseholdRequest**: Simplified household service requests (`service_subcategory`).
- **Assignment**: Tracks task assignments with proof and payments (`status`, `payment_method_selected`).
- **Proof/ProofAttachment**: Validates completion with blockchain (`blockchain_hash`).
- **Dispute/DisputeEvidence**: Manages conflict resolution (`reason_category`).
- **FeeStructure/TransactionLog**: Handles fees and payments (`service_fee_percentage`).
- **Feedback/Loyalty/Schedule/Package**: Enhances engagement and convenience.
- **Notification**: Supports multi-channel alerts (`delivery_channels`).

## Dependencies

- **Python 3.10+**
- **Django 4.x**, **Django REST Framework**, **Django Channels**, **Djoser** (auth), **drf-yasg** (API docs)
- **PostgreSQL 15+** with **PostGIS**
- **Celery 5.x**, **Redis** (async tasks, Channels backend)
- **boto3** (AWS S3)
- **django.contrib.gis** (geospatial)
- See `requirements.txt` for details.

## Setup Instructions

1. **Clone Repository**:
```bash
git clone <repository_url>
cd backend_biddbeez



File structure and contents 



backend_biddbeez/
├── .env.example
├── .gitignore
├── manage.py
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
├── biddbeez/
│ ├── __init__.py
│ ├── settings.py
│ ├── urls.py
│ ├── wsgi.py
│ ├── asgi.py
├── dronecontractor/
│ ├── __init__.py
│ ├── admin.py
│ ├── apps.py
│ ├── consumers.py
│ ├── routing.py
│ ├── migrations/
│ ├── models/
│ │ ├── __init__.py
│ │ ├── user_related.py
│ │ ├── skills_certs.py
│ │ ├── categories.py
│ │ ├── task_related.py
│ │ ├── proof_dispute.py
│ │ ├── financial.py
│ │ ├── engagement.py
│ │ ├── utility.py
│ ├── views/
│ │ ├── __init__.py
│ │ ├── bidder_views.py
│ │ ├── contractor_views.py
│ │ ├── household_views.py
│ │ ├── common_views.py
│ │ ├── compliance_views.py
│ ├── serializers/
│ │ ├── __init__.py
│ │ ├── bidder_serializers.py
│ │ ├── contractor_serializers.py
│ │ ├── household_serializers.py
│ │ ├── common_serializers.py
│ ├── services/
│ │ ├── __init__.py
│ │ ├── geo_matching_service.py
│ │ ├── payment_service.py
│ │ ├── proof_service.py
│ │ ├── compliance_service.py
│ │ ├── notification_service.py
│ │ ├── analytics_service.py
│ │ ├── dispute_service.py
│ ├── tasks/
│ │ ├── __init__.py
│ │ ├── compliance_tasks.py
│ │ ├── notification_tasks.py
│ │ ├── blockchain_tasks.py
│ │ ├── data_processing_tasks.py
│ ├── tests/
│ │ ├── __init__.py
│ │ ├── factories.py
│ │ ├── test_models/
│ │ ├── test_serializers/
│ │ ├── test_views/
│ │ ├── test_services/
│ ├── search_indexes.py
│ ├── urls.py
├── private_sector_app/
│ ├── __init__.py
│ ├── urls.py
│ ├── views.py
├── household_app/
│ ├── __init__.py
│ ├── urls.py
│ ├── views.py
├── integrations/
│ ├── __init__.py
│ ├── national_treasury_client.py
│ ├── compliance_clients.py
│ ├── payment_gateway_clients.py
│ ├── blockchain_client.py
│ ├── communication_clients.py
│ ├── drone_api_clients.py
├── scripts/
│ ├── __init__.py
│ ├── seed_master_data.py
│ ├── seed_test_data.py
│ ├── custom_migration_scripts.py
├── docs/
│ ├── openapi-schema.yml




Top-Level Files
.env.example


# Example environment variables for Bidbeez backend
DATABASE_URL=postgres://user:password@localhost:5432/biddbeez
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_S3_BUCKET=bidbeez-media
CELERY_BROKER_URL=redis://localhost:6379/0
CHANNEL_LAYERS_REDIS=redis://localhost:6379/1
SACAA_API_KEY=your_sacaa_key
PSIRA_API_KEY=your_psira_key
HPCSA_API_KEY=your_hpcsa_key
KAZANG_API_KEY=your_kazang_key
AFRICASTALKING_API_KEY=your_africastalking_key
GOVCHAIN_API_KEY=your_govchain_key
ROCKETMINE_API_KEY=your_rocketmine_key
SECRET_KEY=your_django_secret_key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1




.gitinore 


# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
*.egg-info/

# Django
*.log
*.pot
*.pyc
*.sqlite3
migrations/
media/

# Environment
.env
.env.local
.env.*.local

# Docker
*.dockerignore
Dockerfile.local

# IDE
.vscode/
.idea/
*.sublime-project
*.sublime-workspace


manage .py

#!/usr/bin/env python
import os
import sys

if __name__ == "__main__":
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "biddbeez.settings")
try:
from django.core.management import execute_from_command_line
except ImportError as exc:
raise ImportError(
"Couldn't import Django. Are you sure it's installed and "
"available on your PYTHONPATH environment variable? Did you "
"forget to activate a virtual environment?"
) from exc
execute_from_command_line(sys.argv)


Requirements .txt


Django==4.2.11
djangorestframework==3.15.1
psycopg2-binary==2.9.9
django.contrib.gis==0.1
boto3==1.34.58
celery==5.3.6
redis==5.0.3
django-channels==4.0.0
djoser==2.2.0
drf-yasg==1.21.7
requests==2.31.0
python-decouple==3.8
pytest==8.1.1
pytest-django==4.8.0
black==24.3.0
django-cors-headers==4.3.1




Docker file 

FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .

ENV PYTHONUNBUFFERED=1
EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "biddbeez.wsgi:application"]





Docker-compose.yml


version: '3.8'

services:
web:
build: .
command: python manage.py runserver 0.0.0.0:8000
volumes:
- .:/app
ports:
- "8000:8000"
env_file:
- .env
depends_on:
- db
- redis

db:
image: postgis/postgis:15-3.4
environment:
- POSTGRES_DB=biddbeez
- POSTGRES_USER=user
- POSTGRES_PASSWORD=password
volumes:
- postgres_data:/var/lib/postgresql/data
ports:
- "5432:5432"

redis:
image: redis:7.0
ports:
- "6379:6379"

celery:
build: .
command: celery -A biddbeez worker --loglevel=info
volumes:
- .:/app
env_file:
- .env
depends_on:
- redis
- db

channels:
build: .
command: python manage.py runworker
volumes:
- .:/app
env_file:
- .env
depends_on:
- redis

volumes:
postgres_data:





biddbeez/ Directory
biddbeez/settings.py


import os
from pathlib import Path
from decouple import config

BASE_DIR = Path(__file__).resolve().parent.parent

SECRET_KEY = config('SECRET_KEY')
DEBUG = config('DEBUG', default=False, cast=bool)
ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='localhost,127.0.0.1').split(',')

INSTALLED_APPS = [
'django.contrib.admin',
'django.contrib.auth',
'django.contrib.contenttypes',
'django.contrib.sessions',
'django.contrib.messages',
'django.contrib.staticfiles',
'django.contrib.gis',
'rest_framework',
'django_channels',
'djoser',
'drf_yasg',
'corsheaders',
'dronecontractor',
'private_sector_app',
'household_app',
]

MIDDLEWARE = [
'corsheaders.middleware.CorsMiddleware',
'django.middleware.security.SecurityMiddleware',
'django.contrib.sessions.middleware.SessionMiddleware',
'django.middleware.common.CommonMiddleware',
'django.middleware.csrf.CsrfViewMiddleware',
'django.contrib.auth.middleware.AuthenticationMiddleware',
'django.contrib.messages.middleware.MessageMiddleware',
'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'biddbeez.urls'

TEMPLATES = [
{
'BACKEND': 'django.template.backends.django.DjangoTemplates',
'DIRS': [],
'APP_DIRS': True,
'OPTIONS': {
'context_processors': [
'django.template.context_processors.debug',
'django.template.context_processors.request',
'django.contrib.auth.context_processors.auth',
'django.contrib.messages.context_processors.messages',
],
},
},
]

WSGI_APPLICATION = 'biddbeez.wsgi.application'
ASGI_APPLICATION = 'biddbeez.asgi.application'

DATABASES = {
'default': {
'ENGINE': 'django.contrib.gis.db.backends.postgis',
'NAME': config('DATABASE_NAME', default='biddbeez'),
'USER': config('DATABASE_USER', default='user'),
'PASSWORD': config('DATABASE_PASSWORD', default='password'),
'HOST': config('DATABASE_HOST', default='localhost'),
'PORT': config('DATABASE_PORT', default='5432'),
}
}

AUTH_PASSWORD_VALIDATORS = [
{'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
{'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
{'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
{'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'Africa/Johannesburg'
USE_I18N = True
USE_TZ = True

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
'DEFAULT_AUTHENTICATION_CLASSES': [
'rest_framework.authentication.TokenAuthentication',
'rest_framework.authentication.SessionAuthentication',
],
'DEFAULT_PERMISSION_CLASSES': [
'rest_framework.permissions.IsAuthenticated',
],
}

CHANNEL_LAYERS = {
'default': {
'BACKEND': 'channels_redis.core.RedisChannelLayer',
'CONFIG': {
'hosts': [(config('CHANNEL_LAYERS_REDIS', default='redis://localhost:6379/1'))],
},
},
}

CELERY_BROKER_URL = config('CELERY_BROKER_URL', default='redis://localhost:6379/0')
CELERY_RESULT_BACKEND = CELERY_BROKER_URL
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'

CORS_ALLOWED_ORIGINS = [
'http://localhost:3000',
'https://biddbeez.com',
]

AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = config('AWS_S3_BUCKET')
AWS_S3_REGION_NAME = 'af-south-1'



biddbeez/urls.py


from django.contrib import admin
from django.urls import path, include
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

schema_view = get_schema_view(
openapi.Info(
title="Bidbeez API",
default_version='v1',
description="API for Bidbeez platform with DroneContractor module",
contact=openapi.Contact(email="<EMAIL>"),
),
public=True,
)

urlpatterns = [
path('admin/', admin.site.urls),
path('api-auth/', include('rest_framework.urls')),
path('api/djoser/', include('djoser.urls')),
path('api/djoser/', include('djoser.urls.authtoken')),
path('api/dronecontractor/', include('dronecontractor.urls')),
path('api/private/', include('private_sector_app.urls')),
path('api/household/', include('household_app.urls')),
path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
]





biddbeez/asgi.py


import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import dronecontractor.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'biddbeez.settings')

application = ProtocolTypeRouter({
'http': get_asgi_application(),
'websocket': AuthMiddlewareStack(
URLRouter(
dronecontractor.routing.websocket_urlpatterns
)
),
})




biddbeez/wsgi.py


import os
from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'biddbeez.settings')
application = get_wsgi_application()



biddbeez/init.py


# Empty file





dronecontractor/ Directory
dronecontractor/init.py

# Empty file



dronecontractor/admin.py


from django.contrib import admin
from .models import (
BidderProfile, GigWorker, ContractorProfile, SkillCategory, Skill,
GigWorkerSkillEndorsement, CertificationType, Certification, ServiceCategory,
ServiceSubCategory, Task, TaskAttachment, TaskApplication, Assignment,
HouseholdRequest, Proof, ProofAttachment, Dispute, DisputeEvidence,
FeeStructure, TransactionLog, Feedback, Loyalty, Schedule, Package,
ContractorPreferences, SignupBonus, Notification
)

@admin.register(BidderProfile)
class BidderProfileAdmin(admin.ModelAdmin):
list_display = ('user', 'user_type', 'company_name', 'onboarding_completed')
search_fields = ('user__username', 'company_name')

@admin.register(GigWorker)
class GigWorkerAdmin(admin.ModelAdmin):
list_display = ('user', 'tier', 'rating_average', 'license_status', 'drone_task_capable')
list_filter = ('tier', 'license_status', 'drone_task_capable')
search_fields = ('user__username',)

@admin.register(ContractorProfile)
class ContractorProfileAdmin(admin.ModelAdmin):
list_display = ('contractor', 'rating')
search_fields = ('contractor__user__username',)

@admin.register(SkillCategory)
class SkillCategoryAdmin(admin.ModelAdmin):
list_display = ('name',)
search_fields = ('name',)

@admin.register(Skill)
class SkillAdmin(admin.ModelAdmin):
list_display = ('name', 'category', 'is_verifiable')
list_filter = ('category', 'is_verifiable')
search_fields = ('name',)

@admin.register(GigWorkerSkillEndorsement)
class GigWorkerSkillEndorsementAdmin(admin.ModelAdmin):
list_display = ('skill', 'gig_worker', 'endorsed_by_bidder', 'created_at')
search_fields = ('gig_worker__user__username', 'skill__name')

@admin.register(CertificationType)
class CertificationTypeAdmin(admin.ModelAdmin):
list_display = ('name', 'issuing_body_organisation', 'is_api_verifiable')
search_fields = ('name',)

@admin.register(Certification)
class CertificationAdmin(admin.ModelAdmin):
list_display = ('gig_worker', 'certification_type', 'verification_status', 'last_verified_on')
list_filter = ('verification_status',)
search_fields = ('gig_worker__user__username', 'certification_type__name')

@admin.register(ServiceCategory)
class ServiceCategoryAdmin(admin.ModelAdmin):
list_display = ('name', 'code', 'is_active')
search_fields = ('name', 'code')

@admin.register(ServiceSubCategory)
class ServiceSubCategoryAdmin(admin.ModelAdmin):
list_display = ('name', 'service_category', 'code', 'is_active')
list_filter = ('service_category', 'is_active')
search_fields = ('name', 'code')

@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
list_display = ('title', 'bidder', 'service_category', 'task_status', 'created_at')
list_filter = ('task_status', 'service_category')
search_fields = ('title', 'bidder__username')

@admin.register(TaskAttachment)
class TaskAttachmentAdmin(admin.ModelAdmin):
list_display = ('task', 'description', 'uploaded_at')
search_fields = ('task__title',)

@admin.register(TaskApplication)
class TaskApplicationAdmin(admin.ModelAdmin):
list_display = ('task', 'gig_worker', 'status', 'applied_at')
list_filter = ('status',)
search_fields = ('task__title', 'gig_worker__user__username')

@admin.register(Assignment)
class AssignmentAdmin(admin.ModelAdmin):
list_display = ('task', 'assigned_worker', 'status', 'assigned_at')
list_filter = ('status',)
search_fields = ('task__title', 'assigned_worker__user__username')

@admin.register(HouseholdRequest)
class HouseholdRequestAdmin(admin.ModelAdmin):
list_display = ('household_user', 'service_category', 'status', 'created_at')
list_filter = ('status', 'service_category')
search_fields = ('household_user__username',)

@admin.register(Proof)
class ProofAdmin(admin.ModelAdmin):
list_display = ('assignment', 'submission_type', 'verification_status', 'submitted_at')
list_filter = ('submission_type', 'verification_status')
search_fields = ('assignment__task__title',)

@admin.register(ProofAttachment)
class ProofAttachmentAdmin(admin.ModelAdmin):
list_display = ('proof', 'file_type', 'uploaded_at')
list_filter = ('file_type',)

@admin.register(Dispute)
class DisputeAdmin(admin.ModelAdmin):
list_display = ('assignment', 'raised_by_user_type', 'status', 'created_at')
list_filter = ('status', 'raised_by_user_type')
search_fields = ('assignment__task__title',)

@admin.register(DisputeEvidence)
class DisputeEvidenceAdmin(admin.ModelAdmin):
list_display = ('dispute', 'uploaded_by_user', 'uploaded_at')
search_fields = ('dispute__assignment__task__title',)

@admin.register(FeeStructure)
class FeeStructureAdmin(admin.ModelAdmin):
list_display = ('tier', 'service_fee_percentage', 'escrow_fee_fixed', 'effective_date')
list_filter = ('tier', 'is_active')
search_fields = ('tier',)

@admin.register(TransactionLog)
class TransactionLogAdmin(admin.ModelAdmin):
list_display = ('user', 'transaction_type', 'amount', 'status', 'timestamp')
list_filter = ('transaction_type', 'status')
search_fields = ('user__username',)

@admin.register(Feedback)
class FeedbackAdmin(admin.ModelAdmin):
list_display = ('assignment', 'rating_overall', 'feedback_by_user', 'created_at')
list_filter = ('rating_overall', 'via_ussd')
search_fields = ('assignment__task__title', 'feedback_by_user__username')

@admin.register(Loyalty)
class LoyaltyAdmin(admin.ModelAdmin):
list_display = ('household_user', 'points', 'tier', 'last_updated')
search_fields = ('household_user__username',)

@admin.register(Schedule)
class ScheduleAdmin(admin.ModelAdmin):
list_display = ('household_user', 'contractor', 'frequency', 'start_date')
list_filter = ('frequency',)
search_fields = ('household_user__username', 'contractor__user__username')

@admin.register(Package)
class PackageAdmin(admin.ModelAdmin):
list_display = ('name', 'price', 'discount')
search_fields = ('name',)

@admin.register(ContractorPreferences)
class ContractorPreferencesAdmin(admin.ModelAdmin):
list_display = ('contractor', 'category', 'subcategory')
search_fields = ('contractor__user__username', 'category__name')

@admin.register(SignupBonus)
class SignupBonusAdmin(admin.ModelAdmin):
list_display = ('contractor', 'category', 'bonus_amount', 'credited_at')
search_fields = ('contractor__user__username', 'category__name')



dronecontractor/apps.py


from django.apps import AppConfig

class DroneContractorConfig(AppConfig):
default_auto_field = 'django.db.models.BigAutoField'
name = 'dronecontractor'
verbose_name = 'GigWorker Module'




dronecontractor/consumers.py



from channels.generic.websocket import AsyncWebsocketConsumer
import json
from django.contrib.auth.models import User
from .models import Notification

class NotificationConsumer(AsyncWebsocketConsumer):
async def connect(self):
self.user = self.scope['user']
if self.user.is_authenticated:
self.group_name = f'user_{self.user.id}'
await self.channel_layer.group_add(self.group_name, self.channel_name)
await self.accept()
else:
await self.close()

async def disconnect(self, close_code):
if hasattr(self, 'group_name'):
await self.channel_layer.group_discard(self.group_name, self.channel_name)

async def receive(self, text_data):
data = json.loads(text_data)
if data.get('action') == 'mark_read':
notification_id = data.get('notification_id')
try:
notification = Notification.objects.get(id=notification_id, recipient_user=self.user)
notification.status = 'READ'
notification.read_at = timezone.now()
notification.save()
await self.send(text_data=json.dumps({'status': 'notification_marked_read', 'id': notification_id}))
except Notification.DoesNotExist:
await self.send(text_data=json.dumps({'error': 'Notification not found'}))

async def notification_message(self, event):
await self.send(text_data=json.dumps({
'type': event['notification_type'],
'title': event['message_title'],
'body': event['message_body'],
'id': event['notification_id'],
'cta_link': event.get('cta_link')
}))



dronecontractor/routing.py


from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
re_path(r'ws/notifications/$', consumers.NotificationConsumer.as_asgi()),
]




dronecontractor/models/user_related.py


from django.contrib.auth.models import User
from django.contrib.gis.db import models as gis_models
from django.db import models
from django.contrib.postgres.fields import ArrayField
from django.utils import timezone

class BidderProfile(models.Model):
USER_TYPE_CHOICES = [('ENTERPRISE', 'Enterprise'), ('HOUSEHOLD', 'Household')]
DASHBOARD_CHOICES = [('STANDARD', 'Standard'), ('ADVANCED', 'Advanced Analytics')]

user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='bidder_profile')
user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='ENTERPRISE')
company_name = models.CharField(max_length=255, null=True, blank=True)
company_registration_number = models.CharField(max_length=100, null=True, blank=True)
company_vat_number = models.CharField(max_length=50, null=True, blank=True)
company_address = models.TextField(null=True, blank=True)
contact_person_name = models.CharField(max_length=255, null=True, blank=True)
primary_service_categories = models.ManyToManyField('ServiceCategory', related_name='primary_bidders')
secondary_service_categories = models.ManyToManyField('ServiceCategory', related_name='secondary_bidders', blank=True)
onboarding_completed = models.BooleanField(default=False)
preferred_dashboard = models.CharField(max_length=50, choices=DASHBOARD_CHOICES, default='STANDARD')
profile_picture = models.ImageField(upload_to='bidder_pics/', null=True, blank=True)
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return f"{self.user.username} - {self.get_user_type_display()}"

class GigWorker(models.Model):
TIER_CHOICES = [
('BASIC', 'Basic (KYC Only)'),
('ADVANCED', 'Advanced (Skills Verified)'),
('BONDED', 'Bonded (Insured + Vetted)'),
('COMMUNITY', 'Community (Low-Cost, KYC Only)'),
]
LICENSE_STATUS_CHOICES = [('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('EXPIRED', 'Expired')]

user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='gig_worker_profile')
tier = models.CharField(max_length=10, choices=TIER_CHOICES, default='BASIC')
bio = models.TextField(null=True, blank=True)
skills = models.ManyToManyField('Skill', related_name='skilled_workers', blank=True)
certifications_documents = models.ManyToManyField('Certification', related_name='certified_workers', blank=True)
profile_picture = models.ImageField(upload_to='worker_pics/', null=True, blank=True)
phone_number_verified = models.BooleanField(default=False)
email_verified = models.BooleanField(default=False)
bakkie_available = models.BooleanField(default=False)
current_location = gis_models.PointField(null=True, blank=True, geography=True)
service_radius_km = models.PositiveIntegerField(default=50)
preferred_service_areas = gis_models.MultiPolygonField(null=True, blank=True)
rating_average = models.FloatField(default=5.0)
total_ratings = models.PositiveIntegerField(default=0)
saps_clearance_doc = models.FileField(upload_to='saps_clearances/', null=True, blank=True)
saps_clearance_verified = models.BooleanField(default=False)
saps_clearance_expiry = models.DateField(null=True, blank=True)
psira_license_number = models.CharField(max_length=50, null=True, blank=True)
psira_expiry = models.DateField(null=True, blank=True)
license_status = models.CharField(max_length=20, choices=LICENSE_STATUS_CHOICES, default='PENDING')
drone_task_capable = models.BooleanField(default=False)
onboarding_completed = models.BooleanField(default=False)
last_seen = models.DateTimeField(null=True, blank=True)
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return self.user.username




dronecontractor/models/skills_certs.py

from django.db import models
from django.utils import timezone

class SkillCategory(models.Model):
name = models.CharField(max_length=100, unique=True)
description = models.TextField(blank=True, null=True)

def __str__(self):
return self.name

class Meta:
verbose_name_plural = "Skill Categories"

class Skill(models.Model):
category = models.ForeignKey(SkillCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='skills')
name = models.CharField(max_length=100, unique=True)
description = models.TextField(blank=True, null=True)
is_verifiable = models.BooleanField(default=False)

def __str__(self):
return self.name

class GigWorkerSkillEndorsement(models.Model):
skill = models.ForeignKey(Skill, on_delete=models.CASCADE)
endorsed_by_bidder = models.ForeignKey('BidderProfile', on_delete=models.CASCADE)
gig_worker = models.ForeignKey('GigWorker', on_delete=models.CASCADE)
task = models.ForeignKey('Task', on_delete=models.SET_NULL, null=True)
created_at = models.DateTimeField(auto_now_add=True)

class Meta:
unique_together = ('skill', 'gig_worker', 'endorsed_by_bidder')

class CertificationType(models.Model):
name = models.CharField(max_length=255, unique=True)
issuing_body_organisation = models.CharField(max_length=255, null=True, blank=True)
description = models.TextField(blank=True, null=True)
is_api_verifiable = models.BooleanField(default=False)
verification_api_endpoint = models.URLField(null=True, blank=True)

def __str__(self):
return self.name

class Certification(models.Model):
gig_worker = models.ForeignKey('GigWorker', on_delete=models.CASCADE, related_name='detailed_certifications')
certification_type = models.ForeignKey(CertificationType, on_delete=models.PROTECT)
license_id_number = models.CharField(max_length=100)
issued_date = models.DateField(null=True, blank=True)
expiry_date = models.DateField(null=True, blank=True)
document_scan = models.FileField(upload_to='worker_certifications/', null=True, blank=True)
verification_status = models.CharField(max_length=20, choices=[
('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('EXPIRED', 'Expired')
], default='PENDING')
verified_by_admin = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True)
last_verified_on = models.DateTimeField(null=True, blank=True)
notes = models.TextField(blank=True, null=True)

def __str__(self):
return f"{self.gig_worker.user.username} - {self.certification_type.name}"




dronecontractor/models/categories.py


from django.db import models

class ServiceCategory(models.Model):
code = models.CharField(max_length=30, unique=True)
name = models.CharField(max_length=100)
description = models.TextField(blank=True, null=True)
icon_class = models.CharField(max_length=50, blank=True, null=True)
is_active = models.BooleanField(default=True)

def __str__(self):
return self.name

class Meta:
verbose_name_plural = "Service Categories"

class ServiceSubCategory(models.Model):
service_category = models.ForeignKey(ServiceCategory, on_delete=models.CASCADE, related_name='subcategories')
code = models.CharField(max_length=50, unique=True)
name = models.CharField(max_length=100)
description = models.TextField(blank=True, null=True)
is_active = models.BooleanField(default=True)

def __str__(self):
return f"{self.service_category.name} -> {self.name}"

class Meta:
verbose_name_plural = "Service Subcategories"
unique_together = ('service_category', 'name')



dronecontractor/models/task_related.py


from django.contrib.auth.models import User
from django.contrib.gis.db import models as gis_models
from django.db import models
from django.utils import timezone

class Task(models.Model):
NOTIFICATION_STATUS_CHOICES = [('PENDING', 'Pending Notif'), ('SENT', 'Sent'), ('VIEWED', 'Viewed'), ('ACCEPTED', 'Accepted by Worker')]
TASK_STATUS_CHOICES = [
('OPEN', 'Open for Applications'),
('ASSIGNED', 'Assigned to Worker'),
('IN_PROGRESS', 'In Progress'),
('PENDING_REVIEW', 'Pending Bidder Review'),
('COMPLETED', 'Completed'),
('CANCELLED_BIDDER', 'Cancelled by Bidder'),
('CANCELLED_WORKER', 'Cancelled by Worker'),
('DISPUTED', 'Disputed'),
('EXPIRED', 'Expired')
]
DRONE_TASK_TYPE_CHOICES = [('SURVEY', 'Aerial Survey'), ('SPRAYING', 'Crop Spraying'), ('SURVEILLANCE', 'Security Surveillance'), ('DELIVERY', 'Drone Delivery'), ('OTHER', 'Other Drone Task')]

bidder = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_tasks')
tender_reference = models.CharField(max_length=100, null=True, blank=True)
title = models.CharField(max_length=255)
service_category = models.ForeignKey('ServiceCategory', on_delete=models.PROTECT, related_name='tasks')
service_subcategory = models.ForeignKey('ServiceSubCategory', on_delete=models.PROTECT, related_name='tasks')
description = models.TextField()
required_skills = models.ManyToManyField('Skill', blank=True)
location_point = gis_models.PointField(geography=True)
location_address_text = models.CharField(max_length=500, null=True, blank=True)
required_tier = models.CharField(max_length=10, choices=[
('BASIC', 'Basic'), ('ADVANCED', 'Advanced'), ('BONDED', 'Bonded'), ('COMMUNITY', 'Community')
], default='BASIC')
budget_min = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
budget_max = models.DecimalField(max_digits=12, decimal_places=2)
deadline_date = models.DateTimeField()
cpv_code = models.CharField(max_length=20, null=True, blank=True)
is_drone_task = models.BooleanField(default=False)
drone_task_type = models.CharField(max_length=20, choices=DRONE_TASK_TYPE_CHOICES, null=True, blank=True)
task_status = models.CharField(max_length=20, choices=TASK_STATUS_CHOICES, default='OPEN')
notification_status_to_workers = models.CharField(max_length=20, choices=NOTIFICATION_STATUS_CHOICES, default='PENDING')
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return f"{self.title} ({self.service_subcategory.name})"

class TaskAttachment(models.Model):
task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name='attachments')
file = models.FileField(upload_to='task_attachments/')
description = models.CharField(max_length=255, blank=True, null=True)
uploaded_at = models.DateTimeField(auto_now_add=True)

class TaskApplication(models.Model):
STATUS_CHOICES = [('APPLIED', 'Applied'), ('SHORTLISTED', 'Shortlisted'), ('REJECTED', 'Rejected'), ('HIRED', 'Hired')]

task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name='applications')
gig_worker = models.ForeignKey('GigWorker', on_delete=models.CASCADE, related_name='task_applications')
cover_letter = models.TextField(null=True, blank=True)
proposed_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
estimated_completion_time_hours = models.PositiveIntegerField(null=True, blank=True)
status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='APPLIED')
applied_at = models.DateTimeField(auto_now_add=True)

class Meta:
unique_together = ('task', 'gig_worker')

class Assignment(models.Model):
STATUS_CHOICES = [
('PENDING_ACCEPTANCE', 'Pending Worker Acceptance'),
('ACCEPTED', 'Accepted by Worker'),
('IN_PROGRESS', 'In Progress'),
('SUBMITTED_PROOF', 'Proof Submitted'),
('COMPLETED', 'Completed & Approved'),
('DISPUTED', 'Disputed'),
('CANCELLED', 'Cancelled')
]
PAYMENT_METHOD_CHOICES = [('DIGITAL_ESCROW', 'Digital Escrow'), ('CASH_KAZANG', 'Cash via Kazang')]

task = models.OneToOneField(Task, on_delete=models.CASCADE, related_name='assignment')
assigned_worker = models.ForeignKey('GigWorker', on_delete=models.PROTECT, related_name='assigned_tasks')
agreed_budget = models.DecimalField(max_digits=10, decimal_places=2)
status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='PENDING_ACCEPTANCE')
payment_method_selected = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='DIGITAL_ESCROW')
kazang_payout_location_ref = models.CharField(max_length=100, null=True, blank=True)
assigned_at = models.DateTimeField(auto_now_add=True)
expected_start_time = models.DateTimeField(null=True, blank=True)
actual_start_time = models.DateTimeField(null=True, blank=True)
expected_completion_time = models.DateTimeField(null=True, blank=True)
actual_completion_time = models.DateTimeField(null=True, blank=True)
updated_at = models.DateTimeField(auto_now=True)

def __str__(self):
return f"Assignment for: {self.task.title} to {self.assigned_worker.user.username}"

class HouseholdRequest(models.Model):
STATUS_CHOICES = [('OPEN', 'Open'), ('MATCHED', 'Matched'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')]

household_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='household_requests')
service_category = models.ForeignKey('ServiceCategory', on_delete=models.PROTECT)
service_subcategory = models.ForeignKey('ServiceSubCategory', on_delete=models.PROTECT)
description = models.TextField()
location_point = gis_models.PointField(geography=True)
location_address_text = models.CharField(max_length=500, null=True, blank=True)
budget = models.DecimalField(max_digits=10, decimal_places=2)
preferred_datetime_start = models.DateTimeField()
preferred_datetime_end = models.DateTimeField(null=True, blank=True)
status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='OPEN')
assigned_worker = models.ForeignKey('GigWorker', on_delete=models.SET_NULL, null=True, blank=True)
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)





Drone contractor/models/proof_dispute.py

from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone

class Proof(models.Model):
SUBMISSION_TYPE_CHOICES = [('ONLINE', 'Online'), ('OFFLINE_PENDING_SYNC', 'Offline Pending Sync'), ('OFFLINE_MANUAL_REVIEW', 'Offline Manual Review')]
VERIFICATION_STATUS_CHOICES = [('PENDING', 'Pending Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')]

assignment = models.OneToOneField('Assignment', on_delete=models.CASCADE, related_name='proof_of_completion')
submitted_at = models.DateTimeField(auto_now_add=True)
submission_type = models.CharField(max_length=30, choices=SUBMISSION_TYPE_CHOICES, default='ONLINE')
notes_by_worker = models.TextField(blank=True, null=True)
gps_latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
gps_longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
gps_timestamp = models.DateTimeField(null=True, blank=True)
blockchain_hash = models.CharField(max_length=256, null=True, blank=True)
blockchain_transaction_id = models.CharField(max_length=256, null=True, blank=True)
verification_status = models.CharField(max_length=20, choices=VERIFICATION_STATUS_CHOICES, default='PENDING')
verified_by_bidder = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_proofs')
verified_at = models.DateTimeField(null=True, blank=True)
bidder_comments = models.TextField(blank=True, null=True)

class ProofAttachment(models.Model):
proof = models.ForeignKey(Proof, on_delete=models.CASCADE, related_name='attachments')
file = models.FileField(upload_to='proof_attachments/')
file_type = models.CharField(max_length=20, choices=[('IMAGE', 'Image'), ('VIDEO', 'Video'), ('DOCUMENT', 'Document')])
caption = models.CharField(max_length=255, blank=True, null=True)
uploaded_at = models.DateTimeField(auto_now_add=True)

class Dispute(models.Model):
STATUS_CHOICES = [
('OPEN', 'Open'),
('UNDER_REVIEW', 'Under Review'),
('MEDIATION', 'Mediation'),
('RESOLVED_FAVOR_BIDDER', 'Resolved for Bidder'),
('RESOLVED_FAVOR_WORKER', 'Resolved for Worker'),
('RESOLVED_SPLIT', 'Resolved with Split Responsibility'),
('CLOSED', 'Closed')
]
RAISED_BY_CHOICES = [('BIDDER', 'Bidder'), ('CONTRACTOR', 'Contractor')]

assignment = models.OneToOneField('Assignment', on_delete=models.CASCADE, related_name='dispute')
raised_by_user_type = models.CharField(max_length=20, choices=RAISED_BY_CHOICES)
raised_by_user = models.ForeignKey(User, on_delete=models.PROTECT, related_name='raised_disputes')
reason_category = models.CharField(max_length=100)
reason_description = models.TextField()
desired_outcome = models.TextField(blank=True, null=True)
status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='OPEN')
resolution_details = models.TextField(blank=True, null=True)
resolved_by_admin = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_disputes')
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

class DisputeEvidence(models.Model):
dispute = models.ForeignKey(Dispute, on_delete=models.CASCADE, related_name='evidence_files')
uploaded_by_user = models.ForeignKey(User, on_delete=models.PROTECT)
file = models.FileField(upload_to='dispute_evidence/')
description = models.CharField(max_length=255, blank=True, null=True)
uploaded_at = models.DateTimeField(auto_now_add=True)




dronecontractor/models/financial.py


from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone

class FeeStructure(models.Model):
tier = models.CharField(max_length=10, choices=[
('BASIC', 'Basic'), ('ADVANCED', 'Advanced'), ('BONDED', 'Bonded'), ('COMMUNITY', 'Community')
], unique=True)
service_fee_percentage = models.FloatField()
escrow_fee_fixed = models.DecimalField(max_digits=6, decimal_places=2)
low_budget_escrow_fee = models.DecimalField(max_digits=6, decimal_places=2, default=10.00)
dispute_handling_fee = models.DecimalField(max_digits=6, decimal_places=2, default=50.00)
dispute_responsible_party = models.CharField(max_length=20, choices=[
('BIDDER', 'Bidder'), ('CONTRACTOR', 'Contractor'), ('SPLIT', 'Split')
], default='SPLIT')
community_tier_fee = models.DecimalField(max_digits=6, decimal_places=2, default=5.00)
pricing_adjustment = models.FloatField(default=1.0)
is_active = models.BooleanField(default=True)
effective_date = models.DateField()

class TransactionLog(models.Model):
TRANSACTION_TYPE_CHOICES = [
('ESCROW_FUND', 'Escrow Funding'),
('PAYOUT_WORKER', 'Payout to Worker'),
('REFUND_BIDDER', 'Refund to Bidder'),
('SERVICE_FEE', 'Service Fee Collection'),
('BONUS_PAYMENT', 'Bonus Payment')
]
TRANSACTION_STATUS_CHOICES = [('PENDING', 'Pending'), ('SUCCESSFUL', 'Successful'), ('FAILED', 'Failed')]

user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='transactions')
assignment = models.ForeignKey('Assignment', on_delete=models.SET_NULL, null=True, blank=True, related_name='transactions')
transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
amount = models.DecimalField(max_digits=12, decimal_places=2)
currency = models.CharField(max_length=3, default='ZAR')
status = models.CharField(max_length=20, choices=TRANSACTION_STATUS_CHOICES, default='PENDING')
payment_gateway_ref = models.CharField(max_length=255, null=True, blank=True)
notes = models.TextField(null=True, blank=True)
timestamp = models.DateTimeField(auto_now_add=True)




dronecontractor/models/engagement.py


from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone

class Feedback(models.Model):
assignment = models.ForeignKey('Assignment', on_delete=models.CASCADE, related_name='feedback_entries')
rating_overall = models.PositiveSmallIntegerField()
rating_communication = models.PositiveSmallIntegerField(null=True, blank=True)
rating_quality = models.PositiveSmallIntegerField(null=True, blank=True)
rating_punctuality = models.PositiveSmallIntegerField(null=True, blank=True)
comments = models.TextField(blank=True, null=True)
is_public = models.BooleanField(default=True)
feedback_by_user = models.ForeignKey(User, on_delete=models.CASCADE)
feedback_for_worker = models.ForeignKey('GigWorker', on_delete=models.CASCADE, related_name='received_feedback')
created_at = models.DateTimeField(auto_now_add=True)
via_ussd = models.BooleanField(default=False)

class Loyalty(models.Model):
household_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='loyalty_account')
points = models.IntegerField(default=0)
tier = models.CharField(max_length=50, null=True, blank=True)
last_updated = models.DateTimeField(auto_now=True)

class Schedule(models.Model):
household_user = models.ForeignKey(User, on_delete=models.CASCADE)
contractor = models.ForeignKey('GigWorker', on_delete=models.CASCADE)
tasks = models.ManyToManyField('Task')
frequency = models.CharField(max_length=20, choices=[
('ONCE', 'One-Time'),
('WEEKLY', 'Weekly'),
('MONTHLY', 'Monthly')
])
start_date = models.DateTimeField()
end_date = models.DateTimeField(null=True)
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

class Package(models.Model):
name = models.CharField(max_length=50)
tasks = models.ManyToManyField('Task')
discount = models.FloatField(default=0.1)
price = models.DecimalField(max_digits=10, decimal_places=2)
created_at = models.DateTimeField(auto_now_add=True)




dronecontractor/models/utility.py



from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone
from django.contrib.postgres.fields import ArrayField

class Notification(models.Model):
RECIPIENT_TYPE_CHOICES = [('USER', 'Specific User'), ('GROUP', 'User Group')]
TYPE_CHOICES = [
('NEW_TASK_MATCH', 'New Task Match'),
('APPLICATION_UPDATE', 'Application Update'),
('PAYMENT_CONFIRMATION', 'Payment Confirmation'),
('SYSTEM_ANNOUNCEMENT', 'System Announcement'),
('PROOF_REQUIRED', 'Proof Required'),
('FEEDBACK_REQUEST', 'Feedback Request')
]
CHANNEL_CHOICES = [('IN_APP', 'In-App'), ('EMAIL', 'Email'), ('SMS', 'SMS'), ('WHATSAPP', 'WhatsApp')]
STATUS_CHOICES = [('PENDING', 'Pending Send'), ('SENT', 'Sent'), ('DELIVERED', 'Delivered'), ('FAILED', 'Failed'), ('READ', 'Read')]

recipient_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
notification_type = models.CharField(max_length=30, choices=TYPE_CHOICES)
message_title = models.CharField(max_length=255, null=True, blank=True)
message_body = models.TextField()
delivery_channels = ArrayField(models.CharField(max_length=10, choices=CHANNEL_CHOICES), default=list)
status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PENDING')
related_task = models.ForeignKey('Task', on_delete=models.SET_NULL, null=True, blank=True)
related_assignment = models.ForeignKey('Assignment', on_delete=models.SET_NULL, null=True, blank=True)
cta_link = models.URLField(null=True, blank=True)
created_at = models.DateTimeField(auto_now_add=True)
scheduled_send_time = models.DateTimeField(null=True, blank=True)
sent_at = models.DateTimeField(null=True, blank=True)
read_at = models.DateTimeField(null=True, blank=True)

class ContractorPreferences(models.Model):
contractor = models.ForeignKey('GigWorker', on_delete=models.CASCADE)
category = models.ForeignKey('ServiceCategory', on_delete=models.PROTECT)
subcategory = models.CharField(max_length=50)

class SignupBonus(models.Model):
contractor = models.ForeignKey('GigWorker', on_delete=models.CASCADE)
category = models.ForeignKey('ServiceCategory', on_delete=models.PROTECT)
subcategory = models.CharField(max_length=50)
bonus_amount = models.DecimalField(max_digits=6, decimal_places=2)
credited_at = models.DateTimeField(auto_now_add=True)
applied = models.BooleanField(default=False)




dronecontractor/models/init.py


from .user_related import BidderProfile, GigWorker
from .skills_certs import SkillCategory, Skill, GigWorkerSkillEndorsement, CertificationType, Certification
from .categories import ServiceCategory, ServiceSubCategory
from .task_related import Task, TaskAttachment, TaskApplication, Assignment, HouseholdRequest
from .proof_dispute import Proof, ProofAttachment, Dispute, DisputeEvidence
from .financial import FeeStructure, TransactionLog
from .engagement import Feedback, Loyalty, Schedule, Package
from .utility import Notification, ContractorPreferences, SignupBonus





dronecontractor/views/bidder_views.py


from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.gis.measure import Distance
from django.contrib.gis.db.models.functions import Distance as DistanceFunc
from .serializers import TaskSerializer, TaskApplicationSerializer, AssignmentSerializer
from ..models import Task, TaskApplication, Assignment, GigWorker
from ..services.geo_matching_service import match_contractors
from ..services.notification_service import send_notification

class TaskViewSet(viewsets.ModelViewSet):
queryset = Task.objects.all()
serializer_class = TaskSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(bidder=self.request.user)

def perform_create(self, serializer):
task = serializer.save(bidder=self.request.user)
contractors = match_contractors(task)
send_notification(
users=[c.user for c in contractors],
notification_type='NEW_TASK_MATCH',
message_title=f'New Task: {task.title}',
message_body=f'A new {task.service_subcategory.name} task is available in your area.',
cta_link=f'/tasks/{task.id}',
channels=['IN_APP', 'SMS', 'WHATSAPP']
)

@action(detail=True, methods=['post'])
def assign(self, request, pk=None):
task = self.get_object()
application_id = request.data.get('application_id')
try:
application = TaskApplication.objects.get(id=application_id, task=task)
assignment = Assignment.objects.create(
task=task,
assigned_worker=application.gig_worker,
agreed_budget=application.proposed_rate or task.budget_max,
payment_method_selected=request.data.get('payment_method', 'DIGITAL_ESCROW')
)
task.task_status = 'ASSIGNED'
task.save()
send_notification(
user=application.gig_worker.user,
notification_type='APPLICATION_UPDATE',
message_title='Task Assigned',
message_body=f'You have been assigned task: {task.title}.',
cta_link=f'/assignments/{assignment.id}',
channels=['IN_APP', 'WHATSAPP']
)
return Response({'status': 'assigned', 'assignment_id': assignment.id}, status=status.HTTP_201_CREATED)
except TaskApplication.DoesNotExist:
return Response({'error': 'Application not found'}, status=status.HTTP_404_NOT_FOUND)

class TaskApplicationViewSet(viewsets.ReadOnlyModelViewSet):
queryset = TaskApplication.objects.all()
serializer_class = TaskApplicationSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(task__bidder=self.request.user)





dronecontractor/views/contractor_views.py


from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.gis.measure import Distance
from django.contrib.gis.db.models.functions import Distance as DistanceFunc
from .serializers import (
GigWorkerSerializer, TaskSerializer, TaskApplicationSerializer,
AssignmentSerializer, ProofSerializer
)
from ..models import GigWorker, Task, TaskApplication, Assignment, Proof
from ..services.notification_service import send_notification

class GigWorkerViewSet(viewsets.ModelViewSet):
queryset = GigWorker.objects.all()
serializer_class = GigWorkerSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(user=self.request.user)

@action(detail=False, methods=['post'])
def onboard(self, request):
serializer = self.get_serializer(data=request.data)
serializer.is_valid(raise_exception=True)
serializer.save(user=self.request.user)
return Response(serializer.data, status=status.HTTP_201_CREATED)

class TaskApplicationViewSet(viewsets.ModelViewSet):
queryset = TaskApplication.objects.all()
serializer_class = TaskApplicationSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(gig_worker__user=self.request.user)

def perform_create(self, serializer):
task = Task.objects.get(id=self.request.data['task'])
serializer.save(gig_worker=self.request.user.gig_worker_profile, task=task)
send_notification(
user=task.bidder,
notification_type='APPLICATION_UPDATE',
message_title='New Task Application',
message_body=f'{self.request.user.username} applied for task: {task.title}.',
cta_link=f'/tasks/{task.id}/applications',
channels=['IN_APP', 'EMAIL']
)

class AssignmentViewSet(viewsets.ModelViewSet):
queryset = Assignment.objects.all()
serializer_class = AssignmentSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(assigned_worker__user=self.request.user)

@action(detail=True, methods=['post'])
def submit_proof(self, request, pk=None):
assignment = self.get_object()
serializer = ProofSerializer(data=request.data)
serializer.is_valid(raise_exception=True)
proof = serializer.save(assignment=assignment)
assignment.status = 'SUBMITTED_PROOF'
assignment.save()
send_notification(
user=assignment.task.bidder,
notification_type='PROOF_REQUIRED',
message_title='Proof Submitted',
message_body=f'Proof submitted for task: {assignment.task.title}.',
cta_link=f'/assignments/{assignment.id}/proof',
channels=['IN_APP', 'EMAIL']
)
return Response(serializer.data, status=status.HTTP_201_CREATED)




dronecontractor/views/household_views.py


from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from .serializers import HouseholdRequestSerializer, ScheduleSerializer, PackageSerializer
from ..models import HouseholdRequest, Schedule, Package
from ..services.geo_matching_service import match_contractors
from ..services.notification_service import send_notification

class HouseholdRequestViewSet(viewsets.ModelViewSet):
queryset = HouseholdRequest.objects.all()
serializer_class = HouseholdRequestSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(household_user=self.request.user)

def perform_create(self, serializer):
request = serializer.save(household_user=self.request.user)
contractors = match_contractors(request)
send_notification(
users=[c.user for c in contractors],
notification_type='NEW_TASK_MATCH',
message_title=f'New Household Request: {request.service_subcategory.name}',
message_body=f'A new service request is available in your area.',
cta_link=f'/requests/{request.id}',
channels=['IN_APP', 'SMS', 'WHATSAPP']
)

class ScheduleViewSet(viewsets.ModelViewSet):
queryset = Schedule.objects.all()
serializer_class = ScheduleSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(household_user=self.request.user)

class PackageViewSet(viewsets.ReadOnlyModelViewSet):
queryset = Package.objects.all()
serializer_class = PackageSerializer
permission_classes = [permissions.IsAuthenticated]



dronecontractor/views/common_views.py


from rest_framework import viewsets
from rest_framework.permissions import AllowAny
from .serializers import ServiceCategorySerializer, ServiceSubCategorySerializer, SkillSerializer
from ..models import ServiceCategory, ServiceSubCategory, Skill

class ServiceCategoryViewSet(viewsets.ReadOnlyModelViewSet):
queryset = ServiceCategory.objects.filter(is_active=True)
serializer_class = ServiceCategorySerializer
permission_classes = [AllowAny]

class ServiceSubCategoryViewSet(viewsets.ReadOnlyModelViewSet):
queryset = ServiceSubCategory.objects.filter(is_active=True)
serializer_class = ServiceSubCategorySerializer
permission_classes = [AllowAny]

class SkillViewSet(viewsets.ReadOnlyModelViewSet):
queryset = Skill.objects.all()
serializer_class = SkillSerializer
permission_classes = [AllowAny]






dronecontractor/views/compliance_views.py


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from ..models import Certification
from ..services.compliance_service import verify_certification
from ..tasks.compliance_tasks import async_verify_certification

class CertificationVerificationView(APIView):
permission_classes = [permissions.IsAuthenticated]

def post(self, request):
certification_id = request.data.get('certification_id')
try:
certification = Certification.objects.get(id=certification_id, gig_worker__user=request.user)
async_verify_certification.delay(certification.id)
return Response({'status': 'Verification queued'}, status=status.HTTP_202_ACCEPTED)
except Certification.DoesNotExist:
return Response({'error': 'Certification not found'}, status=status.HTTP_404_NOT_FOUND)



dronecontractor/serializers/init.py


from .bidder_serializers import TaskSerializer, TaskApplicationSerializer
from .contractor_serializers import GigWorkerSerializer, AssignmentSerializer, ProofSerializer
from .household_serializers import HouseholdRequestSerializer, ScheduleSerializer, PackageSerializer
from .common_serializers import ServiceCategorySerializer, ServiceSubCategorySerializer, SkillSerializer




dronecontractor/serializers/bidder_serializers.py


from rest_framework import serializers
from ..models import Task, TaskApplication

class TaskSerializer(serializers.ModelSerializer):
service_category = serializers.SlugRelatedField(slug_field='code', queryset=ServiceCategory.objects.all())
service_subcategory = serializers.SlugRelatedField(slug_field='code', queryset=ServiceSubCategory.objects.all())
required_skills = serializers.SlugRelatedField(many=True, slug_field='name', queryset=Skill.objects.all(), required=False)

class Meta:
model = Task
fields = '__all__'
read_only_fields = ('bidder', 'created_at', 'updated_at', 'task_status', 'notification_status_to_workers')

class TaskApplicationSerializer(serializers.ModelSerializer):
task = TaskSerializer(read_only=True)
gig_worker = serializers.SlugRelatedField(slug_field='user__username', read_only=True)

class Meta:
model = TaskApplication
fields = '__all__'



dronecontractor/serializers/contractor_serializers.py


from rest_framework import serializers
from ..models import GigWorker, Assignment, Proof

class GigWorkerSerializer(serializers.ModelSerializer):
skills = serializers.SlugRelatedField(many=True, slug_field='name', queryset=Skill.objects.all())
certifications_documents = serializers.SlugRelatedField(many=True, slug_field='license_id_number', queryset=Certification.objects.all())

class Meta:
model = GigWorker
fields = '__all__'
read_only_fields = ('user', 'rating_average', 'total_ratings', 'created_at', 'updated_at')

class AssignmentSerializer(serializers.ModelSerializer):
task = TaskSerializer(read_only=True)
assigned_worker = serializers.SlugRelatedField(slug_field='user__username', read_only=True)

class Meta:
model = Assignment
fields = '__all__'

class ProofSerializer(serializers.ModelSerializer):
attachments = serializers.ListField(child=serializers.FileField(), write_only=True, required=False)

class Meta:
model = Proof
fields = '__all__'
read_only_fields = ('assignment', 'submitted_at', 'verification_status', 'verified_by_bidder', 'verified_at')





dronecontractor/serializers/household_serializers.py


from rest_framework import serializers
from ..models import HouseholdRequest, Schedule, Package

class HouseholdRequestSerializer(serializers.ModelSerializer):
service_category = serializers.SlugRelatedField(slug_field='code', queryset=ServiceCategory.objects.all())
service_subcategory = serializers.SlugRelatedField(slug_field='code', queryset=ServiceSubCategory.objects.all())

class Meta:
model = HouseholdRequest
fields = '__all__'
read_only_fields = ('household_user', 'created_at', 'updated_at', 'status', 'assigned_worker')

class ScheduleSerializer(serializers.ModelSerializer):
tasks = TaskSerializer(many=True, read_only=True)

class Meta:
model = Schedule
fields = '__all__'
read_only_fields = ('household_user', 'created_at', 'updated_at')

class PackageSerializer(serializers.ModelSerializer):
tasks = TaskSerializer(many=True, read_only=True)

class Meta:
model = Package
fields = '__all__'





dronecontractor/serializers/common_serializers.py


from rest_framework import serializers
from ..models import ServiceCategory, ServiceSubCategory, Skill

class ServiceCategorySerializer(serializers.ModelSerializer):
class Meta:
model = ServiceCategory
fields = ('code', 'name', 'description', 'icon_class', 'is_active')

class ServiceSubCategorySerializer(serializers.ModelSerializer):
service_category = ServiceCategorySerializer(read_only=True)

class Meta:
model = ServiceSubCategory
fields = ('code', 'name', 'description', 'service_category', 'is_active')

class SkillSerializer(serializers.ModelSerializer):
category = serializers.SlugRelatedField(slug_field='name', queryset=SkillCategory.objects.all(), allow_null=True)

class Meta:
model = Skill
fields = ('name', 'description', 'category', 'is_verifiable')




dronecontractor/services/geo_matching_service.py


from django.contrib.gis.measure import Distance
from django.contrib.gis.db.models.functions import Distance as DistanceFunc
from ..models import GigWorker, Task, HouseholdRequest

def match_contractors(task_or_request, radius_km=50):
location = task_or_request.location_point
required_tier = task_or_request.required_tier if hasattr(task_or_request, 'required_tier') else 'COMMUNITY'
required_skills = task_or_request.required_skills.all() if hasattr(task_or_request, 'required_skills') else []

contractors = GigWorker.objects.filter(
tier__in=[required_tier, 'BONDED'] if required_tier != 'COMMUNITY' else ['COMMUNITY', 'BASIC'],
current_location__distance_lte=(location, Distance(km=radius_km))
).annotate(
distance=DistanceFunc('current_location', location)
).order_by('distance')

if required_skills:
contractors = contractors.filter(skills__in=required_skills).distinct()

return contractors[:10] # Top 10 closest matches



dronecontractor/services/payment_service.py

from django.db import transaction
from ..models import Assignment, TransactionLog, FeeStructure
from ..integrations.payment_gateway_clients import KazangClient

def process_escrow_payment(assignment, amount):
with transaction.atomic():
fee_structure = FeeStructure.objects.get(tier=assignment.task.required_tier, is_active=True)
service_fee = amount * (fee_structure.service_fee_percentage / 100)
escrow_fee = fee_structure.low_budget_escrow_fee if amount < 500 else fee_structure.escrow_fee_fixed

TransactionLog.objects.create(
user=assignment.task.bidder,
assignment=assignment,
transaction_type='ESCROW_FUND',
amount=amount + service_fee + escrow_fee,
status='SUCCESSFUL'
)

if assignment.payment_method_selected == 'CASH_KAZANG':
kazang_client = KazangClient()
kazang_client.initiate_cash_payout(
worker=assignment.assigned_worker,
amount=amount,
location_ref=assignment.kazang_payout_location_ref
)

TransactionLog.objects.create(
user=assignment.assigned_worker.user,
assignment=assignment,
transaction_type='PAYOUT_WORKER',
amount=amount,
status='PENDING'
)



dronecontractor/services/proof_service.py


from django.core.files.storage import default_storage
from ..models import Proof, ProofAttachment
from ..tasks.blockchain_tasks import submit_proof_to_blockchain

def process_proof_submission(assignment, data, files):
proof = Proof.objects.create(
assignment=assignment,
submission_type=data.get('submission_type', 'ONLINE'),
notes_by_worker=data.get('notes_by_worker'),
gps_latitude=data.get('gps_latitude'),
gps_longitude=data.get('gps_longitude'),
gps_timestamp=data.get('gps_timestamp')
)

for file in files:
file_path = default_storage.save(f'proof_attachments/{file.name}', file)
ProofAttachment.objects.create(
proof=proof,
file=file_path,
file_type=data.get('file_type', 'IMAGE'),
caption=data.get('caption')
)

submit_proof_to_blockchain.delay(proof.id)
return proof




dronecontractor/services/compliance_service.py


from ..models import Certification
from ..integrations.compliance_clients import SACAAClient, PSIRAClient, HPCSAClient

def verify_certification(certification):
cert_type = certification.certification_type.name.lower()
clients = {
'sacaa': SACAAClient(),
'psira': PSIRAClient(),
'hpcsa': HPCSAClient()
}

for key, client in clients.items():
if key in cert_type:
result = client.verify(
license_id=certification.license_id_number,
document=certification.document_scan
)
certification.verification_status = 'APPROVED' if result['valid'] else 'REJECTED'
certification.last_verified_on = timezone.now()
certification.save()
return result
return {'valid': False, 'error': 'No verification client found'}





dronecontractor/services/notification_service.py


from django.utils import timezone
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from ..models import Notification
from ..integrations.communication_clients import AfricasTalkingClient

def send_notification(users=None, user=None, group=None, notification_type=None, message_title=None, message_body=None, cta_link=None, channels=None):
if user:
users = [user]
if not users and not group:
return

notifications = []
for user in users or []:
notification = Notification.objects.create(
recipient_user=user,
notification_type=notification_type,
message_title=message_title,
message_body=message_body,
cta_link=cta_link,
delivery_channels=channels or ['IN_APP']
)
notifications.append(notification)

channel_layer = get_channel_layer()
for notification in notifications:
async_to_sync(channel_layer.group_send)(
f'user_{notification.recipient_user.id}',
{
'type': 'notification_message',
'notification_id': notification.id,
'notification_type': notification.notification_type,
'message_title': notification.message_title,
'message_body': notification.message_body,
'cta_link': notification.cta_link
}
)

if 'SMS' in notification.delivery_channels or 'WHATSAPP' in notification.delivery_channels:
africas_talking = AfricasTalkingClient()
africas_talking.send_message(
recipient=notification.recipient_user.phone_number,
message=notification.message_body,
channel='WHATSAPP' if 'WHATSAPP' in notification.delivery_channels else 'SMS'
)




dronecontractor/services/analytics_service.py


from django.db.models import Count, Avg
from ..models import Task, Assignment

def get_category_metrics(category_code, region=None):
tasks = Task.objects.filter(service_category__code=category_code)
if region:
tasks = tasks.filter(location_point__within=region)

return {
'total_tasks': tasks.count(),
'completion_rate': tasks.filter(task_status='COMPLETED').count() / tasks.count() * 100 if tasks.count() else 0,
'avg_budget': tasks.aggregate(Avg('budget_max'))['budget_max__avg'] or 0
}

def predict_task_demand(category_code, subcategory_code, region):
tasks = Task.objects.filter(service_category__code=category_code, service_subcategory__code=subcategory_code)
if region:
tasks = tasks.filter(location_point__within=region)

historical_count = tasks.count()
return {
'task_count_forecast': historical_count * 1.2, # Assume 20% growth
'avg_budget': tasks.aggregate(Avg('budget_max'))['budget_max__avg'] or 0
}



dronecontractor/services/dispute_service.py


from django.utils import timezone
from ..models import Dispute, DisputeEvidence, TransactionLog
from ..services.notification_service import send_notification

def initiate_dispute(assignment, raised_by_user, reason_category, reason_description, desired_outcome):
dispute = Dispute.objects.create(
assignment=assignment,
raised_by_user_type='BIDDER' if assignment.task.bidder == raised_by_user else 'CONTRACTOR',
raised_by_user=raised_by_user,
reason_category=reason_category,
reason_description=reason_description,
desired_outcome=desired_outcome
)

recipient = assignment.assigned_worker.user if raised_by_user == assignment.task.bidder else assignment.task.bidder
send_notification(
user=recipient,
notification_type='SYSTEM_ANNOUNCEMENT',
message_title='Dispute Raised',
message_body=f'A dispute has been raised for task: {assignment.task.title}.',
cta_link=f'/disputes/{dispute.id}',
channels=['IN_APP', 'EMAIL']
)
return dispute

def resolve_dispute(dispute, resolution_status, resolution_details, resolved_by_admin):
dispute.status = resolution_status
dispute.resolution_details = resolution_details
dispute.resolved_by_admin = resolved_by_admin
dispute.updated_at = timezone.now()
dispute.save()

if resolution_status == 'RESOLVED_FAVOR_BIDDER':
TransactionLog.objects.create(
user=dispute.assignment.task.bidder,
assignment=dispute.assignment,
transaction_type='REFUND_BIDDER',
amount=dispute.assignment.agreed_budget,
status='PENDING'
)



dronecontractor/tasks/compliance_tasks.py



from celery import shared_task
from ..models import Certification
from ..services.compliance_service import verify_certification

@shared_task
def async_verify_certification(certification_id):
try:
certification = Certification.objects.get(id=certification_id)
verify_certification(certification)
except Certification.DoesNotExist:
pass



dronecontractor/tasks/notification_tasks.py


from celery import shared_task
from ..services.notification_service import send_notification

@shared_task
def async_send_notification(notification_id):
try:
notification = Notification.objects.get(id=notification_id)
send_notification(
user=notification.recipient_user,
notification_type=notification.notification_type,
message_title=notification.message_title,
message_body=notification.message_body,
cta_link=notification.cta_link,
channels=notification.delivery_channels
)
notification.status = 'SENT'
notification.sent_at = timezone.now()
notification.save()
except Notification.DoesNotExist:
pass



dronecontractor/tasks/blockchain_tasks.py


from celery import shared_task
from ..models import Proof
from ..integrations.blockchain_client import GovChainClient

@shared_task
def submit_proof_to_blockchain(proof_id):
try:
proof = Proof.objects.get(id=proof_id)
govchain = GovChainClient()
result = govchain.submit_proof(
data={
'assignment_id': proof.assignment.id,
'gps_latitude': proof.gps_latitude,
'gps_longitude': proof.gps_longitude,
'notes': proof.notes_by_worker
}
)
proof.blockchain_hash = result['hash']
proof.blockchain_transaction_id = result['transaction_id']
proof.save()
except Proof.DoesNotExist:
pass




dronecontractor/tasks/data_processing_tasks.py

from celery import shared_task
from ..services.analytics_service import get_category_metrics, predict_task_demand

@shared_task
def generate_category_report(category_code):
metrics = get_category_metrics(category_code)
# Save report to storage or database
return metrics

@shared_task
def update_demand_forecasts(category_code, subcategory_code):
forecast = predict_task_demand(category_code, subcategory_code, None)
# Store forecast in database or cache
return forecast



dronecontractor/search_indexes.py


from haystack import indexes
from .models import Task, GigWorker, ServiceCategory, ServiceSubCategory

class TaskIndex(indexes.SearchIndex, indexes.Indexable):
"""
Search index for Task model, enabling full-text and geospatial search for tasks.
"""
text = indexes.CharField(document=True, use_template=True) # Primary search field
title = indexes.CharField(model_attr='title')
description = indexes.CharField(model_attr='description')
service_category = indexes.CharField(model_attr='service_category__name')
service_subcategory = indexes.CharField(model_attr='service_subcategory__name')
cpv_code = indexes.CharField(model_attr='cpv_code', null=True)
task_status = indexes.CharField(model_attr='task_status')
location = indexes.LocationField(model_attr='location_point')
budget_max = indexes.DecimalField(model_attr='budget_max')
required_tier = indexes.CharField(model_attr='required_tier')
is_drone_task = indexes.BooleanField(model_attr='is_drone_task')
created_at = indexes.DateTimeField(model_attr='created_at')

def get_model(self):
return Task

def index_queryset(self, using=None):
# Index only active tasks (OPEN, ASSIGNED, IN_PROGRESS)
return self.get_model().objects.filter(
task_status__in=['OPEN', 'ASSIGNED', 'IN_PROGRESS']
)

def prepare_text(self, obj):
# Combine fields for full-text search
return f"{obj.title} {obj.description} {obj.service_category.name} {obj.service_subcategory.name}"

class GigWorkerIndex(indexes.SearchIndex, indexes.Indexable):
"""
Search index for GigWorker model, enabling search by skills, certifications, and location.
"""
text = indexes.CharField(document=True, use_template=True)
username = indexes.CharField(model_attr='user__username')
bio = indexes.CharField(model_attr='bio', null=True)
skills = indexes.MultiValueField()
certifications = indexes.MultiValueField()
tier = indexes.CharField(model_attr='tier')
rating_average = indexes.FloatField(model_attr='rating_average')
location = indexes.LocationField(model_attr='current_location', null=True)
drone_task_capable = indexes.BooleanField(model_attr='drone_task_capable')
onboarding_completed = indexes.BooleanField(model_attr='onboarding_completed')

def get_model(self):
return GigWorker

def index_queryset(self, using=None):
# Index only onboarded workers
return self.get_model().objects.filter(onboarding_completed=True)

def prepare_skills(self, obj):
return [skill.name for skill in obj.skills.all()]

def prepare_certifications(self, obj):
return [cert.certification_type.name for cert in obj.detailed_certifications.all()]

def prepare_text(self, obj):
# Combine fields for full-text search
skills = ' '.join(self.prepare_skills(obj))
certs = ' '.join(self.prepare_certifications(obj))
return f"{obj.user.username} {obj.bio or ''} {skills} {certs}"



dronecontractor/urls.py


from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
TaskViewSet, TaskApplicationViewSet, AssignmentViewSet, GigWorkerViewSet,
HouseholdRequestViewSet, ScheduleViewSet, PackageViewSet, ServiceCategoryViewSet,
ServiceSubCategoryViewSet, SkillViewSet, CertificationVerificationView
)

router = DefaultRouter()
router.register(r'tasks', TaskViewSet, basename='task')
router.register(r'applications', TaskApplicationViewSet, basename='application')
router.register(r'assignments', AssignmentViewSet, basename='assignment')
router.register(r'workers', GigWorkerViewSet, basename='worker')
router.register(r'household-requests', HouseholdRequestViewSet, basename='household-request')
router.register(r'schedules', ScheduleViewSet, basename='schedule')
router.register(r'packages', PackageViewSet, basename='package')
router.register(r'categories', ServiceCategoryViewSet, basename='category')
router.register(r'subcategories', ServiceSubCategoryViewSet, basename='subcategory')
router.register(r'skills', SkillViewSet, basename='skill')

urlpatterns = [
path('', include(router.urls)),
path('certification/verify/', CertificationVerificationView.as_view(), name='certification-verify'),
]




dronecontractor/tests/factories.py

import factory
from django.contrib.auth.models import User
from django.contrib.gis.geos import Point
from ..models import (
BidderProfile, GigWorker, ServiceCategory, ServiceSubCategory, Skill,
Task, TaskApplication, Assignment, HouseholdRequest
)

class UserFactory(factory.django.DjangoModelFactory):
class Meta:
model = User

username = factory.Sequence(lambda n: f'user_{n}')
email = factory.LazyAttribute(lambda o: f'{o.username}@example.com')
password = factory.PostGenerationMethodCall('set_password', 'password123')

class BidderProfileFactory(factory.django.DjangoModelFactory):
class Meta:
model = BidderProfile

user = factory.SubFactory(UserFactory)
user_type = 'HOUSEHOLD'

class GigWorkerFactory(factory.django.DjangoModelFactory):
class Meta:
model = GigWorker

user = factory.SubFactory(UserFactory)
tier = 'COMMUNITY'
current_location = Point(28.123, -26.456, srid=4326)

class ServiceCategoryFactory(factory.django.DjangoModelFactory):
class Meta:
model = ServiceCategory

code = factory.Sequence(lambda n: f'CAT_{n}')
name = factory.Sequence(lambda n: f'Category {n}')

class ServiceSubCategoryFactory(factory.django.DjangoModelFactory):
class Meta:
model = ServiceSubCategory

service_category = factory.SubFactory(ServiceCategoryFactory)
code = factory.Sequence(lambda n: f'SUBCAT_{n}')
name = factory.Sequence(lambda n: f'Subcategory {n}')

class SkillFactory(factory.django.DjangoModelFactory):
class Meta:
model = Skill

name = factory.Sequence(lambda n: f'Skill_{n}')

class TaskFactory(factory.django.DjangoModelFactory):
class Meta:
model = Task

bidder = factory.SubFactory(UserFactory)
title = factory.Sequence(lambda n: f'Task {n}')
service_category = factory.SubFactory(ServiceCategoryFactory)
service_subcategory = factory.SubFactory(ServiceSubCategoryFactory)
description = 'Sample task description'
location_point = Point(28.123, -26.456, srid=4326)
budget_max = 1000.00
deadline_date = factory.Faker('future_datetime')

class TaskApplicationFactory(factory.django.DjangoModelFactory):
class Meta:
model = TaskApplication

task = factory.SubFactory(TaskFactory)
gig_worker = factory.SubFactory(GigWorkerFactory)
proposed_rate = 800.00

class AssignmentFactory(factory.django.DjangoModelFactory):
class Meta:
model = Assignment

task = factory.SubFactory(TaskFactory)
assigned_worker = factory.SubFactory(GigWorkerFactory)
agreed_budget = 800.00

class HouseholdRequestFactory(factory.django.DjangoModelFactory):
class Meta:
model = HouseholdRequest

household_user = factory.SubFactory(UserFactory)
service_category = factory.SubFactory(ServiceCategoryFactory)
service_subcategory = factory.SubFactory(ServiceSubCategoryFactory)
description = 'Household service request'
location_point = Point(28.789, -26.123, srid=4326)
budget = 300.00
preferred_datetime_start = factory.Faker('future_datetime')





private_sector_app/ Directory
private_sector_app/init.py

# Empty file

private_sector_app/urls.py

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import PrivateTaskViewSet, PrivateAnalyticsView

router = DefaultRouter()
router.register(r'tasks', PrivateTaskViewSet, basename='private-task')

urlpatterns = [
path('', include(router.urls)),
path('analytics/', PrivateAnalyticsView.as_view(), name='private-analytics'),
]




private_sector_app/views.py


from rest_framework import viewsets, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from dronecontractor.models import Task
from dronecontractor.serializers import TaskSerializer
from dronecontractor.services.analytics_service import predict_task_demand

class PrivateTaskViewSet(viewsets.ModelViewSet):
"""
API for private sector tasks, filtering for enterprise users.
"""
queryset = Task.objects.filter(bidder__bidder_profile__user_type='ENTERPRISE')
serializer_class = TaskSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(bidder=self.request.user)

def perform_create(self, serializer):
serializer.save(bidder=self.request.user)

class PrivateAnalyticsView(APIView):
"""
Predictive analytics for private sector task demand.
"""
permission_classes = [permissions.IsAuthenticated]

def get(self, request):
category_code = request.query_params.get('category_code')
subcategory_code = request.query_params.get('subcategory_code')
if not category_code or not subcategory_code:
return Response({'error': 'Category and subcategory required'}, status=400)
forecast = predict_task_demand(category_code, subcategory_code, None)
return Response(forecast)




household_app/ Directory
household_app/init.py


# Empty file


household_app/urls.py


from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import HouseholdRequestViewSet, HouseholdScheduleViewSet

router = DefaultRouter()
router.register(r'requests', HouseholdRequestViewSet, basename='household-request')
router.register(r'schedules', HouseholdScheduleViewSet, basename='household-schedule')

urlpatterns = [
path('', include(router.urls)),
]



household_app/views.py


from rest_framework import viewsets, permissions
from dronecontractor.models import HouseholdRequest, Schedule
from dronecontractor.serializers import HouseholdRequestSerializer, ScheduleSerializer

class HouseholdRequestViewSet(viewsets.ModelViewSet):
"""
API for household service requests, filtering for HOUSEHOLD users.
"""
queryset = HouseholdRequest.objects.filter(household_user__bidder_profile__user_type='HOUSEHOLD')
serializer_class = HouseholdRequestSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(household_user=self.request.user)

def perform_create(self, serializer):
serializer.save(household_user=self.request.user)

class HouseholdScheduleViewSet(viewsets.ModelViewSet):
"""
API for household task schedules.
"""
queryset = Schedule.objects.all()
serializer_class = ScheduleSerializer
permission_classes = [permissions.IsAuthenticated]

def get_queryset(self):
return self.queryset.filter(household_user=self.request.user)



integrations/ Directory
integrations/init.py


from .national_treasury_client import NationalTreasuryClient
from .compliance_clients import SACAAClient, PSIRAClient, HPCSAClient
from .payment_gateway_clients import KazangClient
from .blockchain_client import GovChainClient
from .communication_clients import AfricasTalkingClient
from .drone_api_clients import RocketMineClient, GLOBHEClient



integrations/national_treasury_client.py


import requests
from decouple import config

class NationalTreasuryClient:
"""
Client for National Treasury eTenders API integration.
"""
def __init__(self):
self.base_url = 'https://etenders.treasury.gov.za/api'
self.api_key = config('NATIONAL_TREASURY_API_KEY')

def fetch_tender(self, reference):
response = requests.get(
f'{self.base_url}/tenders/{reference}',
headers={'Authorization': f'Bearer {self.api_key}'}
)
response.raise_for_status()
return response.json()



integrations/compliance_clients.py


import requests
from decouple import config

class SACAAClient:
def __init__(self):
self.base_url = 'https://sacaacompliance.api'
self.api_key = config('SACAA_API_KEY')

def verify(self, license_id, document=None):
response = requests.post(
f'{self.base_url}/verify',
json={'license_id': license_id},
headers={'Authorization': f'Bearer {self.api_key}'}
)
return response.json()

class PSIRAClient:
def __init__(self):
self.base_url = 'https://psiracompliance.api'
self.api_key = config('PSIRA_API_KEY')

def verify(self, license_id, document=None):
response = requests.post(
f'{self.base_url}/verify',
json={'license_id': license_id},
headers={'Authorization': f'Bearer {self.api_key}'}
)
return response.json()

class HPCSAClient:
def __init__(self):
self.base_url = 'https://hpcsacompliance.api'
self.api_key = config('HPCSA_API_KEY')

def verify(self, license_id, document=None):
response = requests.post(
f'{self.base_url}/verify',
json={'license_id': license_id},
headers={'Authorization': f'Bearer {self.api_key}'}
)
return response.json()



integrations/payment_gateway_clients.py

import requests
from decouple import config

class KazangClient:
"""
Client for Kazang cash payment integration.
"""
def __init__(self):
self.base_url = 'https://kazang.api'
self.api_key = config('KAZANG_API_KEY')

def initiate_cash_payout(self, worker, amount, location_ref):
response = requests.post(
f'{self.base_url}/payouts',
json={
'recipient_phone': worker.user.phone_number,
'amount': amount,
'location_ref': location_ref
},
headers={'Authorization': f'Bearer {self.api_key}'}
)
response.raise_for_status()
return response.json()




integrations/blockchain_client.py

import requests
from decouple import config

class GovChainClient:
"""
Client for SA GovChain blockchain proof submission.
"""
def __init__(self):
self.base_url = 'https://govchain.api'
self.api_key = config('GOVCHAIN_API_KEY')

def submit_proof(self, data):
response = requests.post(
f'{self.base_url}/proofs',
json=data,
headers={'Authorization': f'Bearer {self.api_key}'}
)
response.raise_for_status()
return response.json()



integrations/communication_clients.py



import requests
from decouple import config

class AfricasTalkingClient:
"""
Client for Africa's Talking USSD/SMS/WhatsApp integration.
"""
def __init__(self):
self.base_url = 'https://api.africastalking.com'
self.api_key = config('AFRICASTALKING_API_KEY')

def send_message(self, recipient, message, channel='SMS'):
endpoint = '/messaging' if channel == 'SMS' else '/whatsapp'
response = requests.post(
f'{self.base_url}{endpoint}',
json={
'to': recipient,
'message': message
},
headers={'Authorization': f'Bearer {self.api_key}'}
)
response.raise_for_status()
return response.json()



integrations/drone_api_clients.py



import requests
from decouple import config

class RocketMineClient:
"""
Client for RocketMine drone task integration.
"""
def __init__(self):
self.base_url = 'https://rocketmine.api'
self.api_key = config('ROCKETMINE_API_KEY')

def schedule_drone_task(self, task_type, location, params):
response = requests.post(
f'{self.base_url}/tasks',
json={
'task_type': task_type,
'location': location,
'params': params
},
headers={'Authorization': f'Bearer {self.api_key}'}
)
response.raise_for_status()
return response.json()

class GLOBHEClient:
"""
Client for GLOBHE drone task integration.
"""
def __init__(self):
self.base_url = 'https://globhe.api'
self.api_key = config('GLOBHE_API_KEY')

def schedule_drone_task(self, task_type, location, params):
response = requests.post(
f'{self.base_url}/tasks',
json={
'task_type': task_type,
'location': location,
'params': params
},
headers={'Authorization': f'Bearer {self.api_key}'}
)
response.raise_for_status()
return response.json()







scripts/ Directory
scripts/init.py



# Empty file



scripts/seed_master_data.py


from django.core.management.base import BaseCommand
from dronecontractor.models import ServiceCategory, ServiceSubCategory, SkillCategory, Skill

class Command(BaseCommand):
help = 'Seeds master data for categories, subcategories, and skills'

def handle(self, *args, **kwargs):
# Seed Service Categories
categories = [
{'code': 'CONSTRUCTION', 'name': 'Construction'},
{'code': 'ICT', 'name': 'Information and Communication Technology'},
# Add all 15 eTenders categories
]
for cat in categories:
ServiceCategory.objects.get_or_create(code=cat['code'], defaults={'name': cat['name']})

# Seed Service Subcategories
subcategories = {
'CONSTRUCTION': [
{'code': 'CIVIL_ENGINEERING', 'name': 'Civil Engineering'},
{'code': 'BUILDING_MATERIALS', 'name': 'Building Materials'},
],
# Add subcategories for all categories
}
for cat_code, subs in subcategories.items():
category = ServiceCategory.objects.get(code=cat_code)
for sub in subs:
ServiceSubCategory.objects.get_or_create(
service_category=category, code=sub['code'], defaults={'name': sub['name']}
)

# Seed Skill Categories and Skills
skill_cat = SkillCategory.objects.get_or_create(name='Technical Skills')[0]
Skill.objects.get_or_create(name='Network Installation', category=skill_cat)
Skill.objects.get_or_create(name='Plumbing', category=skill_cat)

self.stdout.write(self.style.SUCCESS('Master data seeded successfully'))




scripts/seed_test_data.py

from django.core.management.base import BaseCommand
from dronecontractor.tests.factories import (
UserFactory, BidderProfileFactory, GigWorkerFactory, TaskFactory,
HouseholdRequestFactory, AssignmentFactory
)

class Command(BaseCommand):
help = 'Seeds test data for development and testing'

def handle(self, *args, **kwargs):
# Create users and profiles
bidder = UserFactory()
BidderProfileFactory(user=bidder, user_type='ENTERPRISE')
household = UserFactory()
BidderProfileFactory(user=household, user_type='HOUSEHOLD')
worker = UserFactory()
GigWorkerFactory(user=worker, tier='COMMUNITY')

# Create tasks and requests
task = TaskFactory(bidder=bidder)
request = HouseholdRequestFactory(household_user=household)
AssignmentFactory(task=task, assigned_worker=worker)

self.stdout.write(self.style.SUCCESS('Test data seeded successfully'))





scripts/custom_migration_scripts.py


from django.core.management.base import BaseCommand
from dronecontractor.models import ServiceCategory

class Command(BaseCommand):
help = 'Custom data migration for updating category codes'

def handle(self, *args, **kwargs):
# Example migration: Update legacy category codes
ServiceCategory.objects.filter(code='OLD_CODE').update(code='NEW_CODE')
self.stdout.write(self.style.SUCCESS('Custom migration completed'))




docs/ Directory
docs/openapi-schema.yml



openapi: 3.0.3
info:
title: Bidbeez DroneContractor API
version: 1.0.0
description: API for the DroneContractor module, supporting private sector and household tasks.
paths:
/api/dronecontractor/tasks/:
post:
summary: Create a new task
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
title: { type: string }
service_category: { type: string }
service_subcategory: { type: string }
description: { type: string }
location_point: { type: object, properties: { lat: { type: number }, lng: { type: number } } }
budget_max: { type: number }
deadline_date: { type: string, format: date-time }
cpv_code: { type: string }
is_drone_task: { type: boolean }
responses:
'201':
description: Task created
/api/household/requests/:
post:
summary: Create a household service request
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
service_category: { type: string }
service_subcategory: { type: string }
description: { type: string }
location_point: { type: object, properties: { lat: { type: number }, lng: { type: number } } }
budget: { type: number }
preferred_datetime_start: { type: string, format: date-time }
responses:
'201':
description: Request created
components:
securitySchemes:
BearerAuth:
type: http
scheme: bearer
security:
- BearerAuth: []



SQL query tables


1. Tables from user_related.py


-- Table for BidderProfile (extends User with enterprise/household details)
CREATE TABLE dronecontractor_bidderprofile (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT NOT NULL UNIQUE REFERENCES auth_user(id) ON DELETE CASCADE,
user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('ENTERPRISE', 'HOUSEHOLD')) DEFAULT 'ENTERPRISE',
company_name VARCHAR(255),
company_registration_number VARCHAR(100),
company_vat_number VARCHAR(50),
company_address TEXT,
contact_person_name VARCHAR(255),
onboarding_completed BOOLEAN NOT NULL DEFAULT FALSE,
preferred_dashboard VARCHAR(50) CHECK (preferred_dashboard IN ('STANDARD', 'ADVANCED')) DEFAULT 'STANDARD',
profile_picture VARCHAR(100),
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_bidderprofile_user_id_idx ON dronecontractor_bidderprofile(user_id);
CREATE INDEX dronecontractor_bidderprofile_user_type_idx ON dronecontractor_bidderprofile(user_type);

-- Table for GigWorker (contractor details, renamed from DroneContractor)
CREATE TABLE dronecontractor_gigworker (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT NOT NULL UNIQUE REFERENCES auth_user(id) ON DELETE CASCADE,
tier VARCHAR(10) NOT NULL CHECK (tier IN ('BASIC', 'ADVANCED', 'BONDED', 'COMMUNITY')) DEFAULT 'BASIC',
bio TEXT,
profile_picture VARCHAR(100),
phone_number_verified BOOLEAN NOT NULL DEFAULT FALSE,
email_verified BOOLEAN NOT NULL DEFAULT FALSE,
bakkie_available BOOLEAN NOT NULL DEFAULT FALSE,
current_location GEOGRAPHY(POINT),
service_radius_km INTEGER NOT NULL DEFAULT 50 CHECK (service_radius_km > 0),
preferred_service_areas GEOGRAPHY(MULTIPOLYGON),
rating_average DOUBLE PRECISION NOT NULL DEFAULT 5.0,
total_ratings INTEGER NOT NULL DEFAULT 0 CHECK (total_ratings >= 0),
saps_clearance_doc VARCHAR(100),
saps_clearance_verified BOOLEAN NOT NULL DEFAULT FALSE,
saps_clearance_expiry DATE,
psira_license_number VARCHAR(50),
psira_expiry DATE,
license_status VARCHAR(20) NOT NULL CHECK (license_status IN ('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED')) DEFAULT 'PENDING',
drone_task_capable BOOLEAN NOT NULL DEFAULT FALSE,
onboarding_completed BOOLEAN NOT NULL DEFAULT FALSE,
last_seen TIMESTAMP WITH TIME ZONE,
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_gigworker_user_id_idx ON dronecontractor_gigworker(user_id);
CREATE INDEX dronecontractor_gigworker_tier_idx ON dronecontractor_gigworker(tier);
CREATE INDEX dronecontractor_gigworker_current_location_idx ON dronecontractor_gigworker USING GIST (current_location);




2. Tables from skills_certs.py

-- Table for SkillCategory (groups skills, e.g., Technical Skills)
CREATE TABLE dronecontractor_skillcategory (
id BIGSERIAL PRIMARY KEY,
name VARCHAR(100) NOT NULL UNIQUE,
description TEXT
);
CREATE INDEX dronecontractor_skillcategory_name_idx ON dronecontractor_skillcategory(name);

-- Table for Skill (specific skills, e.g., Network Installation)
CREATE TABLE dronecontractor_skill (
id BIGSERIAL PRIMARY KEY,
category_id BIGINT REFERENCES dronecontractor_skillcategory(id) ON DELETE SET NULL,
name VARCHAR(100) NOT NULL UNIQUE,
description TEXT,
is_verifiable BOOLEAN NOT NULL DEFAULT FALSE
);
CREATE INDEX dronecontractor_skill_name_idx ON dronecontractor_skill(name);

-- ManyToMany table for GigWorker-Skill relationship
CREATE TABLE dronecontractor_gigworker_skills (
id BIGSERIAL PRIMARY KEY,
gigworker_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
skill_id BIGINT NOT NULL REFERENCES dronecontractor_skill(id) ON DELETE CASCADE,
UNIQUE (gigworker_id, skill_id)
);

-- Table for GigWorkerSkillEndorsement (bidder endorsements of worker skills)
CREATE TABLE dronecontractor_gigworkerskillendorsement (
id BIGSERIAL PRIMARY KEY,
skill_id BIGINT NOT NULL REFERENCES dronecontractor_skill(id) ON DELETE CASCADE,
endorsed_by_bidder_id BIGINT NOT NULL REFERENCES dronecontractor_bidderprofile(id) ON DELETE CASCADE,
gig_worker_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
task_id BIGINT REFERENCES dronecontractor_task(id) ON DELETE SET NULL,
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
UNIQUE (skill_id, gig_worker_id, endorsed_by_bidder_id)
);

-- Table for CertificationType (e.g., SACAA RPL, PSIRA Grade A)
CREATE TABLE dronecontractor_certificationtype (
id BIGSERIAL PRIMARY KEY,
name VARCHAR(255) NOT NULL UNIQUE,
issuing_body_organisation VARCHAR(255),
description TEXT,
is_api_verifiable BOOLEAN NOT NULL DEFAULT FALSE,
verification_api_endpoint VARCHAR(200)
);
CREATE INDEX dronecontractor_certificationtype_name_idx ON dronecontractor_certificationtype(name);

-- Table for Certification (worker-specific certifications)
CREATE TABLE dronecontractor_certification (
id BIGSERIAL PRIMARY KEY,
gig_worker_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
certification_type_id BIGINT NOT NULL REFERENCES dronecontractor_certificationtype(id) ON DELETE NO ACTION,
license_id_number VARCHAR(100) NOT NULL,
issued_date DATE,
expiry_date DATE,
document_scan VARCHAR(100),
verification_status VARCHAR(20) NOT NULL CHECK (verification_status IN ('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED')) DEFAULT 'PENDING',
verified_by_admin_id BIGINT REFERENCES auth_user(id) ON DELETE SET NULL,
last_verified_on TIMESTAMP WITH TIME ZONE,
notes TEXT
);
CREATE INDEX dronecontractor_certification_gig_worker_idx ON dronecontractor_certification(gig_worker_id);
CREATE INDEX dronecontractor_certification_verification_status_idx ON dronecontractor_certification(verification_status);

-- ManyToMany table for GigWorker-Certification relationship
CREATE TABLE dronecontractor_gigworker_certifications (
id BIGSERIAL PRIMARY KEY,
gigworker_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
certification_id BIGINT NOT NULL REFERENCES dronecontractor_certification(id) ON DELETE CASCADE,
UNIQUE (gigworker_id, certification_id)
);




-- Table for ServiceCategory (eTenders categories, e.g., CONSTRUCTION)
CREATE TABLE dronecontractor_servicecategory (
id BIGSERIAL PRIMARY KEY,
code VARCHAR(30) NOT NULL UNIQUE,
name VARCHAR(100) NOT NULL,
description TEXT,
icon_class VARCHAR(50),
is_active BOOLEAN NOT NULL DEFAULT TRUE
);
CREATE INDEX dronecontractor_servicecategory_code_idx ON dronecontractor_servicecategory(code);

-- Table for ServiceSubCategory (eTenders subcategories, e.g., Civil Engineering)
CREATE TABLE dronecontractor_servicesubcategory (
id BIGSERIAL PRIMARY KEY,
service_category_id BIGINT NOT NULL REFERENCES dronecontractor_servicecategory(id) ON DELETE CASCADE,
code VARCHAR(50) NOT NULL UNIQUE,
name VARCHAR(100) NOT NULL,
description TEXT,
is_active BOOLEAN NOT NULL DEFAULT TRUE,
UNIQUE (service_category_id, name)
);
CREATE INDEX dronecontractor_servicesubcategory_service_category_idx ON dronecontractor_servicesubcategory(service_category_id);

-- ManyToMany table for BidderProfile-ServiceCategory (primary categories)
CREATE TABLE dronecontractor_bidderprofile_primary_service_categories (
id BIGSERIAL PRIMARY KEY,
bidderprofile_id BIGINT NOT NULL REFERENCES dronecontractor_bidderprofile(id) ON DELETE CASCADE,
servicecategory_id BIGINT NOT NULL REFERENCES dronecontractor_servicecategory(id) ON DELETE CASCADE,
UNIQUE (bidderprofile_id, servicecategory_id)
);

-- ManyToMany table for BidderProfile-ServiceCategory (secondary categories)
CREATE TABLE dronecontractor_bidderprofile_secondary_service_categories (
id BIGSERIAL PRIMARY KEY,
bidderprofile_id BIGINT NOT NULL REFERENCES dronecontractor_bidderprofile(id) ON DELETE CASCADE,
servicecategory_id BIGINT NOT NULL REFERENCES dronecontractor_servicecategory(id) ON DELETE CASCADE,
UNIQUE (bidderprofile_id, servicecategory_id)
);




-- Table for Task (eTenders tasks)
CREATE TABLE dronecontractor_task (
id BIGSERIAL PRIMARY KEY,
bidder_id BIGINT NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
tender_reference VARCHAR(100),
title VARCHAR(255) NOT NULL,
service_category_id BIGINT NOT NULL REFERENCES dronecontractor_servicecategory(id) ON DELETE NO ACTION,
service_subcategory_id BIGINT NOT NULL REFERENCES dronecontractor_servicesubcategory(id) ON DELETE NO ACTION,
description TEXT NOT NULL,
location_point GEOGRAPHY(POINT) NOT NULL,
location_address_text VARCHAR(500),
required_tier VARCHAR(10) NOT NULL CHECK (required_tier IN ('BASIC', 'ADVANCED', 'BONDED', 'COMMUNITY')) DEFAULT 'BASIC',
budget_min NUMERIC(12,2),
budget_max NUMERIC(12,2) NOT NULL,
deadline_date TIMESTAMP WITH TIME ZONE NOT NULL,
cpv_code VARCHAR(20),
is_drone_task BOOLEAN NOT NULL DEFAULT FALSE,
drone_task_type VARCHAR(20) CHECK (drone_task_type IN ('SURVEY', 'SPRAYING', 'SURVEILLANCE', 'DELIVERY', 'OTHER')),
task_status VARCHAR(20) NOT NULL CHECK (task_status IN ('OPEN', 'ASSIGNED', 'IN_PROGRESS', 'PENDING_REVIEW', 'COMPLETED', 'CANCELLED_BIDDER', 'CANCELLED_WORKER', 'DISPUTED', 'EXPIRED')) DEFAULT 'OPEN',
notification_status_to_workers VARCHAR(20) NOT NULL CHECK (notification_status_to_workers IN ('PENDING', 'SENT', 'VIEWED', 'ACCEPTED')) DEFAULT 'PENDING',
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_task_bidder_id_idx ON dronecontractor_task(bidder_id);
CREATE INDEX dronecontractor_task_service_category_idx ON dronecontractor_task(service_category_id);
CREATE INDEX dronecontractor_task_task_status_idx ON dronecontractor_task(task_status);
CREATE INDEX dronecontractor_task_location_point_idx ON dronecontractor_task USING GIST (location_point);

-- ManyToMany table for Task-Skill relationship
CREATE TABLE dronecontractor_task_required_skills (
id BIGSERIAL PRIMARY KEY,
task_id BIGINT NOT NULL REFERENCES dronecontractor_task(id) ON DELETE CASCADE,
skill_id BIGINT NOT NULL REFERENCES dronecontractor_skill(id) ON DELETE CASCADE,
UNIQUE (task_id, skill_id)
);

-- Table for TaskAttachment (task-related files)
CREATE TABLE dronecontractor_taskattachment (
id BIGSERIAL PRIMARY KEY,
task_id BIGINT NOT NULL REFERENCES dronecontractor_task(id) ON DELETE CASCADE,
file VARCHAR(100) NOT NULL,
description VARCHAR(255),
uploaded_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_taskattachment_task_id_idx ON dronecontractor_taskattachment(task_id);

-- Table for TaskApplication (worker applications for tasks)
CREATE TABLE dronecontractor_taskapplication (
id BIGSERIAL PRIMARY KEY,
task_id BIGINT NOT NULL REFERENCES dronecontractor_task(id) ON DELETE CASCADE,
gig_worker_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
cover_letter TEXT,
proposed_rate NUMERIC(10,2),
estimated_completion_time_hours INTEGER,
status VARCHAR(20) NOT NULL CHECK (status IN ('APPLIED', 'SHORTLISTED', 'REJECTED', 'HIRED')) DEFAULT 'APPLIED',
applied_at TIMESTAMP WITH TIME ZONE NOT NULL,
UNIQUE (task_id, gig_worker_id)
);
CREATE INDEX dronecontractor_taskapplication_task_id_idx ON dronecontractor_taskapplication(task_id);

-- Table for Assignment (task assignments to workers)
CREATE TABLE dronecontractor_assignment (
id BIGSERIAL PRIMARY KEY,
task_id BIGINT NOT NULL UNIQUE REFERENCES dronecontractor_task(id) ON DELETE CASCADE,
assigned_worker_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE NO ACTION,
agreed_budget NUMERIC(10,2) NOT NULL,
status VARCHAR(30) NOT NULL CHECK (status IN ('PENDING_ACCEPTANCE', 'ACCEPTED', 'IN_PROGRESS', 'SUBMITTED_PROOF', 'COMPLETED', 'DISPUTED', 'CANCELLED')) DEFAULT 'PENDING_ACCEPTANCE',
payment_method_selected VARCHAR(20) NOT NULL CHECK (payment_method_selected IN ('DIGITAL_ESCROW', 'CASH_KAZANG')) DEFAULT 'DIGITAL_ESCROW',
kazang_payout_location_ref VARCHAR(100),
assigned_at TIMESTAMP WITH TIME ZONE NOT NULL,
expected_start_time TIMESTAMP WITH TIME ZONE,
actual_start_time TIMESTAMP WITH TIME ZONE,
expected_completion_time TIMESTAMP WITH TIME ZONE,
actual_completion_time TIMESTAMP WITH TIME ZONE,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_assignment_task_id_idx ON dronecontractor_assignment(task_id);
CREATE INDEX dronecontractor_assignment_assigned_worker_idx ON dronecontractor_assignment(assigned_worker_id);

-- Table for HouseholdRequest (simplified household service requests)
CREATE TABLE dronecontractor_householdrequest (
id BIGSERIAL PRIMARY KEY,
household_user_id BIGINT NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
service_category_id BIGINT NOT NULL REFERENCES dronecontractor_servicecategory(id) ON DELETE NO ACTION,
service_subcategory_id BIGINT NOT NULL REFERENCES dronecontractor_servicesubcategory(id) ON DELETE NO ACTION,
description TEXT NOT NULL,
location_point GEOGRAPHY(POINT) NOT NULL,
location_address_text VARCHAR(500),
budget NUMERIC(10,2) NOT NULL,
preferred_datetime_start TIMESTAMP WITH TIME ZONE NOT NULL,
preferred_datetime_end TIMESTAMP WITH TIME ZONE,
status VARCHAR(20) NOT NULL CHECK (status IN ('OPEN', 'MATCHED', 'COMPLETED', 'CANCELLED')) DEFAULT 'OPEN',
assigned_worker_id BIGINT REFERENCES dronecontractor_gigworker(id) ON DELETE SET NULL,
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_householdrequest_household_user_idx ON dronecontractor_householdrequest(household_user_id);
CREATE INDEX dronecontractor_householdrequest_location_point_idx ON dronecontractor_householdrequest USING GIST (location_point);





-- Table for Proof (task completion evidence)
CREATE TABLE dronecontractor_proof (
id BIGSERIAL PRIMARY KEY,
assignment_id BIGINT NOT NULL UNIQUE REFERENCES dronecontractor_assignment(id) ON DELETE CASCADE,
submitted_at TIMESTAMP WITH TIME ZONE NOT NULL,
submission_type VARCHAR(30) NOT NULL CHECK (submission_type IN ('ONLINE', 'OFFLINE_PENDING_SYNC', 'OFFLINE_MANUAL_REVIEW')) DEFAULT 'ONLINE',
notes_by_worker TEXT,
gps_latitude NUMERIC(9,6),
gps_longitude NUMERIC(9,6),
gps_timestamp TIMESTAMP WITH TIME ZONE,
blockchain_hash VARCHAR(256),
blockchain_transaction_id VARCHAR(256),
verification_status VARCHAR(20) NOT NULL CHECK (verification_status IN ('PENDING', 'APPROVED', 'REJECTED')) DEFAULT 'PENDING',
verified_by_bidder_id BIGINT REFERENCES auth_user(id) ON DELETE SET NULL,
verified_at TIMESTAMP WITH TIME ZONE,
bidder_comments TEXT
);
CREATE INDEX dronecontractor_proof_assignment_id_idx ON dronecontractor_proof(assignment_id);

-- Table for ProofAttachment (proof media files)
CREATE TABLE dronecontractor_proofattachment (
id BIGSERIAL PRIMARY KEY,
proof_id BIGINT NOT NULL REFERENCES dronecontractor_proof(id) ON DELETE CASCADE,
file VARCHAR(100) NOT NULL,
file_type VARCHAR(20) NOT NULL CHECK (file_type IN ('IMAGE', 'VIDEO', 'DOCUMENT')),
caption VARCHAR(255),
uploaded_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_proofattachment_proof_id_idx ON dronecontractor_proofattachment(proof_id);

-- Table for Dispute (task-related conflicts)
CREATE TABLE dronecontractor_dispute (
id BIGSERIAL PRIMARY KEY,
assignment_id BIGINT NOT NULL UNIQUE REFERENCES dronecontractor_assignment(id) ON DELETE CASCADE,
raised_by_user_type VARCHAR(20) NOT NULL CHECK (raised_by_user_type IN ('BIDDER', 'CONTRACTOR')),
raised_by_user_id BIGINT NOT NULL REFERENCES auth_user(id) ON DELETE NO ACTION,
reason_category VARCHAR(100) NOT NULL,
reason_description TEXT NOT NULL,
desired_outcome TEXT,
status VARCHAR(30) NOT NULL CHECK (status IN ('OPEN', 'UNDER_REVIEW', 'MEDIATION', 'RESOLVED_FAVOR_BIDDER', 'RESOLVED_FAVOR_WORKER', 'RESOLVED_SPLIT', 'CLOSED')) DEFAULT 'OPEN',
resolution_details TEXT,
resolved_by_admin_id BIGINT REFERENCES auth_user(id) ON DELETE SET NULL,
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_dispute_assignment_id_idx ON dronecontractor_dispute(assignment_id);

-- Table for DisputeEvidence (dispute-related files)
CREATE TABLE dronecontractor_disputeevidence (
id BIGSERIAL PRIMARY KEY,
dispute_id BIGINT NOT NULL REFERENCES dronecontractor_dispute(id) ON DELETE CASCADE,
uploaded_by_user_id BIGINT NOT NULL REFERENCES auth_user(id) ON DELETE NO ACTION,
file VARCHAR(100) NOT NULL,
description VARCHAR(255),
uploaded_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_disputeevidence_dispute_id_idx ON dronecontractor_disputeevidence(dispute_id);




-- Table for FeeStructure (pricing and fees)
CREATE TABLE dronecontractor_feestructure (
id BIGSERIAL PRIMARY KEY,
tier VARCHAR(10) NOT NULL UNIQUE CHECK (tier IN ('BASIC', 'ADVANCED', 'BONDED', 'COMMUNITY')),
service_fee_percentage DOUBLE PRECISION NOT NULL,
escrow_fee_fixed NUMERIC(6,2) NOT NULL,
low_budget_escrow_fee NUMERIC(6,2) NOT NULL DEFAULT 10.00,
dispute_handling_fee NUMERIC(6,2) NOT NULL DEFAULT 50.00,
dispute_responsible_party VARCHAR(20) NOT NULL CHECK (dispute_responsible_party IN ('BIDDER', 'CONTRACTOR', 'SPLIT')) DEFAULT 'SPLIT',
community_tier_fee NUMERIC(6,2) NOT NULL DEFAULT 5.00,
pricing_adjustment DOUBLE PRECISION NOT NULL DEFAULT 1.0,
is_active BOOLEAN NOT NULL DEFAULT TRUE,
effective_date DATE NOT NULL
);
CREATE INDEX dronecontractor_feestructure_tier_idx ON dronecontractor_feestructure(tier);

-- Table for TransactionLog (financial transactions)
CREATE TABLE dronecontractor_transactionlog (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT REFERENCES auth_user(id) ON DELETE SET NULL,
assignment_id BIGINT REFERENCES dronecontractor_assignment(id) ON DELETE SET NULL,
transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('ESCROW_FUND', 'PAYOUT_WORKER', 'REFUND_BIDDER', 'SERVICE_FEE', 'BONUS_PAYMENT')),
amount NUMERIC(12,2) NOT NULL,
currency CHAR(3) NOT NULL DEFAULT 'ZAR',
status VARCHAR(20) NOT NULL CHECK (status IN ('PENDING', 'SUCCESSFUL', 'FAILED')) DEFAULT 'PENDING',
payment_gateway_ref VARCHAR(255),
notes TEXT,
timestamp TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_transactionlog_user_id_idx ON dronecontractor_transactionlog(user_id);
CREATE INDEX dronecontractor_transactionlog_assignment_id_idx ON dronecontractor_transactionlog(assignment_id);





-- Table for Feedback (user feedback on assignments)
CREATE TABLE dronecontractor_feedback (
id BIGSERIAL PRIMARY KEY,
assignment_id BIGINT NOT NULL REFERENCES dronecontractor_assignment(id) ON DELETE CASCADE,
rating_overall SMALLINT NOT NULL CHECK (rating_overall BETWEEN 1 AND 5),
rating_communication SMALLINT CHECK (rating_communication BETWEEN 1 AND 5),
rating_quality SMALLINT CHECK (rating_quality BETWEEN 1 AND 5),
rating_punctuality SMALLINT CHECK (rating_punctuality BETWEEN 1 AND 5),
comments TEXT,
is_public BOOLEAN NOT NULL DEFAULT TRUE,
feedback_by_user_id BIGINT NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
feedback_for_worker_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
via_ussd BOOLEAN NOT NULL DEFAULT FALSE
);
CREATE INDEX dronecontractor_feedback_assignment_id_idx ON dronecontractor_feedback(assignment_id);

-- Table for Loyalty (household loyalty points)
CREATE TABLE dronecontractor_loyalty (
id BIGSERIAL PRIMARY KEY,
household_user_id BIGINT NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
points INTEGER NOT NULL DEFAULT 0 CHECK (points >= 0),
tier VARCHAR(50),
last_updated TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_loyalty_household_user_idx ON dronecontractor_loyalty(household_user_id);

-- Table for Schedule (recurring household tasks)
CREATE TABLE dronecontractor_schedule (
id BIGSERIAL PRIMARY KEY,
household_user_id BIGINT NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
contractor_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
frequency VARCHAR(20) NOT NULL CHECK (frequency IN ('ONCE', 'WEEKLY', 'MONTHLY')),
start_date TIMESTAMP WITH TIME ZONE NOT NULL,
end_date TIMESTAMP WITH TIME ZONE,
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);
CREATE INDEX dronecontractor_schedule_household_user_idx ON dronecontractor_schedule(household_user_id);

-- ManyToMany table for Schedule-Task relationship
CREATE TABLE dronecontractor_schedule_tasks (
id BIGSERIAL PRIMARY KEY,
schedule_id BIGINT NOT NULL REFERENCES dronecontractor_schedule(id) ON DELETE CASCADE,
task_id BIGINT NOT NULL REFERENCES dronecontractor_task(id) ON DELETE CASCADE,
UNIQUE (schedule_id, task_id)
);

-- Table for Package (bundled household services)
CREATE TABLE dronecontractor_package (
id BIGSERIAL PRIMARY KEY,
name VARCHAR(50) NOT NULL,
discount DOUBLE PRECISION NOT NULL DEFAULT 0.1,
price NUMERIC(10,2) NOT NULL,
created_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- ManyToMany table for Package-Task relationship
CREATE TABLE dronecontractor_package_tasks (
id BIGSERIAL PRIMARY KEY,
package_id BIGINT NOT NULL REFERENCES dronecontractor_package(id) ON DELETE CASCADE,
task_id BIGINT NOT NULL REFERENCES dronecontractor_task(id) ON DELETE CASCADE,
UNIQUE (package_id, task_id)
);



-- Table for Notification (multi-channel notifications)
CREATE TABLE dronecontractor_notification (
id BIGSERIAL PRIMARY KEY,
recipient_user_id BIGINT NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
notification_type VARCHAR(30) NOT NULL CHECK (notification_type IN ('NEW_TASK_MATCH', 'APPLICATION_UPDATE', 'PAYMENT_CONFIRMATION', 'SYSTEM_ANNOUNCEMENT', 'PROOF_REQUIRED', 'FEEDBACK_REQUEST')),
message_title VARCHAR(255),
message_body TEXT NOT NULL,
delivery_channels VARCHAR(10)[] NOT NULL CHECK (delivery_channels <@ ARRAY['IN_APP', 'EMAIL', 'SMS', 'WHATSAPP']::VARCHAR[]),
status VARCHAR(10) NOT NULL CHECK (status IN ('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'READ')) DEFAULT 'PENDING',
related_task_id BIGINT REFERENCES dronecontractor_task(id) ON DELETE SET NULL,
related_assignment_id BIGINT REFERENCES dronecontractor_assignment(id) ON DELETE SET NULL,
cta_link VARCHAR(200),
created_at TIMESTAMP WITH TIME ZONE NOT NULL,
scheduled_send_time TIMESTAMP WITH TIME ZONE,
sent_at TIMESTAMP WITH TIME ZONE,
read_at TIMESTAMP WITH TIME ZONE
);
CREATE INDEX dronecontractor_notification_recipient_user_idx ON dronecontractor_notification(recipient_user_id);
CREATE INDEX dronecontractor_notification_status_idx ON dronecontractor_notification(status);

-- Table for ContractorPreferences (worker category preferences)
CREATE TABLE dronecontractor_contractorpreferences (
id BIGSERIAL PRIMARY KEY,
contractor_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
category_id BIGINT NOT NULL REFERENCES dronecontractor_servicecategory(id) ON DELETE NO ACTION,
subcategory VARCHAR(50) NOT NULL
);
CREATE INDEX dronecontractor_contractorpreferences_contractor_id_idx ON dronecontractor_contractorpreferences(contractor_id);

-- Table for SignupBonus (contractor incentives)
CREATE TABLE dronecontractor_signupbonus (
id BIGSERIAL PRIMARY KEY,
contractor_id BIGINT NOT NULL REFERENCES dronecontractor_gigworker(id) ON DELETE CASCADE,
category_id BIGINT NOT NULL REFERENCES dronecontractor_servicecategory(id) ON DELETE NO ACTION,
subcategory VARCHAR(50) NOT NULL,
bonus_amount NUMERIC(6,2) NOT NULL,
credited_at TIMESTAMP WITH TIME ZONE NOT NULL,
applied BOOLEAN NOT NULL DEFAULT FALSE
);
CREATE INDEX dronecontractor_signupbonus_contractor_id_idx ON dronecontractor_signupbonus(contractor_id);