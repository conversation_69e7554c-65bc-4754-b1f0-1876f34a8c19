Bidder Dashboard (Market’s Command Center - bidder_dashboard.dart)
├── 1. Core Components (Command Center’s Displays)
│ ├── 1.1. WelcomeBanner: Personalized greeting with gamified streak (e.g., “Welcome, Thabo! 3 Win Streak!” in Sesotho)
│ │ ├── 1.1.1. ProfileStrengthGauge: Animated 3D gauge (e.g., “75% Complete, Add Certifications!” with holographic effect)
│ │ ├── 1.1.2. WinStreakBadge: Displays tender win streak (e.g., “3 Wins in a Row!” with 3D fire animation)
│ │ ├── 1.1.3. LanguageToggle: Switches language (English, Zulu, Xhosa, Afrikaans, Sesotho)
│ │ ├── 1.1.4. VoiceAssistantButton: Activates voice navigation (e.g., “Show tenders in Limpopo”)
│ │ └── 1.1.5. CollaborationHub: Quick access to team chat and taskboards (e.g., “5 unread messages”)
│ ├── 1.2. TenderCarousel: Swipeable tender cards with enhanced visuals and AI recommendations (e.g., 100 RFQs/month)
│ │ ├── 1.2.1. TenderCard: Displays tender details (e.g., “Construction in EC, Deadline: May 20, 2025” with floating 3D effect)
│ │ │ ├── *******. RiskScoreChip: Color-coded risk (via riskengine/core/tender_risk/, e.g., “70/100, Medium Risk”)
│ │ │ ├── *******. InstitutionRiskChip: Issuer risk (via riskengine/core/institution_risk/, e.g., “Issuer: 80/100”)
│ │ │ ├── *******. SuccessPredictionChip: Win likelihood (via riskengine/core/bid_success_prediction/, e.g., “80% Win Chance”)
│ │ │ ├── *******. AIRecommendationTag: Suggests actions (e.g., “Top Pick: 90% Win Chance” with glowing effect)
│ │ │ ├── *******. ARPreviewButton: Opens AR view of tender site (e.g., “View Construction Site in EC”)
│ │ │ ├── *******. CompetitorInsight: Competitor data (via riskengine/core/competitor_intelligence/, e.g., “Competitor #789 won 5 tenders”)
│ │ │ └── *******. QuickActionButton: One-tap RFQ creation (e.g., “Create RFQ” in bidder’s language)
│ │ └── 1.2.2. FilterSortBar: Filters tenders (e.g., “Low Risk”, “High Win Chance”, “AI Recommended”)
│ ├── 1.3. QuoteOverviewSection: Shows active quotes with trust scores (e.g., 500 quotes for 100 RFQs)
│ │ ├── 1.3.1. QuoteCard: Quote details with holographic effect (e.g., “Supplier #456: R10,000 for bricks”)
│ │ │ ├── *******. TrustScoreChip: Supplier trust (via riskengine/core/supplier_trust/, e.g., “85/100, Low Risk”)
│ │ │ ├── *******. WhatIfInsight: Simulates outcomes (via riskengine/core/what_if_simulation/, e.g., “Delay increases risk by 10%”)
│ │ │ ├── *******. MitigationTip: Suggests actions (via riskengine/core/risk_mitigation/, e.g., “Negotiate faster delivery”)
│ │ │ ├── *******. CollaborationLink: Links to team chat (e.g., “Discuss with team”)
│ │ │ └── *******. ActionButton: One-tap quote selection (e.g., 20 selections/month)
│ │ └── 1.3.2. RiskAlertBanner: Shows alerts (via riskengine/core/bidder_alerts/, e.g., “Supplier #456: High Risk!”)
│ ├── 1.4. BeeTaskSection: Manages Bee tasks with logistics risk (e.g., 2,000 tasks/month, R300/task)
│ │ ├── 1.4.1. TaskCard: Task details (e.g., “Briefing in EC” in bidder’s language)
│ │ │ ├── *******. LogisticsRiskChip: Bee risk (via riskengine/core/bee_logistics_risk/, e.g., “Bee #321: 92/100”)
│ │ │ ├── *******. MitigationTip: Suggests adjustments (e.g., “Hire backup Bee”)
│ │ │ ├── *******. CollaborationLink: Links to team taskboard (e.g., “Assign to team”)
│ │ │ └── *******. ActionButton: One-tap task booking (e.g., “Book Now”)
│ │ └── 1.4.2. TaskProgressBar: Animated 3D bar (e.g., “75% Complete” with holographic progress)
│ ├── 1.5. AnalyticsSnapshot: Quick stats with risk and success insights (e.g., 100 RFQs, 20 awards/month)
│ │ ├── 1.5.1. SuccessRateGauge: Animated 3D gauge (e.g., “20% Win Rate” with glowing effect)
│ │ ├── 1.5.2. SpendCard: Shows spend (e.g., “R6,000 on RFQs”)
│ │ ├── 1.5.3. RiskTrendGraph: Interactive mini-graph (e.g., “5% suppliers high-risk in EC”)
│ │ ├── 1.5.4. SuccessPredictionTrend: Win likelihood trend (e.g., “Win Chance Up 10%”)
│ │ ├── 1.5.5. ComplianceStatus: Shows compliance (e.g., “95% Compliant”)
│ │ └── 1.5.6. AIInsightCard: Suggests next actions (e.g., “Focus on Limpopo tenders: 90% win chance”)
│ ├── 1.6. GamificationSection: Motivates action with gamified elements (e.g., 1,000 users engaged)
│ │ ├── 1.6.1. ProgressBar: Animated holographic bar (e.g., “80% to Tender Pro!”)
│ │ ├── 1.6.2. BadgeCarousel: Swipeable 3D badges (e.g., “Tender Pro”, “Risk Master” with AR effects)
│ │ ├── 1.6.3. LeaderboardButton: Links to leaderboard (e.g., “#5 in Gauteng!”)
│ │ ├── 1.6.4. ActionPrompt: Gamified prompt (e.g., “Submit 2 RFQs to Earn 50 XP!” with AR confetti)
│ │ └── 1.6.5. TrophyRoom: Displays all earned badges with AR showcase (e.g., “View in AR”)
│ ├── 1.7. TenderMapDiscovery: Interactive Mapbox tender explorer (e.g., 100 RFQs/month)
│ │ ├── 1.7.1. MapboxWidget: Fullscreen map of South Africa
│ │ │ ├── *******. ClusteredTenderMarkers: Icons representing tenders in each province (e.g., 10 RFQs in Limpopo)
│ │ │ ├── *******. Color-coded Risk: Red = High Risk, Yellow = Medium, Green = Low (via riskengine/core/tender_risk/)
│ │ │ ├── *******. Animated Pin Drops: For new tenders (e.g., “New RFQ in Limpopo!” with 3D pin drop animation)
│ │ │ ├── *******. Language-specific Popup Tooltips: E.g., “Sicelo, view this RFQ” in Zulu: “Sicelo, bheka le RFQ”
│ │ │ ├── *******. ARMapOverlay: AR view of tender sites (e.g., “View site in AR”)
│ │ │ └── *******. VoiceNavigation: Voice commands (e.g., “Zoom to Eastern Cape”)
│ │ ├── 1.7.2. RegionFilterBar: Filters tenders by province/region (e.g., “Eastern Cape”)
│ │ ├── 1.7.3. SearchBox: Keyword, CPV code, or category filtering (e.g., “Construction”, CPV 45000000)
│ │ ├── 1.7.4. TenderDetailPreview: Opens sidebar with key RFQ data
│ │ │ ├── *******. Title: E.g., “Construction in EC”
│ │ │ ├── *******. Deadline: E.g., “May 20, 2025”
│ │ │ ├── *******. Risk Score: E.g., “70/100, Medium Risk” (via riskengine/core/tender_risk/)
│ │ │ ├── *******. Win Chance: E.g., “80% Win Chance” (via riskengine/core/bid_success_prediction/)
│ │ │ └── *******. ActionButton: “Submit RFQ” (one-tap RFQ creation)
│ │ ├── 1.7.5. MyBidsLayer: Toggle overlay showing tenders the bidder has engaged with (e.g., 20 active RFQs)
│ │ └── 1.7.6. GeoAlertBanner: “Tenders available near you!” (uses location + province, e.g., 5 RFQs in Gauteng)
│ └── 1.8. CollaborationHub: Real-time team collaboration tools
│ ├── 1.8.1. LiveChatScreen: Team chat for tender discussions (e.g., “Discuss RFQ #123”)
│ ├── 1.8.2. TaskBoardScreen: Kanban-style taskboard for tender tasks (e.g., “Assign briefing to Sipho”)
│ ├── 1.8.3. NotificationBubble: Shows unread messages/tasks (e.g., “5 unread messages”)
│ └── 1.8.4. VoiceChatButton: Initiates voice chat with team (e.g., “Start voice call”)
├── 2. Data Models (Customer’s Tendering List)
│ ├── 2.1. BidbeezUser: Bidder info (e.g., 100 bidders tendering for work)
│ │ ├── 2.1.1. user_id
│ │ ├── 2.1.2. username
│ │ ├── 2.1.3. email
│ │ ├── 2.1.4. phone_number
│ │ ├── 2.1.5. language_preference: English, Zulu, Xhosa, Afrikaans, Sesotho
│ │ ├── 2.1.6. region: E.g., “Gauteng”
│ │ ├── 2.1.7. risk_preference: E.g., “Low Risk” (via riskengine/data_models/user_risk_preference.py)
│ │ ├── 2.1.8. performance_score: E.g., 75/100 (via riskengine/data_models/bidder_performance_score.py)
│ │ ├── 2.1.9. location: Lat/Long for geo-alerts (via TenderMapDiscovery)
│ │ ├── 2.1.10. tender_win_streak: E.g., 3 (for gamification)
│ │ ├── 2.1.11. progress_level: E.g., 80% (for gamification)
│ │ ├── 2.1.12. badges: E.g., “Tender Pro” (for gamification)
│ │ ├── 2.1.13. leaderboard_rank: E.g., 5 (for gamification)
│ │ ├── 2.1.14. team_id: Links to collaboration team (for CollaborationHub)
│ │ └── 2.1.15. created_at
│ ├── 2.2. Tender: Tenders bidder aims to win (e.g., 140,000 RFQs/year)
│ │ ├── 2.2.1. tender_id
│ │ ├── 2.2.2. title: E.g., “Construction in EC”
│ │ ├── 2.2.3. description
│ │ ├── 2.2.4. region
│ │ ├── 2.2.5. language: English, Zulu, Xhosa, Afrikaans, Sesotho
│ │ ├── 2.2.6. risk_score: E.g., 70/100 (via riskengine/core/tender_risk/models.py)
│ │ ├── 2.2.7. institution_risk_score: E.g., 80/100 (via riskengine/core/institution_risk/models.py)
│ │ ├── 2.2.8. success_prediction: E.g., 80% (via riskengine/core/bid_success_prediction/models.py)
│ │ ├── 2.2.9. cpv_code: E.g., 45000000 (Construction, for Mapbox search/filtering)
│ │ ├── 2.2.10. category: E.g., “Construction” (for Mapbox search/filtering)
│ │ ├── 2.2.11. ar_model_url: URL to AR model for tender site (e.g., “s3://bidbeez/ar/construction_ec.obj”)
│ │ ├── 2.2.12. created_at
│ │ └── 2.2.13. status: E.g., “Open,” “Awarded”
│ ├── 2.3. GeoTender: Tender location data for bidding (used by TenderMapDiscovery)
│ │ ├── 2.3.1. tender_id (FK)
│ │ ├── 2.3.2. latitude
│ │ ├── 2.3.3. longitude
│ │ ├── 2.3.4. region
│ │ └── 2.3.5. province: E.g., “Limpopo” (for Mapbox filtering)
│ ├── 2.4. SupplierQuote: Quotes from suppliers (traders providing goods/services, e.g., 500 quotes for 100 RFQs)
│ │ ├── 2.4.1. quote_id
│ │ ├── 2.4.2. tender_id (FK)
│ │ ├── 2.4.3. supplier_id
│ │ ├── 2.4.4. amount: E.g., R10,000
│ │ ├── 2.4.5. delivery_time
│ │ ├── 2.4.6. trust_score: E.g., 85/100 (via riskengine/core/supplier_trust/models.py)
│ │ ├── 2.4.7. status: E.g., “Submitted,” “Accepted”
│ │ ├── 2.4.8. language: English, Zulu, Xhosa, Afrikaans, Sesotho
│ │ └── 2.4.9. created_at
│ ├── 2.5. BeeTask: Tasks booked to support tendering (e.g., 2,000 tasks/month)
│ │ ├── 2.5.1. task_id
│ │ ├── 2.5.2. bidder_id
│ │ ├── 2.5.3. bee_id
│ │ ├── 2.5.4. tender_id (FK)
│ │ ├── 2.5.5. task_type: E.g., “Briefing,” “Delivery”
│ │ ├── 2.5.6. cost: R300 (R54 platform, R246 Bee)
│ │ ├── 2.5.7. logistics_risk_score: E.g., 92/100 (via riskengine/core/bee_logistics_risk/models.py)
│ │ ├── 2.5.8. status: E.g., “Requested,” “Completed”
│ │ ├── 2.5.9. location: E.g., “EC”
│ │ ├── 2.5.10. language: English, Zulu, Xhosa, Afrikaans, Sesotho
│ │ ├── 2.5.11. team_assignee_id: Links to team member (for CollaborationHub)
│ │ └── 2.5.12. created_at
│ ├── 2.6. RiskDispute: Disputes raised by bidders on risk scores (via riskengine/data_models/risk_dispute.py, e.g., 5 disputes/month)
│ │ ├── 2.6.1. dispute_id
│ │ ├── 2.6.2. bidder_id
│ │ ├── 2.6.3. entity_type: E.g., “Supplier,” “Tender,” “Bee”
│ │ ├── 2.6.4. entity_id
│ │ ├── 2.6.5. reason: E.g., “Supplier #456 score too low”
│ │ ├── 2.6.6. status: E.g., “Open,” “Resolved”
│ │ ├── 2.6.7. language: English, Zulu, Xhosa, Afrikaans, Sesotho
│ │ └── 2.6.8. created_at
│ ├── 2.7. RiskAuditLog: Logs risk-related actions (via riskengine/data_models/risk_audit_log.py, e.g., 2,100 logs/month)
│ │ ├── 2.7.1. log_id
│ │ ├── 2.7.2. bidder_id
│ │ ├── 2.7.3. action: E.g., “Viewed Supplier #456 Trust Score”
│ │ ├── 2.7.4. entity_type
│ │ ├── 2.7.5. entity_id
│ │ ├── 2.7.6. timestamp
│ │ └── 2.7.7. details: E.g., “Score: 85/100”
│ ├── 2.8. ChatMessage: Messages for team collaboration (for CollaborationHub)
│ │ ├── 2.8.1. message_id
│ │ ├── 2.8.2. team_id
│ │ ├── 2.8.3. sender_id
│ │ ├── 2.8.4. content: E.g., “Discuss RFQ #123”
│ │ ├── 2.8.5. language: English, Zulu, Xhosa, Afrikaans, Sesotho
│ │ ├── 2.8.6. timestamp
│ │ └── 2.8.7. is_read: Boolean (e.g., false for unread messages)
│ └── 2.9. TeamTask: Tasks for team collaboration (for CollaborationHub)
│ ├── 2.9.1. task_id
│ ├── 2.9.2. team_id
│ ├── 2.9.3. tender_id (FK)
│ ├── 2.9.4. assignee_id
│ ├── 2.9.5. description: E.g., “Prepare RFQ #123”
│ ├── 2.9.6. status: E.g., “To Do,” “In Progress,” “Done”
│ ├── 2.9.7. language: English, Zulu, Xhosa, Afrikaans, Sesotho
│ ├── 2.9.8. due_date: E.g., “May 20, 2025”
│ └── 2.9.9. created_at
├── 3. APIs (Customer’s Tendering System)
│ ├── 3.1. create_rfq: Creates RFQ with language preference (e.g., 100 RFQs/month)
│ ├── 3.2. get_quotes: Fetches supplier quotes with trust scores in bidder’s language (e.g., 500 quotes/month)
│ ├── 3.3. select_quote: Selects winning supplier quote (e.g., 20 selections/month)
│ ├── 3.4. book_bee_task: Books Bee tasks with logistics risk scores in bidder’s language (e.g., 2,000 tasks/month)
│ ├── 3.5. get_tender_notifications: Fetches alerts with risk data in bidder’s language (e.g., 1,000 invites/month)
│ ├── 3.6. get_bidder_analytics: Fetches tendering analytics with risk insights in bidder’s language (e.g., 20% win rate)
│ ├── 3.7. get_compliance_status: Fetches compliance status (e.g., 95% compliant)
│ ├── 3.8. get_tender_risk_score: Fetches tender risk score (via riskengine/core/tender_risk/api.py, e.g., “RFQ #123: 70/100”)
│ ├── 3.9. get_supplier_trust_score: Fetches supplier trust score (via riskengine/core/supplier_trust/api.py, e.g., “Supplier #456: 85/100”)
│ ├── 3.10. get_bee_logistics_risk_score: Fetches Bee logistics risk score (via riskengine/core/bee_logistics_risk/api.py, e.g., “Bee #321: 92/100”)
│ ├── 3.11. get_institution_risk_score: Fetches tender issuer risk score (via riskengine/core/institution_risk/api.py, e.g., “Issuer: 80/100”)
│ ├── 3.12. get_bid_success_prediction: Fetches win likelihood (via riskengine/core/bid_success_prediction/api.py, e.g., “80% chance to win RFQ #123”)
│ ├── 3.13. get_competitor_intelligence: Fetches competitor data (via riskengine/core/competitor_intelligence/api.py, e.g., “Competitor #789 won 5 tenders”)
│ ├── 3.14. run_what_if_simulation: Simulates outcomes (via riskengine/core/what_if_simulation/api.py, e.g., “Delay increases risk by 10%”)
│ ├── 3.15. get_risk_mitigation: Fetches mitigation suggestions (via riskengine/core/risk_mitigation/api.py, e.g., “Hire backup Bee”)
│ ├── 3.16. raise_risk_dispute: Raises disputes on risk scores (e.g., 5 disputes/month)
│ ├── 3.17. get_tenders_by_location: Fetches tenders by region for Mapbox (e.g., 10 RFQs in Limpopo)
│ ├── 3.18. get_geo_alerts: Fetches location-based alerts (e.g., 5 RFQs in Gauteng)
│ ├── 3.19. search_tenders: Searches tenders by keyword/CPV/category (e.g., “Construction”, CPV 45000000)
│ ├── 3.20. get_gamification_stats: Fetches gamification data (e.g., tender win streak, badges)
│ ├── 3.21. update_user_language: Updates bidder’s language preference
│ ├── 3.22. get_ai_recommendations: Fetches AI-driven tender recommendations (e.g., “Top tender in Gauteng: 90% win chance”)
│ ├── 3.23. get_ar_model: Fetches AR model for tender site (e.g., “s3://bidbeez/ar/construction_ec.obj”)
│ ├── 3.24. get_team_chat: Fetches team chat messages (e.g., 5 unread messages)
│ └── 3.25. get_team_tasks: Fetches team taskboard tasks (e.g., “Prepare RFQ #123”)
├── 4. Frontend (Customer’s Tendering Area - bidder_dashboard.dart)
│ ├── 4.1. WelcomeBanner: Personalized greeting with gamified streak (e.g., “Welcome, Thabo! 3 Win Streak!” in Sesotho)
│ │ ├── 4.1.1. ProfileStrengthGauge: Animated 3D gauge (e.g., “75% Complete, Add Certifications!” with holographic effect)
│ │ ├── 4.1.2. WinStreakBadge: Displays tender win streak (e.g., “3 Wins in a Row!” with 3D fire animation)
│ │ ├── 4.1.3. LanguageToggle: Switches language (English, Zulu, Xhosa, Afrikaans, Sesotho)
│ │ ├── 4.1.4. VoiceAssistantButton: Activates voice navigation (e.g., “Show tenders in Limpopo”)
│ │ └── 4.1.5. CollaborationHub: Quick access to team chat and taskboards (e.g., “5 unread messages”)
│ ├── 4.2. TenderCarousel: Swipeable tender cards with enhanced visuals and AI recommendations (e.g., 100 RFQs/month)
│ │ ├── 4.2.1. TenderCard: Displays tender details (e.g., “Construction in EC, Deadline: May 20, 2025” with floating 3D effect)
│ │ │ ├── *******. RiskScoreChip: Color-coded risk (via riskengine/core/tender_risk/, e.g., “70/100, Medium Risk”)
│ │ │ ├── *******. InstitutionRiskChip: Issuer risk (via riskengine/core/institution_risk/, e.g., “Issuer: 80/100”)
│ │ │ ├── *******. SuccessPredictionChip: Win likelihood (via riskengine/core/bid_success_prediction/, e.g., “80% Win Chance”)
│ │ │ ├── *******. AIRecommendationTag: Suggests actions (e.g., “Top Pick: 90% Win Chance” with glowing effect)
│ │ │ ├── *******. ARPreviewButton: Opens AR view of tender site (e.g., “View Construction Site in EC”)
│ │ │ ├── *******. CompetitorInsight: Competitor data (via riskengine/core/competitor_intelligence/, e.g., “Competitor #789 won 5 tenders”)
│ │ │ └── *******. QuickActionButton: One-tap RFQ creation (e.g., “Create RFQ” in bidder’s language)
│ │ └── 4.2.2. FilterSortBar: Filters tenders (e.g., “Low Risk”, “High Win Chance”, “AI Recommended”)
│ ├── 4.3. QuoteOverviewSection: Shows active quotes with trust scores (e.g., 500 quotes for 100 RFQs)
│ │ ├── 4.3.1. QuoteCard: Quote details with holographic effect (e.g., “Supplier #456: R10,000 for bricks”)
│ │ │ ├── *******. TrustScoreChip: Supplier trust (via riskengine/core/supplier_trust/, e.g., “85/100, Low Risk”)
│ │ │ ├── *******. WhatIfInsight: Simulates outcomes (via riskengine/core/what_if_simulation/, e.g., “Delay increases risk by 10%”)
│ │ │ ├── *******. MitigationTip: Suggests actions (via riskengine/core/risk_mitigation/, e.g., “Negotiate faster delivery”)
│ │ │ ├── *******. CollaborationLink: Links to team chat (e.g., “Discuss with team”)
│ │ │ └── *******. ActionButton: One-tap quote selection (e.g., 20 selections/month)
│ │ └── 4.3.2. RiskAlertBanner: Shows alerts (via riskengine/core/bidder_alerts/, e.g., “Supplier #456: High Risk!”)
│ ├── 4.4. BeeTaskSection: Manages Bee tasks with logistics risk (e.g., 2,000 tasks/month, R300/task)
│ │ ├── 4.4.1. TaskCard: Task details (e.g., “Briefing in EC” in bidder’s language)
│ │ │ ├── *******. LogisticsRiskChip: Bee risk (via riskengine/core/bee_logistics_risk/, e.g., “Bee #321: 92/100”)
│ │ │ ├── *******. MitigationTip: Suggests adjustments (e.g., “Hire backup Bee”)
│ │ │ ├── *******. CollaborationLink: Links to team taskboard (e.g., “Assign to team”)
│ │ │ └── *******. ActionButton: One-tap task booking (e.g., “Book Now”)
│ │ └── 4.4.2. TaskProgressBar: Animated 3D bar (e.g., “75% Complete” with holographic progress)
│ ├── 4.5. AnalyticsDashboard: Displays tendering stats with risk insights (e.g., “5 RFQs, 3 awarded” in bidder’s language)
│ │ ├── 4.5.1. SuccessRateGauge: Animated 3D gauge (e.g., “20% Win Rate” with glowing effect)
│ │ ├── 4.5.2. SpendCard: Shows spend (e.g., “R6,000 on RFQs”)
│ │ ├── 4.5.3. RiskTrendGraph: Interactive mini-graph (e.g., “5% suppliers high-risk in EC”)
│ │ ├── 4.5.4. SuccessPredictionTrend: Win likelihood trend (e.g., “Win Chance Up 10%”)
│ │ ├── 4.5.5. ComplianceStatus: Shows compliance (e.g., “95% Compliant”)
│ │ ├── 4.5.6. RFQ Status: “5 RFQs, 3 awarded”
│ │ ├── 4.5.7. Risk Insights: “5% suppliers high-risk in EC” (via riskengine/core/)
│ │ ├── 4.5.8. Success Insights: “80% win likelihood for RFQ #123” (via riskengine/core/bid_success_prediction/)
│ │ └── 4.5.9. AIInsightCard: Suggests next actions (e.g., “Focus on Limpopo tenders: 90% win chance”)
│ ├── 4.6. RiskScoreDisplay: Displays risk scores (via riskengine/integrations/bidder_ux.py, e.g., “Supplier #456: 85/100, Low Risk” in bidder’s language)
│ │ ├── 4.6.1. Supplier Trust Scores
│ │ ├── 4.6.2. Tender Risk Scores
│ │ ├── 4.6.3. Bee Logistics Risk Scores
│ │ ├── 4.6.4. Institution Risk Scores
│ │ ├── 4.6.5. Risk Alerts
│ │ └── 4.6.6. Bid Success Predictions
│ ├── 4.7. RiskMitigationWidget: Suggests mitigation actions (e.g., “Hire backup Bee” in bidder’s language)
│ ├── 4.8. CompetitorIntelligenceWidget: Shows competitor data (e.g., “Competitor #789 won 5 tenders” in bidder’s language)
│ ├── 4.9. WhatIfSimulationScreen: Simulates outcomes (e.g., “Delay increases risk by 10%” in bidder’s language)
│ ├── 4.10. RiskDisputeScreen: Allows raising disputes (e.g., 5 disputes/month, in bidder’s language)
│ │ ├── 4.10.1. DisputeCard: Shows dispute details (e.g., “Supplier #456 score too low” in bidder’s language)
│ │ ├── 4.10.2. ActionButton: One-tap dispute submission (e.g., “Raise Dispute”)
│ │ └── 4.10.3. DisputeStatus: Tracks progress (e.g., “Open”, “Resolved”)
│ ├── 4.11. TenderMapDiscovery: Interactive Mapbox tender explorer (e.g., 100 RFQs/month)
│ │ ├── 4.11.1. MapboxWidget: Fullscreen map of South Africa
│ │ │ ├── ********. ClusteredTenderMarkers: Icons representing tenders in each province (e.g., 10 RFQs in Limpopo)
│ │ │ ├── ********. Color-coded Risk: Red = High Risk, Yellow = Medium, Green = Low (via riskengine/core/tender_risk/)
│ │ │ ├── ********. Animated Pin Drops: For new tenders (e.g., “New RFQ in Limpopo!” with 3D pin drop animation)
│ │ │ ├── ********. Language-specific Popup Tooltips: E.g., “Sicelo, view this RFQ” in Zulu: “Sicelo, bheka le RFQ”
│ │ │ ├── ********. ARMapOverlay: AR view of tender sites (e.g., “View site in AR”)
│ │ │ └── ********. VoiceNavigation: Voice commands (e.g., “Zoom to Eastern Cape”)
│ │ ├── 4.11.2. RegionFilterBar: Lets bidder filter tenders by province/region (e.g., “Eastern Cape”)
│ │ ├── 4.11.3. SearchBox: Allows keyword, CPV code, or category filtering (e.g., “Construction”, CPV 45000000)
│ │ ├── 4.11.4. TenderDetailPreview: Opens sidebar with key RFQ data
│ │ │ ├── ********. Title: E.g., “Construction in EC”
│ │ │ ├── ********. Deadline: E.g., “May 20, 2025”
│ │ │ ├── ********. Risk Score: E.g., “70/100, Medium Risk” (via riskengine/core/tender_risk/)
│ │ │ ├── ********. Win Chance: E.g., “80% Win Chance” (via riskengine/core/bid_success_prediction/)
│ │ │ └── ********. ActionButton: “Submit RFQ” (one-tap RFQ creation)
│ │ ├── 4.11.5. MyBidsLayer: Toggle overlay showing tenders the bidder has engaged with (e.g., 20 active RFQs)
│ │ └── 4.11.6. GeoAlertBanner: “Tenders available near you!” (uses location + province, e.g., 5 RFQs in Gauteng)
│ └── 4.12. CollaborationHub: Real-time team collaboration tools
│ ├── 4.12.1. LiveChatScreen: Team chat for tender discussions (e.g., “Discuss RFQ #123”)
│ ├── 4.12.2. TaskBoardScreen: Kanban-style taskboard for tender tasks (e.g., “Assign briefing to Sipho”)
│ ├── 4.12.3. NotificationBubble: Shows unread messages/tasks (e.g., “5 unread messages”)
│ └── 4.12.4. VoiceChatButton: Initiates voice chat with team (e.g., “Start voice call”)
└── 5. Integration with Other Modules (Customer’s Market Connections)
├── 5.1. Backend: Stores bidder data (e.g., 100 bidders), processes RFQs (e.g., 100/month)
├── 5.2. Notification System: Sends tender/risk alerts in bidder’s language (e.g., 1,000 invites/month via riskengine/core/bidder_alerts/tasks.py)
├── 5.3. Audit & Compliance: Logs actions (e.g., 2,100 logs/month via riskengine/data_models/risk_audit_log.py), checks compliance (e.g., 95% compliant)
├── 5.4. Payment Gateway: Processes RFQ fees (e.g., R6,000–R6,500 for 100 RFQs), Bee tasks (R300/task)
├── 5.5. Third-Party Integrations: eTenders API (140,000 RFQs/year), SANTACO logistics (200,000 taxis), Mapbox (for TenderMapDiscovery), SAPS/CIPC/Treasury (via riskengine/services/fetchers/), AR SDK (e.g., ARKit/ARCore for AR previews)
├── 5.6. AI/ML: Suggests tenders (e.g., “90% win chance in Gauteng”), powers Risk Engine (via riskengine/core/bid_success_prediction/)
├── 5.7. Analytics Dashboard: Provides tendering insights with risk data (e.g., 20% win rate, via riskengine/integrations/analytics_engine.py)
├── 5.8. Risk Engine: Provides risk scores, predictions, and mitigation (via riskengine/)
├── 5.9. SkillSync: Connects to subcontractors (e.g., 50 subcontractors/month)
├── 5.10. Drone Contractor: Supports drone tasks (e.g., 2,000 tasks/month)
├── 5.11. QueenBee AI: Enhances risk predictions and recommendations (via riskengine/integrations/queenbee_ai.py)
└── 5.12. CRM/Slack Webhooks: Sends risk alerts and team notifications (via riskengine/integrations/crm_slack_webhooks.py)





s