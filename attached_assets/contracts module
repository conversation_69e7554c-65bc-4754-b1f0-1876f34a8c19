The AI powered Contracts Module
Inbox

prince <PERSON><PERSON><PERSON>, Jun 16, 4:35 AM (2 days ago)
to bidbeezfinals, me



```markdown
bidbeez/
└── contracts/
├── models/
│ ├── __init__.py
│ ├── contract_standard.py
│ ├── contract_clause.py # Added source_page_number
│ ├── translated_clause_text.py # NEW: Multilingual support
│ ├── clause_query_cluster.py # NEW: NLP query grouping
│ ├── tender_specific_clause.py # NEW: Tender variants
│ └── contract_risk_analysis.py # Added confidence_score
│
├── services/
│ ├── __init__.py
│ ├── bid_decision_analyzer.py # Integrated SHAP explainer
│ ├── feature_store.py # NEW: Vector caching
│ ├── query_clustering.py # NEW: NLP grouping
│ └── hybrid_model_router.py # NEW: Fallback engine
│
├── ml_model/
│ ├── __init__.py
│ ├── shap_explainer.py # NEW: SHAP implementation
│ └── confidence_calculator.py # NEW: Uncertainty scoring
│
├── dashboards/
│ ├── __init__.py
│ ├── ai_performance_dashboard.py # NEW: Monitoring
│ └── risk_plan_builder.py # NEW: Mitigation wizard
│
├── ui/
│ ├── risk_bar_component.dart # NEW: Visual indicator
│ ├── red_flag_viewer.dart # NEW: Clause warnings
│ └── multilingual_clause.dart # NEW: Translation support
│
└── tests/
├── __init__.py
├── test_shap_explainer.py # NEW
└── test_hybrid_model.py # NEW
```

### Critical Implementation Files:

**1. models/contract_clause.py** (Added traceability)
```python
class ContractClause(models.Model):
title = models.CharField(max_length=200)
reference_code = models.CharField(max_length=50)
source_page_number = models.IntegerField( # NEW
null=True,
blank=True,
help_text="Original document page number for traceability"
)
# ... existing fields ...
```

**2. models/translated_clause_text.py** (Multilingual support)
```python
class TranslatedClauseText(models.Model):
clause = models.ForeignKey(ContractClause, on_delete=models.CASCADE)
language = models.CharField(max_length=10, choices=[
('zu', 'Zulu'), ('xh', 'Xhosa'), ('af', 'Afrikaans'), ('en', 'English')
])
translated_title = models.CharField(max_length=200)
translated_description = models.TextField()
last_updated = models.DateTimeField(auto_now=True)
```

**3. services/bid_decision_analyzer.py** (SHAP integration)
```python
import shap
from ml_model.shap_explainer import ShapExplainer

class BidDecisionAnalyzer:
def __init__(self):
self.explainer = ShapExplainer()

def explain_prediction(self, features):
"""Replace stub with actual SHAP explainer"""
shap_values = self.explainer.explain(self.model, features)
return self._format_explanation(shap_values)

def _format_explanation(self, shap_values):
"""Convert SHAP output to business-friendly format"""
factors = []
for i, value in enumerate(shap_values):
factors.append({
"feature": self.feature_names[i],
"impact": float(value),
"description": self.feature_descriptions[i]
})

return {
"summary": self.explainer.summary_plot(shap_values),
"factors": sorted(factors, key=lambda x: abs(x['impact']), reverse=True)[:5]
}
```

**4. ml_model/shap_explainer.py** (Core explainability)
```python
import shap
import numpy as np
import json

class ShapExplainer:
def __init__(self):
self.explainer = None

def load_explainer(self, model_path):
"""Initialize SHAP explainer based on model type"""
if "tree" in model_path:
self.explainer = shap.TreeExplainer(model_path)
else:
self.explainer = shap.DeepExplainer(model_path)

def explain(self, model, input_data):
"""Generate SHAP values for prediction"""
if not self.explainer:
self.load_explainer(model)

shap_values = self.explainer.shap_values(input_data)
return shap_values[0] # For single output models

def summary_plot(self, shap_values, feature_names):
"""Generate business-readable summary"""
abs_vals = np.abs(shap_values).mean(0)
sorted_idx = abs_vals.argsort()[::-1]

return "Top risk drivers: " + ", ".join(
[f"{feature_names[i]} ({shap_values[i]:.2f})"
for i in sorted_idx[:3]]
)
```

**5. models/clause_query_cluster.py** (NLP grouping)
```python
from django.db import models
from django.contrib.postgres.fields import ArrayField

class ClauseQueryCluster(models.Model):
centroid_text = models.TextField(
help_text="Representative query text for this cluster"
)
queries = models.ManyToManyField(
'contracts.ClauseQuery',
related_name='clusters'
)
cluster_id = models.IntegerField(
help_text="NLP-generated cluster identifier"
)
keywords = ArrayField(
models.CharField(max_length=50),
default=list,
help_text="Key terms identifying this cluster"
)
first_detected = models.DateTimeField(auto_now_add=True)
last_occurrence = models.DateTimeField(auto_now=True)

class Meta:
indexes = [
models.Index(fields=['cluster_id']),
models.Index(fields=['keywords']),
]
```

**6. services/query_clustering.py** (Clustering engine)
```python
from sklearn.cluster import DBSCAN
from sentence_transformers import SentenceTransformer
from .models import ClauseQuery, ClauseQueryCluster

class QueryClusterEngine:
def __init__(self):
self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
self.cluster_model = DBSCAN(eps=0.5, min_samples=2)

def cluster_new_queries(self):
"""Group unclustered queries using NLP"""
unclustered = ClauseQuery.objects.filter(clusters__isnull=True)
texts = [q.query_text for q in unclustered]

if not texts:
return

embeddings = self.model.encode(texts)
clusters = self.cluster_model.fit_predict(embeddings)

for idx, query in enumerate(unclustered):
cluster_id = clusters[idx]
if cluster_id != -1: # Ignore outliers
cluster, _ = ClauseQueryCluster.objects.get_or_create(
cluster_id=cluster_id,
defaults={'centroid_text': query.query_text}
)
cluster.queries.add(query)
cluster.save()
```

**7. services/hybrid_model_router.py** (Fallback system)
```python
class HybridModelRouter:
CONFIDENCE_THRESHOLD = 0.65

def predict(self, features):
# Get primary model prediction
ml_pred, ml_confidence = self.ml_predictor.predict(features)

if ml_confidence >= self.CONFIDENCE_THRESHOLD:
return ml_pred, ml_confidence

# Fallback to rules engine
return self.rules_engine.predict(features), 1.0

def ml_predictor(self, features):
# ... existing ML prediction logic ...
return prediction, confidence

def rules_engine(self, features):
"""Expert system for low-confidence scenarios"""
if features['liquidated_damages'] > 0.1:
return "No_Bid"
elif features['issuer_risk'] > 7.0:
return "Caution"
return "Proceed"
```

**8. dashboards/ai_performance_dashboard.py** (Monitoring)
```python
from django.db.models import Count, Avg
from django.utils import timezone

class AIPerformanceDashboard:
def get_metrics(self):
one_month_ago = timezone.now() - timedelta(days=30)
return {
"risk_score_distribution": self.get_score_distribution(),
"clause_match_rate": self.get_clause_match_rate(),
"override_frequency": self.get_override_stats(one_month_ago),
"confidence_trends": self.get_confidence_trends()
}

def get_score_distribution(self):
return ContractRiskAnalysis.objects.annotate(
score_bucket=Case(
When(risk_score__lt=4, then=Value("Low")),
When(risk_score__lt=7, then=Value("Medium")),
default=Value("High"),
output_field=CharField()
)
).values('score_bucket').annotate(count=Count('id'))

def get_override_stats(self, since):
return TenderClauseOverride.objects.filter(
applied_at__gte=since
).values('reason').annotate(
count=Count('id'),
avg_risk=Avg('override_risk_score')
)
```

**9. ui/risk_bar_component.dart** (Visual indicator)
```dart
import 'package:flutter/material.dart';

class RiskBar extends StatelessWidget {
final double riskScore;
final double confidence;

const RiskBar({required this.riskScore, this.confidence = 1.0});

@override
Widget build(BuildContext context) {
return Column(
children: [
LinearProgressIndicator(
value: riskScore / 10.0,
backgroundColor: Colors.grey[300],
color: _getColor(riskScore),
minHeight: 25,
),
Text("Confidence: ${(confidence * 100).toStringAsFixed(1)}%"),
Tooltip(
message: _getRiskDescription(riskScore),
child: Icon(Icons.info_outline, size: 16),
)
],
);
}

Color _getColor(double score) {
if (score < 4) return Colors.green;
if (score < 7) return Colors.orange;
return Colors.red;
}

String _getRiskDescription(double score) {
if (score < 4) return "Low Risk: Standard bidding procedures";
if (score < 7) return "Medium Risk: Requires mitigation planning";
return "High Risk: Consider no-bid or specialized strategy";
}
}
```

**10. dashboards/risk_plan_builder.py** (Mitigation wizard)
```python
class RiskPlanBuilder:
def generate_plan(self, risk_analysis):
plan = {
"tender_id": risk_analysis.tender.id,
"created": datetime.now(),
"sections": []
}

for risk in risk_analysis.key_risk_factors:
section = {
"risk_factor": risk['feature'],
"impact_score": risk['impact'],
"actions": self._get_mitigation_actions(risk),
"bee_services": self._get_recommended_services(risk)
}
plan['sections'].append(section)

return plan

def _get_mitigation_actions(self, risk):
if "liquidated_damages" in risk['feature']:
return [
"Negotiate damages cap prior to bid submission",
"Add 15% contingency to affected line items"
]
# ... other risk-specific actions ...

def _get_recommended_services(self, risk):
if risk['impact'] > 0.3:
return ["legal_review", "insurance_specialist"]
return ["contract_advisor"]
```

# Update choices in TranslatedClauseText
language = models.CharField(max_length=10, choices=[
('zu', 'Zulu'), ('xh', 'Xhosa'), ('af', 'Afrikaans'), ('en', 'English'),
('st', 'Sotho'), ('tn', 'Tswana'), ('ve', 'Venda')
])



from google.cloud import translate_v2 as translate
class TranslationEngine:
def translate_clause(self, text, target_language):
client = translate.Client()
result = client.translate(text, target_language=target_language)
return result['translatedText']




def _format_explanation(self, shap_values):
narrative = "The decision to bid is influenced by high liquidated damages (42% impact), which could lead to financial penalties if delays occur."
return {
"summary": self.explainer.summary_plot(shap_values),
"factors": [...],
"narrative": narrative
}


class HybridModelRouter:
def get_dynamic_threshold(self, features):
if features['tender_value'] > 1000000:
return 0.75 # Stricter for high-value tenders
return 0.65

def predict(self, features):
threshold = self.get_dynamic_threshold(features)
ml_pred, ml_confidence = self.ml_predictor.predict(features)
if ml_confidence >= threshold:
return ml_pred, ml_confidence
return self.rules_engine.predict(features), 1.0



def rules_engine(self, features):
if features['liquidated_damages'] > 0.1:
return "No_Bid"
elif features['issuer_risk'] > 7.0:
return "Caution"
elif features['regulatory_compliance'] < 0.8:
return "No_Bid"
elif features['subcontractor_risk'] > 0.5:
return "Caution"
return "Proceed"





from hdbscan import HDBSCAN
class QueryClusterEngine:
def __init__(self):
self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
self.cluster_model = HDBSCAN(min_cluster_size=2, min_samples=1)
self.feature_store = FeatureStore()

def cluster_new_queries(self):
unclustered = ClauseQuery.objects.filter(clusters__isnull=True)
texts = [q.query_text for q in unclustered]
if not texts:
return
embeddings = self.feature_store.get_cached_embeddings(texts) or self.model.encode(texts)
clusters = self.cluster_model.fit_predict(embeddings)
self.feature_store.cache_embeddings(texts, embeddings)
# Save clusters as before




class RiskBar extends StatelessWidget {
@override
Widget build(BuildContext context) {
return Column(
children: [
LinearProgressIndicator(
value: riskScore / 10.0,
backgroundColor: Colors.grey[300],
color: _getColor(riskScore),
minHeight: 25,
semanticsLabel: 'Risk level: ${riskScore.toStringAsFixed(1)} out of 10',
),
Text(
"Confidence: ${(confidence * 100).toStringAsFixed(1)}%",
style: TextStyle(color: Theme.of(context).textTheme.bodyText1.color),
),
Tooltip(
message: _getRiskDescription(riskScore),
child: Icon(
Icons.info_outline,
size: 16,
semanticLabel: 'Risk description',
),
)
],
);
}
}




from fastapi import FastAPI, Depends
from fastapi.security import OAuth2PasswordBearer
app = FastAPI()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

@app.get("/clauses/{clause_id}")
async def get_clause(clause_id: str, token: str = Depends(oauth2_scheme)):
clause = ContractClause.objects.get(id=clause_id)
return {
"title": clause.title,
"risk_level": clause.risk_level,
"description": clause.description
}





class OnboardingWizard extends StatefulWidget {
@override
_OnboardingWizardState createState() => _OnboardingWizardState();
}
class _OnboardingWizardState extends State<OnboardingWizard> {
int _step = 0;
final _steps = [
{"title": "Understand Risk", "video": "risk_bar_tutorial.mp4"},
{"title": "Mitigate Risks", "video": "mitigation_plan_tutorial.mp4"}
];
@override
Widget build(BuildContext context) {
return Scaffold(
body: Column(
children: [
Text(_steps[_step]["title"]),
VideoPlayer(url: _steps[_step]["video"]),
ElevatedButton(
onPressed: () => setState(() => _step++),
child: Text("Next"),
)
],
),
);
}
}




from fastapi import WebSocket
class CollaborationService:
async def handle_collaboration(self, websocket: WebSocket, clause_id: str):
await websocket.accept()
while True:
data = await websocket.receive_json()
if data["type"] == "annotation":
ClauseAnnotation.objects.create(
clause_id=clause_id,
user_id=data["user_id"],
text=data["text"]
)
await websocket.send_json({"status": "saved"})




from py2neo import Graph
class RiskPlanBuilder:
def __init__(self):
self.graph = Graph("bolt://localhost:7687", auth=("neo4j", "password"))

def generate_plan(self, risk_analysis):
plan = {"tender_id": risk_analysis.tender.id, "sections": []}
for risk in risk_analysis.key_risk_factors:
# Query knowledge graph for issuer-specific mitigation
query = """
MATCH (t:Tender {id: $tender_id})-[:HAS_ISSUER]->(i:Issuer)
MATCH (i)-[:HAS_RISK]->(r:Risk {factor: $factor})
RETURN r.mitigation
"""
mitigations = self.graph.run(query, tender_id=risk_analysis.tender.id, factor=risk['feature']).data()
section = {
"risk_factor": risk['feature'],
"actions": [m["r.mitigation"] for m in mitigations] or self._get_mitigation_actions(risk)
}
plan["sections"].append(section)
return plan



graph TD
A[Tender Upload] --> B[AI Clause Parsing]
B --> C{Multilingual Translation}
C --> D[Standard Clause Matching]
D -->|Match Found| E[Risk Scoring with SHAP]
D -->|No Match| F[Custom Clause Analysis]
E --> G{Confidence > Dynamic Threshold?}
F --> G
G -->|Yes| H[Generate Explanation & Visualization]
G -->|No| I[Rules Engine with Expanded Logic]
H --> J[Generate Contextual Mitigation Plan]
I --> J
J --> K[Real-Time Collaboration]
K --> L[Blockchain Audit Trail]
L --> M[Interactive Dashboard & API Output]








### Enhanced Workflow Integration:

```mermaid
graph TD
A[Tender Parsing] --> B[Clause Identification]
B --> C{Standard Clause?}
C -->|Yes| D[Apply Predefined Risk]
C -->|No| E[Custom Risk Analysis]
D --> F[Hybrid Model Prediction]
E --> F
F --> G{Confidence > 65%?}
G -->|Yes| H[SHAP Explanation]
G -->|No| I[Rules Engine Fallback]
H --> J[Generate Mitigation Plan]
I --> J
J --> K[Blockchain Anchoring]
K --> L[Dashboard Reporting]
```

### Strategic Monitoring Metrics:

1. **Risk Distribution Health**
- Target: ≥70% Low/Medium risk tenders
- Alert: >30% High risk concentration

2. **AI Confidence Metrics**
- Target: ≥80% predictions >65% confidence
- Alert: Confidence drops >15% week-over-week

3. **Clause Override Rate**
- Target: <5% of clauses overridden
- Alert: >10% override rate for any issuer

4. **Query Resolution Speed**
- Target: <48hr average response time
- Alert: >72hr response time for critical clauses

### Implementation Roadmap:

1. **Week 1: Core Enhancements**
- Deploy SHAP explainer with test tenders
- Implement confidence scoring
- Add page number traceability

2. **Week 2: Multilingual & NLP**
- Launch Zulu/Afrikaans translations
- Deploy query clustering engine
- Build cluster dashboard

3. **Week 3: UI & Monitoring**
- Release RiskBar component
- Implement AI performance dashboard
- Connect to blockchain anchoring

4. **Week 4: Pilot & Refinement**
- A/B test with 3 provincial departments
- Calibrate hybrid model thresholds
- Finalize mitigation wizard

This implementation transforms the Contracts module into an AI-powered legal intelligence platform that provides:
- **Transparent** risk explanations with SHAP
- **Confident** decisions through hybrid modeling
- **Actionable** insights via mitigation planning
- **Inclusive** access with multilingual support
- **Continuous** improvement through NLP clustering

The system now meets enterprise standards for explainable AI while addressing unique South African procurement requirements through its specialized focus on GCC compliance, multilingual support, and blockchain-verified audit trails.


```sql
-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ======================
-- 1. Contract Standards
-- ======================
CREATE TABLE contracts_contractstandard (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
name VARCHAR(100) NOT NULL,
code VARCHAR(20) UNIQUE NOT NULL,
version VARCHAR(50),
governing_body VARCHAR(255),
description TEXT,
applicable_sectors JSONB DEFAULT '[]' CHECK (jsonb_typeof(applicable_sectors) = 'array'),
enforceable_in JSONB DEFAULT '[]' CHECK (jsonb_typeof(enforceable_in) = 'array'),
is_active BOOLEAN DEFAULT TRUE,
created_at TIMESTAMPTZ DEFAULT NOW(),
updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ======================
-- 2. Contract Clauses (Enhanced)
-- ======================
CREATE TABLE contracts_contractclause (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
contract_standard_id UUID NOT NULL REFERENCES contracts_contractstandard(id) ON DELETE CASCADE,
title VARCHAR(200) NOT NULL,
reference_code VARCHAR(50),
description TEXT,
risk_level VARCHAR(10) CHECK (risk_level IN ('Low', 'Medium', 'High')),
risk_weight FLOAT DEFAULT 1.0 CHECK (risk_weight BETWEEN 0.1 AND 5.0),
recommended_action TEXT,
explanation_model JSONB DEFAULT '{}'::JSONB
CHECK (jsonb_schema_valid('{"type":"object","properties":{"risk_factor":{"type":"number"},"reason":{"type":"string"}}}'::JSONB, explanation_model)),
tags JSONB DEFAULT '[]' CHECK (jsonb_typeof(tags) = 'array'),
visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'registered', 'verified', 'admin')),
source_page_number INT, -- NEW: For traceability
is_flagged BOOLEAN DEFAULT FALSE,
last_updated TIMESTAMPTZ DEFAULT NOW(),
embedding_vector VECTOR(384) -- For NLP similarity search
);

-- ======================
-- 3. Multilingual Clause Texts (NEW)
-- ======================
CREATE TABLE contracts_translatedclausetext (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
clause_id UUID NOT NULL REFERENCES contracts_contractclause(id) ON DELETE CASCADE,
language_code CHAR(2) NOT NULL CHECK (language_code IN ('zu', 'xh', 'af', 'en')),
translated_title VARCHAR(200) NOT NULL,
translated_description TEXT NOT NULL,
last_updated TIMESTAMPTZ DEFAULT NOW(),
UNIQUE (clause_id, language_code)
);

-- ======================
-- 4. Tender-Contract Links
-- ======================
CREATE TABLE contracts_tendercontractlink (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
tender_id UUID UNIQUE NOT NULL REFERENCES tender_parsedtender(id) ON DELETE CASCADE,
contract_standard_id UUID REFERENCES contracts_contractstandard(id),
detected_via_ai BOOLEAN DEFAULT FALSE,
confidence FLOAT CHECK (confidence BETWEEN 0 AND 1.0)
);

-- ======================
-- 5. Clause Compliance Logs
-- ======================
CREATE TABLE contracts_clausecompliancelog (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
clause_id UUID NOT NULL REFERENCES contracts_contractclause(id),
tender_id UUID NOT NULL REFERENCES tender_parsedtender(id),
submitted_by_id UUID REFERENCES users_bee(id),
status VARCHAR(20) NOT NULL CHECK (status IN ('Pending', 'Submitted', 'Verified', 'Rejected')),
comment TEXT,
submitted_at TIMESTAMPTZ DEFAULT NOW(),
verified_at TIMESTAMPTZ
);

-- ======================
-- 6. Clause Queries (Enhanced)
-- ======================
CREATE TABLE contracts_clausequery (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
clause_id UUID NOT NULL REFERENCES contracts_contractclause(id),
tender_id UUID NOT NULL REFERENCES tender_parsedtender(id),
requested_by_id UUID NOT NULL REFERENCES auth_user(id),
query_text TEXT NOT NULL,
status VARCHAR(20) DEFAULT 'Submitted'
CHECK (status IN ('Submitted', 'Awaiting_Response', 'Answered', 'Clarified')),
response_from_issuer TEXT,
is_public BOOLEAN DEFAULT FALSE,
response_file VARCHAR(255),
created_at TIMESTAMPTZ DEFAULT NOW(),
answered_at TIMESTAMPTZ,
embedding_vector VECTOR(384) -- For clustering
);

-- ======================
-- 7. Query Clusters (NEW)
-- ======================
CREATE TABLE contracts_clausequerycluster (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
centroid_text TEXT NOT NULL,
cluster_id INT NOT NULL,
keywords TEXT[] DEFAULT '{}',
first_detected TIMESTAMPTZ DEFAULT NOW(),
last_occurrence TIMESTAMPTZ DEFAULT NOW()
);

-- Junction table for query-cluster relationship
CREATE TABLE contracts_query_cluster_membership (
query_id UUID NOT NULL REFERENCES contracts_clausequery(id) ON DELETE CASCADE,
cluster_id UUID NOT NULL REFERENCES contracts_clausequerycluster(id) ON DELETE CASCADE,
PRIMARY KEY (query_id, cluster_id)
);

-- ======================
-- 8. Smart Contract Templates
-- ======================
CREATE TABLE contracts_smartcontracttemplate (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
contract_standard_id UUID REFERENCES contracts_contractstandard(id),
name VARCHAR(150) NOT NULL,
file_type VARCHAR(10) CHECK (file_type IN ('DOCX', 'JSON')),
template_file VARCHAR(255) NOT NULL,
description TEXT,
fields_required JSONB DEFAULT '[]' CHECK (jsonb_typeof(fields_required) = 'array')
);

-- ======================
-- 9. Clause Risk Index
-- ======================
CREATE TABLE contracts_clauseriskindex (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
clause_id UUID NOT NULL REFERENCES contracts_contractclause(id),
region VARCHAR(100) NOT NULL,
frequency INT DEFAULT 0 CHECK (frequency >= 0),
dispute_count INT DEFAULT 0 CHECK (dispute_count >= 0),
average_score FLOAT DEFAULT 0.0 CHECK (average_score BETWEEN 0 AND 10.0),
last_updated TIMESTAMPTZ DEFAULT NOW()
);

-- ======================
-- 10. Issuer Risk Profile
-- ======================
CREATE TABLE contracts_issuerriskprofile (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
entity_id UUID NOT NULL REFERENCES entities_publicentity(id),
total_tenders INT DEFAULT 0 CHECK (total_tenders >= 0),
flagged_clauses_count INT DEFAULT 0 CHECK (flagged_clauses_count >= 0),
average_response_delay INTERVAL,
dispute_history_score FLOAT DEFAULT 0.0 CHECK (dispute_history_score BETWEEN 0 AND 10.0),
last_updated TIMESTAMPTZ DEFAULT NOW()
);

-- ======================
-- 11. Clause Feedback
-- ======================
CREATE TABLE contracts_clausefeedback (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
clause_id UUID NOT NULL REFERENCES contracts_contractclause(id),
user_id UUID REFERENCES auth_user(id),
rating INT NOT NULL CHECK (rating BETWEEN 1 AND 5),
comment TEXT,
created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ======================
-- 12. Tender-Specific Clauses (NEW)
-- ======================
CREATE TABLE contracts_tenderspecificclause (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
tender_id UUID NOT NULL REFERENCES tender_parsedtender(id),
base_clause_id UUID REFERENCES contracts_contractclause(id),
custom_title VARCHAR(200) NOT NULL,
custom_text TEXT NOT NULL,
risk_level VARCHAR(10) CHECK (risk_level IN ('Low', 'Medium', 'High')),
applied_by_id UUID REFERENCES auth_user(id),
applied_at TIMESTAMPTZ DEFAULT NOW()
);

-- ======================
-- 13. Contract Risk Analysis (Enhanced)
-- ======================
CREATE TABLE contracts_contractriskanalysis (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
tender_id UUID NOT NULL REFERENCES tender_parsedtender(id),
analyzed_for_id UUID NOT NULL REFERENCES auth_user(id),
risk_score FLOAT NOT NULL CHECK (risk_score BETWEEN 0 AND 10.0),
confidence_score FLOAT NOT NULL CHECK (confidence_score BETWEEN 0 AND 1.0), -- NEW
executive_summary TEXT NOT NULL,
recommendation VARCHAR(20) NOT NULL
CHECK (recommendation IN ('Proceed', 'Caution', 'No_Bid')),
key_risk_factors JSONB NOT NULL DEFAULT '[]'
CHECK (jsonb_schema_valid('{"type":"array","items":{"type":"object"}}'::JSONB, key_risk_factors)),
analysis_date TIMESTAMPTZ DEFAULT NOW(),
model_version VARCHAR(50) NOT NULL,
coaching_notes TEXT,
anchored_transaction VARCHAR(128), -- Blockchain reference
anchored_at TIMESTAMPTZ
);

-- ======================
-- 14. AI Performance Metrics (NEW)
-- ======================
CREATE TABLE contracts_aiperformancemetrics (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
metric_date DATE NOT NULL DEFAULT CURRENT_DATE,
risk_score_distribution JSONB NOT NULL DEFAULT '{}',
clause_match_rate FLOAT CHECK (clause_match_rate BETWEEN 0 AND 1.0),
override_frequency JSONB DEFAULT '{}',
confidence_trends JSONB DEFAULT '{}',
created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ======================
-- 15. Mitigation Plans (NEW)
-- ======================
CREATE TABLE contracts_mitigationplan (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
risk_analysis_id UUID NOT NULL REFERENCES contracts_contractriskanalysis(id),
plan_data JSONB NOT NULL DEFAULT '{}',
created_by_id UUID REFERENCES auth_user(id),
created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ======================
-- Indexes for Performance
-- ======================
-- Clause indexes
CREATE INDEX idx_clauses_title_trgm ON contracts_contractclause USING gin(title gin_trgm_ops);
CREATE INDEX idx_clauses_risk_level ON contracts_contractclause(risk_level);
CREATE INDEX idx_clauses_embedding ON contracts_contractclause USING ivfflat (embedding_vector vector_cosine_ops);

-- Query indexes
CREATE INDEX idx_queries_status ON contracts_clausequery(status);
CREATE INDEX idx_queries_created ON contracts_clausequery(created_at);
CREATE INDEX idx_queries_embedding ON contracts_clausequery USING ivfflat (embedding_vector vector_cosine_ops);

-- Risk analysis indexes
CREATE INDEX idx_riskanalysis_score ON contracts_contractriskanalysis(risk_score);
CREATE INDEX idx_riskanalysis_recommendation ON contracts_contractriskanalysis(recommendation);
CREATE INDEX idx_riskanalysis_confidence ON contracts_contractriskanalysis(confidence_score);

-- Cluster indexes
CREATE INDEX idx_cluster_keywords ON contracts_clausequerycluster USING gin(keywords);

-- Multilingual indexes
CREATE INDEX idx_translations_language ON contracts_translatedclausetext(language_code);

-- ======================
-- Security Policies (RLS Example)
-- ======================
ALTER TABLE contracts_contractclause ENABLE ROW LEVEL SECURITY;
CREATE POLICY clause_select_policy ON contracts_contractclause
FOR SELECT USING (
visibility = 'public' OR
(visibility = 'registered' AND current_user_role() IN ('registered', 'verified', 'admin')) OR
(visibility = 'verified' AND current_user_role() IN ('verified', 'admin')) OR
(visibility = 'admin' AND current_user_role() = 'admin')
);

-- ======================
-- Materialized Views for Reporting
-- ======================
CREATE MATERIALIZED VIEW mv_clause_risk_summary AS
SELECT
c.id AS clause_id,
c.title,
c.risk_level,
COUNT(f.id) AS feedback_count,
AVG(f.rating) AS avg_rating,
MAX(r.dispute_count) AS max_disputes
FROM contracts_contractclause c
LEFT JOIN contracts_clausefeedback f ON c.id = f.clause_id
LEFT JOIN contracts_clauseriskindex r ON c.id = r.clause_id
GROUP BY c.id;

CREATE MATERIALIZED VIEW mv_issuer_risk_profile AS
SELECT
i.entity_id,
e.name AS entity_name,
i.total_tenders,
i.flagged_clauses_count,
EXTRACT(EPOCH FROM i.average_response_delay)/3600 AS avg_response_hours,
i.dispute_history_score,
AVG(a.risk_score) AS avg_risk_score
FROM contracts_issuerriskprofile i
JOIN entities_publicentity e ON i.entity_id = e.id
LEFT JOIN contracts_contractriskanalysis a ON e.id = a.tender_id
GROUP BY i.id, e.name;

-- ======================
-- Seed Data: Contract Standards
-- ======================
INSERT INTO contracts_contractstandard (id, name, code, version, governing_body, description, applicable_sectors, enforceable_in)
VALUES
(uuid_generate_v4(), 'JBCC Principal Building Agreement', 'JBCC', '6.2', 'JBCC South Africa',
'Standard contract for building works', '["building", "civil"]', '["South Africa"]'),

(uuid_generate_v4(), 'FIDIC Red Book', 'FIDIC', '2017', 'FIDIC International',
'Conditions of Contract for Construction', '["infrastructure", "engineering"]', '["South Africa", "Namibia", "Botswana"]'),

(uuid_generate_v4(), 'NEC3 Engineering & Construction Contract', 'NEC3', '', 'NEC Global',
'Collaborative project management contract', '["engineering", "roads", "maintenance"]', '["South Africa", "UK"]'),

(uuid_generate_v4(), 'GCC - General Conditions of Contract', 'GCC', '2015', 'National Treasury RSA',
'Standard contract for goods/services procurement', '["goods", "services", "supply_chain"]', '["South Africa"]');

-- ======================
-- Seed Data: Contract Clauses
-- ======================
WITH standards AS (
SELECT id FROM contracts_contractstandard WHERE code = 'JBCC'
)
INSERT INTO contracts_contractclause (id, contract_standard_id, title, reference_code, description, risk_level, source_page_number)
SELECT
uuid_generate_v4(),
standards.id,
'Retention',
'6.5',
'Percentage of payments held until completion',
'Medium',
42
FROM standards;

WITH standards AS (
SELECT id FROM contracts_contractstandard WHERE code = 'FIDIC'
)
INSERT INTO contracts_contractclause (id, contract_standard_id, title, reference_code, description, risk_level, source_page_number)
SELECT
uuid_generate_v4(),
standards.id,
'Liquidated Damages',
'17.4',
'Damages payable for delay',
'High',
87
FROM standards;

WITH standards AS (
SELECT id FROM contracts_contractstandard WHERE code = 'GCC'
)
INSERT INTO contracts_contractclause (id, contract_standard_id, title, reference_code, description, risk_level, source_page_number)
SELECT
uuid_generate_v4(),
standards.id,
'Termination for Default',
'15.1',
'Allows state termination for non-delivery',
'High',
112
FROM standards;

-- ======================
-- Refresh Materialized Views
-- ======================
REFRESH MATERIALIZED VIEW mv_clause_risk_summary;
REFRESH MATERIALIZED VIEW mv_issuer_risk_profile;

-- ======================
-- Sample Risk Analysis
-- ======================
INSERT INTO contracts_contractriskanalysis (
id, tender_id, analyzed_for_id, risk_score, confidence_score,
executive_summary, recommendation, key_risk_factors, model_version
)
VALUES (
uuid_generate_v4(),
(SELECT id FROM tender_parsedtender LIMIT 1),
(SELECT id FROM auth_user LIMIT 1),
6.7,
0.82,
'High risk due to liquidated damages and material volatility',
'Caution',
'[{"factor": "Liquidated Damages", "weight": 0.42, "page": 87},
{"factor": "Steel Price Volatility", "weight": 0.28}]'::JSONB,
'v2.1-shap'
);

-- ======================
-- Sample Multilingual Clause
-- ======================
INSERT INTO contracts_translatedclausetext (
id, clause_id, language_code, translated_title, translated_description
)
SELECT
uuid_generate_v4(),
id,
'zu',
'Ukunqunyelwa Kwemali',
'Ingxenye yemali egcinwa ibe ngu-5% yalo msebenzi kuze kube sekugcineni'
FROM contracts_contractclause
WHERE reference_code = '6.5' LIMIT 1;
```

### Key Enhancements in Schema:

1. **AI-Specific Optimizations**
- `embedding_vector` columns for NLP similarity search (using pgvector)
- Materialized views for risk summary reports
- Confidence scoring in risk analysis
- Dedicated AI performance metrics table

2. **Enhanced Security**
- Field-level JSON schema validation
- Row-Level Security (RLS) policies
- Blockchain transaction anchoring
- Permission-based visibility controls

3. **Multilingual Support**
- Dedicated translation table with language codes
- Indexed for efficient language-specific queries
- Supports Zulu, Xhosa, Afrikaans, English

4. **Clause Management**
- Page number tracking for audit trails
- Risk weight quantification
- Tender-specific clause customization

5. **Query Intelligence**
- NLP clustering via embedding vectors
- Keyword indexing for fast lookup
- Cluster membership tracking

6. **Performance Features**
- Trigram indexes for fast text search
- IVFFlat indexes for vector similarity
- Materialized views for complex aggregations

7. **Data Integrity**
- Comprehensive CHECK constraints
- JSON schema validation for critical fields
- Referential integrity with CASCADE deletes

### Critical Queries for AI Operations:

**1. Find Similar Clauses (NLP Search)**
```sql
SELECT id, title, description,
(embedding_vector <=> query_embedding) AS similarity
FROM contracts_contractclause
ORDER BY embedding_vector <=> query_embedding
LIMIT 5;
```

**2. Cluster Similar Queries**
```sql
INSERT INTO contracts_query_cluster_membership (query_id, cluster_id)
SELECT q.id, c.id
FROM contracts_clausequery q
JOIN contracts_clausequerycluster c
ON (q.embedding_vector <=> c.centroid_embedding) < 0.25;
```

**3. Generate Risk Report**
```sql
SELECT
r.risk_score,
r.confidence_score,
r.recommendation,
f->>'factor' AS risk_factor,
f->>'weight' AS weight
FROM contracts_contractriskanalysis r
CROSS JOIN jsonb_array_elements(r.key_risk_factors) f
WHERE tender_id = 'tender-uuid'
ORDER BY (f->>'weight')::FLOAT DESC;
```

**4. Retrieve Multilingual Clause**
```sql
SELECT
c.title AS english_title,
t.translated_title,
t.translated_description
FROM contracts_contractclause c
LEFT JOIN contracts_translatedclausetext t
ON c.id = t.clause_id AND t.language_code = 'zu'
WHERE c.id = 'clause-uuid';
```

**5. Monitor AI Performance**
```sql
SELECT
metric_date,
(risk_score_distribution->>'High')::FLOAT AS high_risk_pct,
clause_match_rate,
(override_frequency->>'count')::INT AS overrides
FROM contracts_aiperformancemetrics
WHERE metric_date > CURRENT_DATE - INTERVAL '30 days'
ORDER BY metric_date DESC;
```

### Schema Management Recommendations:

1. **Partitioning Strategy**
```sql
-- Time-based partitioning for analysis table
CREATE TABLE risk_analysis_2023 PARTITION OF contracts_contractriskanalysis
FOR VALUES FROM ('2023-01-01') TO ('2024-01-01');
```

2. **Vector Index Tuning**
```sql
SET ivfflat.probes = 10; -- For approximate search quality/speed balance
```

3. **Automated Refresh**
```sql
-- Daily refresh for materialized views
CREATE OR REPLACE FUNCTION refresh_views() RETURNS VOID AS $$
BEGIN
REFRESH MATERIALIZED VIEW mv_clause_risk_summary;
REFRESH MATERIALIZED VIEW mv_issuer_risk_profile;
END;
$$ LANGUAGE plpgsql;

CREATE EVENT TRIGGER daily_refresh
ON SCHEDULE '0 3 * * *' -- 3AM daily
DO CALL refresh_views();
```

4. **Anonymization for Development**
```sql
CREATE EXTENSION anon;
SECURITY LABEL FOR anon ON COLUMN contracts_clausequery.requested_by_id
IS 'MASKED WITH FUNCTION anon.fake_id()';
```

This schema supports all AI-driven features while maintaining enterprise-grade security, multilingual capabilities, and high performance for South Africa's public procurement ecosystem. The design enables:
- **Explainable AI** through SHAP integration
- **Continuous learning** via feedback loops
- **Regulatory compliance** with audit trails
- **Market-responsive** risk analysis
- **Scalable operations** through partitioning and indexing
