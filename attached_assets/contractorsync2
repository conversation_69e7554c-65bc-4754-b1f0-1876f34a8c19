contractorsync/
├── __init__.py # Marks directory as Python package
├── admin.py # Django admin interface for model management
├── apps.py # Django app configuration
├── filters.py # Custom queryset filters (geospatial, trust, trade)
├── models.py # Core data models (Tenant, SubcontractorProfile, etc.)
├── serializers.py # REST API serializers (tenant-aware)
├── signals.py # Automation for badges, AI matching, trust updates
├── tasks.py # Celery async tasks (vetting, AI, GA, MLOps)
├── urls.py # URL routes for views and APIs
├── views.py # HTTP request handlers and API endpoints
│
├── middleware/ # Custom Django middleware
│ ├── __init__.py
│ └── tenant_middleware.py # Determines current tenant from request
│
├── services/ # Business logic services
│ ├── __init__.py
│ ├── contract_service.py # Smart contract creation and escrow
│ ├── compliance_service.py # Certification and vetting logic
│ ├── ai_matching_service.py # AI-driven tender-subcontractor matching
│ ├── ga_team_optimizer.py # Genetic algorithm for team formation
│ └── ai_agents/ # Autonomous AI agents
│ ├── __init__.py
│ ├── compliance_agent.py # Proactive compliance monitoring
│ └── rfq_suggestion_agent.py # RFQ scope suggestions
│
├── utils/ # Utility functions
│ ├── __init__.py
│ ├── compliance_checker.py # External API verification (CIDB, Experian)
│ ├── signature_utils.py # Digital signatures and MFA
│ ├── trust_calculator.py # Trust score and heading computation
│ ├── pricing_calculator.py # ZAR pricing via PriceSync
│ ├── ocr_parser.py # OCR for certifications and catalogs
│ └── notification_service.py # Email/WhatsApp notifications
│
├── mlops/ # Machine Learning Operations
│ ├── __init__.py
│ ├── train_embedding_model.py # Script for training AI models
│ ├── model_registry.py # Manages AI model loading/versioning
│ └── data_preprocessing.py # Data preparation for AI training
│
├── templates/
│ └── contractorsync/
│ ├── base.html # Base template with Tailwind CSS
│ ├── profile_builder.html # CV-like wizard for onboarding
│ ├── team_map.html # Mapbox UI for subcontractor discovery
│ ├── community.html # TikTok-style social feed
│ ├── rfq_form.html # RFQ creation with AI suggestions
│ └── certification_expiry_notice.html # Email for expiry reminders
│
├── static/
│ └── contractorsync/
│ ├── css/
│ │ ├── mapbox.css # Mapbox and card styling
│ │ └── tailwind.css # Compiled Tailwind CSS
│ └── js/
│ ├── wizard.js # Profile builder navigation
│ ├── animations.js # Confetti and swipe animations
│ └── mapbox.js # Mapbox GL JS interactions
│
├── migrations/
│ ├── __init__.py
│ └── 0001_initial.py # Auto-generated model migrations
│
├── tests/
│ ├── __init__.py
│ ├── test_models.py # Unit tests for models
│ ├── test_views.py # Unit tests for views and APIs
│ ├── test_services.py # Unit tests for services
│ └── test_utils.py # Unit tests for utilities
│
├── management/
│ └── commands/
│ ├── __init__.py
│ └── seed_data.py # Command to seed trade categories
│
├── docs/
│ ├── api.md # API documentation
│ ├── onboarding.md # Onboarding process guide
│ └── deployment.md # AWS deployment instructions
│
├── Dockerfile # Docker configuration for containerization
├── docker-compose.yml # Local development with PostgreSQL, Redis
├── requirements.txt # Python dependencies
├── package.json # Node.js dependencies for frontend assets
├── .env.example # Environment variable template
├── README.md # Module overview and setup instructions
└── setup.py # Package installation script



from django.apps import AppConfig

class ContractorSyncConfig(AppConfig):
default_auto_field = 'django.db.models.BigAutoField'
name = 'contractorsync'
verbose_name = 'ContractorSync'




django==4.2.7
djangorestframework==3.14.0
django-htmx==1.17.3
django-storages==1.14.2
django-paystack==0.1.0
django-cryptography==1.1
psycopg2-binary==2.9.9
celery==5.3.6
redis==5.0.1
geopandas==0.14.1
mapbox-sdk-py==0.13.2
requests==2.31.0
pybreaker==1.1.0
tenacity==8.2.3
transformers==4.30.0
sentence-transformers==2.2.2
faiss-cpu==1.7.4
deap==1.3.1
scikit-learn==1.3.0
numpy==1.26.0
pytesseract==0.3.10
python-dotenv==1.0.0




DATABASE_URL=postgres://user:password@localhost:5432/bidbeez
REDIS_URL=redis://localhost:6379/0
MAPBOX_TOKEN=your_mapbox_token
PAYSTACK_SECRET=your_paystack_secret
EXPERIAN_API_KEY=your_experian_key
SANTAM_API_KEY=your_santam_key
TWILIO_SID=your_twilio_sid
TWILIO_TOKEN=your_twilio_token
OPENAI_API_KEY=your_openai_key
ML_MODEL_PATH=/app/mlops/models/
TENANT_MODEL_SCHEMA=public
DEFAULT_TENANT_ID=xxxx-xxxx-xxxx
TWILIO_WHATSAPP_FROM_NUMBER=+14155238886
DEFAULT_FROM_EMAIL=<EMAIL>




FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "bidbeez.wsgi"]




version: '3.8'
services:
web:
build: .
ports:
- "8000:8000"
depends_on:
- db
- redis
env_file:
- .env
db:
image: postgres:15
environment:
POSTGRES_DB: bidbeez
POSTGRES_USER: user
POSTGRES_PASSWORD: password
redis:
image: redis:7




# ContractorSync Module
A Django module for subcontractor management within Bidbeez, featuring multi-tenancy, AI matching, genetic algorithms, and global-standard vetting.

## Setup
1. Install dependencies: `pip install -r requirements.txt`
2. Configure `.env` based on `.env.example`
3. Run migrations: `python manage.py migrate`
4. Start development server: `docker-compose up`

## Deployment
See `docs/deployment.md` for AWS ECS instructions.




from setuptools import setup, find_packages

setup(
name='contractorsync',
version='1.0.0',
packages=find_packages(),
install_requires=open('requirements.txt').readlines(),
)




from django.contrib import admin
from .models import Tenant, TradeCategory, SubcontractorProfile, ContractorSmartContract, ContractorCertification, CommunityPost, OptimizedTeamSuggestion

@admin.register(Tenant)
class TenantAdmin(admin.ModelAdmin):
list_display = ('name', 'slug', 'is_active')
search_fields = ('name', 'slug')

@admin.register(SubcontractorProfile)
class SubcontractorProfileAdmin(admin.ModelAdmin):
list_display = ('company_name', 'tenant', 'trust_heading', 'trust_score')
list_filter = ('tenant', 'trust_heading', 'trade__sector')
search_fields = ('company_name',)



import django_filters
from django.contrib.gis.db.models.functions import Distance
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from .models import SubcontractorProfile

class SubcontractorProfileFilter(django_filters.FilterSet):
tenant = django_filters.UUIDFilter(field_name='tenant__id')
trade = django_filters.ModelChoiceFilter(queryset=TradeCategory.objects.all())
trust_heading = django_filters.ChoiceFilter(choices=[
('Corporate Trusted', 'Corporate Trusted'),
('Standard', 'Standard'),
('Basic', 'Basic')
])
max_distance = django_filters.NumberFilter(method='filter_by_distance')

def filter_by_distance(self, queryset, name, value):
lat = self.request.query_params.get('lat')
lon = self.request.query_params.get('lon')
if lat and lon:
point = Point(float(lon), float(lat), srid=4326)
return queryset.filter(
location__distance_lte=(point, D(km=value))
).annotate(distance=Distance('location', point))
return queryset

class Meta:
model = SubcontractorProfile
fields = ['tenant', 'trade', 'trust_heading']



from django.db import models
from django.contrib.auth.models import User
from django.contrib.gis.db import models as gis_models
from django.contrib.postgres.fields import JSONField, ArrayField
from hashlib import sha256
import json
import uuid

class TenantManager(models.Manager):
def for_tenant(self, tenant):
return self.get_queryset().filter(tenant=tenant)

class Tenant(models.Model):
id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
name = models.CharField(max_length=255, unique=True)
slug = models.SlugField(max_length=255, unique=True)
logo = models.ImageField(upload_to='tenant_logos/', blank=True)
primary_color = models.CharField(max_length=7, default='#007bff')
is_active = models.BooleanField(default=True)
created_at = models.DateTimeField(auto_now_add=True)

def __str__(self):
return self.name

class TradeCategory(models.Model):
name = models.CharField(max_length=100)
sector = models.CharField(max_length=50, choices=[
('Construction', 'Construction'), ('IT', 'IT'), ('Healthcare', 'Healthcare'),
('Services', 'Services'), ('Manufacturing', 'Manufacturing')
])
description = models.TextField(blank=True)

class Meta:
indexes = [models.Index(fields=['name', 'sector'])]

def __str__(self):
return f"{self.name} ({self.sector})"

class CertificationRequirement(models.Model):
trade = models.ForeignKey(TradeCategory, on_delete=models.CASCADE)
name = models.CharField(max_length=150)
issuing_body = models.CharField(max_length=100, choices=[
('CIDB', 'CIDB'), ('HPCSA', 'HPCSA'), ('ICASA', 'ICASA'), ('PSIRA', 'PSIRA'), ('Other', 'Other')
])
required = models.BooleanField(default=True)
valid_duration_months = models.IntegerField(default=12)

class Meta:
unique_together = ['trade', 'name']

def __str__(self):
return f"{self.name} ({self.trade})"

class ContractorCertification(models.Model):
user = models.ForeignKey(User, on_delete=models.CASCADE)
tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, null=True)
certification = models.ForeignKey(CertificationRequirement, on_delete=models.CASCADE)
uploaded_file = models.FileField(upload_to='certifications/')
issue_date = models.DateField()
expiry_date = models.DateField()
verified = models.BooleanField(default=False)
verification_details = JSONField(default=dict)

objects = TenantManager()

def __str__(self):
return f"{self.certification.name} - {self.user.username}"

class SubcontractorProfile(models.Model):
id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
user = models.OneToOneField(User, on_delete=models.CASCADE)
tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, null=True)
company_name = models.CharField(max_length=255)
logo = models.ImageField(upload_to='logos/', blank=True)
contact_email = models.CharField(max_length=255) # Encrypted
contact_phone = models.CharField(max_length=20)
whatsapp_number = models.CharField(max_length=20, blank=True)
trade = models.ForeignKey(TradeCategory, on_delete=models.SET_NULL, null=True)
company_size = models.CharField(max_length=20, choices=[('SME', 'SME'), ('Mid', 'Mid'), ('Large', 'Large')])
financial_capacity = models.DecimalField(max_digits=12, decimal_places=2, null=True)
credit_score = models.IntegerField(null=True)
insurances = JSONField(default=list)
equipment = JSONField(default=list)
past_projects = JSONField(default=list)
reviews = JSONField(default=list)
portfolio = JSONField(default=dict)
portfolio_embedding = ArrayField(models.FloatField(), blank=True, null=True, size=768)
sector_data = JSONField(default=dict)
location = gis_models.PointField(srid=4326, blank=True, null=True)
trust_score = models.FloatField(default=0.0)
trust_heading = models.CharField(max_length=50, default='Basic')
badges = JSONField(default=list)
audit_log = JSONField(default=list)
profile_type = models.CharField(max_length=20, default='Company', editable=False)
b_bbee_level = models.CharField(max_length=10, blank=True)
cidb_grade = models.CharField(max_length=10, blank=True)
is_verified = models.BooleanField(default=False)
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

objects = TenantManager()

def vet_subcontractor(self):
from .services.compliance_service import vet_subcontractor
return vet_subcontractor(self)

def __str__(self):
return self.company_name

class ContractorSmartContract(models.Model):
id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
STATUS_CHOICES = [('draft', 'Draft'), ('active', 'Active'), ('completed', 'Completed'), ('disputed', 'Disputed')]
bidder = models.ForeignKey(User, related_name='contractor_contracts', on_delete=models.CASCADE)
subcontractor = models.ForeignKey(SubcontractorProfile, on_delete=models.SET_NULL, null=True)
tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, null=True)
tender_title = models.CharField(max_length=255)
tender_id = models.CharField(max_length=100)
trade = models.ForeignKey(TradeCategory, on_delete=models.SET_NULL, null=True)
contract_value = models.DecimalField(max_digits=12, decimal_places=2)
location = gis_models.PointField(srid=4326, blank=True, null=True)
work_scope = JSONField()
work_scope_embedding = ArrayField(models.FloatField(), blank=True, null=True, size=768)
milestones = JSONField()
compliance_requirements = JSONField()
status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
contract_hash = models.CharField(max_length=256, unique=True)
escrow_transaction_id = models.CharField(max_length=100, blank=True)
audit_log = JSONField(default=list)
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

objects = TenantManager()

def generate_contract_hash(self):
data = {'bidder': str(self.bidder.id), 'subcontractor': str(self.subcontractor.id if self.subcontractor else 'N/A'), 'value': str(self.contract_value)}
return sha256(json.dumps(data, sort_keys=True).encode()).hexdigest()

def save(self, *args, **kwargs):
if not self.contract_hash:
self.contract_hash = self.generate_contract_hash()
super().save(*args, **kwargs)

class SubcontractorQuote(models.Model):
subcontractor = models.ForeignKey(User, on_delete=models.CASCADE)
contract = models.ForeignKey(ContractorSmartContract, on_delete=models.CASCADE)
price_zar = models.DecimalField(max_digits=12, decimal_places=2)
timeline_days = models.IntegerField()
additional_notes = models.TextField(blank=True)
submitted_at = models.DateTimeField(auto_now_add=True)

objects = TenantManager()

class CommunityPost(models.Model):
user = models.ForeignKey(User, on_delete=models.CASCADE)
tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, null=True)
content = models.TextField()
media = JSONField(default=list)
likes_count = models.IntegerField(default=0)
is_flagged = models.BooleanField(default=False)
created_at = models.DateTimeField(auto_now_add=True)

objects = TenantManager()

class DisqualificationRule(models.Model):
trade = models.ForeignKey(TradeCategory, on_delete=models.CASCADE)
rule_description = models.TextField()
auto_learned = models.BooleanField(default=False)
active = models.BooleanField(default=True)
created_at = models.DateTimeField(auto_now_add=True)

class OptimizedTeamSuggestion(models.Model):
id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
contract = models.ForeignKey(ContractorSmartContract, on_delete=models.CASCADE)
tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, null=True)
suggested_team_members = models.ManyToManyField(SubcontractorProfile)
score_details = JSONField(default=dict)
generated_at = models.DateTimeField(auto_now_add=True)
is_accepted = models.BooleanField(default=False)

objects = TenantManager()





from rest_framework import serializers
from .models import Tenant, SubcontractorProfile, ContractorSmartContract, CommunityPost, OptimizedTeamSuggestion

class TenantSerializer(serializers.ModelSerializer):
class Meta:
model = Tenant
fields = ['id', 'name', 'slug']

class SubcontractorProfileSerializer(serializers.ModelSerializer):
tenant = TenantSerializer(read_only=True)
location = serializers.SerializerMethodField()

def get_location(self, obj):
return {'latitude': obj.location.y, 'longitude': obj.location.x} if obj.location else None

class Meta:
model = SubcontractorProfile
fields = ['id', 'tenant', 'company_name', 'trust_score', 'trust_heading']

class OptimizedTeamSuggestionSerializer(serializers.ModelSerializer):
suggested_team_members = SubcontractorProfileSerializer(many=True)

class Meta:
model = OptimizedTeamSuggestion
fields = ['id', 'contract', 'suggested_team_members', 'score_details', 'generated_at']




from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import SubcontractorProfile, ContractorSmartContract
from .services.ai_matching_service import ai_auto_match_subcontractors

@receiver(post_save, sender=SubcontractorProfile)
def update_trust_score(sender, instance, created, **kwargs):
instance.vet_subcontractor()
if instance.is_profile_complete():
instance.badges.append('Profile Pro')
instance.save()

@receiver(post_save, sender=ContractorSmartContract)
def match_subcontractors(sender, instance, created, **kwargs):
if created:
ai_auto_match_subcontractors(instance)




from celery import shared_task
from django.core.mail import send_mail
from .models import ContractorCertification, CommunityPost, ContractorSmartContract
from .services.ga_team_optimizer import TeamOptimizer
from .utils.notification_service import send_email_notification

@shared_task
def send_certification_expiry_reminders():
certs = ContractorCertification.objects.filter(expiry_date__lte=timezone.now() + timezone.timedelta(days=30))
for cert in certs:
send_email_notification(cert.user.email, "Certification Expiry", f"Expires on {cert.expiry_date}")

@shared_task
def moderate_community_posts():
posts = CommunityPost.objects.filter(content__iregex=r'spam|inappropriate')
for post in posts:
post.is_flagged = True
post.save()

@shared_task(name="contractorsync.async_optimize_team_task")
def async_optimize_team_task(contract_id, tenant_id):
contract = ContractorSmartContract.objects.get(id=contract_id, tenant_id=tenant_id)
available_subs = SubcontractorProfile.objects.for_tenant(tenant_id).filter(is_verified=True)
optimizer = TeamOptimizer(contract, available_subs)
teams = optimizer.optimize()
for team in teams:
suggestion = OptimizedTeamSuggestion.objects.create(
contract=contract, tenant=contract.tenant, score_details=team
)
suggestion.suggested_team_members.set(team['subcontractors'])
send_email_notification(
contract.bidder.email, "Team Suggestions Ready", f"View recommendations for {contract.tender_title}"
)





from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'profiles', views.SubcontractorProfileViewSet)
router.register(r'contracts', views.ContractorSmartContractViewSet)
router.register(r'posts', views.CommunityPostViewSet)

urlpatterns = [
path('api/', include(router.urls)),
path('profile/builder/', views.profile_builder_view, name='profile_builder'),
path('team/map/', views.team_map_view, name='team_map'),
path('rfqs/create/', views.rfq_form_view, name='rfq_form'),
path('community/', views.community_view, name='community'),
path('api/rfq-suggestions/', views.suggest_rfq_content, name='api_rfq_suggestions'),
]




from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.decorators import action, api_view
from rest_framework.response import Response
from .models import SubcontractorProfile, ContractorSmartContract, CommunityPost
from .serializers import SubcontractorProfileSerializer, ContractorSmartContractSerializer, OptimizedTeamSuggestionSerializer
from .services.ai_agents.rfq_suggestion_agent import get_rule_based_scope_suggestions
from .tasks import async_optimize_team_task

def profile_builder_view(request):
trades = TradeCategory.objects.all()
return render(request, 'contractorsync/profile_builder.html', {'trades': trades})

@api_view(['POST'])
def suggest_rfq_content(request):
trade_id = request.data.get('trade_id')
tender_title = request.data.get('tender_title', '')
trade = TradeCategory.objects.get(id=trade_id)
suggestions = get_rule_based_scope_suggestions(trade.name)
return Response(suggestions)

class ContractorSmartContractViewSet(viewsets.ModelViewSet):
queryset = ContractorSmartContract.objects.all()
serializer_class = ContractorSmartContractSerializer

def get_queryset(self):
return self.queryset.for_tenant(self.request.tenant) if self.request.tenant else self.queryset.none()

@action(detail=True, methods=['post'], url_path='optimize-team')
def optimize_team(self, request, pk=None):
contract = self.get_object()
task = async_optimize_team_task.delay(str(contract.id), str(contract.tenant.id))
return Response({"message": "Team optimization started", "task_id": task.id})

@action(detail=True, methods=['get'], url_path='team-suggestions')
def team_suggestions(self, request, pk=None):
contract = self.get_object()
suggestions = OptimizedTeamSuggestion.objects.for_tenant(self.request.tenant).filter(contract=contract)
return Response(OptimizedTeamSuggestionSerializer(suggestions, many=True).data)



from django.http import Http404
from .models import Tenant

class TenantMiddleware:
def __init__(self, get_response):
self.get_response = get_response

def __call__(self, request):
request.tenant = None
host = request.get_host().split(':')[0]
if '.' in host:
subdomain = host.split('.')[0]
try:
request.tenant = Tenant.objects.get(slug=subdomain, is_active=True)
except Tenant.DoesNotExist:
pass
if not request.tenant and request.user.is_authenticated:
try:
request.tenant = request.user.subcontractor_profile.tenant
except AttributeError:
pass
if not request.tenant and hasattr(settings, 'DEFAULT_TENANT_ID'):
try:
request.tenant = Tenant.objects.get(id=settings.DEFAULT_TENANT_ID)
except Tenant.DoesNotExist:
pass
return self.get_response(request)




from pybreaker import CircuitBreaker
from .models import ContractorSmartContract
import requests

breaker = CircuitBreaker(fail_max=5, reset_timeout=60)

@breaker
def create_smart_contract(bidder, subcontractor, tender_title, contract_value):
contract = ContractorSmartContract.objects.create(
bidder=bidder, subcontractor=subcontractor, tender_title=tender_title, contract_value=contract_value
)
response = requests.post('https://paystack.api/escrow', data={'contract_id': contract.id})
contract.escrow_transaction_id = response.json()['transaction_id']
contract.save()
return contract




import requests
from .models import SubcontractorProfile
from .utils.trust_calculator import calculate_trust_score, assign_trust_heading

def vet_subcontractor(profile):
results = {
'certifications': False, 'credit_score': None, 'insurances': False,
'past_projects': False, 'reviews': False
}
certs = ContractorCertification.objects.filter(user=profile.user)
results['certifications'] = all(c.verified for c in certs)
response = requests.get('https://experian.api/credit', params={'id': profile.user.id})
results['credit_score'] = response.json()['score'] if response.status_code == 200 else None
for insurance in profile.insurances:
response = requests.post('https://santam.api/verify', data={'policy': insurance['policy_id']})
results['insurances'] |= response.json()['valid']
results['past_projects'] = len(profile.past_projects) >= 5
results['reviews'] = sum(r['score'] for r in profile.reviews) / len(profile.reviews) >= 4 if profile.reviews else False
profile.trust_score = calculate_trust_score(profile, results)
profile.trust_heading = assign_trust_heading(profile.trust_score, results)
profile.save()
return results




import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
from .models import SubcontractorProfile, ContractorSmartContract
from .utils.notification_service import send_email_notification
from ..mlops.model_registry import get_embedding_model

embedding_model = get_embedding_model('sentence-embeddings')

def generate_embedding_for_text(text):
return embedding_model.encode(text).tolist() if embedding_model else None

def update_embeddings_on_instance(instance):
if isinstance(instance, SubcontractorProfile) and instance.portfolio:
text = json.dumps(instance.portfolio) + (instance.trade.name if instance.trade else "")
instance.portfolio_embedding = generate_embedding_for_text(text)
instance.save(update_fields=['portfolio_embedding'])
elif isinstance(instance, ContractorSmartContract) and instance.work_scope:
text = json.dumps(instance.work_scope) + instance.tender_title
instance.work_scope_embedding = generate_embedding_for_text(text)
instance.save(update_fields=['work_scope_embedding'])

def ai_auto_match_subcontractors(contract, num_matches=10):
if not contract.work_scope_embedding:
update_embeddings_on_instance(contract)
target_embedding = np.array(contract.work_scope_embedding).reshape(1, -1)
profiles = SubcontractorProfile.objects.for_tenant(contract.tenant).filter(portfolio_embedding__isnull=False)
similarities = cosine_similarity(target_embedding, np.array([p.portfolio_embedding for p in profiles]))
top_matches = sorted(zip(profiles, similarities[0]), key=lambda x: x[1], reverse=True)[:num_matches]
for profile, score in top_matches:
send_email_notification(profile.user.email, "New Opportunity", f"Match for {contract.tender_title}")
return top_matches





import random
import numpy as np
from deap import base, creator, tools, algorithms
from .models import SubcontractorProfile, ContractorSmartContract

creator.create("FitnessMulti", base.Fitness, weights=(1.0, -1.0, 1.0))
creator.create("Individual", list, fitness=creator.FitnessMulti)

class TeamOptimizer:
def __init__(self, contract, available_subcontractors_qs):
self.contract = contract
self.available_subs = list(available_subcontractors_qs.for_tenant(contract.tenant))
self.required_skills = set(self.contract.work_scope.get('required_skills', []))
self.toolbox = base.Toolbox()
self.toolbox.register("indices", random.sample, range(len(self.available_subs)), k=random.randint(1, 5))
self.toolbox.register("individual", tools.initIterate, creator.Individual, self.toolbox.indices)
self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
self.toolbox.register("evaluate", self._evaluate_team)
self.toolbox.register("mate", tools.cxTwoPoint)
self.toolbox.register("mutate", tools.mutShuffleIndexes, indpb=0.05)
self.toolbox.register("select", tools.selNSGA2)

def _evaluate_team(self, individual_indices):
team_subs = [self.available_subs[i] for i in individual_indices]
skill_coverage = len(set(sub.portfolio.get('skills', []) for sub in team_subs).intersection(self.required_skills)) / len(self.required_skills)
total_cost = sum(sub.financial_capacity or 0 for sub in team_subs)
compliance_score = sum(sub.trust_score for sub in team_subs) / len(team_subs) / 100.0
return skill_coverage, total_cost, compliance_score

def optimize(self, population_size=100, generations=100, top_n_teams=5):
pop = self.toolbox.population(n=population_size)
hof = tools.HallOfFame(top_n_teams)
algorithms.eaMuPlusLambda(pop, self.toolbox, mu=population_size, lambda_=population_size, cxpb=0.7, mutpb=0.2, ngen=generations, halloffame=hof)
return [{'subcontractors': [self.available_subs[i] for i in ind], 'skill_coverage': ind.fitness.values[0], 'total_cost': ind.fitness.values[1], 'compliance_score': ind.fitness.values[2]} for ind in hof]






from ..models import ContractorCertification
from ..utils.notification_service import send_email_notification

def check_compliance_status():
certs = ContractorCertification.objects.filter(expiry_date__lte=timezone.now() + timezone.timedelta(days=30))
for cert in certs:
send_email_notification(cert.user.email, "Certification Expiry", f"Your {cert.certification.name} expires on {cert.expiry_date}")




def get_rule_based_scope_suggestions(trade_name):
if "electrical" in trade_name.lower():
return {
'work_scope': ["Install power reticulation", "Issue CoC"],
'milestones': [{"name": "First Fix", "percentage": 50}, {"name": "Testing", "percentage": 50}],
'compliance_requirements': [{"name": "Electrical CoC", "issuing_body": "DoL/ECAB"}]
}
return {}




import requests

def verify_certification(file, issuing_body):
apis = {
'CIDB': 'https://cidb.api/verify',
'HPCSA': 'https://hpcsa.api/verify',
'ICASA': 'https://icasa.api/verify',
'PSIRA': 'https://psira.api/verify'
}
response = requests.post(apis.get(issuing_body, ''), files={'doc': file})
return response.json()['valid'] if response.status_code == 200 else False




from twilio.rest import Client
from django.core.cache import cache
from django.conf import settings

def generate_otp_signature(user):
client = Client(settings.TWILIO_SID, settings.TWILIO_TOKEN)
verification = client.verify.v2.services('SERVICE_SID').verifications.create(to=user.email, channel='email')
cache.set(f'otp_{user.id}', verification.sid, timeout=300)
return verification.sid

def verify_otp_signature(user, otp):
stored_otp = cache.get(f'otp_{user.id}')
return stored_otp == otp




def calculate_trust_score(profile, vetting_results):
weights = {'certifications': 0.25, 'credit_score': 0.2, 'insurances': 0.15, 'past_projects': 0.2, 'reviews': 0.2}
score = (
(1 if vetting_results['certifications'] else 0) * weights['certifications'] * 100 +
(vetting_results['credit_score'] or 0) / 1000 * weights['credit_score'] * 100 +
(1 if vetting_results['insurances'] else 0) * weights['insurances'] * 100 +
(1 if vetting_results['past_projects'] else 0) * weights['past_projects'] * 100 +
(1 if vetting_results['reviews'] else 0) * weights['reviews'] * 100
)
return min(score, 100.0)

def assign_trust_heading(score, vetting_results):
if score > 80 and all(vetting_results.values()):
return 'Corporate Trusted'
elif score > 50:
return 'Standard'
return 'Basic'


import pytesseract
from PIL import Image

def parse_document(file):
image = Image.open(file)
text = pytesseract.image_to_string(image)
return {'cert_name': '', 'issuing_body': '', 'services': []} # Structured data





from django.core.mail import send_mail
from twilio.rest import Client
from django.conf import settings

def send_email_notification(to_email, subject, message):
send_mail(subject, message, settings.DEFAULT_FROM_EMAIL, [to_email])

def send_whatsapp_notification(to_number, message):
client = Client(settings.TWILIO_SID, settings.TWILIO_TOKEN)
client.messages.create(
from_=f"whatsapp:{settings.TWILIO_WHATSAPP_FROM_NUMBER}",
body=message,
to=f"whatsapp:{to_number}"
)





import os
from sentence_transformers import SentenceTransformer
from django.conf import settings

_loaded_models = {}

def get_embedding_model(model_name):
if model_name in _loaded_models:
return _loaded_models[model_name]
model_path = os.path.join(settings.ML_MODEL_PATH, model_name)
model = SentenceTransformer(model_path if os.path.exists(model_path) else 'all-MiniLM-L6-v2')
_loaded_models[model_name] = model
return model





from sentence_transformers import SentenceTransformer, InputExample, losses
from .data_preprocessing import prepare_training_data

def train_embedding_model():
model = SentenceTransformer('all-MiniLM-L6-v2')
train_examples = [InputExample(texts=['example scope', 'example portfolio'])]
train_dataloader = prepare_training_data(train_examples)
train_loss = losses.CosineSimilarityLoss(model)
model.fit(train_objectives=[(train_dataloader, train_loss)], epochs=1)
model.save(settings.ML_MODEL_PATH + '/sentence-embeddings')




from torch.utils.data import DataLoader

def prepare_training_data(examples):
return DataLoader(examples, shuffle=True, batch_size=16)





<!DOCTYPE html>
<html>
<head>
<title>Bidbeez - ContractorSync</title>
<link href="{% static 'contractorsync/css/tailwind.css' %}" rel="stylesheet">
</head>
<body class="bg-gray-100">
{% block content %}{% endblock %}
</body>
</html>





{% extends "contractorsync/base.html" %}
{% block content %}
<div class="container mx-auto p-4">
<form method="post" hx-post="{% url 'profile_builder' %}" class="space-y-4">
{% csrf_token %}
<div class="step">
<h2 class="text-xl">Company Details</h2>
<input name="company_name" class="border p-2 w-full" placeholder="Company Name" required>
<select name="company_size" class="border p-2 w-full">
<option value="SME">SME</option>
<option value="Mid">Mid</option>
<option value="Large">Large</option>
</select>
<input type="file" name="logo" accept="image/*">
</div>
<div class="step">
<h2 class="text-xl">Trade & Sector</h2>
<select name="trade" hx-get="{% url 'api_rfq_suggestions' %}" hx-target="#scope" class="border p-2 w-full">
{% for trade in trades %}
<option value="{{ trade.id }}">{{ trade.name }} ({{ trade.sector }})</option>
{% endfor %}
</select>
<textarea id="scope" name="scope_of_work" class="border p-2 w-full" placeholder="Services"></textarea>
</div>
<div class="step">
<h2 class="text-xl">Certifications</h2>
<input type="file" name="certifications" accept=".pdf,.jpg" multiple>
</div>
<button type="submit" class="bg-blue-500 text-white p-2 rounded">Complete Profile</button>
</form>
</div>
<script src="{% static 'contractorsync/js/wizard.js' %}"></script>
{% endblock %}



{% extends "contractorsync/base.html" %}
{% block content %}
<div id="map" class="h-96"></div>
<div class="flex overflow-x-auto">
{% for subcontractor in subcontractors %}
<div class="border p-4 m-2 min-w-64">
<h3>{{ subcontractor.company_name }}</h3>
<span class="badge {{ subcontractor.trust_heading|lower }}-badge">{{ subcontractor.trust_heading }}</span>
<p>Trust Score: {{ subcontractor.trust_score }}</p>
<button class="bg-blue-500 text-white p-2" onclick="inviteToRFQ({{ subcontractor.id }})">Invite</button>
</div>
{% endfor %}
</div>
<script src="https://api.mapbox.com/mapbox-gl-js/v2.9.0/mapbox-gl.js"></script>
<script src="{% static 'contractorsync/js/mapbox.js' %}"></script>
{% endblock %}




document.querySelectorAll('.step').forEach((step, index) => {
if (index > 0) step.style.display = 'none';
});
function nextStep() {
// Navigate steps and validate
}




mapboxgl.accessToken = '{{ mapbox_access_token }}';
const map = new mapboxgl.Map({
container: 'map',
style: 'mapbox://styles/mapbox/streets-v11',
center: [28.0473, -26.2041],
zoom: 10
});



from django.test import TestCase
from .models import Tenant, SubcontractorProfile, TradeCategory

class SubcontractorProfileTests(TestCase):
def setUp(self):
self.tenant = Tenant.objects.create(name='Test Tenant', slug='test')
self.trade = TradeCategory.objects.create(name='Electrical', sector='Construction')
self.profile = SubcontractorProfile.objects.create(
user=User.objects.create_user('test'), tenant=self.tenant, company_name='Test Co', trade=self.trade
)

def test_trust_score(self):
self.assertEqual(self.profile.trust_heading, 'Basic')




# ContractorSync Onboarding
1. Register with company details.
2. Complete profile via wizard (trade, certifications, portfolio).
3. Undergo vetting (corporate: full checks; public: B-BBEE/CSD).
4. Receive trust heading (Corporate Trusted, Standard, Basic).














-- Enable PostGIS extension for geospatial fields
CREATE EXTENSION IF NOT EXISTS postgis;

-- Table: contractorsync_tenant
-- Purpose: Stores tenant information for multi-tenancy
CREATE TABLE contractorsync_tenant (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
name VARCHAR(255) NOT NULL UNIQUE,
slug VARCHAR(255) NOT NULL UNIQUE,
logo VARCHAR(100),
primary_color VARCHAR(7) DEFAULT '#007bff',
is_active BOOLEAN NOT NULL DEFAULT TRUE,
created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_contractorsync_tenant_slug ON contractorsync_tenant (slug);

-- Table: contractorsync_tradecategory
-- Purpose: Defines trade categories across sectors
CREATE TABLE contractorsync_tradecategory (
id SERIAL PRIMARY KEY,
name VARCHAR(100) NOT NULL,
sector VARCHAR(50) NOT NULL,
description TEXT,
CONSTRAINT valid_sector CHECK (sector IN ('Construction', 'IT', 'Healthcare', 'Services', 'Manufacturing'))
);
CREATE INDEX idx_contractorsync_tradecategory_name_sector ON contractorsync_tradecategory (name, sector);

-- Table: contractorsync_certificationrequirement
-- Purpose: Specifies certification requirements for trades
CREATE TABLE contractorsync_certificationrequirement (
id SERIAL PRIMARY KEY,
trade_id INTEGER NOT NULL REFERENCES contractorsync_tradecategory(id) ON DELETE CASCADE,
name VARCHAR(150) NOT NULL,
issuing_body VARCHAR(100) NOT NULL,
required BOOLEAN NOT NULL DEFAULT TRUE,
valid_duration_months INTEGER NOT NULL DEFAULT 12,
document_format VARCHAR(20) NOT NULL DEFAULT 'PDF',
CONSTRAINT unique_trade_name UNIQUE (trade_id, name),
CONSTRAINT valid_issuing_body CHECK (issuing_body IN ('CIDB', 'HPCSA', 'ICASA', 'PSIRA', 'Other'))
);
CREATE INDEX idx_contractorsync_certificationrequirement_issuing_body ON contractorsync_certificationrequirement (issuing_body);

-- Table: contractorsync_contractorcertiication
-- Purpose: Stores subcontractor certifications with verification details
CREATE TABLE contractorsync_contractorcertiication (
id SERIAL PRIMARY KEY,
user_id INTEGER NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
tenant_id UUID REFERENCES contractorsync_tenant(id) ON DELETE CASCADE,
certification_id INTEGER NOT NULL REFERENCES contractorsync_certificationrequirement(id) ON DELETE CASCADE,
trade_id INTEGER NOT NULL REFERENCES contractorsync_tradecategory(id) ON DELETE CASCADE,
uploaded_file VARCHAR(100) NOT NULL,
issue_date DATE NOT NULL,
expiry_date DATE NOT NULL,
verified BOOLEAN NOT NULL DEFAULT FALSE,
verification_details JSONB DEFAULT '{}',
CONSTRAINT unique_user_certification UNIQUE (user_id, certification_id)
);
CREATE INDEX idx_contractorsync_contractorcertiication_verified_expiry ON contractorsync_contractorcertiication (verified, expiry_date);

-- Table: contractorsync_subcontractorprofile
-- Purpose: Central table for subcontractor profiles
CREATE TABLE contractorsync_subcontractorprofile (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id INTEGER NOT NULL UNIQUE REFERENCES auth_user(id) ON DELETE CASCADE,
tenant_id UUID REFERENCES contractorsync_tenant(id) ON DELETE CASCADE,
company_name VARCHAR(255) NOT NULL,
logo VARCHAR(100),
contact_email VARCHAR(255) NOT NULL, -- Encrypted in application layer
contact_phone VARCHAR(20) NOT NULL,
whatsapp_number VARCHAR(20),
trade_id INTEGER REFERENCES contractorsync_tradecategory(id) ON DELETE SET NULL,
company_size VARCHAR(20) NOT NULL,
financial_capacity NUMERIC(12, 2),
credit_score INTEGER,
insurances JSONB DEFAULT '[]',
equipment JSONB DEFAULT '[]',
past_projects JSONB DEFAULT '[]',
reviews JSONB DEFAULT '[]',
portfolio JSONB DEFAULT '{}',
portfolio_embedding FLOAT[] CHECK (array_length(portfolio_embedding, 1) = 768),
sector_data JSONB DEFAULT '{}',
location geometry(Point, 4326),
trust_score FLOAT NOT NULL DEFAULT 0.0,
trust_heading VARCHAR(50) NOT NULL DEFAULT 'Basic',
badges JSONB DEFAULT '[]',
audit_log JSONB DEFAULT '[]',
profile_type VARCHAR(20) NOT NULL DEFAULT 'Company',
b_bbee_level VARCHAR(10),
cidb_grade VARCHAR(10),
is_verified BOOLEAN NOT NULL DEFAULT FALSE,
created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT valid_company_size CHECK (company_size IN ('SME', 'Mid', 'Large')),
CONSTRAINT valid_trust_heading CHECK (trust_heading IN ('Corporate Trusted', 'Standard', 'Basic'))
);
CREATE INDEX idx_contractorsync_subcontractorprofile_trust_heading_score ON contractorsync_subcontractorprofile (trust_heading, trust_score);
CREATE INDEX idx_contractorsync_subcontractorprofile_company_name ON contractorsync_subcontractorprofile (company_name);

-- Table: contractorsync_contractorsmartcontract
-- Purpose: Manages tender contracts with escrow and AI embeddings
CREATE TABLE contractorsync_contractorsmartcontract (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
bidder_id INTEGER NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
subcontractor_id UUID REFERENCES contractorsync_subcontractorprofile(id) ON DELETE SET NULL,
tenant_id UUID REFERENCES contractorsync_tenant(id) ON DELETE CASCADE,
tender_title VARCHAR(255) NOT NULL,
tender_id VARCHAR(100) NOT NULL,
trade_id INTEGER REFERENCES contractorsync_tradecategory(id) ON DELETE SET NULL,
contract_value NUMERIC(12, 2) NOT NULL,
location geometry(Point, 4326),
work_scope JSONB NOT NULL,
work_scope_embedding FLOAT[] CHECK (array_length(work_scope_embedding, 1) = 768),
milestones JSONB NOT NULL,
compliance_requirements JSONB NOT NULL,
status VARCHAR(20) NOT NULL DEFAULT 'draft',
contract_hash VARCHAR(256) NOT NULL UNIQUE,
escrow_transaction_id VARCHAR(100),
audit_log JSONB DEFAULT '[]',
created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT valid_status CHECK (status IN ('draft', 'active', 'completed', 'disputed'))
);
CREATE INDEX idx_contractorsync_contractorsmartcontract_status_tender_id ON contractorsync_contractorsmartcontract (status, tender_id);

-- Table: contractorsync_subcontractorquote
-- Purpose: Stores subcontractor quotes for RFQs
CREATE TABLE contractorsync_subcontractorquote (
id SERIAL PRIMARY KEY,
subcontractor_id INTEGER NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
contract_id UUID NOT NULL REFERENCES contractorsync_contractorsmartcontract(id) ON DELETE CASCADE,
price_zar NUMERIC(12, 2) NOT NULL,
timeline_days INTEGER NOT NULL,
additional_notes TEXT,
submitted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_contractorsync_subcontractorquote_submitted_at ON contractorsync_subcontractorquote (submitted_at);

-- Table: contractorsync_communitypost
-- Purpose: Enables social engagement via a feed
CREATE TABLE contractorsync_communitypost (
id SERIAL PRIMARY KEY,
user_id INTEGER NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
tenant_id UUID REFERENCES contractorsync_tenant(id) ON DELETE CASCADE,
content TEXT NOT NULL,
media JSONB DEFAULT '[]',
likes_count INTEGER NOT NULL DEFAULT 0,
is_flagged BOOLEAN NOT NULL DEFAULT FALSE,
created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_contractorsync_communitypost_created_at_flagged ON contractorsync_communitypost (created_at, is_flagged);

-- Table: contractorsync_disqualificationrule
-- Purpose: Defines rules for disqualifying subcontractors
CREATE TABLE contractorsync_disqualificationrule (
id SERIAL PRIMARY KEY,
trade_id INTEGER NOT NULL REFERENCES contractorsync_tradecategory(id) ON DELETE CASCADE,
rule_description TEXT NOT NULL,
auto_learned BOOLEAN NOT NULL DEFAULT FALSE,
active BOOLEAN NOT NULL DEFAULT TRUE,
created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_contractorsync_disqualificationrule_active ON contractorsync_disqualificationrule (active);

-- Table: contractorsync_optimizedteamsuggestion
-- Purpose: Stores AI-generated team suggestions
CREATE TABLE contractorsync_optimizedteamsuggestion (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
contract_id UUID NOT NULL REFERENCES contractorsync_contractorsmartcontract(id) ON DELETE CASCADE,
tenant_id UUID REFERENCES contractorsync_tenant(id) ON DELETE CASCADE,
score_details JSONB DEFAULT '{}',
generated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
is_accepted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Junction table for ManyToMany relationship in optimizedteamsuggestion
CREATE TABLE contractorsync_optimizedteamsuggestion_suggested_team_members (
optimizedteamsuggestion_id UUID NOT NULL REFERENCES contractorsync_optimizedteamsuggestion(id) ON DELETE CASCADE,
subcontractorprofile_id UUID NOT NULL REFERENCES contractorsync_subcontractorprofile(id) ON DELETE CASCADE,
PRIMARY KEY (optimizedteamsuggestion_id, subcontractorprofile_id)
);