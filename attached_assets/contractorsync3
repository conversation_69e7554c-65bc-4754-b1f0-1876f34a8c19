/src
├── assets/ # Static assets
│ ├── images/ # Static images (e.g., default logos, backgrounds)
│ │ ├── default-logo.png
│ │ └── background-wave.jpg
│ ├── icons/ # SVG icon sprite sheets and individual SVGs
│ │ ├── sprite.svg # Combined SVG sprite for icons
│ │ ├── trust-badge.svg # Individual SVG for trust badge
│ │ ├── verified-check.svg # SVG for verified status
│ │ ├── location-pin.svg # SVG for map markers
│ │ └── upload-icon.svg # SVG for upload zones
│ └── fonts/ # Custom fonts (e.g., Inter)
│ ├── Inter-Regular.ttf
│ └── Inter-Bold.ttf
├── components/ # Reusable React components
│ ├── common/ # Generic components
│ │ ├── Button.jsx # Custom button component
│ │ ├── Input.jsx # Custom input component
│ │ ├── Modal.jsx # Modal component for dialogs
│ │ ├── Spinner.jsx # SVG-based loading spinner
│ │ ├── MapComponent.jsx # Reusable map component using react-leaflet
│ │ ├── TrustGauge.jsx # SVG-based trust score gauge
│ │ ├── ExpiryCountdown.jsx # SVG-based certification expiry countdown
│ │ └── NotificationBadge.jsx # SVG-based notification badge
│ ├── layout/ # Layout components
│ │ ├── Header.jsx # Enhanced header with SVG notification bell
│ │ ├── Footer.jsx # Optional footer component
│ │ ├── Sidebar.jsx # Collapsible sidebar with SVG icons
│ │ ├── MainLayout.jsx # Main app layout
│ │ └── AuthLayout.jsx # Authentication layout
│ ├── forms/ # Form components with react-hook-form
│ │ ├── LoginForm.jsx # Login form with SVG icons
│ │ ├── RegisterForm.jsx # Multi-step registration form
│ │ ├── SubcontractorProfileForm.jsx # Multi-step profile edit form
│ │ ├── TenderCreationForm.jsx # Tender creation form with map picker
│ │ └── CertificationUploadForm.jsx # Certification upload form
│ ├── cards/ # Card components for lists
│ │ ├── SubcontractorCard.jsx # Subcontractor card with SVG badges
│ │ ├── TenderCard.jsx # Tender card with SVG status indicators
│ │ ├── PostCard.jsx # Community post card with SVG reactions
│ │ └── QuoteCard.jsx # Quote card for tender details
│ └── dashboard/ # Dashboard-specific components
│ ├── TrustScoreWidget.jsx # SVG-based trust score widget
│ ├── CertificationStatusChart.jsx # Chart for certification statuses
│ ├── PerformanceChart.jsx # Bar chart for performance metrics
│ ├── QuoteComparisonChart.jsx # Bar chart for quote comparisons
│ ├── TeamSuggestionCarousel.jsx # Carousel for AI team suggestions
│ └── SystemOverviewChart.jsx # Pie chart for admin dashboard
├── pages/ # Page components
│ ├── Auth/
│ │ ├── LoginPage.jsx # Enhanced login page with SVG background
│ │ └── RegisterPage.jsx # Multi-step registration page
│ ├── Dashboard/
│ │ ├── GeneralDashboardPage.jsx # Dashboard for bidders/subcontractors
│ │ └── AdminDashboardPage.jsx # Admin-specific dashboard
│ ├── Tenants/
│ │ ├── TenantSelectionPage.jsx # Tenant selection with SVG dropdown
│ │ └── TenantSettingsPage.jsx # Tenant management page
│ ├── Subcontractors/
│ │ ├── SubcontractorListPage.jsx # List page with map toggle
│ │ ├── SubcontractorProfilePage.jsx # Enhanced profile page with SVG visuals
│ │ └── SubcontractorFormPage.jsx # Profile creation/edit page
│ ├── Tenders/
│ │ ├── TenderListPage.jsx # Tender list with SVG filters
│ │ ├── TenderDetailPage.jsx # Tender details with quote charts
│ │ └── TenderCreationPage.jsx # Tender creation with drag-and-drop
│ ├── Certifications/
│ │ ├── CertificationManagementPage.jsx # Certification list with SVG countdowns
│ │ └── UploadCertificationPage.jsx # Certification upload page
│ ├── Community/
│ │ ├── CommunityFeedPage.jsx # Community feed with infinite scroll
│ │ └── PostDetailPage.jsx # Individual post page
│ ├── Settings/
│ │ └── UserSettingsPage.jsx # User settings with SVG profile avatar
│ └── NotFoundPage.jsx # 404 page
├── services/ # API and external service integrations
│ ├── authService.js # Authentication API calls
│ ├── apiService.js # Axios instance with interceptors
│ ├── tenantService.js # Tenant-related API calls
│ ├── subcontractorService.js # Subcontractor API calls
│ ├── tenderService.js # Tender/contract API calls
│ ├── certificationService.js # Certification API calls
│ ├── communityService.js # Community post API calls
│ └── socketService.js # Socket.io client for real-time updates
├── hooks/ # Custom React hooks
│ ├── useAuth.js # Authentication state hook
│ ├── useFormValidation.js # Form validation with yup
│ ├── useMapLocation.js # Map location handling
│ ├── useRealTimeUpdates.js # Socket.io real-time updates
│ └── useInfiniteScroll.js # Infinite scroll for community feed
├── store/ # Redux store and slices
│ ├── index.js # Store configuration
│ ├── authSlice.js # Authentication state
│ ├── tenantSlice.js # Tenant state
│ ├── subcontractorSlice.js # Subcontractor data state
│ ├── tenderSlice.js # Tender/contract data state
│ ├── certificationSlice.js # Certification data state
│ ├── communitySlice.js # Community post data state
│ └── notificationSlice.js # Real-time notifications
├── utils/ # Utility functions and constants
│ ├── helpers.js # Date formatting, UUID generation
│ ├── validators.js # Yup validation schemas
│ ├── constants.js # App-wide constants (e.g., API endpoints)
│ └── svgUtils.js # SVG manipulation utilities
├── styles/ # Global styles and Tailwind config
│ ├── index.css # Tailwind and custom styles
│ ├── tailwind.config.js # Tailwind configuration with SVG utilities
│ └── leaflet.css # React-leaflet map styles
├── App.jsx # Main application component
├── main.jsx # Entry point with Redux and Router
└── vite.config.js # Vite configuration



npm install framer-motion react-hook-form @hookform/resolvers yup react-toastify react-icons chart.js react-chartjs-2 socket.io-client



/** @type {import('tailwindcss').Config} */
export default {
content: [
"./index.html",
"./src/**/*.{js,ts,jsx,tsx}",
],
theme: {
extend: {
colors: {
primary: '#007bff',
secondary: '#6c757d',
accent: '#28a745',
'cs-dark': '#343a40',
'cs-light': '#f8f9fa',
'cs-warning': '#ffc107',
},
animation: {
'fade-in': 'fadeIn 0.3s ease-in',
'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
},
keyframes: {
fadeIn: {
'0%': { opacity: '0' },
'100%': { opacity: '1' },
},
pulse: {
'0%, 100%': { opacity: '1' },
'50%': { opacity: '0.5' },
},
},
},
},
plugins: [],
}



<svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
<defs>
<linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style={{ stopColor: '#007bff', stopOpacity: 0.1 }} />
<stop offset="100%" style={{ stopColor: '#28a745', stopOpacity: 0.1 }} />
</linearGradient>
</defs>
<path d="M0,100 C150,50 300,150 400,100 L400,200 L0,200 Z" fill="url(#grad)" />
</svg>



import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { motion } from 'framer-motion';

const schema = yup.object({
email: yup.string().email('Invalid email').required('Email is required'),
password: yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
}).required();

const LoginPage = () => {
const { register, handleSubmit, formState: { errors } } = useForm({
resolver: yupResolver(schema),
});
const dispatch = useDispatch();
const navigate = useNavigate();
const { loading, error } = useSelector((state) => state.auth);

const onSubmit = async (data) => {
const resultAction = await dispatch(login(data));
if (login.fulfilled.match(resultAction)) {
navigate('/dashboard');
toast.success('Welcome back!');
}
};

return (
<motion.div
className="text-gray-800 relative"
initial={{ opacity: 0 }}
animate={{ opacity: 1 }}
transition={{ duration: 0.5 }}
>
<svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg"> {/* SVG background */} </svg>
<h2 className="text-3xl font-bold text-center mb-6 text-cs-dark">Login to ContractorSync</h2>
<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
<div>
<label className="block text-sm font-medium text-gray-700" htmlFor="email">Email</label>
<input
{...register('email')}
className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
aria-invalid={errors.email ? 'true' : 'false'}
/>
{errors.email && <p className="text-red-500 text-sm">{errors.email.message}</p>}
</div>
<div>
<label className="block text-sm font-medium text-gray-700" htmlFor="password">Password</label>
<input
type="password"
{...register('password')}
className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
aria-invalid={errors.password ? 'true' : 'false'}
/>
{errors.password && <p className="text-red-500 text-sm">{errors.password.message}</p>}
</div>
{error && <p className="text-red-500 text-sm">{error}</p>}
<button
type="submit"
className="w-full py-2 px-4 bg-primary text-white rounded-md hover:bg-blue-700"
disabled={loading}
>
{loading ? 'Logging in...' : 'Login'}
</button>
</form>
</motion.div>
);
};



const ProgressRing = ({ percentage }) => {
const radius = 40;
const circumference = 2 * Math.PI * radius;
const strokeDashoffset = circumference - (percentage / 100) * circumference;

return (
<svg className="w-24 h-24" xmlns="http://www.w3.org/2000/svg">
<circle cx="50" cy="50" r={radius} fill="none" stroke="#e5e7eb" strokeWidth="8" />
<circle
cx="50"
cy="50"
r={radius}
fill="none"
stroke="#007bff"
strokeWidth="8"
strokeDasharray={circumference}
strokeDashoffset={strokeDashoffset}
className="transition-all duration-500"
/>
<text x="50" y="55" textAnchor="middle" className="text-lg font-bold">{percentage}%</text>
</svg>
);
};




import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const PerformanceChart = () => {
const data = {
labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
datasets: [
{
label: 'Project Completion (%)',
data: [85, 90, 88, 92, 95, 87],
backgroundColor: '#007bff',
},
{
label: 'Budget Adherence (%)',
data: [78, 82, 80, 85, 88, 90],
backgroundColor: '#28a745',
},
],
};

return (
<div className="bg-white p-6 rounded-lg shadow-md">
<h2 className="text-xl font-semibold mb-4">Performance Metrics</h2>
```chartjs
{
type: 'bar',
data: {
labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
datasets: [
{
label: 'Project Completion (%)',
data: [85, 90, 88, 92, 95, 87],
backgroundColor: '#007bff',
borderColor: '#005bb5',
borderWidth: 1
},
{
label: 'Budget Adherence (%)',
data: [78, 82, 80, 85, 88, 90],
backgroundColor: '#28a745',
borderColor: '#1f7a38',
borderWidth: 1
}
]
},
options: {
responsive: true,
plugins: {
legend: {
position: 'top',
labels: {
color: '#343a40'
}
},
title: {
display: true,
text: 'Performance Metrics',
color: '#343a40'
}
},
scales: {
y: {
beginAtZero: true,
ticks: {
color: '#343a40'
},
grid: {
color: '#e5e7eb'
}
},
x: {
ticks: {
color: '#343a40'
},
grid: {
color: '#e5e7eb'
}
}
}
}
}
```
</div>
);
};





import { io } from 'socket.io-client';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { updateNotifications } from '../store/notificationSlice';

const RealTimeUpdates = () => {
const dispatch = useDispatch();

useEffect(() => {
const socket = io(import.meta.env.VITE_API_BASE_URL);
socket.on('new_quote', (data) => {
dispatch(updateNotifications({ type: 'quote', data }));
toast.info(`New quote received for tender #${data.tender_id}`);
});
return () => socket.disconnect();
}, [dispatch]);

return null;
};




import { MapContainer, TileLayer, Marker, Popup } from 'react-leaflet';
import L from 'leaflet';

const customIcon = L.divIcon({
html: `<svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/></svg>`,
className: '',
iconSize: [24, 24],
iconAnchor: [12, 24],
});

const SubcontractorMap = ({ subcontractors }) => {
return (
<MapContainer center={[-26.2041, 28.0473]} zoom={10} className="h-96 w-full rounded-lg">
<TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
{subcontractors.map((sub) => (
<Marker
key={sub.id}
position={[sub.location.coordinates[1], sub.location.coordinates[0]]}
icon={customIcon}
>
<Popup>
<Link to={`/subcontractors/${sub.id}`}>{sub.company_name}</Link>
<p>Trust: {sub.trust_heading}</p>
</Popup>
</Marker>
))}
</MapContainer>
);
};




import { MapContainer, TileLayer, Marker, Popup } from 'react-leaflet';
import L from 'leaflet';

const customIcon = L.divIcon({
html: `<svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/></svg>`,
className: '',
iconSize: [24, 24],
iconAnchor: [12, 24],
});

const SubcontractorMap = ({ subcontractors }) => {
return (
<MapContainer center={[-26.2041, 28.0473]} zoom={10} className="h-96 w-full rounded-lg">
<TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
{subcontractors.map((sub) => (
<Marker
key={sub.id}
position={[sub.location.coordinates[1], sub.location.coordinates[0]]}
icon={customIcon}
>
<Popup>
<Link to={`/subcontractors/${sub.id}`}>{sub.company_name}</Link>
<p>Trust: {sub.trust_heading}</p>
</Popup>
</Marker>
))}
</MapContainer>
);
};




const TrustGauge = ({ score }) => {
const angle = (score / 10) * 180 - 90;
return (
<svg className="w-48 h-24" xmlns="http://www.w3.org/2000/svg">
<path d="M10,90 A80,80 0 0,1 190,90" fill="none" stroke="#e5e7eb" strokeWidth="10" />
<path
d="M10,90 A80,80 0 0,1 190,90"
fill="none"
stroke="#007bff"
strokeWidth="10"
strokeDasharray="251.2"
strokeDashoffset={251.2 - (score / 10) * 251.2}
/>
<line
x1="100"
y1="90"
x2={100 + 80 * Math.cos((angle * Math.PI) / 180)}
y2={90 + 80 * Math.sin((angle * Math.PI) / 180)}
stroke="#343a40"
strokeWidth="4"
/>
<text x="100" y="60" textAnchor="middle" className="text-2xl font-bold">{score}</text>
</svg>
);
};



const QuoteComparisonChart = ({ quotes }) => {
const data = {
labels: quotes.map((q) => q.subcontractor.company_name),
datasets: [
{
label: 'Price (ZAR)',
data: quotes.map((q) => q.price),
backgroundColor: '#007bff',
},
{
label: 'Timeline (Days)',
data: quotes.map((q) => q.timeline),
backgroundColor: '#28a745',
},
],
};

return (
<div className="bg-white p-6 rounded-lg shadow-md">
<h2 className="text-xl font-semibold mb-4">Quote Comparison</h2>
```chartjs
{
type: 'bar',
data: {
labels: ${JSON.stringify(quotes.map((q) => q.subcontractor.company_name))},
datasets: [
{
label: 'Price (ZAR)',
data: ${JSON.stringify(quotes.map((q) => q.price))},
backgroundColor: '#007bff',
borderColor: '#005bb5',
borderWidth: 1
},
{
label: 'Timeline (Days)',
data: ${JSON.stringify(quotes.map((q) => q.timeline))},
backgroundColor: '#28a745',
borderColor: '#1f7a38',
borderWidth: 1
}
]
},
options: {
responsive: true,
plugins: {
legend: {
position: 'top',
labels: {
color: '#343a40'
}
},
title: {
display: true,
text: 'Quote Comparison',
color: '#343a40'
}
},
scales: {
y: {
beginAtZero: true,
ticks: {
color: '#343a40'
},
grid: {
color: '#e5e7eb'
}
},
x: {
ticks: {
color: '#343a40'
},
grid: {
color: '#e5e7eb'
}
}
}
}
}
```
</div>
);
};



const QuoteComparisonChart = ({ quotes }) => {
const data = {
labels: quotes.map((q) => q.subcontractor.company_name),
datasets: [
{
label: 'Price (ZAR)',
data: quotes.map((q) => q.price),
backgroundColor: '#007bff',
},
{
label: 'Timeline (Days)',
data: quotes.map((q) => q.timeline),
backgroundColor: '#28a745',
},
],
};

return (
<div className="bg-white p-6 rounded-lg shadow-md">
<h2 className="text-xl font-semibold mb-4">Quote Comparison</h2>
```chartjs
{
type: 'bar',
data: {
labels: ${JSON.stringify(quotes.map((q) => q.subcontractor.company_name))},
datasets: [
{
label: 'Price (ZAR)',
data: ${JSON.stringify(quotes.map((q) => q.price))},
backgroundColor: '#007bff',
borderColor: '#005bb5',
borderWidth: 1
},
{
label: 'Timeline (Days)',
data: ${JSON.stringify(quotes.map((q) => q.timeline))},
backgroundColor: '#28a745',
borderColor: '#1f7a38',
borderWidth: 1
}
]
},
options: {
responsive: true,
plugins: {
legend: {
position: 'top',
labels: {
color: '#343a40'
}
},
title: {
display: true,
text: 'Quote Comparison',
color: '#343a40'
}
},
scales: {
y: {
beginAtZero: true,
ticks: {
color: '#343a40'
},
grid: {
color: '#e5e7eb'
}
},
x: {
ticks: {
color: '#343a40'
},
grid: {
color: '#e5e7eb'
}
}
}
}
}
```
</div>
);
};





const ExpiryCountdown = ({ expiryDate }) => {
const daysLeft = Math.ceil((new Date(expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
const percentage = Math.min((daysLeft / 365) * 100, 100);
const radius = 30;
const circumference = 2 * Math.PI * radius;
const strokeDashoffset = circumference - (percentage / 100) * circumference;

return (
<div className="flex items-center space-x-2">
<svg className="w-16 h-16" xmlns="http://www.w3.org/2000/svg">
<circle cx="32" cy="32" r={radius} fill="none" stroke="#e5e7eb" strokeWidth="6" />
<circle
cx="32"
cy="32"
r={radius}
fill="none"
stroke={daysLeft < 30 ? '#dc3545' : '#28a745'}
strokeWidth="6"
strokeDasharray={circumference}
strokeDashoffset={strokeDashoffset}
/>
<text x="32" y="36" textAnchor="middle" className="text-sm font-bold">{daysLeft}</text>
</svg>
<span>{daysLeft} days left</span>
</div>
);
};








import { useState, useEffect } from 'react';
import api from '../../services/apiService';
import PostCard from '../../components/cards/PostCard';
import Spinner from '../../components/common/Spinner';

const CommunityFeedPage = () => {
const [posts, setPosts] = useState([]);
const [page, setPage] = useState(1);
const [loading, setLoading] = useState(false);
const [hasMore, setHasMore] = useState(true);

const fetchPosts = async () => {
if (!hasMore || loading) return;
setLoading(true);
try {
const response = await api.get(`/community/posts?page=${page}`);
setPosts((prev) => [...prev, ...response.data.results]);
setHasMore(response.data.next !== null);
setPage((prev) => prev + 1);
} catch (error) {
toast.error('Failed to load posts');
} finally {
setLoading(false);
}
};

useEffect(() => {
fetchPosts();
}, []);

const handleScroll = () => {
if (
window.innerHeight + document.documentElement.scrollTop >=
document.documentElement.offsetHeight - 100
) {
fetchPosts();
}
};

useEffect(() => {
window.addEventListener('scroll', handleScroll);
return () => window.removeEventListener('scroll', handleScroll);
}, [loading, hasMore]);

return (
<div className="p-6">
<h1 className="text-3xl font-bold text-cs-dark mb-6">Community Feed</h1>
<div className="space-y-6">
{posts.map((post) => (
<PostCard key={post.id} post={post} />
))}
</div>
{loading && <Spinner />}
{!hasMore && <p className="text-center text-gray-600">No more posts to show</p>}
</div>
);
};




import { useState, useEffect } from 'react';
import api from '../../services/apiService';
import PostCard from '../../components/cards/PostCard';
import Spinner from '../../components/common/Spinner';

const CommunityFeedPage = () => {
const [posts, setPosts] = useState([]);
const [page, setPage] = useState(1);
const [loading, setLoading] = useState(false);
const [hasMore, setHasMore] = useState(true);

const fetchPosts = async () => {
if (!hasMore || loading) return;
setLoading(true);
try {
const response = await api.get(`/community/posts?page=${page}`);
setPosts((prev) => [...prev, ...response.data.results]);
setHasMore(response.data.next !== null);
setPage((prev) => prev + 1);
} catch (error) {
toast.error('Failed to load posts');
} finally {
setLoading(false);
}
};

useEffect(() => {
fetchPosts();
}, []);

const handleScroll = () => {
if (
window.innerHeight + document.documentElement.scrollTop >=
document.documentElement.offsetHeight - 100
) {
fetchPosts();
}
};

useEffect(() => {
window.addEventListener('scroll', handleScroll);
return () => window.removeEventListener('scroll', handleScroll);
}, [loading, hasMore]);

return (
<div className="p-6">
<h1 className="text-3xl font-bold text-cs-dark mb-6">Community Feed</h1>
<div className="space-y-6">
{posts.map((post) => (
<PostCard key={post.id} post={post} />
))}
</div>
{loading && <Spinner />}
{!hasMore && <p className="text-center text-gray-600">No more posts to show</p>}
</div>
);
};




const SystemOverviewChart = () => {
const data = {
labels: ['Users', 'Tenders', 'Quotes', 'Posts'],
datasets: [
{
label: 'Activity',
data: [1200, 350, 800, 200],
backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545'],
},
],
};

return (
<div className="bg-white p-6 rounded-lg shadow-md">
<h2 className="text-xl font-semibold mb-4">System Activity</h2>
```chartjs
{
type: 'pie',
data: {
labels: ['Users', 'Tenders', 'Quotes', 'Posts'],
datasets: [
{
label: 'Activity',
data: [1200, 350, 800, 200],
backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545'],
borderColor: ['#005bb5', '#1f7a38', '#cc9a06', '#a71d2a'],
borderWidth: 1
}
]
},
options: {
responsive: true,
plugins: {
legend: {
position: 'top',
labels: {
color: '#343a40'
}
},
title: {
display: true,
text: 'System Activity',
color: '#343a40'
}
}
}
}
```
</div>
);
};



import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import api from '../../services/apiService';
import { motion } from 'framer-motion';
import { toast } from 'react-toastify';
import { MapContainer, TileLayer, Marker } from 'react-leaflet';
import Spinner from '../../components/common/Spinner';
import TrustGauge from '../../components/common/TrustGauge';
import ExpiryCountdown from '../../components/common/ExpiryCountdown';

const SubcontractorProfilePage = () => {
const { id } = useParams();
const [profile, setProfile] = useState(null);
const [loading, setLoading] = useState(true);
const [error, setError] = useState(null);

useEffect(() => {
const fetchProfile = async () => {
try {
setLoading(true);
const response = await api.get(`/subcontractors/${id}`);
setProfile(response.data);
} catch (err) {
setError('Failed to load profile');
toast.error('Failed to load profile');
} finally {
setLoading(false);
}
};
fetchProfile();
}, [id]);

if (loading) return <Spinner />;
if (error) return <div className="text-red-500 text-center">{error}</div>;
if (!profile) return null;

return (
<motion.div
className="p-6"
initial={{ opacity: 0 }}
animate={{ opacity: 1 }}
transition={{ duration: 0.5 }}
>
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
<div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
<img
src={profile.logo || 'https://via.placeholder.com/150'}
alt={`${profile.company_name} Logo`}
className="w-32 h-32 rounded-full object-cover border-2 border-primary"
/>
<div>
<h1 className="text-3xl font-bold text-cs-dark">{profile.company_name}</h1>
<p className="text-gray-600">{profile.trade?.name || 'N/A'}</p>
<div className="flex items-center space-x-2 mt-2">
<TrustGauge score={profile.trust_score} />
<span className="text-sm font-medium">{profile.trust_heading}</span>
{profile.is_verified && (
<svg
className="w-6 h-6 text-accent"
xmlns="http://www.w3.org/2000/svg"
fill="none"
viewBox="0 0 24 24"
stroke="currentColor"
aria-label="Verified"
>
<path
strokeLinecap="round"
strokeLinejoin="round"
strokeWidth="2"
d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
/>
</svg>
)}
</div>
</div>
</div>
</div>

<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
<div className="lg:col-span-2 space-y-6">
{/* General Info */}
<div className="bg-white rounded-lg shadow-md p-6">
<h2 className="text-xl font-semibold mb-4">General Information</h2>
<p><strong>Contact:</strong> {profile.contact_info.email}</p>
<p><strong>Company Size:</strong> {profile.company_size}</p>
<p><strong>Financial Capacity:</strong> {profile.financial_capacity}</p>
<p><strong>B-BBEE Level:</strong> {profile.bbbee_level}</p>
<p><strong>CIDB Grade:</strong> {profile.cidb_grade}</p>
</div>

{/* Certifications */}
<div className="bg-white rounded-lg shadow-md p-6">
<h2 className="text-xl font-semibold mb-4">Certifications</h2>
{profile.certifications.map((cert) => (
<div key={cert.id} className="flex items-center justify-between py-2 border-b">
<div>
<p className="font-medium">{cert.name}</p>
<p className="text-sm text-gray-600">Issued by {cert.issuing_body}</p>
</div>
<ExpiryCountdown expiryDate={cert.expiry_date} />
</div>
))}
</div>

{/* Portfolio */}
<div className="bg-white rounded-lg shadow-md p-6">
<h2 className="text-xl font-semibold mb-4">Portfolio</h2>
<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
{profile.portfolio?.images.map((img, index) => (
<motion.img
key={index}
src={img.url}
alt="Portfolio Image"
className="w-full h-48 object-cover rounded-md"
whileHover={{ scale: 1.05 }}
/>
))}
</div>
</div>
</div>

<div className="space-y-6">
{/* Location Map */}
<div className="bg-white rounded-lg shadow-md p-6">
<h2 className="text-xl font-semibold mb-4">Location</h2>
<MapContainer
center={[profile.location.coordinates[1], profile.location.coordinates[0]]}
zoom={13}
className="h-64 w-full rounded-lg"
>
<TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
<Marker position={[profile.location.coordinates[1], profile.location.coordinates[0]]} />
</MapContainer>
</div>

{/* Actions */}
<div className="bg-white rounded-lg shadow-md p-6">
<h2 className="text-xl font-semibold mb-4">Actions</h2>
<Link
to={`/tenders/create?subcontractor=${id}`}
className="block w-full py-2 px-4 bg-primary text-white rounded-md hover:bg-blue-700 text-center mb-2"
>
Initiate Tender
</Link>
<button
className="block w-full py-2 px-4 bg-secondary text-white rounded-md hover:bg-gray-600 text-center"
onClick={() => toast.info('Contact feature coming soon!')}
>
Contact Subcontractor
</button>
</div>
</div>
</div>
</motion.div>
);
};

export default SubcontractorProfilePage;

