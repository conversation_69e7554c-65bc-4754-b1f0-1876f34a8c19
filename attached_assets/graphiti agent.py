Perfect! I've created a comprehensive integration guide. Now let me create the enhanced Graphiti agent code that incorporates <PERSON>'s coding patterns:

## Enhanced Graphiti Agent with <PERSON>s

**src/coding_knowledge.py** - <PERSON>s Integration
```python
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import requests
from dataclasses import dataclass

from .config import GraphitiConfig

logger = logging.getLogger(__name__)

@dataclass
class CodingPattern:
    """Represents a coding pattern extracted from <PERSON>'s repos"""
    name: str
    description: str
    technology: str
    category: str
    code_example: str
    usage_context: str
    best_practices: List[str]
    dependencies: List[str]

class AdrianHajdinKnowledge:
    """Knowledge base of <PERSON>'s coding patterns and practices"""
    
    def __init__(self, config: GraphitiConfig):
        self.config = config
        self.patterns: Dict[str, CodingPattern] = {}
        self.technology_stacks: Dict[str, List[str]] = {}
        self.project_templates: Dict[str, Dict[str, Any]] = {}
        self.best_practices: Dict[str, List[str]] = {}
        
    async def load_training_data(self) -> None:
        """Load <PERSON>jdin's coding patterns from the training data"""
        try:
            # URLs to the generated training data
            training_data_urls = {
                'coding_patterns': 'https://cdn1.genspark.ai/user-upload-image/jupyter/toolu_01Wc4xohoGm4F1txytwFLhFy/output/coding_patterns_documentation.json',
                'training_data': 'https://cdn1.genspark.ai/user-upload-image/jupyter/toolu_01A7Xm6mVFpEcyJY4R2Y8Z2D/output/training_data/coding_agent_training_data.json',
                'implementation_guide': 'https://cdn1.genspark.ai/user-upload-image/jupyter/toolu_01A9x5ZAD6Ru1HqgkAxxuqQm/output/implementation_guide.json',
                'comprehensive_analysis': 'https://cdn1.genspark.ai/user-upload-image/jupyter/toolu_01VTgVHQMG1QR8hsXS7hz6Fn/output/analysis/comprehensive_analysis.json'
            }
            
            # Load each dataset
            for data_type, url in training_data_urls.items():
                try:
                    response = requests.get(url, timeout=30)
                    response.raise_for_status()
                    data = response.json()
                    await self._process_training_data(data_type, data)
                    logger.info(f"Loaded {data_type} successfully")
                except Exception as e:
                    logger.warning(f"Failed to load {data_type}: {e}")
            
            logger.info("Adrian Hajdin knowledge base loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load training data: {e}")
            # Load fallback patterns
            await self._load_fallback_patterns()
    
    async def _process_training_data(self, data_type: str, data: Dict[str, Any]) -> None:
        """Process different types of training data"""
        if data_type == 'coding_patterns':
            await self._process_coding_patterns(data)
        elif data_type == 'training_data':
            await self._process_agent_training_data(data)
        elif data_type == 'implementation_guide':
            await self._process_implementation_guide(data)
        elif data_type == 'comprehensive_analysis':
            await self._process_comprehensive_analysis(data)
    
    async def _process_coding_patterns(self, data: Dict[str, Any]) -> None:
        """Process coding patterns documentation"""
        if 'coding_standards' in data:
            for standard_type, standards in data['coding_standards'].items():
                self.best_practices[standard_type] = standards
        
        if 'implementation_patterns' in data:
            for pattern_name, pattern_data in data['implementation_patterns'].items():
                self.patterns[pattern_name] = CodingPattern(
                    name=pattern_name,
                    description=pattern_data.get('description', ''),
                    technology=pattern_data.get('technology', ''),
                    category='implementation',
                    code_example=pattern_data.get('code_example', ''),
                    usage_context=pattern_data.get('usage_context', ''),
                    best_practices=pattern_data.get('best_practices', []),
                    dependencies=pattern_data.get('dependencies', [])
                )
    
    async def _process_agent_training_data(self, data: Dict[str, Any]) -> None:
        """Process agent training data"""
        if 'code_patterns' in data:
            for category, patterns in data['code_patterns'].items():
                for pattern_name, pattern_info in patterns.items():
                    self.patterns[f"{category}_{pattern_name}"] = CodingPattern(
                        name=pattern_name,
                        description=pattern_info.get('description', ''),
                        technology=category,
                        category=category,
                        code_example=pattern_info.get('example', ''),
                        usage_context=pattern_info.get('usage', ''),
                        best_practices=pattern_info.get('best_practices', []),
                        dependencies=pattern_info.get('dependencies', [])
                    )
        
        if 'project_templates' in data:
            self.project_templates = data['project_templates']
    
    async def _process_implementation_guide(self, data: Dict[str, Any]) -> None:
        """Process implementation guide"""
        if 'component_examples' in data:
            for comp_name, comp_data in data['component_examples'].items():
                self.patterns[f"component_{comp_name}"] = CodingPattern(
                    name=comp_name,
                    description=comp_data.get('description', ''),
                    technology='React',
                    category='component',
                    code_example=comp_data.get('code', ''),
                    usage_context=comp_data.get('usage', ''),
                    best_practices=comp_data.get('best_practices', []),
                    dependencies=comp_data.get('dependencies', [])
                )
    
    async def _process_comprehensive_analysis(self, data: Dict[str, Any]) -> None:
        """Process comprehensive analysis data"""
        if 'technology_stacks' in data:
            self.technology_stacks = data['technology_stacks']
        
        if 'common_patterns' in data:
            for pattern_name, pattern_data in data['common_patterns'].items():
                self.patterns[f"analysis_{pattern_name}"] = CodingPattern(
                    name=pattern_name,
                    description=pattern_data.get('description', ''),
                    technology=pattern_data.get('technology', ''),
                    category='analysis',
                    code_example=pattern_data.get('example', ''),
                    usage_context=pattern_data.get('context', ''),
                    best_practices=pattern_data.get('best_practices', []),
                    dependencies=pattern_data.get('dependencies', [])
                )
    
    async def _load_fallback_patterns(self) -> None:
        """Load basic fallback patterns if external data fails"""
        fallback_patterns = {
            'nextjs_app_router': CodingPattern(
                name='Next.js App Router',
                description='Modern Next.js app directory structure',
                technology='Next.js',
                category='architecture',
                code_example='''
// app/layout.tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}
                '''.strip(),
                usage_context='Modern Next.js applications',
                best_practices=['Use app directory', 'Implement proper layouts', 'Optimize for performance'],
                dependencies=['next', 'react', 'typescript']
            ),
            'tailwind_responsive': CodingPattern(
                name='Tailwind Responsive Design',
                description='Responsive design patterns with Tailwind CSS',
                technology='Tailwind CSS',
                category='styling',
                code_example='''
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
  <div className="bg-white rounded-lg shadow-md p-6">
    <h3 className="text-lg font-semibold mb-2">Card Title</h3>
    <p className="text-gray-600">Card content goes here.</p>
  </div>
</div>
                '''.strip(),
                usage_context='Modern responsive web applications',
                best_practices=['Mobile-first approach', 'Use grid system', 'Consistent spacing'],
                dependencies=['tailwindcss']
            )
        }
        
        self.patterns.update(fallback_patterns)
        logger.info("Loaded fallback patterns")
    
    def get_pattern_by_technology(self, technology: str) -> List[CodingPattern]:
        """Get patterns for a specific technology"""
        return [
            pattern for pattern in self.patterns.values()
            if technology.lower() in pattern.technology.lower()
        ]
    
    def get_pattern_by_category(self, category: str) -> List[CodingPattern]:
        """Get patterns for a specific category"""
        return [
            pattern for pattern in self.patterns.values()
            if category.lower() in pattern.category.lower()
        ]
    
    def search_patterns(self, query: str) -> List[CodingPattern]:
        """Search patterns by query"""
        query_lower = query.lower()
        matches = []
        
        for pattern in self.patterns.values():
            if (query_lower in pattern.name.lower() or
                query_lower in pattern.description.lower() or
                query_lower in pattern.technology.lower() or
                query_lower in pattern.category.lower() or
                query_lower in pattern.usage_context.lower()):
                matches.append(pattern)
        
        return matches
    
    def get_best_practices(self, technology: str = None) -> List[str]:
        """Get best practices for a technology or general"""
        if technology:
            practices = []
            for pattern in self.get_pattern_by_technology(technology):
                practices.extend(pattern.best_practices)
            return list(set(practices))
        else:
            all_practices = []
            for practices_list in self.best_practices.values():
                all_practices.extend(practices_list)
            return list(set(all_practices))
    
    def get_recommended_stack(self, project_type: str) -> List[str]:
        """Get recommended technology stack for project type"""
        if project_type.lower() in self.technology_stacks:
            return self.technology_stacks[project_type.lower()]
        
        # Default recommendations based on common patterns
        defaults = {
            'web_app': ['Next.js', 'TypeScript', 'Tailwind CSS', 'React'],
            'mobile_app': ['React Native', 'TypeScript', 'Expo'],
            'saas': ['Next.js', 'TypeScript', 'Tailwind CSS', 'Supabase', 'Stripe'],
            'dashboard': ['Next.js', 'TypeScript', 'Tailwind CSS', 'Recharts'],
            'portfolio': ['Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion']
        }
        
        return defaults.get(project_type.lower(), ['Next.js', 'TypeScript', 'Tailwind CSS'])
```

**Enhanced Agent with Coding Knowledge (src/coding_agent.py)**
```python
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

import openai
from openai import AsyncOpenAI

from .config import GraphitiConfig
from .memory import GraphitiMemory
from .coding_knowledge import AdrianHajdinKnowledge, CodingPattern

logger = logging.getLogger(__name__)

class CodingGraphitiAgent:
    """Enhanced Graphiti Agent with Adrian Hajdin's coding patterns"""
    
    def __init__(self, config: GraphitiConfig):
        self.config = config
        self.memory = GraphitiMemory(config)
        self.coding_knowledge = AdrianHajdinKnowledge(config)
        self.openai_client = AsyncOpenAI(api_key=config.openai_api_key)
        self.conversation_history: List[Dict[str, str]] = []
        
    async def initialize(self) -> None:
        """Initialize the agent and its components"""
        await self.memory.initialize()
        await self.coding_knowledge.load_training_data()
        logger.info(f"Coding Agent {self.config.agent_name} initialized successfully")
    
    async def process_coding_request(self, user_message: str, user_id: str = "default") -> str:
        """Process coding-related requests with Adrian Hajdin patterns"""
        try:
            # Analyze the request type
            request_type = await self._analyze_request_type(user_message)
            
            # Store user message in memory
            await self.memory.add_episode(
                content=f"Coding Request ({user_id}): {user_message}",
                episode_type="coding_request"
            )
            
            # Search for relevant patterns
            relevant_patterns = self._find_relevant_patterns(user_message)
            
            # Search memory for similar requests
            relevant_memories = await self.memory.search_memory(user_message)
            
            # Generate response based on request type
            if request_type == "code_generation":
                response = await self._generate_code(user_message, relevant_patterns)
            elif request_type == "architecture_advice":
                response = await self._provide_architecture_advice(user_message, relevant_patterns)
            elif request_type == "best_practices":
                response = await self._provide_best_practices(user_message, relevant_patterns)
            elif request_type == "debugging_help":
                response = await self._provide_debugging_help(user_message, relevant_patterns, relevant_memories)
            else:
                response = await self._generate_general_coding_response(user_message, relevant_patterns, relevant_memories)
            
            # Store response in memory
            await self.memory.add_episode(
                content=f"Coding Response: {response}",
                episode_type="coding_response"
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to process coding request: {e}")
            return f"I apologize, but I encountered an error processing your coding request: {str(e)}"
    
    async def _analyze_request_type(self, message: str) -> str:
        """Analyze the type of coding request"""
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in ['generate', 'create', 'build', 'make', 'write code']):
            return "code_generation"
        elif any(keyword in message_lower for keyword in ['architecture', 'structure', 'organize', 'design']):
            return "architecture_advice"
        elif any(keyword in message_lower for keyword in ['best practice', 'recommend', 'should i', 'better way']):
            return "best_practices"
        elif any(keyword in message_lower for keyword in ['debug', 'error', 'fix', 'problem', 'issue']):
            return "debugging_help"
        else:
            return "general"
    
    def _find_relevant_patterns(self, query: str) -> List[CodingPattern]:
        """Find relevant coding patterns for the query"""
        # Search patterns
        patterns = self.coding_knowledge.search_patterns(query)
        
        # If no direct matches, try technology-based search
        if not patterns:
            technologies = ['react', 'nextjs', 'typescript', 'tailwind', 'javascript']
            for tech in technologies:
                if tech in query.lower():
                    patterns.extend(self.coding_knowledge.get_pattern_by_technology(tech))
                    break
        
        # Limit to most relevant patterns
        return patterns[:5]
    
    async def _generate_code(self, request: str, patterns: List[CodingPattern]) -> str:
        """Generate code based on request and patterns"""
        # Build context from patterns
        pattern_context = self._build_pattern_context(patterns)
        
        prompt = f"""You are an expert developer trained on Adrian Hajdin's coding patterns and modern web development practices.

Request: {request}

Relevant Patterns and Examples:
{pattern_context}

Generate clean, modern code following these principles:
1. Use TypeScript when applicable
2. Follow modern React patterns (hooks, functional components)
3. Use Tailwind CSS for styling
4. Include proper error handling
5. Add helpful comments
6. Follow the coding patterns shown in the examples

Provide a complete, working code example with explanations."""

        try:
            response = await self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert developer specializing in modern web development with React, Next.js, and TypeScript."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.3
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate code: {e}")
            return "I apologize, but I'm having trouble generating code right now."
    
    async def _provide_architecture_advice(self, request: str, patterns: List[CodingPattern]) -> str:
        """Provide architecture advice based on patterns"""
        # Extract project type from request
        project_type = self._extract_project_type(request)
        recommended_stack = self.coding_knowledge.get_recommended_stack(project_type)
        
        pattern_context = self._build_pattern_context(patterns)
        
        prompt = f"""Based on Adrian Hajdin's modern development patterns, provide architecture advice for:

Request: {request}
Detected Project Type: {project_type}
Recommended Stack: {', '.join(recommended_stack)}

Relevant Patterns:
{pattern_context}

Provide comprehensive architecture advice including:
1. Recommended technology stack
2. Folder structure
3. Key architectural decisions
4. Best practices to follow
5. Potential challenges and solutions"""

        try:
            response = await self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert software architect specializing in modern web applications."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.4
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to provide architecture advice: {e}")
            return "I apologize, but I'm having trouble providing architecture advice right now."
    
    async def _provide_best_practices(self, request: str, patterns: List[CodingPattern]) -> str:
        """Provide best practices based on patterns"""
        # Extract technology from request
        technology = self._extract_technology(request)
        best_practices = self.coding_knowledge.get_best_practices(technology)
        
        pattern_context = self._build_pattern_context(patterns)
        
        practices_text = "\n".join([f"• {practice}" for practice in best_practices[:10]])
        
        response = f"""Based on Adrian Hajdin's coding patterns and modern development practices:

**Best Practices for {technology or 'General Development'}:**

{practices_text}

**Relevant Patterns from Analysis:**
{pattern_context}

**Key Recommendations:**
1. Always use TypeScript for type safety
2. Implement responsive design with Tailwind CSS
3. Follow the app directory structure in Next.js
4. Use proper error boundaries and handling
5. Optimize for performance and SEO
6. Maintain consistent code formatting
7. Implement proper authentication patterns
8. Use modern React patterns (hooks, suspense)
"""
        
        return response
    
    async def _provide_debugging_help(self, request: str, patterns: List[CodingPattern], memories: List[Dict[str, Any]]) -> str:
        """Provide debugging help using patterns and memory"""
        memory_context = self._build_memory_context(memories)
        pattern_context = self._build_pattern_context(patterns)
        
        prompt = f"""Help debug this issue using modern development practices:

Issue: {request}

Similar Past Issues:
{memory_context}

Relevant Code Patterns:
{pattern_context}

Provide debugging steps and solution following these principles:
1. Identify the root cause
2. Suggest modern solutions
3. Provide code examples if needed
4. Include prevention strategies
5. Reference best practices"""

        try:
            response = await self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert debugging specialist for modern web applications."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to provide debugging help: {e}")
            return "I apologize, but I'm having trouble providing debugging help right now."
    
    async def _generate_general_coding_response(self, message: str, patterns: List[CodingPattern], memories: List[Dict[str, Any]]) -> str:
        """Generate general coding response"""
        memory_context = self._build_memory_context(memories)
        pattern_context = self._build_pattern_context(patterns)
        
        prompt = f"""You are a coding assistant trained on Adrian Hajdin's modern development patterns.

User Question: {message}

Relevant Memory Context:
{memory_context}

Relevant Code Patterns:
{pattern_context}

Provide a helpful response that:
1. Addresses the user's question directly
2. References modern development practices
3. Includes code examples if relevant
4. Suggests best practices
5. Is conversational and educational"""

        try:
            response = await self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {"role": "system", "content": "You are a helpful coding assistant specializing in modern web development."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.5
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return "I apologize, but I'm having trouble generating a response right now."
    
    def _build_pattern_context(self, patterns: List[CodingPattern]) -> str:
        """Build context string from patterns"""
        if not patterns:
            return "No specific patterns found."
        
        context_parts = []
        for i, pattern in enumerate(patterns[:3], 1):
            context_parts.append(f"""
{i}. **{pattern.name}** ({pattern.technology})
   Description: {pattern.description}
   Usage: {pattern.usage_context}
   
   Example:
   ```
   {pattern.code_example[:300]}...
   ```
   
   Best Practices: {', '.join(pattern.best_practices[:3])}
            """.strip())
        
        return "\n\n".join(context_parts)
    
    def _build_memory_context(self, memories: List[Dict[str, Any]]) -> str:
        """Build context string from memories"""
        if not memories:
            return "No relevant memories found."
        
        context_parts = ["Relevant past interactions:"]
        for i, memory in enumerate(memories[:3], 1):
            context_parts.append(
                f"{i}. {memory.get('relationship', 'Related')}: {memory.get('content', 'No content')[:150]}..."
            )
        
        return "\n".join(context_parts)
    
    def _extract_project_type(self, request: str) -> str:
        """Extract project type from request"""
        request_lower = request.lower()
        
        if any(word in request_lower for word in ['saas', 'subscription', 'payment']):
            return 'saas'
        elif any(word in request_lower for word in ['dashboard', 'admin', 'analytics']):
            return 'dashboard'
        elif any(word in request_lower for word in ['portfolio', 'personal', 'showcase']):
            return 'portfolio'
        elif any(word in request_lower for word in ['mobile', 'react native', 'app']):
            return 'mobile_app'
        else:
            return 'web_app'
    
    def _extract_technology(self, request: str) -> str:
        """Extract technology from request"""
        request_lower = request.lower()
        
        technologies = {
            'react': ['react', 'jsx', 'component'],
            'nextjs': ['next.js', 'nextjs', 'next'],
            'typescript': ['typescript', 'ts', 'types'],
            'tailwind': ['tailwind', 'css', 'styling'],
            'javascript': ['javascript', 'js']
        }
        
        for tech, keywords in technologies.items():
            if any(keyword in request_lower for keyword in keywords):
                return tech
        
        return ""
    
    async def get_coding_stats(self) -> Dict[str, Any]:
        """Get statistics about the coding knowledge base"""
        return {
            "total_patterns": len(self.coding_knowledge.patterns),
            "technologies": list(self.coding_knowledge.technology_stacks.keys()),
            "pattern_categories": list(set(p.category for p in self.coding_knowledge.patterns.values())),
            "project_templates": list(self.coding_knowledge.project_templates.keys()),
            "agent_name": self.config.agent_name,
            "status": "active"
        }
    
    async def close(self) -> None:
        """Close the agent and cleanup resources"""
        await self.memory.close()
        logger.info(f"Coding Agent {self.config.agent_name} closed")
```

## Updated Runner with Coding Features

**run_coding_agent.py**
```python
#!/usr/bin/env python3
"""
Enhanced Graphiti Agent with Adrian Hajdin coding patterns
"""
import asyncio
import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

from src.config import GraphitiConfig
from src.coding_agent import CodingGraphitiAgent
from src.utils import setup_logging, validate_environment

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global agent instance
agent: CodingGraphitiAgent = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    global agent
    
    # Startup
    logger.info("Starting Coding Graphiti Agent...")
    
    # Validate environment
    env_check = validate_environment()
    if not env_check["valid"]:
        logger.error("Environment validation failed:")
        for issue in env_check["issues"]:
            logger.error(f"  - {issue}")
        raise RuntimeError("Environment validation failed")
    
    # Initialize agent
    config = GraphitiConfig()
    config.validate()
    
    agent = CodingGraphitiAgent(config)
    await agent.initialize()
    
    logger.info("Coding Graphiti Agent started successfully!")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Coding Graphiti Agent...")
    if agent:
        await agent.close()
    logger.info("Shutdown complete.")

# FastAPI app
app = FastAPI(
    title="Adrian Hajdin Coding Agent API",
    description="AI Coding Agent powered by Graphiti and trained on Adrian Hajdin's patterns",
    version="1.0.0",
    lifespan=lifespan
)

# Request/Response models
class CodingRequest(BaseModel):
    message: str
    user_id: str = "default"
    request_type: str = "general"  # code_generation, architecture, best_practices, debugging

class MessageResponse(BaseModel):
    response: str
    timestamp: str
    patterns_used: int = 0

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy", 
        "agent": agent.config.agent_name if agent else "not initialized",
        "features": ["coding_patterns", "graphiti_memory", "adrian_hajdin_training"]
    }

@app.post("/code", response_model=MessageResponse)
async def process_coding_request(request: CodingRequest):
    """Process coding requests with Adrian Hajdin patterns"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    try:
        response = await agent.process_coding_request(request.message, request.user_id)
        from datetime import datetime
        return MessageResponse(
            response=response,
            timestamp=datetime.now().isoformat(),
            patterns_used=len(agent.coding_knowledge.patterns)
        )
    except Exception as e:
        logger.error(f"Coding request error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/patterns/search")
async def search_patterns(q: str):
    """Search coding patterns"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    patterns = agent.coding_knowledge.search_patterns(q)
    return {
        "query": q,
        "results": [
            {
                "name": p.name,
                "description": p.description,
                "technology": p.technology,
                "category": p.category,
                "usage_context": p.usage_context
            }
            for p in patterns[:10]
        ]
    }

@app.get("/patterns/technology/{tech}")
async def get_patterns_by_technology(tech: str):
    """Get patterns for specific technology"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    patterns = agent.coding_knowledge.get_pattern_by_technology(tech)
    return {
        "technology": tech,
        "patterns": [
            {
                "name": p.name,
                "description": p.description,
                "code_example": p.code_example[:200] + "..." if len(p.code_example) > 200 else p.code_example
            }
            for p in patterns
        ]
    }

@app.get("/recommendations/stack")
async def get_stack_recommendation(project_type: str):
    """Get recommended technology stack"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    stack = agent.coding_knowledge.get_recommended_stack(project_type)
    return {
        "project_type": project_type,
        "recommended_stack": stack,
        "description": f"Recommended technologies for {project_type} based on Adrian Hajdin's patterns"
    }

@app.get("/stats")
async def get_coding_stats():
    """Get coding knowledge statistics"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return await agent.get_coding_stats()

if __name__ == "__main__":
    uvicorn.run(
        "run_coding_agent:app",
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=True
    )
