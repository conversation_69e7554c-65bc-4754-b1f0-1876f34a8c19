Core Production Upgrades
These implementations address the essential functional gaps.


1. Django Admin Configuration (admin.py)
This file provides crucial internal oversight for your support and operations teams.
# Bidbeez/backend/services/toolhire/admin.py
from django.contrib import admin
from .models import P2PListing, Booking, TraditionalListing, ToolCategory

@admin.register(P2PListing)
class P2PListingAdmin(admin.ModelAdmin):
list_display = ('title', 'owner', 'tenant', 'daily_rate_zar', 'is_available', 'rating', 'hires_completed')
list_filter = ('is_available', 'tenant')
search_fields = ('title', 'owner__username', 'description')
ordering = ('-created_at',)

@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
list_display = ('id', 'renter', 'status', 'total_cost', 'created_at')
list_filter = ('status', 'tenant')
search_fields = ('id', 'renter__username')
raw_id_fields = ('renter', 'listing', 'traditional_listing') # For performance
ordering = ('-created_at',)

@admin.register(TraditionalListing)
class TraditionalListingAdmin(admin.ModelAdmin):
list_display = ('title', 'company', 'tenant', 'daily_rate_zar', 'is_available')
list_filter = ('is_available', 'tenant')
search_fields = ('title', 'company__name')

@admin.register(ToolCategory)
class ToolCategoryAdmin(admin.ModelAdmin):
list_display = ('name', 'description')

2. Expanded Integration Test (Simulating Failures)
This test ensures the system behaves correctly when its dependencies fail, a critical aspect of resilience.
# Bidbeez/backend/services/toolhire/tests/integration/test_booking_flow_with_failures.py
from django.test import TestCase
from unittest.mock import patch
from ..models import Booking, P2PListing
from ..services.booking_orchestration_service import BookingStateEngine
# ... (other imports)

class BookingFailureTests(TestCase):
def setUp(self):
# ... (setup users, tenants, listings, etc.)
self.booking = Booking.objects.create(...)
self.engine = BookingStateEngine(self.booking)

@patch('toolhire.clients.escrow_client.secure_funds')
def test_booking_disputed_when_escrow_fails(self, mock_secure_funds):
"""
Verify that the booking status is set to DISPUTED if the escrow call fails.
"""
# Configure the mock to raise an exception when called
mock_secure_funds.side_effect = Exception("Escrow service unavailable")

# Initial state transition
self.engine.confirm_booking()
self.assertEqual(self.booking.status, 'AWAITING_PAYMENT')

# This transition should fail and trigger the dispute logic
with self.assertRaises(RuntimeError):
self.engine.secure_funds()

# Check the final state of the booking
self.booking.refresh_from_db()
self.assertEqual(self.booking.status, 'DISPUTED')

3. Photo Upload Logic
This uses a secure, two-step process: first, get a secure upload URL from the documents service, then upload the file directly to cloud storage.
Backend Endpoint:
# Bidbeez/backend/services/toolhire/api/v1/endpoints/p2p_listings.py
from rest_framework.decorators import action
from rest_framework.response import Response
from ...clients import documents_client # Assuming a new client for the documents service

class P2PListingViewSet(viewsets.ModelViewSet):
# ... existing code ...

@action(detail=True, methods=['post'])
def generate_photo_upload_url(self, request, pk=None):
"""
Generates a pre-signed URL for securely uploading a photo.
"""
listing = self.get_object()
# Ensure the user owns this listing
if listing.owner != request.user:
return Response({'error': 'Permission denied'}, status=403)

file_type = request.data.get('file_type', 'image/jpeg')
file_name = f"toolhire/listings/{listing.id}/{uuid.uuid4()}.jpg"

try:
# Call the central documents service to get a secure URL
upload_data = documents_client.get_presigned_upload_url(file_name, file_type)
return Response(upload_data)
except Exception as e:
return Response({'error': str(e)}, status=500)

Frontend Component:
// standalone_app_source/frontend/src/components/PhotoUploader.jsx
import React, { useState } from 'react';
import { useP2PService } from '../services/sdk';

export default function PhotoUploader({ listingId, onUploadSuccess }) {
const [isUploading, setIsUploading] = useState(false);
const p2pService = useP2PService();

const handleFileChange = async (event) => {
const file = event.target.files[0];
if (!file) return;

setIsUploading(true);
try {
// Step 1: Get the secure, pre-signed upload URL from our backend
const { upload_url, file_key } = await p2pService.generatePhotoUploadUrl(listingId, file.type);

// Step 2: Upload the file directly to the cloud storage (e.g., S3)
await fetch(upload_url, {
method: 'PUT',
body: file,
headers: { 'Content-Type': file.type },
});

// Step 3: Notify backend of successful upload to link the photo
await p2pService.linkPhotoToListing(listingId, file_key);
onUploadSuccess(file_key); // Update parent component state

} catch (error) {
console.error("Upload failed:", error);
} finally {
setIsUploading(false);
}
};

return (
<div>
<input type="file" accept="image/*" onChange={handleFileChange} disabled={isUploading} />
{isUploading && <p>Uploading...</p>}
</div>
);
}

4. Notification Hooks
This is integrated directly into the BookingStateEngine.
# Bidbeez/backend/services/toolhire/services/booking_orchestration_service.py
from ..clients import notification_client # NEW: Client for the notification service

class BookingStateEngine:
# ... existing states and __init__ ...

def __init__(self, booking_instance: Booking):
# ...
self.machine.add_transition('secure_funds', 'AWAITING_PAYMENT', 'AWAITING_PICKUP',
before='_secure_funds_in_escrow',
after='_notify_owner_of_payment') # NEW: after hook
self.machine.add_transition('complete_rental', 'PENDING_COMPLETION', 'COMPLETED',
after=['_release_escrow_and_notify_hivemind', '_send_rating_reminders']) # NEW
# ...

def _notify_owner_of_payment(self):
"""Send an SMS to the tool owner that their rental is confirmed and paid."""
owner = self.booking.listing.owner
message = f"Hi {owner.first_name}, payment for your tool '{self.booking.listing.title}' has been secured. The rental is confirmed."
notification_client.send_sms(phone_number=owner.phone_number, message=message)

def _send_rating_reminders(self):
"""Send notifications to both parties to rate their experience."""
renter_msg = "Your rental is complete! Please rate your experience with the tool and owner."
owner_msg = "Your tool has been returned. Please rate your experience with the renter."
notification_client.send_email(email=self.booking.renter.email, subject="Rate Your Rental", message=renter_msg)
notification_client.send_email(email=self.booking.listing.owner.email, subject="Rate Your Renter", message=owner_msg)

5. Payment Gateway Webhook Receiver
This endpoint listens for notifications from external payment processors.
# Bidbeez/backend/services/toolhire/api/v1/endpoints/webhooks.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from ...models import Booking
from ...services.booking_orchestration_service import BookingStateEngine
# Assume a utility for verifying webhook signatures
from ...core.utils import verify_yoco_signature

class YocoWebhookReceiver(APIView):
permission_classes = [AllowAny]

def post(self, request, *args, **kwargs):
# Step 1: Verify the webhook signature to ensure it's from Yoco
if not verify_yoco_signature(request.headers, request.body):
return Response({"status": "unauthorized"}, status=401)

event_data = request.data

# Step 2: Process the event
if event_data['event_type'] == 'charge.succeeded':
booking_id = event_data['metadata'].get('booking_id')
if booking_id:
try:
booking = Booking.objects.get(id=booking_id, status='AWAITING_PAYMENT')
engine = BookingStateEngine(booking)
# Trigger the state transition, as payment is now confirmed
engine.secure_funds()
except Booking.DoesNotExist:
# Log an error: received a webhook for a non-existent or invalid booking
pass

return Response({"status": "ok"})

Strategic Enhancement Upgrades
These are the high-value features for market domination.
1. Tool Inspection Service (Validator Workflow)
Model Update:
# Bidbeez/backend/services/toolhire/models/p2p_listing.py
class P2PListing(models.Model):
# ... existing fields ...
VERIFICATION_CHOICES = [
('UNVERIFIED', 'Unverified'),
('PENDING', 'Pending Validation'),
('VERIFIED', 'Verified'),
('REJECTED', 'Rejected'),
]
verification_status = models.CharField(max_length=20, choices=VERIFICATION_CHOICES, default='UNVERIFIED')
validator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='validated_listings')
validated_at = models.DateTimeField(null=True, blank=True)

New Service:
# Bidbeez/backend/services/toolhire/services/validation_service.py
from ..clients import bees_client # Assuming a client for the Bees microservice

class ToolValidationService:
@staticmethod
def request_validation(listing_id: str, requesting_user):
listing = P2PListing.objects.get(id=listing_id, owner=requesting_user)
if listing.verification_status != 'UNVERIFIED':
raise ValueError("Listing is already verified or pending validation.")

# Find a nearby, qualified "Bee" with validator skills
bee = bees_client.find_nearby_validator(
location=listing.location,
skill='tool_inspection'
)

if not bee:
raise RuntimeError("No qualified validators available in your area.")

# Assign the task to the Bee via the Bees microservice
bees_client.create_task(
bee_id=bee['id'],
task_type='VALIDATE_TOOL',
metadata={'listing_id': listing.id}
)

listing.verification_status = 'PENDING'
listing.save()
return {"status": "pending", "assigned_validator_id": bee['id']}

2. Dynamic Pricing Suggestions (AI Integration)
Backend Endpoint:
# Bidbeez/backend/services/toolhire/api/v1/endpoints/p2p_listings.py
class P2PListingViewSet(viewsets.ModelViewSet):
# ... existing code ...

@action(detail=False, methods=['post'])
def get_pricing_suggestion(self, request):
"""
Calls Queenbee to get a dynamic pricing suggestion.
"""
category = request.data.get('category')
location = request.data.get('location') # GeoJSON point

try:
suggestion = queenbee_client.get_dynamic_price(category, location)
return Response(suggestion)
except Exception as e:
return Response({'error': str(e)}, status=500)

Frontend Integration:
// standalone_app_source/frontend/src/pages/P2P/ListNewTool.jsx
function ListNewTool() {
// ... existing state and handlers ...
const [suggestion, setSuggestion] = useState(null);

const handlePriceFocus = async () => {
// When user focuses on the price input, get a suggestion
if (form.category && form.location) {
const priceSuggestion = await p2pService.getPricingSuggestion(form.category, form.location);
setSuggestion(priceSuggestion);
}
};

return (
<form onSubmit={handleSubmit}>
{/* ... other form fields ... */}
<input
name="daily_rate_zar"
type="number"
value={form.daily_rate_zar}
onChange={handleChange}
onFocus={handlePriceFocus}
placeholder="Daily Rate (ZAR)"
required
/>
{suggestion && (
<p className="suggestion">
Suggested rate in your area: <strong>ZAR {suggestion.optimal_rate}</strong>
<button type="button" onClick={() => setForm({...form, daily_rate_zar: suggestion.optimal_rate})}>
Use Suggestion
</button>
</p>
)}
<button type="submit">List Tool</button>
</form>
);
}

3. "Hire to Own" Option
Model Update:
# Bidbeez/backend/services/toolhire/models/p2p_listing.py
class P2PListing(models.Model):
# ... existing fields ...
is_hire_to_own_available = models.BooleanField(default=False)
purchase_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
# e.g., 0.7 means 70% of rental fees contribute to purchase
rental_to_purchase_conversion_rate = models.FloatField(null=True, blank=True)

State Machine Update:
# Bidbeez/backend/services/toolhire/services/booking_orchestration_service.py
class BookingStateEngine:
STATES = [
# ... existing states ...
'CONVERTED_TO_PURCHASE' # NEW STATE
]

def __init__(self, booking_instance: Booking):
# ... existing transitions ...
self.machine.add_transition('convert_to_purchase', 'IN_PROGRESS', 'CONVERTED_TO_PURCHASE',
before='_process_purchase_payment')

def _process_purchase_payment(self):
"""Calculates final payment and instructs escrow to handle the purchase."""
listing = self.booking.listing
rentals_paid = self.booking.total_cost # Or sum of all past rentals for this item
contribution = rentals_paid * listing.rental_to_purchase_conversion_rate
final_price = listing.purchase_price - contribution

# Instruct escrow to perform a different type of transaction
escrow_client.execute_purchase(
booking_id=self.booking.id,
buyer_id=self.booking.renter.id,
seller_id=listing.owner.id,
amount=final_price
)




SQL QUERY TABLE SCHEMA

-- =============================================================================
-- Bidbeez ToolHire Module - Complete SQL Table Schema
-- =============================================================================
-- Dialect: PostgreSQL with PostGIS Extension
-- Author: Gemini AI
-- Date: June 14, 2025
-- Description: This script defines the complete database schema for the
-- toolhire microservice and its related components.
-- =============================================================================

-- Enable PostGIS Extension if it's not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS postgis;

-- -----------------------------------------------------------------------------
-- Prerequisite Tables (Simulating other Bidbeez services for context)
-- -----------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS tenants (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
name VARCHAR(255) NOT NULL,
created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS auth_user (
id SERIAL PRIMARY KEY,
username VARCHAR(150) UNIQUE NOT NULL,
first_name VARCHAR(150),
last_name VARCHAR(150),
email VARCHAR(254) UNIQUE NOT NULL,
phone_number VARCHAR(30) UNIQUE,
is_active BOOLEAN NOT NULL DEFAULT TRUE,
tenant_id UUID REFERENCES tenants(id) ON DELETE SET NULL,
-- Other Django auth fields would be here
date_joined TIMESTAMTz NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS companies (
id SERIAL PRIMARY KEY,
name VARCHAR(255) NOT NULL,
registration_number VARCHAR(100) UNIQUE,
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE
-- Other company details
);


-- -----------------------------------------------------------------------------
-- Core ToolHire Tables
-- -----------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS tool_categories (
id SERIAL PRIMARY KEY,
name VARCHAR(100) UNIQUE NOT NULL,
description TEXT
);

CREATE TABLE IF NOT EXISTS p2p_listings (
id SERIAL PRIMARY KEY,
owner_id INTEGER NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
title VARCHAR(120) NOT NULL,
description TEXT,
category_id INTEGER REFERENCES tool_categories(id) ON DELETE SET NULL,

daily_rate_zar DECIMAL(10, 2) NOT NULL CHECK (daily_rate_zar >= 0),
estimated_value DECIMAL(10, 2) NOT NULL CHECK (estimated_value > 0), -- For insurance calculation

-- PostGIS geospatial point for location-based searches
location GEOMETRY(Point, 4326) NOT NULL,

is_available BOOLEAN NOT NULL DEFAULT TRUE,
photo_urls JSONB NOT NULL DEFAULT '[]'::jsonb,

-- Trust & Gamification Metrics
rating DECIMAL(3, 2) NOT NULL DEFAULT 5.00 CHECK (rating BETWEEN 0 AND 5),
hires_completed INTEGER NOT NULL DEFAULT 0 CHECK (hires_completed >= 0),

-- Verification Workflow
verification_status VARCHAR(20) NOT NULL DEFAULT 'UNVERIFIED' CHECK (verification_status IN ('UNVERIFIED', 'PENDING', 'VERIFIED', 'REJECTED')),
validator_id INTEGER REFERENCES auth_user(id) ON DELETE SET NULL,
validated_at TIMESTAMPTZ,

-- Monetization Upgrades
is_hire_to_own_available BOOLEAN NOT NULL DEFAULT FALSE,
purchase_price DECIMAL(10, 2) CHECK (purchase_price > 0),
rental_to_purchase_conversion_rate REAL CHECK (rental_to_purchase_conversion_rate BETWEEN 0 AND 1),

created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS traditional_listings (
id SERIAL PRIMARY KEY,
company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
title VARCHAR(120) NOT NULL,
category_id INTEGER REFERENCES tool_categories(id) ON DELETE SET NULL,

daily_rate_zar DECIMAL(10, 2) NOT NULL CHECK (daily_rate_zar >= 0),
estimated_value DECIMAL(10, 2) NOT NULL CHECK (estimated_value > 0),

depot_location GEOMETRY(Point, 4326) NOT NULL,
is_available BOOLEAN NOT NULL DEFAULT TRUE,

created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS rental_waivers (
id SERIAL PRIMARY KEY,
listing_id INTEGER NOT NULL REFERENCES p2p_listings(id) ON DELETE CASCADE,
title VARCHAR(255) NOT NULL,
waiver_text TEXT NOT NULL,
version INTEGER NOT NULL DEFAULT 1,
is_active BOOLEAN NOT NULL DEFAULT TRUE,
created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS bookings (
id SERIAL PRIMARY KEY,
renter_id INTEGER NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,

-- A booking can be for either a P2P or a traditional listing
p2p_listing_id INTEGER REFERENCES p2p_listings(id) ON DELETE CASCADE,
traditional_listing_id INTEGER REFERENCES traditional_listings(id) ON DELETE CASCADE,

status VARCHAR(50) NOT NULL DEFAULT 'PENDING_CONFIRMATION',

-- Financials
total_cost DECIMAL(10, 2) NOT NULL,
security_deposit DECIMAL(10, 2) NOT NULL,

-- Logistics
pickup_location GEOMETRY(Point, 4326),
start_date TIMESTAMPTZ NOT NULL,
end_date TIMESTAMPTZ NOT NULL,

-- Auditing and Legal
audit_log JSONB NOT NULL DEFAULT '[]'::jsonb,
insurance_policy JSONB DEFAULT '{}'::jsonb,
accepted_waiver_id INTEGER REFERENCES rental_waivers(id) ON DELETE SET NULL,
waiver_accepted_at TIMESTAMPTZ,

created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

CONSTRAINT check_one_listing_type
CHECK ((p2p_listing_id IS NOT NULL AND traditional_listing_id IS NULL) OR
(p2p_listing_id IS NULL AND traditional_listing_id IS NOT NULL))
);


-- -----------------------------------------------------------------------------
-- Whitelabel & Analytics Tables
-- -----------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS tenant_themes (
id SERIAL PRIMARY KEY,
tenant_id UUID NOT NULL UNIQUE REFERENCES tenants(id) ON DELETE CASCADE,
primary_color VARCHAR(7) NOT NULL DEFAULT '#000000',
secondary_color VARCHAR(7) NOT NULL DEFAULT '#FFFFFF',
logo_url VARCHAR(500),
custom_domain VARCHAR(255) UNIQUE
);

CREATE TABLE IF NOT EXISTS tenant_analytics (
id SERIAL PRIMARY KEY,
tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
task_id VARCHAR(50) UNIQUE NOT NULL,
task_type VARCHAR(50) NOT NULL,
result JSONB,
created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);


-- -----------------------------------------------------------------------------
-- Indexes for Performance
-- -----------------------------------------------------------------------------

-- Geospatial indexes using GIST (Generalized Search Tree)
CREATE INDEX IF NOT EXISTS idx_p2p_listings_location ON p2p_listings USING GIST (location);
CREATE INDEX IF NOT EXISTS idx_traditional_listings_depot_location ON traditional_listings USING GIST (depot_location);

-- Foreign key and common query field indexes
CREATE INDEX IF NOT EXISTS idx_p2p_listings_owner_id ON p2p_listings (owner_id);
CREATE INDEX IF NOT EXISTS idx_p2p_listings_tenant_id ON p2p_listings (tenant_id);
CREATE INDEX IF NOT EXISTS idx_p2p_listings_category_id ON p2p_listings (category_id);

CREATE INDEX IF NOT EXISTS idx_bookings_renter_id ON bookings (renter_id);
CREATE INDEX IF NOT EXISTS idx_bookings_tenant_id ON bookings (tenant_id);
CREATE INDEX IF NOT EXISTS idx_bookings_p2p_listing_id ON bookings (p2p_listing_id);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings (status);

CREATE INDEX IF NOT EXISTS idx_tenant_analytics_tenant_id ON tenant_analytics (tenant_id);



-- =============================================================================
-- Bidbeez ToolHire Module - Schema Enhancements & Hardening
-- =============================================================================
-- Dialect: PostgreSQL
-- Author: Gemini AI
-- Date: June 14, 2025
-- Location: Saint Helena Bay, Western Cape, South Africa
-- Description: This script adds tables for payments, logs, disputes, reviews,
-- and webhooks. It also implements Row-Level Security policies
-- for true multi-tenant data isolation.
-- =============================================================================

-- -----------------------------------------------------------------------------
-- Section 1: Next Recommended Table Enhancements
-- -----------------------------------------------------------------------------

-- 1. Escrow + Payment Tracking Table
CREATE TABLE IF NOT EXISTS toolhire_payment_requests (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
booking_id INTEGER NOT NULL REFERENCES toolhire_bookings(id) ON DELETE CASCADE,
requesting_actor_id INTEGER NOT NULL REFERENCES auth_user(id),

-- Split payment details for Multi-Party Escrow
total_amount DECIMAL(10, 2) NOT NULL,
owner_payout_amount DECIMAL(10, 2) NOT NULL,
platform_fee_amount DECIMAL(10, 2) NOT NULL,
insurance_premium_amount DECIMAL(10, 2) DEFAULT 0.00,
logistics_fee_amount DECIMAL(10, 2) DEFAULT 0.00,

payment_status VARCHAR(50) NOT NULL DEFAULT 'PENDING' CHECK (payment_status IN ('PENDING', 'SUCCEEDED', 'FAILED', 'REFUNDED')),
payment_method VARCHAR(100), -- e.g., 'Yoco', 'Peach Payments', 'Internal Wallet'
processor_transaction_id VARCHAR(255),

escrow_release_triggered_at TIMESTAMPTZ,

created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE toolhire_payment_requests IS 'Tracks individual payment attempts and the multi-party split for each booking.';


-- 2. Immutable Booking State Transition Logs
CREATE TABLE IF NOT EXISTS toolhire_booking_logs (
id BIGSERIAL PRIMARY KEY,
booking_id INTEGER NOT NULL REFERENCES toolhire_bookings(id) ON DELETE CASCADE,
actor_id INTEGER REFERENCES auth_user(id) ON DELETE SET NULL, -- Who initiated the change

from_state VARCHAR(50) NOT NULL,
to_state VARCHAR(50) NOT NULL,

remarks TEXT, -- e.g., 'Payment failed via Yoco', 'Owner confirmed pickup'
transition_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE toolhire_booking_logs IS 'An immutable audit trail of every status change for a booking, replacing the JSONB field for better querying.';


-- 3. Damage Reports / Dispute System Table
CREATE TABLE IF NOT EXISTS toolhire_disputes (
id SERIAL PRIMARY KEY,
booking_id INTEGER NOT NULL REFERENCES toolhire_bookings(id) ON DELETE CASCADE,
raised_by_id INTEGER NOT NULL REFERENCES auth_user(id),

dispute_type VARCHAR(100) NOT NULL CHECK (dispute_type IN ('TOOL_DAMAGE', 'LATE_RETURN', 'INCORRECT_ITEM', 'OTHER')),
dispute_status VARCHAR(50) NOT NULL DEFAULT 'OPEN' CHECK (dispute_status IN ('OPEN', 'UNDER_REVIEW', 'RESOLVED', 'CLOSED')),

description TEXT NOT NULL,
evidence_photo_urls JSONB, -- Links to photos in the Documents service
inspector_report_id VARCHAR(255), -- Link to a Bee inspector's report

resolution_details TEXT,

created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE toolhire_disputes IS 'Manages disputes related to bookings, including evidence and resolution.';


-- 4. Granular Feedback & Rating Table
CREATE TABLE IF NOT EXISTS toolhire_reviews (
id SERIAL PRIMARY KEY,
booking_id INTEGER NOT NULL UNIQUE REFERENCES toolhire_bookings(id) ON DELETE CASCADE, -- One review per booking
reviewer_id INTEGER NOT NULL REFERENCES auth_user(id),
reviewee_id INTEGER NOT NULL REFERENCES auth_user(id), -- The other party in the transaction

-- Granular ratings
rating_overall DECIMAL(2, 1) NOT NULL CHECK (rating_overall BETWEEN 1.0 AND 5.0),
rating_communication DECIMAL(2, 1) CHECK (rating_communication BETWEEN 1.0 AND 5.0),
rating_tool_condition DECIMAL(2, 1) CHECK (rating_tool_condition BETWEEN 1.0 AND 5.0),

comment TEXT,
is_public BOOLEAN NOT NULL DEFAULT TRUE,

created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE toolhire_reviews IS 'Stores detailed star ratings and comments for completed bookings.';


-- 5. Webhook Activity Logging Table
CREATE TABLE IF NOT EXISTS toolhire_webhooks (
id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
received_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
source_gateway VARCHAR(50) NOT NULL, -- e.g., 'Yoco', 'Peach Payments'
is_signature_valid BOOLEAN NOT NULL,

request_headers JSONB,
request_body JSONB,

processing_status VARCHAR(50) NOT NULL DEFAULT 'RECEIVED' CHECK (processing_status IN ('RECEIVED', 'PROCESSED', 'ERROR')),
processing_notes TEXT
);
COMMENT ON TABLE toolhire_webhooks IS 'Logs all incoming webhook notifications for audit and debugging purposes.';


-- -----------------------------------------------------------------------------
-- Section 2: Scalability & Trust Enhancements
-- -----------------------------------------------------------------------------

-- 1. Row-Level Security (RLS) Policies
-- This ensures that even a buggy API cannot accidentally leak data from one tenant to another.
-- NOTE: A corresponding TenantMiddleware must be implemented in Django to set the 'app.current_tenant' session variable.

-- Enable RLS on a key table
ALTER TABLE toolhire_p2p_listings ENABLE ROW LEVEL SECURITY;
ALTER TABLE toolhire_bookings ENABLE ROW LEVEL SECURITY;
-- Repeat for all tenant-linked tables...

-- Create the policy
CREATE POLICY tenant_isolation_policy ON toolhire_p2p_listings
FOR ALL
USING (tenant_id::text = current_setting('app.current_tenant', true))
WITH CHECK (tenant_id::text = current_setting('app.current_tenant', true));

CREATE POLICY tenant_isolation_policy_bookings ON toolhire_bookings
FOR ALL
USING (tenant_id::text = current_setting('app.current_tenant', true))
WITH CHECK (tenant_id::text = current_setting('app.current_tenant', true));

COMMENT ON POLICY tenant_isolation_policy ON toolhire_p2p_listings IS 'Ensures users can only see and interact with listings belonging to their own tenant.';


-- 2. Multi-Party Escrow Model: Already handled by adding split payment columns to `toolhire_payment_requests`.

-- 3. Tool Condition Ledger Table
CREATE TABLE IF NOT EXISTS tool_condition_audits (
id BIGSERIAL PRIMARY KEY,
listing_id INTEGER NOT NULL REFERENCES toolhire_p2p_listings(id) ON DELETE CASCADE,
booking_id INTEGER REFERENCES toolhire_bookings(id) ON DELETE SET NULL, -- Null if it's a periodic inspection

inspected_by_id INTEGER NOT NULL REFERENCES auth_user(id), -- Could be owner, renter, or a Bee validator
inspection_type VARCHAR(50) NOT NULL CHECK (inspection_type IN ('PICKUP_CHECK', 'RETURN_CHECK', 'VALIDATION_INSPECTION')),

condition_code VARCHAR(50), -- e.g., 'A1_EXCELLENT', 'B2_MINOR_SCRATCHES'
notes TEXT,
condition_photo_urls JSONB,

inspection_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE tool_condition_audits IS 'Creates a historical ledger of a tool''s condition over time, crucial for trust and dispute resolution.';


-- -----------------------------------------------------------------------------
-- Indexes for New Tables
-- -----------------------------------------------------------------------------

CREATE INDEX IF NOT EXISTS idx_payment_requests_booking_id ON toolhire_payment_requests (booking_id);
CREATE INDEX IF NOT EXISTS idx_booking_logs_booking_id ON toolhire_booking_logs (booking_id);
CREATE INDEX IF NOT EXISTS idx_disputes_booking_id ON toolhire_disputes (booking_id);
CREATE INDEX IF NOT EXISTS idx_reviews_booking_id ON toolhire_reviews (booking_id);
CREATE INDEX IF NOT EXISTS idx_condition_audits_listing_id ON tool_condition_audits (listing_id);

