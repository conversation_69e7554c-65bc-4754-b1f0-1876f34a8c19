<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐝 BeeRunner - Ultra Modern Task Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #f59e0b;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #3b82f6;
            --dark: #0f172a;
            --light: #f8fafc;
            --bee-yellow: #fbbf24;
            --bee-orange: #f97316;
            --bee-purple: #8b5cf6;
            --bee-green: #22c55e;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Ultra Modern Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--bee-yellow), var(--bee-orange), var(--primary), var(--bee-purple));
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.75rem;
            font-weight: 900;
            color: var(--primary);
        }

        .logo i {
            font-size: 2.5rem;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: buzz 2s ease-in-out infinite;
        }

        @keyframes buzz {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px) rotate(-1deg); }
            75% { transform: translateX(2px) rotate(1deg); }
        }

        .bee-profile {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .bee-info {
            text-align: right;
        }

        .bee-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .bee-details {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin-top: 2px;
        }

        .bee-status {
            background: linear-gradient(135deg, var(--success), #059669);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: var(--shadow-lg);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .bee-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.5rem;
            box-shadow: var(--shadow-xl);
            position: relative;
        }

        .bee-avatar::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--bee-yellow), var(--bee-orange), var(--primary), var(--bee-purple));
            border-radius: 50%;
            z-index: -1;
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Ultra Modern Navigation */
        .nav-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            padding: 8px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            overflow-x: auto;
        }

        .nav-tab {
            padding: 15px 30px;
            border: none;
            background: transparent;
            border-radius: 16px;
            cursor: pointer;
            font-weight: 600;
            color: var(--gray-600);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95rem;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .nav-tab:hover::before {
            left: 100%;
        }

        .nav-tab:hover {
            background: var(--gray-100);
            color: var(--gray-800);
            transform: translateY(-2px);
        }

        .nav-tab.active {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        /* AI Insights Panel */
        .ai-insights {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(139, 92, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .ai-insights::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--bee-purple), var(--primary));
        }

        .ai-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .ai-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--bee-purple), var(--primary));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(139, 92, 246, 0.5); }
            to { box-shadow: 0 0 30px rgba(139, 92, 246, 0.8); }
        }

        .ai-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .ai-subtitle {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .ai-insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .ai-insight-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .ai-insight-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .insight-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin-bottom: 8px;
        }

        .insight-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .insight-trend {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
            display: inline-block;
        }

        .trend-up {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success);
        }

        .trend-down {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            color: white;
            box-shadow: var(--shadow-lg);
            position: relative;
        }

        .stat-icon.earnings { background: linear-gradient(135deg, var(--success), #059669); }
        .stat-icon.tasks { background: linear-gradient(135deg, var(--primary), var(--primary-dark)); }
        .stat-icon.rating { background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange)); }
        .stat-icon.distance { background: linear-gradient(135deg, var(--info), #1d4ed8); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--gray-800);
            margin-bottom: 8px;
            background: linear-gradient(135deg, var(--gray-800), var(--gray-600));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .stat-progress {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .stat-progress-fill {
            height: 100%;
            background: linear-gradient(135deg, var(--bee-yellow), var(--bee-orange));
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 4px;
        }

        .stat-change {
            font-size: 0.875rem;
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .stat-change.positive {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success);
        }

        .stat-change.negative {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }

        /* Map Card */
        .map-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .map-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, var(--info), var(--primary));
        }

        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .map-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .map-controls {
            display: flex;
            gap: 10px;
        }

        .map-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .map-btn-primary {
            background: var(--primary);
            color: white;
        }

        .map-btn-outline {
            background: transparent;
            color: var(--gray-600);
            border: 2px solid var(--gray-300);
        }

        .map-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        #map {
            height: 400px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Ultra Modern Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-bee"></i>
                    <span>BeeRunner</span>
                </div>
                <div class="bee-profile">
                    <div class="bee-info">
                        <div class="bee-name">Thabo Mthembu</div>
                        <div class="bee-details">Motorcycle • 4.8★ • 247 Tasks</div>
                        <div class="bee-status">🟢 Available</div>
                    </div>
                    <div class="bee-avatar">TM</div>
                </div>
            </div>
        </header>

        <!-- AI Insights Panel -->
        <section class="ai-insights">
            <div class="ai-header">
                <div class="ai-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <div>
                    <div class="ai-title">AI Performance Insights</div>
                    <div class="ai-subtitle">Real-time analytics powered by machine learning</div>
                </div>
            </div>
            <div class="ai-insights-grid">
                <div class="ai-insight-card">
                    <div class="insight-label">Optimal Earning Window</div>
                    <div class="insight-value">14:00 - 18:00</div>
                    <div class="insight-trend trend-up">↗ +23% demand</div>
                </div>
                <div class="ai-insight-card">
                    <div class="insight-label">Recommended Zone</div>
                    <div class="insight-value">Sandton CBD</div>
                    <div class="insight-trend trend-up">↗ High value tasks</div>
                </div>
                <div class="ai-insight-card">
                    <div class="insight-label">Efficiency Score</div>
                    <div class="insight-value">94%</div>
                    <div class="insight-trend trend-up">↗ +5% this week</div>
                </div>
                <div class="ai-insight-card">
                    <div class="insight-label">Next Task ETA</div>
                    <div class="insight-value">8 mins</div>
                    <div class="insight-trend trend-up">↗ High probability</div>
                </div>
            </div>
        </section>

        <!-- Ultra Modern Navigation -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showSection('overview')">
                <i class="fas fa-chart-pie"></i>
                Overview
            </button>
            <button class="nav-tab" onclick="showSection('tasks')">
                <i class="fas fa-tasks"></i>
                Available Tasks
            </button>
            <button class="nav-tab" onclick="showSection('active')">
                <i class="fas fa-route"></i>
                Active Routes
            </button>
            <button class="nav-tab" onclick="showSection('earnings')">
                <i class="fas fa-coins"></i>
                Earnings
            </button>
            <button class="nav-tab" onclick="showSection('performance')">
                <i class="fas fa-chart-line"></i>
                Performance
            </button>
            <button class="nav-tab" onclick="showSection('profile')">
                <i class="fas fa-user-circle"></i>
                Profile
            </button>
        </nav>

        <!-- Content Sections -->
        <main>
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-value">R2,847</div>
                                <div class="stat-label">Today's Earnings</div>
                                <div class="stat-progress">
                                    <div class="stat-progress-fill" style="width: 78%"></div>
                                </div>
                            </div>
                            <div class="stat-icon earnings">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15% from yesterday
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-value">12</div>
                                <div class="stat-label">Tasks Completed</div>
                                <div class="stat-progress">
                                    <div class="stat-progress-fill" style="width: 85%"></div>
                                </div>
                            </div>
                            <div class="stat-icon tasks">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +3 from average
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-value">4.8</div>
                                <div class="stat-label">Average Rating</div>
                                <div class="stat-progress">
                                    <div class="stat-progress-fill" style="width: 96%"></div>
                                </div>
                            </div>
                            <div class="stat-icon rating">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +0.2 this month
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-value">127km</div>
                                <div class="stat-label">Distance Traveled</div>
                                <div class="stat-progress">
                                    <div class="stat-progress-fill" style="width: 63%"></div>
                                </div>
                            </div>
                            <div class="stat-icon distance">
                                <i class="fas fa-route"></i>
                            </div>
                        </div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            -8% fuel efficient
                        </div>
                    </div>
                </div>

                <!-- Map Card -->
                <div class="map-card">
                    <div class="map-header">
                        <div class="map-title">🗺️ Live Location & Available Tasks</div>
                        <div class="map-controls">
                            <button class="map-btn map-btn-primary" onclick="centerMap()">
                                <i class="fas fa-crosshairs"></i>
                                Center
                            </button>
                            <button class="map-btn map-btn-outline" onclick="toggleTraffic()">
                                <i class="fas fa-traffic-light"></i>
                                Traffic
                            </button>
                            <button class="map-btn map-btn-outline" onclick="findNearbyTasks()">
                                <i class="fas fa-search"></i>
                                Find Tasks
                            </button>
                        </div>
                    </div>
                    <div id="map"></div>
                </div>
            </section>

            <!-- Other sections will be added here -->
            <section id="tasks" class="content-section">
                <div style="text-align: center; padding: 60px; color: var(--gray-500);">
                    <i class="fas fa-tasks" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h3>Available Tasks</h3>
                    <p>Task listings will be displayed here</p>
                </div>
            </section>

            <section id="active" class="content-section">
                <div style="text-align: center; padding: 60px; color: var(--gray-500);">
                    <i class="fas fa-route" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h3>Active Routes</h3>
                    <p>Current route tracking will be displayed here</p>
                </div>
            </section>

            <section id="earnings" class="content-section">
                <div style="text-align: center; padding: 60px; color: var(--gray-500);">
                    <i class="fas fa-coins" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h3>Earnings Dashboard</h3>
                    <p>Detailed earnings analytics will be displayed here</p>
                </div>
            </section>

            <section id="performance" class="content-section">
                <div style="text-align: center; padding: 60px; color: var(--gray-500);">
                    <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h3>Performance Analytics</h3>
                    <p>Performance metrics and insights will be displayed here</p>
                </div>
            </section>

            <section id="profile" class="content-section">
                <div style="text-align: center; padding: 60px; color: var(--gray-500);">
                    <i class="fas fa-user-circle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h3>Bee Profile</h3>
                    <p>Profile management will be displayed here</p>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="app.js"></script>
</body>
</html>