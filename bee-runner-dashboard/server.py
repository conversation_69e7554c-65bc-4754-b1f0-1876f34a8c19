#!/usr/bin/env python3
"""
BeeRunner Dashboard Server
Ultra-modern Bee task runner dashboard with AI insights
"""

import json
import os
from datetime import datetime, timedelta
from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Supabase configuration (would be loaded from environment)
SUPABASE_URL = os.getenv('SUPABASE_URL', 'https://uvksgkpxeyyssvdsxbts.supabase.co')
SUPABASE_KEY = os.getenv('SUPABASE_KEY', 'your-supabase-key')

# Serve static files
@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    return send_from_directory('.', filename)

# Bee Profile API
@app.route('/api/bee/profile')
def get_bee_profile():
    """Get current bee profile information"""
    profile = {
        "id": "bee_001",
        "user_id": "user_123",
        "full_name": "Thabo Mthembu",
        "phone": "+27 82 123 4567",
        "transport_mode": "motorcycle",
        "max_range_km": 50,
        "rating": 4.8,
        "total_earnings": 28470,
        "performance_score": 94,
        "is_active": True,
        "is_verified": True,
        "availability_status": "available",
        "current_location": {
            "latitude": -26.2041,
            "longitude": 28.0473,
            "address": "Johannesburg CBD, Gauteng"
        },
        "skills": "Document delivery, Site inspection, Equipment transport",
        "preferred_categories": "document_delivery,site_inspection",
        "risk_score": 15,
        "last_heartbeat": datetime.now().isoformat(),
        "battery_level": 85,
        "network_type": "4G",
        "app_version": "2.1.0"
    }
    return jsonify(profile)

@app.route('/api/bee/availability', methods=['POST'])
def update_availability():
    """Update bee availability status"""
    data = request.get_json()
    
    # In production, this would update the bee_availability table
    response = {
        "success": True,
        "message": "Availability updated successfully",
        "is_available": data.get('is_available', True),
        "can_travel_now": data.get('can_travel_now', True),
        "updated_at": datetime.now().isoformat()
    }
    return jsonify(response)

# Tasks API
@app.route('/api/tasks/available')
def get_available_tasks():
    """Get available tasks for the bee"""
    tasks = [
        {
            "id": "task_001",
            "title": "Document Delivery - Sandton",
            "description": "Urgent tender document delivery to client office",
            "category": "document_delivery",
            "task_type": "delivery",
            "pickup_address": "Rosebank, Johannesburg",
            "delivery_address": "Sandton City, Sandton",
            "pickup_latitude": -26.1448,
            "pickup_longitude": 28.0436,
            "delivery_latitude": -26.1076,
            "delivery_longitude": 28.0567,
            "payment_amount": 250,
            "estimated_distance_km": 8.5,
            "estimated_duration_minutes": 25,
            "priority": "high",
            "deadline": "2024-02-15T16:00:00Z",
            "is_urgent": True,
            "weight_kg": 0.5,
            "special_instructions": "Handle with care - confidential documents",
            "proof_requirements": "Photo of delivery confirmation",
            "ai_recommended": True,
            "recommendation_score": 0.92,
            "route_optimized": True,
            "tender_priority_score": 9,
            "created_at": "2024-02-15T10:30:00Z"
        },
        {
            "id": "task_002",
            "title": "Site Inspection Support",
            "description": "Assist with tender site inspection and photo documentation",
            "category": "site_inspection",
            "task_type": "inspection",
            "pickup_address": "Midrand, Johannesburg",
            "delivery_address": "Centurion, Pretoria",
            "pickup_latitude": -25.9953,
            "pickup_longitude": 28.1294,
            "delivery_latitude": -25.8601,
            "delivery_longitude": 28.1881,
            "payment_amount": 450,
            "estimated_distance_km": 15.2,
            "estimated_duration_minutes": 45,
            "priority": "medium",
            "deadline": "2024-02-15T18:00:00Z",
            "is_urgent": False,
            "weight_kg": 2.0,
            "special_instructions": "Bring measuring tape and camera",
            "proof_requirements": "Site photos and measurements",
            "ai_recommended": True,
            "recommendation_score": 0.87,
            "route_optimized": False,
            "tender_priority_score": 7,
            "created_at": "2024-02-15T09:15:00Z"
        },
        {
            "id": "task_003",
            "title": "Equipment Transport",
            "description": "Transport surveying equipment to construction site",
            "category": "equipment_transport",
            "task_type": "transport",
            "pickup_address": "Kempton Park, Johannesburg",
            "delivery_address": "Boksburg, Ekurhuleni",
            "pickup_latitude": -26.1017,
            "pickup_longitude": 28.2305,
            "delivery_latitude": -26.2085,
            "delivery_longitude": 28.2621,
            "payment_amount": 320,
            "estimated_distance_km": 12.8,
            "estimated_duration_minutes": 35,
            "priority": "medium",
            "deadline": "2024-02-15T17:30:00Z",
            "is_urgent": False,
            "weight_kg": 15.0,
            "special_instructions": "Fragile equipment - secure properly",
            "proof_requirements": "Delivery signature and equipment photos",
            "ai_recommended": False,
            "recommendation_score": 0.73,
            "route_optimized": True,
            "tender_priority_score": 6,
            "created_at": "2024-02-15T08:45:00Z"
        }
    ]
    return jsonify(tasks)

@app.route('/api/tasks/<task_id>/accept', methods=['POST'])
def accept_task(task_id):
    """Accept a task assignment"""
    # In production, this would update the bee_tasks table
    response = {
        "success": True,
        "message": f"Task {task_id} accepted successfully",
        "task_id": task_id,
        "assigned_at": datetime.now().isoformat(),
        "status": "assigned"
    }
    return jsonify(response)

# Earnings API
@app.route('/api/earnings')
def get_earnings():
    """Get bee earnings data"""
    earnings = {
        "today": {
            "amount": 2847,
            "tasks_completed": 12,
            "hours_worked": 8.5,
            "average_per_task": 237
        },
        "yesterday": {
            "amount": 2475,
            "tasks_completed": 11,
            "hours_worked": 9.0,
            "average_per_task": 225
        },
        "this_week": {
            "amount": 18650,
            "tasks_completed": 67,
            "hours_worked": 42.5,
            "average_per_task": 278
        },
        "this_month": {
            "amount": 67890,
            "tasks_completed": 247,
            "hours_worked": 165.0,
            "average_per_task": 275
        },
        "total_lifetime": {
            "amount": 284700,
            "tasks_completed": 1247,
            "hours_worked": 850.0,
            "average_per_task": 228
        },
        "best_day": {
            "amount": 3250,
            "date": "2024-02-10",
            "tasks_completed": 15
        },
        "trends": {
            "daily_growth": 15.0,
            "weekly_growth": 8.5,
            "monthly_growth": 12.3
        }
    }
    return jsonify(earnings)

# Performance API
@app.route('/api/performance')
def get_performance():
    """Get bee performance metrics"""
    performance = {
        "overall_rating": 4.8,
        "total_ratings": 247,
        "performance_score": 94,
        "metrics": {
            "on_time_delivery": 96,
            "customer_satisfaction": 4.8,
            "communication_rating": 4.9,
            "professionalism_rating": 4.7,
            "quality_rating": 4.8,
            "punctuality_rating": 4.6
        },
        "efficiency": {
            "fuel_efficiency": 87,
            "route_optimization": 92,
            "time_management": 89,
            "task_completion_rate": 98
        },
        "safety": {
            "safety_score": 98,
            "incidents": 0,
            "safety_training_completed": True,
            "last_safety_update": "2024-01-15"
        },
        "recent_feedback": [
            {
                "rating": 5,
                "comment": "Excellent service, very professional",
                "date": "2024-02-14"
            },
            {
                "rating": 5,
                "comment": "Fast delivery, great communication",
                "date": "2024-02-13"
            },
            {
                "rating": 4,
                "comment": "Good service, on time delivery",
                "date": "2024-02-12"
            }
        ]
    }
    return jsonify(performance)

# AI Insights API
@app.route('/api/ai/insights')
def get_ai_insights():
    """Get AI-powered insights for the bee"""
    insights = {
        "optimal_earning_window": {
            "start_time": "14:00",
            "end_time": "18:00",
            "expected_demand_increase": 23,
            "confidence": 0.89
        },
        "recommended_zone": {
            "name": "Sandton CBD",
            "reason": "High value tasks available",
            "expected_earnings": 450,
            "confidence": 0.92
        },
        "efficiency_score": {
            "current": 94,
            "trend": "increasing",
            "improvement_this_week": 5,
            "suggestions": [
                "Optimize route planning",
                "Accept more AI-recommended tasks"
            ]
        },
        "next_task_prediction": {
            "eta_minutes": 8,
            "probability": 0.85,
            "expected_value": 280,
            "location": "Rosebank area"
        },
        "performance_insights": {
            "strengths": ["Punctuality", "Communication", "Route efficiency"],
            "improvement_areas": ["Fuel efficiency", "Peak hour optimization"],
            "recommendations": [
                "Consider electric vehicle for better efficiency",
                "Focus on high-value tasks during peak hours"
            ]
        },
        "market_trends": {
            "demand_forecast": "increasing",
            "peak_hours": ["07:00-09:00", "14:00-18:00"],
            "high_demand_areas": ["Sandton", "Rosebank", "Midrand"],
            "seasonal_factors": "End of month tender deadlines"
        }
    }
    return jsonify(insights)

# Location API
@app.route('/api/location/update', methods=['POST'])
def update_location():
    """Update bee location"""
    data = request.get_json()
    
    # In production, this would update bee_locations and bee_heartbeats tables
    response = {
        "success": True,
        "message": "Location updated successfully",
        "latitude": data.get('latitude'),
        "longitude": data.get('longitude'),
        "accuracy": data.get('accuracy', 10),
        "timestamp": datetime.now().isoformat()
    }
    return jsonify(response)

@app.route('/api/location/heartbeat', methods=['POST'])
def send_heartbeat():
    """Send heartbeat with location and status"""
    data = request.get_json()
    
    # In production, this would insert into bee_heartbeats table
    response = {
        "success": True,
        "message": "Heartbeat received",
        "next_heartbeat_interval": 30,
        "server_time": datetime.now().isoformat()
    }
    return jsonify(response)

# Routes API
@app.route('/api/routes/active')
def get_active_routes():
    """Get active routes for the bee"""
    routes = [
        {
            "id": "route_001",
            "task_id": "task_001",
            "status": "in_progress",
            "start_latitude": -26.2041,
            "start_longitude": 28.0473,
            "end_latitude": -26.1076,
            "end_longitude": 28.0567,
            "waypoints": [
                {"lat": -26.1448, "lng": 28.0436, "type": "pickup"},
                {"lat": -26.1076, "lng": 28.0567, "type": "delivery"}
            ],
            "estimated_distance_km": 8.5,
            "estimated_duration_min": 25,
            "progress_percentage": 35,
            "last_safety_ping": datetime.now().isoformat(),
            "created_at": "2024-02-15T11:30:00Z"
        }
    ]
    return jsonify(routes)

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "bee-runner-dashboard",
        "timestamp": datetime.now().isoformat(),
        "version": "2.1.0"
    })

if __name__ == '__main__':
    port = 3025  # Fixed port to avoid conflicts
    print(f"🐝 Starting BeeRunner Dashboard on port {port}")
    print(f"🌐 Frontend: http://localhost:{port}")
    print(f"📊 API: http://localhost:{port}/api/bee/profile")
    print(f"🤖 AI Insights: http://localhost:{port}/api/ai/insights")
    
    app.run(host='0.0.0.0', port=port, debug=True)
