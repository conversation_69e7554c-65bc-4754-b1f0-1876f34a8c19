// BeeRunner Dashboard - Ultra Modern JavaScript with AI Insights

// Global variables
let map;
let currentLocation = { lat: -26.2041, lng: 28.0473 }; // Johannesburg
let beeMarker;
let taskMarkers = [];
let currentSection = 'overview';
let isOnline = true;

// Mock data for Bee Runner
const mockBeeProfile = {
    id: 'bee_001',
    name: '<PERSON><PERSON><PERSON> Mthembu',
    phone: '+27 82 123 4567',
    transport_mode: 'motorcycle',
    max_range_km: 50,
    rating: 4.8,
    total_earnings: 28470,
    performance_score: 94,
    is_available: true,
    current_location: { lat: -26.2041, lng: 28.0473 },
    battery_level: 85,
    network_type: '4G'
};

const mockTasks = [
    {
        id: 'task_001',
        title: 'Document Delivery - Sandton',
        description: 'Urgent tender document delivery to client office',
        category: 'document_delivery',
        pickup_address: 'Rosebank, Johannesburg',
        delivery_address: 'Sandton City, Sandton',
        pickup_latitude: -26.1448,
        pickup_longitude: 28.0436,
        delivery_latitude: -26.1076,
        delivery_longitude: 28.0567,
        payment_amount: 250,
        estimated_distance_km: 8.5,
        estimated_duration_minutes: 25,
        priority: 'high',
        deadline: '2024-02-15T16:00:00Z',
        is_urgent: true,
        ai_recommended: true,
        recommendation_score: 0.92
    },
    {
        id: 'task_002',
        title: 'Site Inspection Support',
        description: 'Assist with tender site inspection and photo documentation',
        category: 'site_inspection',
        pickup_address: 'Midrand, Johannesburg',
        delivery_address: 'Centurion, Pretoria',
        pickup_latitude: -25.9953,
        pickup_longitude: 28.1294,
        delivery_latitude: -25.8601,
        delivery_longitude: 28.1881,
        payment_amount: 450,
        estimated_distance_km: 15.2,
        estimated_duration_minutes: 45,
        priority: 'medium',
        deadline: '2024-02-15T18:00:00Z',
        is_urgent: false,
        ai_recommended: true,
        recommendation_score: 0.87
    },
    {
        id: 'task_003',
        title: 'Equipment Transport',
        description: 'Transport surveying equipment to construction site',
        category: 'equipment_transport',
        pickup_address: 'Kempton Park, Johannesburg',
        delivery_address: 'Boksburg, Ekurhuleni',
        pickup_latitude: -26.1017,
        pickup_longitude: 28.2305,
        delivery_latitude: -26.2085,
        delivery_longitude: 28.2621,
        payment_amount: 320,
        estimated_distance_km: 12.8,
        estimated_duration_minutes: 35,
        priority: 'medium',
        deadline: '2024-02-15T17:30:00Z',
        is_urgent: false,
        ai_recommended: false,
        recommendation_score: 0.73
    }
];

const mockEarnings = {
    today: 2847,
    yesterday: 2475,
    thisWeek: 18650,
    thisMonth: 67890,
    totalTasks: 247,
    averagePerTask: 275,
    bestDay: 3250,
    efficiency: 94
};

const mockPerformance = {
    onTimeDelivery: 96,
    customerSatisfaction: 4.8,
    fuelEfficiency: 87,
    safetyScore: 98,
    communicationRating: 4.9,
    professionalismRating: 4.7
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🐝 BeeRunner Dashboard initialized');
    initializeMap();
    updateBeeLocation();
    loadAvailableTasks();
    startRealTimeUpdates();
    updateAIInsights();
});

// Initialize Leaflet map
function initializeMap() {
    map = L.map('map').setView([currentLocation.lat, currentLocation.lng], 12);
    
    // Add tile layer with modern style
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19
    }).addTo(map);
    
    // Add bee marker (current location)
    const beeIcon = L.divIcon({
        className: 'bee-marker',
        html: '<div style="background: linear-gradient(135deg, #fbbf24, #f97316); width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; box-shadow: 0 4px 12px rgba(0,0,0,0.3); border: 3px solid white;"><i class="fas fa-bee"></i></div>',
        iconSize: [36, 36],
        iconAnchor: [18, 18]
    });
    
    beeMarker = L.marker([currentLocation.lat, currentLocation.lng], { icon: beeIcon })
        .addTo(map)
        .bindPopup(`
            <div style="text-align: center; padding: 10px;">
                <strong>🐝 ${mockBeeProfile.name}</strong><br>
                <span style="color: #10b981;">● Available</span><br>
                <small>Rating: ${mockBeeProfile.rating}★</small>
            </div>
        `);
    
    // Add task markers
    loadTaskMarkers();
}

// Load task markers on map
function loadTaskMarkers() {
    // Clear existing task markers
    taskMarkers.forEach(marker => map.removeLayer(marker));
    taskMarkers = [];
    
    mockTasks.forEach(task => {
        const taskIcon = L.divIcon({
            className: 'task-marker',
            html: `<div style="background: ${task.ai_recommended ? 'linear-gradient(135deg, #8b5cf6, #6366f1)' : 'linear-gradient(135deg, #3b82f6, #1d4ed8)'}; width: 25px; height: 25px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.3); border: 2px solid white;">${task.ai_recommended ? '🤖' : '📋'}</div>`,
            iconSize: [29, 29],
            iconAnchor: [14, 14]
        });
        
        const marker = L.marker([task.pickup_latitude, task.pickup_longitude], { icon: taskIcon })
            .addTo(map)
            .bindPopup(`
                <div style="min-width: 200px; padding: 10px;">
                    <strong>${task.title}</strong><br>
                    <p style="margin: 8px 0; font-size: 0.9em; color: #666;">${task.description}</p>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                        <span style="font-weight: bold; color: #10b981;">R${task.payment_amount}</span>
                        <span style="font-size: 0.8em; color: #666;">${task.estimated_distance_km}km</span>
                    </div>
                    ${task.ai_recommended ? '<div style="background: linear-gradient(135deg, #8b5cf6, #6366f1); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7em; margin-top: 8px; text-align: center;">🤖 AI Recommended</div>' : ''}
                    <button onclick="acceptTask('${task.id}')" style="width: 100%; background: #10b981; color: white; border: none; padding: 8px; border-radius: 8px; margin-top: 10px; cursor: pointer; font-weight: bold;">Accept Task</button>
                </div>
            `);
        
        taskMarkers.push(marker);
    });
}

// Navigation functionality
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from all nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected section
    const section = document.getElementById(sectionName);
    if (section) {
        section.classList.add('active');
    }
    
    // Add active class to clicked tab
    event.target.classList.add('active');
    
    currentSection = sectionName;
    
    // Load section-specific data
    switch(sectionName) {
        case 'tasks':
            loadAvailableTasks();
            break;
        case 'earnings':
            loadEarningsData();
            break;
        case 'performance':
            loadPerformanceData();
            break;
    }
}

// Map control functions
function centerMap() {
    map.setView([currentLocation.lat, currentLocation.lng], 14);
    showNotification('Map centered on your location', 'info');
}

function toggleTraffic() {
    showNotification('Traffic layer toggled', 'info');
    // In a real implementation, this would toggle traffic overlay
}

function findNearbyTasks() {
    showNotification('Searching for nearby tasks...', 'info');
    // Highlight nearby tasks with animation
    taskMarkers.forEach(marker => {
        marker.openPopup();
        setTimeout(() => marker.closePopup(), 2000);
    });
}

// Task management
function acceptTask(taskId) {
    const task = mockTasks.find(t => t.id === taskId);
    if (task) {
        showNotification(`Task accepted: ${task.title}`, 'success');
        // In real implementation, this would update the task status
        // and start navigation
    }
}

function loadAvailableTasks() {
    // This would load tasks from the API
    console.log('Loading available tasks...');
}

function loadEarningsData() {
    // This would load earnings data from the API
    console.log('Loading earnings data...');
}

function loadPerformanceData() {
    // This would load performance data from the API
    console.log('Loading performance data...');
}

// Real-time updates
function startRealTimeUpdates() {
    // Update location every 30 seconds
    setInterval(updateBeeLocation, 30000);
    
    // Update AI insights every 2 minutes
    setInterval(updateAIInsights, 120000);
    
    // Update stats every minute
    setInterval(updateStats, 60000);
}

function updateBeeLocation() {
    // Simulate slight location changes
    const variation = 0.001;
    currentLocation.lat += (Math.random() - 0.5) * variation;
    currentLocation.lng += (Math.random() - 0.5) * variation;
    
    if (beeMarker) {
        beeMarker.setLatLng([currentLocation.lat, currentLocation.lng]);
    }
}

function updateAIInsights() {
    // Simulate AI insights updates
    const insights = [
        { label: 'Optimal Earning Window', value: '14:00 - 18:00', trend: 'up' },
        { label: 'Recommended Zone', value: 'Sandton CBD', trend: 'up' },
        { label: 'Efficiency Score', value: '94%', trend: 'up' },
        { label: 'Next Task ETA', value: '8 mins', trend: 'up' }
    ];
    
    console.log('AI insights updated:', insights);
}

function updateStats() {
    // Simulate real-time stats updates
    const progressBars = document.querySelectorAll('.stat-progress-fill');
    progressBars.forEach(bar => {
        const currentWidth = parseFloat(bar.style.width);
        const variation = (Math.random() - 0.5) * 5; // ±2.5%
        const newWidth = Math.max(0, Math.min(100, currentWidth + variation));
        bar.style.width = `${newWidth}%`;
    });
}

// Utility functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
        color: white;
        padding: 15px 25px;
        border-radius: 16px;
        box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        max-width: 350px;
        font-weight: 600;
        backdrop-filter: blur(20px);
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// Add CSS for content sections
const style = document.createElement('style');
style.textContent = `
    .content-section {
        display: none;
    }
    
    .content-section.active {
        display: block;
    }
    
    .bee-marker, .task-marker {
        border: none !important;
        background: transparent !important;
    }
`;
document.head.appendChild(style);
