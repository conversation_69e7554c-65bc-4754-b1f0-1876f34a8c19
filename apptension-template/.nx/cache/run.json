{"run": {"command": "nx run webapp:graphql:generate-types:watch", "startTime": "2025-06-28T17:07:51.123Z", "endTime": "2025-06-28T17:50:48.731Z", "inner": false}, "tasks": [{"taskId": "webapp:graphql:generate-types:watch", "target": "graphql", "projectName": "webapp", "hash": "7350989892421705038", "startTime": "2025-06-28T17:07:51.128Z", "endTime": "2025-06-28T17:50:48.730Z", "params": "", "cacheStatus": "cache-miss", "status": 1}]}