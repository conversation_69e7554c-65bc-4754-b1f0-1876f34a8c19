{"version": "6.0", "nxVersion": "19.0.1", "deps": {"@aws-sdk/client-cloudformation": "^3.577.0", "@aws-sdk/client-codebuild": "^3.579.0", "@aws-sdk/client-ecr": "^3.577.0", "@aws-sdk/client-ecs": "^3.577.0", "@aws-sdk/client-iam": "^3.577.0", "@aws-sdk/client-lambda": "^3.577.0", "@aws-sdk/client-s3": "^3.577.0", "@aws-sdk/client-ses": "^3.577.0", "@aws-sdk/client-sfn": "^3.577.0", "@aws-sdk/client-sts": "^3.577.0", "@iconify-icons/ion": "^1.2.10", "@iconify/react": "^4.1.1", "@sentry/react": "^8.2.1", "@supercharge/strings": "^2.0.0", "ramda": "^0.28.0", "react": "18.3.1", "react-dom": "18.3.1", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.48.2", "react-intl": "^6.5.5", "react-loading-skeleton": "^3.3.1", "react-markdown": "^8.0.7", "react-router": "6.29.0", "react-router-dom": "6.29.0", "regenerator-runtime": "^0.14.1", "styled-components": "6.1.11", "@apollo/client": "^3.9.6", "@apollo/rover": "^0.19.1", "@babel/preset-react": "^7.24.1", "@eslint/compat": "^1.0.1", "@eslint/eslintrc": "^2.1.1", "@graphql-codegen/cli": "^5.0.0", "@graphql-typed-document-node/core": "^3.2.0", "@nx/devkit": "19.0.1", "@nx/eslint": "19.0.1", "@nx/eslint-plugin": "19.0.1", "@nx/jest": "19.0.1", "@nx/js": "19.0.1", "@nx/node": "19.0.1", "@nx/plugin": "19.0.1", "@nx/react": "19.0.1", "@nx/web": "19.0.1", "@nx/webpack": "19.0.1", "@sb/cli": "workspace:*", "@sb/core": "workspace:*", "@storybook/addon-actions": "^8.0.9", "@storybook/react": "^8.0.9", "@svgr/webpack": "^8.1.0", "@tailwindcss/typography": "^0.5.13", "@testing-library/dom": "10.1.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "15.0.7", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/gtag.js": "^0.0.19", "@types/jest": "^29.5.12", "@types/node": "20.12.12", "@types/ramda": "^0.28.25", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-router": "^5.1.20", "@types/react-router-dom": "5.3.3", "@types/react-test-renderer": "^18.3.0", "@typescript-eslint/eslint-plugin": "7.10.0", "@typescript-eslint/parser": "7.10.0", "@typescript-eslint/scope-manager": "7.10.0", "@vitejs/plugin-react": "^4.3.4", "aws-cdk": "^2.177.0", "aws-cdk-lib": "^2.177.0", "babel-jest": "29.7.0", "constructs": "^10.3.0", "esbuild": "0.21.3", "eslint": "9.3.0", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-formatjs": "^4.13.3", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-testing-library": "^6.2.2", "graphql": "^16.8.1", "husky": "^9.0.11", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-matcher-utils": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^15.2.2", "nx": "19.0.1", "nx-cloud": "18.0.1", "plop": "^4.0.1", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "ts-jest": "29.1.3", "ts-node": "10.9.2", "tsconfig-paths": "^4.2.0", "tslib": "^2.6.2", "typescript": "5.4.5", "vite": "^5.4.14", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^4.3.2"}, "pathMappings": {"@sb/cli": ["packages/internal/cli"], "@sb/infra-core": ["packages/infra/infra-core/src/index.ts"], "@sb/infra-shared": ["packages/infra/infra-shared/src/index.ts"], "@sb/tools": ["packages/internal/tools/src/index.ts"], "@sb/webapp-api-client": ["packages/webapp-libs/webapp-api-client/src/index.ts"], "@sb/webapp-api-client/*": ["packages/webapp-libs/webapp-api-client/src/*"], "@sb/webapp-contentful": ["packages/webapp-libs/webapp-contentful/src/index.ts"], "@sb/webapp-contentful/*": ["packages/webapp-libs/webapp-contentful/src/*"], "@sb/webapp-core": ["packages/webapp-libs/webapp-core/src/index.ts"], "@sb/webapp-core/*": ["packages/webapp-libs/webapp-core/src/*"], "@sb/webapp-crud-demo": ["packages/webapp-libs/webapp-crud-demo/src/index.ts"], "@sb/webapp-crud-demo/*": ["packages/webapp-libs/webapp-crud-demo/src/*"], "@sb/webapp-documents": ["packages/webapp-libs/webapp-documents/src/routes/index.ts"], "@sb/webapp-documents/*": ["packages/webapp-libs/webapp-documents/src/*"], "@sb/webapp-emails": ["packages/webapp-libs/webapp-emails/src/index.ts"], "@sb/webapp-emails/*": ["packages/webapp-libs/webapp-emails/src/*"], "@sb/webapp-finances": ["packages/webapp-libs/webapp-finances/src/index.ts"], "@sb/webapp-finances/*": ["packages/webapp-libs/webapp-finances/src/*"], "@sb/webapp-generative-ai": ["packages/webapp-libs/webapp-generative-ai/src/index.ts"], "@sb/webapp-generative-ai/*": ["packages/webapp-libs/webapp-generative-ai/src/*"], "@sb/webapp-notifications": ["packages/webapp-libs/webapp-notifications/src/index.ts"], "@sb/webapp-notifications/*": ["packages/webapp-libs/webapp-notifications/src/*"], "@sb/webapp-tenants": ["packages/webapp-libs/webapp-tenants/src/index.ts"], "@sb/webapp-tenants/*": ["packages/webapp-libs/webapp-tenants/src/*"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": ".github/workflows/workers.yml", "hash": "9716842596790884499"}, {"file": ".github/workflows/infra.yml", "hash": "7580410537893057947"}, {"file": "bitbucket-pipelines.yml", "hash": "3372635193453120544"}, {"file": "docker-compose.local.yml", "hash": "12882853524295927081"}, {"file": "package.json", "hash": "17187114397828355782"}, {"file": ".github/workflows/status-dashboard.yml", "hash": "18319080748661550703"}, {"file": "jest.preset.js", "hash": "9084884494335109525"}, {"file": ".graphqlconfig", "hash": "10330933711386175094"}, {"file": ".github/ISSUE_TEMPLATE/bug_report.yml", "hash": "10195051694741567548"}, {"file": ".github/images/features/cms.png", "hash": "7003158396802797082"}, {"file": "LICENSE", "hash": "10759455907477567288"}, {"file": ".lintstagedrc", "hash": "275437508165664555"}, {"file": ".husky/pre-commit", "hash": "15884505069578013481"}, {"file": ".giti<PERSON>re", "hash": "13328050810975745861"}, {"file": "docker-compose.yml", "hash": "10811011035268927550"}, {"file": ".github/ISSUE_TEMPLATE/feature_request.yml", "hash": "5362044892491849040"}, {"file": ".github/pull_request_template.md", "hash": "12382669776816351243"}, {"file": ".github/workflows/webapp.yml", "hash": "6201064662899900258"}, {"file": "eslint.config.js", "hash": "9350880639636807209"}, {"file": "SECURITY.md", "hash": "5704965359532657457"}, {"file": "patches/<EMAIL>", "hash": "4704678247169477099"}, {"file": ".github/images/features/payments.png", "hash": "1274383807663315501"}, {"file": ".github/workflows/actions/setup/action.yml", "hash": "13253370325735305346"}, {"file": ".github/images/features/emails.png", "hash": "12640195780727448934"}, {"file": "tsconfig.base.json", "hash": "16156226223391277625"}, {"file": "tailwind.workspace-preset.ts", "hash": "11024878233470497544"}, {"file": ".github/workflows/deploy-prod.yml", "hash": "17751758559378082812"}, {"file": "img.png", "hash": "4150688594547846269"}, {"file": "test-simple.html", "hash": "16484191832930649474"}, {"file": ".prettier<PERSON>", "hash": "16267754514737964994"}, {"file": ".github/images/saas-bp-logo.png", "hash": "12912746602476646306"}, {"file": "CODE_OF_CONDUCT.md", "hash": "14371778494719274169"}, {"file": ".dockerignore", "hash": "8023243285235115467"}, {"file": ".github/workflows/actions/deploy/action.yml", "hash": "10455397006976802994"}, {"file": ".github/workflows/backend.yml", "hash": "14335934327281704648"}, {"file": "pnpm-lock.yaml", "hash": "4917775558705292483"}, {"file": "babel.config.json", "hash": "5546405625092116747"}, {"file": ".versionrc.js", "hash": "2617960276687658221"}, {"file": ".github/images/features/auth.png", "hash": "278299804156521027"}, {"file": "README.md", "hash": "11177730902058793904"}, {"file": "test-direct.html", "hash": "2050283615278995005"}, {"file": ".github/ISSUE_TEMPLATE/docs.yml", "hash": "17735307642043913517"}, {"file": ".github/ISSUE_TEMPLATE/config.yml", "hash": "12643274184107067000"}, {"file": ".github/workflows/tools.yml", "hash": "16131165575092452518"}, {"file": ".github/images/features/subscriptions.png", "hash": "9785965179178019955"}, {"file": "CONTRIBUTING.md", "hash": "7193564160193669463"}, {"file": "jest.config.ts", "hash": "17981274922028690832"}, {"file": ".github/workflows/docs.yml", "hash": "9862339686249913803"}, {"file": "nx.json", "hash": "2676973267434721067"}, {"file": "mock-api.js", "hash": "14367435437720647574"}, {"file": ".github/images/features/multitenancy.png", "hash": "12140740413996502070"}, {"file": "pnpm-workspace.yaml", "hash": "831139700542376288"}, {"file": ".github/workflows/deploy-qa.yml", "hash": "7053363587530573759"}, {"file": ".env.shared", "hash": "13390675942770300162"}, {"file": "CHANGELOG.md", "hash": "16411181500750353378"}, {"file": "docker-compose.ci.yml", "hash": "17185954166260445554"}, {"file": ".prettieri<PERSON>re", "hash": "9757919650126762165"}], "projectFileMap": {"webapp-api-client": [{"file": "packages/webapp-libs/webapp-api-client/.gitignore", "hash": "3075125998154361740"}, {"file": "packages/webapp-libs/webapp-api-client/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-api-client/eslint.config.js", "hash": "13014235967999683303", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-api-client/graphql/codegen.ts", "hash": "12120715811373182053", "deps": ["npm:fs", "npm:path", "npm:@graphql-codegen/cli", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-api-client/graphql/schema/api.graphql", "hash": "9881075811558766933"}, {"file": "packages/webapp-libs/webapp-api-client/graphql/schema/loader.js", "hash": "10598993857230449240", "deps": ["npm:fs", "npm:graphql"]}, {"file": "packages/webapp-libs/webapp-api-client/jest.config.ts", "hash": "18237004820983024670"}, {"file": "packages/webapp-libs/webapp-api-client/package.json", "hash": "13240576634726431637", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-api-client/project.json", "hash": "9269495639116195441"}, {"file": "packages/webapp-libs/webapp-api-client/scripts/download-graphql-schema.js", "hash": "5735081473644713986", "deps": ["npm:child_process", "npm:fs-extra", "npm:dotenv"]}, {"file": "packages/webapp-libs/webapp-api-client/scripts/merge-schemas.js", "hash": "18190351037578220425", "deps": ["npm:fs", "npm:@graphql-tools/merge", "npm:graphql"]}, {"file": "packages/webapp-libs/webapp-api-client/sonar-project.properties", "hash": "7502570292846334595"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/__tests__/helpers.spec.ts", "hash": "12123485234367837138", "deps": ["webapp-core", "npm:axios", "npm:http-status-codes"]}, {"file": "packages/webapp-libs/webapp-api-client/src/api/auth/auth.hooks.ts", "hash": "13469802264231831261", "deps": ["webapp-core", "npm:react"]}, {"file": "packages/webapp-libs/webapp-api-client/src/api/auth/auth.requests.ts", "hash": "14927764084600663182"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/auth/auth.types.ts", "hash": "12371399734001624086"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/auth/index.ts", "hash": "3068897526707846781"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/client.ts", "hash": "2373805896537945966", "deps": ["npm:axios", "npm:axios-case-converter"]}, {"file": "packages/webapp-libs/webapp-api-client/src/api/helpers.ts", "hash": "5385265654760208475", "deps": ["webapp-core", "npm:http-status-codes"]}, {"file": "packages/webapp-libs/webapp-api-client/src/api/index.ts", "hash": "17568655678449415531"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/interceptors.ts", "hash": "991542303831613977", "deps": ["npm:axios", "npm:http-status-codes"]}, {"file": "packages/webapp-libs/webapp-api-client/src/api/stripe/history/index.ts", "hash": "7686140500415130773"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/stripe/history/types.ts", "hash": "397578130642703123"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/stripe/index.ts", "hash": "15287045839910661873"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/stripe/paymentMethod/index.ts", "hash": "7686140500415130773"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/stripe/paymentMethod/types.ts", "hash": "4520611868333944025"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/subscription/index.ts", "hash": "15781927815553554814"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/subscription/types.ts", "hash": "11915181852555506515"}, {"file": "packages/webapp-libs/webapp-api-client/src/api/types.ts", "hash": "14046634336016716478", "deps": ["npm:axios"]}, {"file": "packages/webapp-libs/webapp-api-client/src/constants/index.ts", "hash": "5461851763665830533"}, {"file": "packages/webapp-libs/webapp-api-client/src/constants/tenant.types.ts", "hash": "9094392546371138061"}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/__generated/gql/fragment-masking.ts", "hash": "3782723417099434096", "deps": ["npm:@graphql-typed-document-node/core", "npm:graphql"]}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/__generated/gql/gql.ts", "hash": "1167863727311758243", "deps": ["npm:@graphql-typed-document-node/core"]}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/__generated/gql/graphql.ts", "hash": "16639280036802054071", "deps": ["npm:@graphql-typed-document-node/core"]}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/__generated/gql/index.ts", "hash": "15145554174544902988"}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/apolloClient.ts", "hash": "9331850792428582253", "deps": ["npm:@apollo/client", "npm:@apollo/client/errors", "npm:@apollo/client/link/core", "npm:@apollo/client/link/error", "npm:@apollo/client/link/retry", "npm:@apollo/client/utilities", "webapp-core", "npm:apollo-upload-client", "npm:graphql/language"]}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/common.graphql.ts", "hash": "14148514435249956246"}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/index.ts", "hash": "14119700806875983902"}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/types.ts", "hash": "16071631886557506002"}, {"file": "packages/webapp-libs/webapp-api-client/src/graphql/webSocketLink.ts", "hash": "5612418012415168119", "deps": ["npm:@apollo/client", "npm:@apollo/client/link/core", "npm:subscriptions-transport-ws"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/index.ts", "hash": "3128411336909191201"}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/useApiForm/__tests__/useApiForm.hook.spec.tsx", "hash": "13601667700756195290", "deps": ["npm:@testing-library/react-hooks", "npm:graphql/error/GraphQLError"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/useApiForm/index.ts", "hash": "1503099652889938457"}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/useApiForm/useApiForm.hook.ts", "hash": "10294893082510591329", "deps": ["webapp-core", "npm:graphql/error/GraphQLError", "npm:ramda", "npm:react", "npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/useApiForm/useApiForm.types.ts", "hash": "6314929971302442912", "deps": ["npm:graphql/error/GraphQLError", "npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/useApiForm/useTranslatedErrors/__tests__/useTranslatedErrors.hook.spec.tsx", "hash": "16322794682704116225"}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/useApiForm/useTranslatedErrors/index.ts", "hash": "9502312751235461121"}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/useApiForm/useTranslatedErrors/useTranslatedErrors.hook.ts", "hash": "4818568230177660370", "deps": ["npm:ramda", "npm:react", "npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/usePagedPaginatedQuery/__tests__/usePagedPaginatedQuery.graphql.ts", "hash": "5009052561821761893", "deps": ["npm:@apollo/client", "npm:@graphql-typed-document-node/core"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/usePagedPaginatedQuery/__tests__/usePagedPaginatedQuery.hook.spec.tsx", "hash": "2792275852003964091", "deps": ["npm:@apollo/client/testing", "npm:@testing-library/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/usePagedPaginatedQuery/index.ts", "hash": "7030729413522703597"}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/usePagedPaginatedQuery/usePagedPaginatedQuery.hook.ts", "hash": "11179487051739066894", "deps": ["npm:@apollo/client", "webapp-core", "npm:ramda", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/usePaginatedQuery/__tests__/usePaginatedQuery.graphql.ts", "hash": "6494986161529529427", "deps": ["npm:@apollo/client", "npm:@graphql-typed-document-node/core"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/usePaginatedQuery/__tests__/usePaginatedQuery.hook.spec.tsx", "hash": "3194460976509069588", "deps": ["npm:@apollo/client/testing", "npm:@testing-library/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/usePaginatedQuery/index.ts", "hash": "11339614739983319062"}, {"file": "packages/webapp-libs/webapp-api-client/src/hooks/usePaginatedQuery/usePaginatedQuery.hook.ts", "hash": "12160615235609152664", "deps": ["npm:@apollo/client", "npm:react"]}, {"file": "packages/webapp-libs/webapp-api-client/src/index.ts", "hash": "9872486071475351449"}, {"file": "packages/webapp-libs/webapp-api-client/src/providers/commonQuery/commonQuery.component.tsx", "hash": "6575285469706189492", "deps": ["npm:@apollo/client", "webapp-core", "npm:react"]}, {"file": "packages/webapp-libs/webapp-api-client/src/providers/commonQuery/commonQuery.context.ts", "hash": "13953399546468632940", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-api-client/src/providers/commonQuery/commonQuery.graphql.ts", "hash": "9063537015442706638"}, {"file": "packages/webapp-libs/webapp-api-client/src/providers/commonQuery/commonQuery.hook.ts", "hash": "13739369258394137502", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-api-client/src/providers/commonQuery/index.ts", "hash": "5058482632747249493"}, {"file": "packages/webapp-libs/webapp-api-client/src/providers/index.ts", "hash": "15997982428473855124"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/factories/auth.ts", "hash": "7325965564592593251"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/factories/commonQuery.ts", "hash": "16044888471510638572"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/factories/index.ts", "hash": "8453655183092372492"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/factories/pagination.ts", "hash": "2616827138351345541"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/factories/stripe.ts", "hash": "5101289536270416485"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/factories/subscription.ts", "hash": "5360269092057771949"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/mocks/server/handlers/auth.ts", "hash": "5091995265697638972", "deps": ["npm:msw"]}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/mocks/server/handlers/graphql.ts", "hash": "2482566790052128860", "deps": ["npm:msw"]}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/mocks/server/handlers/index.ts", "hash": "12255903640773887140"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/mocks/server/index.ts", "hash": "12594348167956979549", "deps": ["npm:msw/node"]}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/setupTests.ts", "hash": "17855512840290832591", "deps": ["webapp-core", "npm:axios", "npm:axios/lib/adapters/http"]}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/utils/factoryCreators.ts", "hash": "16793235775446879604", "deps": ["webapp-core", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/utils/fixtures.ts", "hash": "2736036686649978630", "deps": ["npm:@apollo/client/testing", "npm:graphql/error", "npm:graphql/language"]}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/utils/index.ts", "hash": "17722279654726505390"}, {"file": "packages/webapp-libs/webapp-api-client/src/tests/utils/rendering.tsx", "hash": "6628112828989690040", "deps": ["npm:@apollo/client/testing", "webapp-core", "npm:@storybook/react", "npm:@testing-library/dom", "npm:@testing-library/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-api-client/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-api-client/src/utils/__tests__/eventEmmiter.spec.ts", "hash": "15029240666146674879"}, {"file": "packages/webapp-libs/webapp-api-client/src/utils/eventEmitter.ts", "hash": "5364922896800970373"}, {"file": "packages/webapp-libs/webapp-api-client/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-api-client/tsconfig.lib.json", "hash": "15913993754464611151"}, {"file": "packages/webapp-libs/webapp-api-client/tsconfig.spec.json", "hash": "4967487600183142863"}, {"file": "packages/webapp-libs/webapp-api-client/node_modules/.bin/msw", "hash": "727261962758787165"}], "status-dashboard": [{"file": "packages/internal/status-dashboard/.babelrc", "hash": "5712476190361238632"}, {"file": "packages/internal/status-dashboard/.gitignore", "hash": "5403484524555424706"}, {"file": "packages/internal/status-dashboard/README.md", "hash": "7152240747008883163"}, {"file": "packages/internal/status-dashboard/cdk.json", "hash": "12635046677625749872"}, {"file": "packages/internal/status-dashboard/eslint.config.js", "hash": "17897051932273815321", "deps": ["npm:../../../eslint.config.js"]}, {"file": "packages/internal/status-dashboard/infra/.gitignore", "hash": "1271703533778212942"}, {"file": "packages/internal/status-dashboard/infra/main.ts", "hash": "14954749154915330950", "deps": ["npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/internal/status-dashboard/infra/stacks/statusDashboard/index.ts", "hash": "10993285307191200008"}, {"file": "packages/internal/status-dashboard/infra/stacks/statusDashboard/stack.ts", "hash": "161678669905446030", "deps": ["npm:fs", "npm:path", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-route53", "npm:aws-cdk-lib/aws-s3-deployment", "npm:aws-cdk-lib/aws-certificatemanager", "npm:aws-cdk-lib/aws-s3", "infra-core", "infra-shared"]}, {"file": "packages/internal/status-dashboard/package.json", "hash": "10145749012458284331", "deps": ["core", "infra-core", "infra-shared", "tools"]}, {"file": "packages/internal/status-dashboard/project.json", "hash": "12846178197957332057"}, {"file": "packages/internal/status-dashboard/src/App.tsx", "hash": "6306388629436434118", "deps": ["npm:react", "npm:styled-components"]}, {"file": "packages/internal/status-dashboard/src/components/valueList.tsx", "hash": "8060344068345203647", "deps": ["npm:react"]}, {"file": "packages/internal/status-dashboard/src/components/versionMatrix.tsx", "hash": "1852432326872894885", "deps": ["npm:react"]}, {"file": "packages/internal/status-dashboard/src/favicon.ico", "hash": "16635144065603055158"}, {"file": "packages/internal/status-dashboard/src/hooks/useFetchVersions/index.ts", "hash": "18086184471510690294"}, {"file": "packages/internal/status-dashboard/src/hooks/useFetchVersions/useFetchVersions.tsx", "hash": "6835434041092772650", "deps": ["npm:react"]}, {"file": "packages/internal/status-dashboard/src/index.html", "hash": "8368839149549602844"}, {"file": "packages/internal/status-dashboard/src/main.tsx", "hash": "9507439064762675042", "deps": ["npm:react", "npm:react-dom"]}, {"file": "packages/internal/status-dashboard/src/polyfills.ts", "hash": "12959605551678143287", "deps": ["npm:core-js/stable", "npm:regenerator-runtime/runtime"]}, {"file": "packages/internal/status-dashboard/src/robots.txt", "hash": "2790380794362165217"}, {"file": "packages/internal/status-dashboard/src/styles.ts", "hash": "3491425090354175054", "deps": ["npm:styled-components"]}, {"file": "packages/internal/status-dashboard/tsconfig.app.json", "hash": "8751358259475673984"}, {"file": "packages/internal/status-dashboard/tsconfig.infra.json", "hash": "2911187151943493405"}, {"file": "packages/internal/status-dashboard/tsconfig.json", "hash": "12931153809775213879"}, {"file": "packages/internal/status-dashboard/tsconfig.spec.json", "hash": "12943757776788991084"}], "contentful": [{"file": "packages/contentful/.env.shared", "hash": "6903410894433235244"}, {"file": "packages/contentful/.gitignore", "hash": "2836708013400663046"}, {"file": "packages/contentful/migrations/0001-create-demo-item.js", "hash": "12544953227730202466"}, {"file": "packages/contentful/package.json", "hash": "11559284621057406244"}, {"file": "packages/contentful/scripts/run_migrations.js", "hash": "13356115928543510191", "deps": ["npm:fs", "npm:path", "npm:contentful-migration", "npm:dotenv"]}], "webapp-contentful": [{"file": "packages/webapp-libs/webapp-contentful/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-contentful/eslint.config.js", "hash": "4729949328900901634", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-contentful/graphql/codegen.ts", "hash": "573948838298900316", "deps": ["npm:@graphql-codegen/cli"]}, {"file": "packages/webapp-libs/webapp-contentful/graphql/schema/contentful.graphql", "hash": "4851371028596907507"}, {"file": "packages/webapp-libs/webapp-contentful/graphql/schema/loader.js", "hash": "10598993857230449240", "deps": ["npm:fs", "npm:graphql"]}, {"file": "packages/webapp-libs/webapp-contentful/jest.config.ts", "hash": "8627224931714520139"}, {"file": "packages/webapp-libs/webapp-contentful/package.json", "hash": "9174996971863713147", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-contentful/project.json", "hash": "16715837546697740870"}, {"file": "packages/webapp-libs/webapp-contentful/scripts/download-graphql-schema.js", "hash": "15161997615621492824", "deps": ["npm:child_process", "npm:fs-extra", "npm:dotenv"]}, {"file": "packages/webapp-libs/webapp-contentful/sonar-project.properties", "hash": "1892575107307060966"}, {"file": "packages/webapp-libs/webapp-contentful/src/config/config.graphql.ts", "hash": "5716150014509762147", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-contentful/src/config/index.ts", "hash": "2338664660238434771"}, {"file": "packages/webapp-libs/webapp-contentful/src/config/routes.ts", "hash": "17712503784628839309"}, {"file": "packages/webapp-libs/webapp-contentful/src/helpers/__tests__/image.spec.ts", "hash": "11136073611449161072", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-contentful/src/helpers/image.ts", "hash": "2796567162502515123", "deps": ["webapp-api-client", "npm:query-string", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-contentful/src/helpers/index.ts", "hash": "16584808758835938606"}, {"file": "packages/webapp-libs/webapp-contentful/src/hooks/index.ts", "hash": "16326780224726618937"}, {"file": "packages/webapp-libs/webapp-contentful/src/hooks/useFavoriteDemoItem/__tests__/useFavoriteDemoItem.hook.spec.tsx", "hash": "11228576570783962833", "deps": ["npm:@testing-library/react-hooks", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-contentful/src/hooks/useFavoriteDemoItem/index.ts", "hash": "6541486850199349830"}, {"file": "packages/webapp-libs/webapp-contentful/src/hooks/useFavoriteDemoItem/useFavoriteDemoItem.graphql.ts", "hash": "8284555565359825714", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-contentful/src/hooks/useFavoriteDemoItem/useFavoriteDemoItem.hook.ts", "hash": "9357075782059587666", "deps": ["npm:@apollo/client", "webapp-core", "npm:ramda", "npm:react"]}, {"file": "packages/webapp-libs/webapp-contentful/src/index.ts", "hash": "2141845099170272614"}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItem/__tests__/demoItem.component.spec.tsx", "hash": "12628304022925467035", "deps": ["webapp-core", "npm:@testing-library/react", "npm:ramda", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItem/demoItem.component.tsx", "hash": "7651278384123631790", "deps": ["npm:@apollo/client", "webapp-api-client", "npm:react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItem/demoItem.graphql.ts", "hash": "10774651289834529938", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItem/demoItem.stories.tsx", "hash": "17528526035669132953", "deps": ["webapp-core", "npm:@storybook/react", "npm:ramda", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItem/demoItemContent.component.tsx", "hash": "6049113440662142226", "deps": ["webapp-api-client", "webapp-core", "npm:react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItem/index.ts", "hash": "13463357559832350573"}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItems/__tests__/demoItems.component.spec.tsx", "hash": "5909916108812058246", "deps": ["webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItems/demoItemListItem/__tests__/demoItemListItem.component.spec.tsx", "hash": "2615470506850125884", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:ramda", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItems/demoItemListItem/demoItemListItem.component.tsx", "hash": "18091015416988254986", "deps": ["webapp-api-client", "webapp-core", "npm:lucide-react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItems/demoItemListItem/index.ts", "hash": "2708710842193260972"}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItems/demoItems.component.tsx", "hash": "9231951436043392988", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItems/demoItems.graphql.ts", "hash": "11333802172512205135", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItems/demoItems.stories.tsx", "hash": "11190110429319947616", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/demoItems/index.ts", "hash": "17637880000429218377"}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/index.ts", "hash": "13273040220160598974", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/privacyPolicy/__tests__/privacyPolicy.component.spec.tsx", "hash": "5897639383690467151", "deps": ["npm:@testing-library/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/privacyPolicy/index.ts", "hash": "15591529268951401262"}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/privacyPolicy/privacyPolicy.component.tsx", "hash": "8375853174180248022", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:lucide-react"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/privacyPolicy/privacyPolicy.stories.tsx", "hash": "16838063210683003737", "deps": ["npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/termsAndConditions/__tests__/termsAndConditions.component.spec.tsx", "hash": "12584116491675539379", "deps": ["npm:@testing-library/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/termsAndConditions/index.ts", "hash": "2559265601328409892"}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/termsAndConditions/termsAndConditions.component.tsx", "hash": "5970588279486733578", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:lucide-react"]}, {"file": "packages/webapp-libs/webapp-contentful/src/routes/termsAndConditions/termsAndConditions.stories.tsx", "hash": "7202245644367770581", "deps": ["npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-contentful/src/tests/factories/config.ts", "hash": "9297297741508759117", "deps": ["npm:@apollo/client/testing", "webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-contentful/src/tests/factories/demoItem.ts", "hash": "1614098300560130900", "deps": ["webapp-api-client", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-contentful/src/tests/factories/helpers.ts", "hash": "165327089617037422", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-contentful/src/tests/factories/index.ts", "hash": "6526878365862890440"}, {"file": "packages/webapp-libs/webapp-contentful/src/tests/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-contentful/src/tests/utils/rendering.tsx", "hash": "17478335798572459210", "deps": ["webapp-api-client", "webapp-core", "npm:@storybook/react", "npm:@testing-library/react", "npm:react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-contentful/src/types.ts", "hash": "3590969495199180612"}, {"file": "packages/webapp-libs/webapp-contentful/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-contentful/src/utils/storybook.tsx", "hash": "11643507843931640800", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-contentful/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-contentful/tsconfig.lib.json", "hash": "11174001502938284996"}, {"file": "packages/webapp-libs/webapp-contentful/tsconfig.spec.json", "hash": "5881582925758343424"}], "webapp": [{"file": "packages/webapp/.browserslistrc", "hash": "2888533144288818662"}, {"file": "packages/webapp/.dockerignore", "hash": "15589204452520441516"}, {"file": "packages/webapp/.editorconfig", "hash": "15361077232597264515"}, {"file": "packages/webapp/.env.shared", "hash": "8445105802076458802"}, {"file": "packages/webapp/.gitattributes", "hash": "13731718093452790511"}, {"file": "packages/webapp/.gitignore", "hash": "13022386404114091079"}, {"file": "packages/webapp/.prettierrc", "hash": "1487542123593522159"}, {"file": "packages/webapp/.storybook/decorators.tsx", "hash": "14817856892428650255", "deps": ["webapp-core", "npm:@storybook/react", "npm:react-intl"]}, {"file": "packages/webapp/.storybook/main.ts", "hash": "5714319440381158731", "deps": ["npm:@storybook/react-vite", "npm:vite"]}, {"file": "packages/webapp/.storybook/preview.ts", "hash": "16789874534646624729", "deps": ["npm:@storybook/addon-themes", "npm:@storybook/react", "npm:jest-mock"]}, {"file": "packages/webapp/.stylelintrc", "hash": "12200230767852061447"}, {"file": "packages/webapp/cdk.json", "hash": "12635046677625749872"}, {"file": "packages/webapp/eslint.config.js", "hash": "11985699292906728301", "deps": ["webapp-core"]}, {"file": "packages/webapp/index.html", "hash": "4211008723296534904"}, {"file": "packages/webapp/infra/.gitignore", "hash": "1271703533778212942"}, {"file": "packages/webapp/infra/main.ts", "hash": "5982399375907041175", "deps": ["npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/webapp/infra/stacks/webApp/index.ts", "hash": "14778128362876759093"}, {"file": "packages/webapp/infra/stacks/webApp/stack.ts", "hash": "711232403690380528", "deps": ["npm:fs", "infra-core", "infra-shared", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-s3-deployment"]}, {"file": "packages/webapp/jest.config.ts", "hash": "8401470903184093121"}, {"file": "packages/webapp/package.json", "hash": "8741935409310840748", "deps": ["core", "infra-core", "infra-shared", "tools", "webapp-api-client", "webapp-contentful", "webapp-core", "webapp-crud-demo", "webapp-documents", "webapp-finances", "webapp-generative-ai", "webapp-notifications", "webapp-tenants"]}, {"file": "packages/webapp/plop/crud/actions/components.js", "hash": "15314175650029677020", "deps": ["npm:path"]}, {"file": "packages/webapp/plop/crud/actions/module.js", "hash": "12251637020330202561", "deps": ["npm:path"]}, {"file": "packages/webapp/plop/crud/index.js", "hash": "14453905655418868489"}, {"file": "packages/webapp/plop/crud/templates/api/types.hbs", "hash": "6108688452761283626"}, {"file": "packages/webapp/plop/crud/templates/components/addItem/__tests__/addItem.component.spec.hbs", "hash": "9824172067951295980"}, {"file": "packages/webapp/plop/crud/templates/components/addItem/addItem.component.hbs", "hash": "6680117873044976834"}, {"file": "packages/webapp/plop/crud/templates/components/addItem/addItem.graphql.hbs", "hash": "15960930664737365694"}, {"file": "packages/webapp/plop/crud/templates/components/addItem/addItem.stories.hbs", "hash": "15385433317682506030"}, {"file": "packages/webapp/plop/crud/templates/components/addItem/index.hbs", "hash": "13840079223947421373"}, {"file": "packages/webapp/plop/crud/templates/components/editItem/__tests__/editItem.component.spec.hbs", "hash": "7192052832926597480"}, {"file": "packages/webapp/plop/crud/templates/components/editItem/editItem.component.hbs", "hash": "8798670434595603181"}, {"file": "packages/webapp/plop/crud/templates/components/editItem/editItem.graphql.hbs", "hash": "4714091574355649992"}, {"file": "packages/webapp/plop/crud/templates/components/editItem/editItem.stories.hbs", "hash": "17108892686283176314"}, {"file": "packages/webapp/plop/crud/templates/components/editItem/index.hbs", "hash": "11496928477568400156"}, {"file": "packages/webapp/plop/crud/templates/components/itemDetails/__tests__/itemDetails.component.spec.hbs", "hash": "13535628071708485181"}, {"file": "packages/webapp/plop/crud/templates/components/itemDetails/index.hbs", "hash": "5544210651424241498"}, {"file": "packages/webapp/plop/crud/templates/components/itemDetails/itemDetails.component.hbs", "hash": "9950936331549678559"}, {"file": "packages/webapp/plop/crud/templates/components/itemDetails/itemDetails.graphql.hbs", "hash": "6441611765940084005"}, {"file": "packages/webapp/plop/crud/templates/components/itemDetails/itemDetails.stories.hbs", "hash": "11291075979611296999"}, {"file": "packages/webapp/plop/crud/templates/components/itemForm/__tests__/itemForm.component.spec.hbs", "hash": "3774656813315891347"}, {"file": "packages/webapp/plop/crud/templates/components/itemForm/index.hbs", "hash": "15881153011290747329"}, {"file": "packages/webapp/plop/crud/templates/components/itemForm/itemForm.component.hbs", "hash": "6009555841043676362"}, {"file": "packages/webapp/plop/crud/templates/components/itemForm/itemForm.hook.hbs", "hash": "5821465576367873348"}, {"file": "packages/webapp/plop/crud/templates/components/itemForm/itemForm.stories.hbs", "hash": "6578766174843753512"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/__tests__/itemList.component.spec.hbs", "hash": "6722823539658160552"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/index.hbs", "hash": "13711443952056180081"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemList.component.hbs", "hash": "11877365402676320170"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemList.graphql.hbs", "hash": "4571989907747428651"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemList.stories.hbs", "hash": "5400087499315066384"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemListItem/__tests__/itemListItem.component.spec.hbs", "hash": "94461815501024849"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemListItem/index.hbs", "hash": "15395120170665513772"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemListItem/itemDropDownMenu/index.hbs", "hash": "8015157077721230080"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemListItem/itemDropDownMenu/itemDropDownMenu.hbs", "hash": "9330634633143262624"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemListItem/itemListItem.component.hbs", "hash": "13422797023011470367"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/itemListItem/itemListItem.stories.hbs", "hash": "2378827857283794952"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/listSkeleton/index.hbs", "hash": "17843872686209254174"}, {"file": "packages/webapp/plop/crud/templates/components/itemList/listSkeleton/listSkeleton.component.hbs", "hash": "17340483221443425222"}, {"file": "packages/webapp/plop/crud/templates/components/useItem/__tests__/useItem.hook.spec.hbs", "hash": "10257344743657178881"}, {"file": "packages/webapp/plop/crud/templates/components/useItem/index.hbs", "hash": "13096374719223905209"}, {"file": "packages/webapp/plop/crud/templates/components/useItem/useItem.hook.hbs", "hash": "13032297193891772210"}, {"file": "packages/webapp/plop/crud/templates/factory.hbs", "hash": "12885126459716275842"}, {"file": "packages/webapp/plop/crud/templates/types.hbs", "hash": "13982643233947333596"}, {"file": "packages/webapp/plop/email/index.js", "hash": "2504942864467209088", "deps": ["npm:path"]}, {"file": "packages/webapp/plop/email/templates/component.hbs", "hash": "9424909835618814450"}, {"file": "packages/webapp/plop/email/templates/index.hbs", "hash": "12316475837506416121"}, {"file": "packages/webapp/plop/email/templates/stories.hbs", "hash": "14951458020843115372"}, {"file": "packages/webapp/plop/icon/index.js", "hash": "11536427684893167256"}, {"file": "packages/webapp/plop/notification/index.js", "hash": "3607209285417952235", "deps": ["npm:path"]}, {"file": "packages/webapp/plop/notification/templates/component.hbs", "hash": "16330115341984406869"}, {"file": "packages/webapp/plop/notification/templates/index.hbs", "hash": "14692897395090806318"}, {"file": "packages/webapp/plop/notification/templates/stories.hbs", "hash": "13143102696002467534"}, {"file": "packages/webapp/plop/reactComponent/index.js", "hash": "8056768670437655192", "deps": ["npm:path", "npm:@supercharge/strings", "npm:ramda"]}, {"file": "packages/webapp/plop/reactComponent/templates/__tests__/component.spec.hbs", "hash": "2704565142234872285"}, {"file": "packages/webapp/plop/reactComponent/templates/component.hbs", "hash": "7071582628489685105"}, {"file": "packages/webapp/plop/reactComponent/templates/index.hbs", "hash": "14692897395090806318"}, {"file": "packages/webapp/plop/reactComponent/templates/stories.hbs", "hash": "14842705651240145842"}, {"file": "packages/webapp/plop/reactHook/index.js", "hash": "10784496210349304215", "deps": ["npm:path"]}, {"file": "packages/webapp/plop/reactHook/templates/__tests__/hook.spec.hbs", "hash": "7991831600949837578"}, {"file": "packages/webapp/plop/reactHook/templates/hook.hbs", "hash": "6031504003297405578"}, {"file": "packages/webapp/plop/reactHook/templates/index.hbs", "hash": "5871178750145995147"}, {"file": "packages/webapp/plopfile.js", "hash": "8144499929700607334", "deps": ["npm:inquirer-directory"]}, {"file": "packages/webapp/project.json", "hash": "8636459336045090232"}, {"file": "packages/webapp/public/email-assets/Inter-Regular.woff", "hash": "14663520851397818649"}, {"file": "packages/webapp/public/email-assets/Inter-SemiBold.woff", "hash": "13994330349323349095"}, {"file": "packages/webapp/public/email-assets/logo.png", "hash": "9415324965878358599"}, {"file": "packages/webapp/public/icons/android-chrome-192x192.png", "hash": "7805311172896621447"}, {"file": "packages/webapp/public/icons/android-chrome-512x512.png", "hash": "11508204352934746972"}, {"file": "packages/webapp/public/icons/apple-touch-icon.png", "hash": "7664767031274450072"}, {"file": "packages/webapp/public/icons/favicon-16x16.png", "hash": "8787940139147702237"}, {"file": "packages/webapp/public/icons/favicon-32x32.png", "hash": "3105044335528016683"}, {"file": "packages/webapp/public/icons/favicon.ico", "hash": "17325223685750378680"}, {"file": "packages/webapp/public/icons/mstile-150x150.png", "hash": "16478184919602093075"}, {"file": "packages/webapp/public/icons/safari-pinned-tab.svg", "hash": "15440133679761156814"}, {"file": "packages/webapp/public/manifest.json", "hash": "7650926325823678863"}, {"file": "packages/webapp/scripts/build-storybook.js", "hash": "14264656029981974928", "deps": ["npm:child_process"]}, {"file": "packages/webapp/scripts/start-storybook.sh", "hash": "7830258697575530525"}, {"file": "packages/webapp/scripts/verify.js", "hash": "12827525500396088886", "deps": ["npm:ramda", "npm:util", "npm:path", "npm:terminal-link", "npm:react-dev-utils/chalk", "npm:child_process"]}, {"file": "packages/webapp/sonar-project.properties", "hash": "14831609777215085856"}, {"file": "packages/webapp/src/app/__tests__/app.component.spec.tsx", "hash": "15385446633385630883", "deps": ["webapp-core", "npm:@testing-library/react", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp/src/app/app.component.tsx", "hash": "11958480601739197241", "deps": ["webapp-api-client", "webapp-contentful", "webapp-core", "webapp-crud-demo", "webapp-documents", "webapp-finances", "webapp-generative-ai", "webapp-tenants", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp/src/app/asyncComponents.ts", "hash": "764292374812211527", "deps": ["webapp-core"]}, {"file": "packages/webapp/src/app/config/routes.ts", "hash": "1120352801326452607", "deps": ["webapp-contentful", "webapp-core", "webapp-crud-demo", "webapp-finances", "webapp-generative-ai", "webapp-tenants"]}, {"file": "packages/webapp/src/app/index.tsx", "hash": "186699740520820786"}, {"file": "packages/webapp/src/app/initApp.tsx", "hash": "8650824079359415882", "deps": ["webapp-core", "npm:react", "npm:react-dom/client", "npm:react-helmet-async", "npm:regenerator-runtime/runtime", ["npm:intl", "dynamic"], ["npm:intl/locale-data/jsonp/en.js", "dynamic"], ["npm:intl/locale-data/jsonp/pl.js", "dynamic"]]}, {"file": "packages/webapp/src/app/providers/apiProvider.tsx", "hash": "3374664497022194433", "deps": ["webapp-api-client", "webapp-core", "npm:react", "npm:react-router"]}, {"file": "packages/webapp/src/app/providers/apollo.tsx", "hash": "5229590038321503143", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react"]}, {"file": "packages/webapp/src/app/providers/index.ts", "hash": "14557912911305354964"}, {"file": "packages/webapp/src/app/providers/router.tsx", "hash": "8003092150694583837", "deps": ["npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp/src/app/providers/sentry.tsx", "hash": "972811337514052948", "deps": ["webapp-core", "npm:@sentry/react", "npm:react"]}, {"file": "packages/webapp/src/app/providers/validRoutesProvider/index.ts", "hash": "9052757635127162219"}, {"file": "packages/webapp/src/app/providers/validRoutesProvider/useLanguageFromParams/index.ts", "hash": "10366367381723492249"}, {"file": "packages/webapp/src/app/providers/validRoutesProvider/useLanguageFromParams/useLanguageFromParams.hook.ts", "hash": "14935665832273101060", "deps": ["webapp-core", "npm:react"]}, {"file": "packages/webapp/src/app/providers/validRoutesProvider/validRoutesProviders.tsx", "hash": "6102225394912066263", "deps": ["webapp-core", "webapp-tenants", "npm:react", "npm:react-helmet-async", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "packages/webapp/src/assets/fonts/Inter-Regular.woff", "hash": "14663520851397818649"}, {"file": "packages/webapp/src/assets/fonts/Inter-SemiBold.woff", "hash": "13994330349323349095"}, {"file": "packages/webapp/src/debug-app.tsx", "hash": "14167784822448405469", "deps": ["npm:react", "npm:react-dom/client"]}, {"file": "packages/webapp/src/favicon.ico", "hash": "17325223685750378680"}, {"file": "packages/webapp/src/images/favicon.png", "hash": "11508204352934746972"}, {"file": "packages/webapp/src/images/icons/facebook.svg", "hash": "915935867716677032"}, {"file": "packages/webapp/src/images/icons/google.svg", "hash": "11864222897157695698"}, {"file": "packages/webapp/src/images/icons/headerLogo.svg", "hash": "12429049183309015111"}, {"file": "packages/webapp/src/images/icons/headerLogoDark.svg", "hash": "5828242214500270920"}, {"file": "packages/webapp/src/images/icons/index.ts", "hash": "10715406959632621228"}, {"file": "packages/webapp/src/images/icons/makeIcon.ts", "hash": "18444446805407251210", "deps": ["npm:react", "npm:styled-components"]}, {"file": "packages/webapp/src/index.tsx", "hash": "15788333292914561996", "deps": ["npm:react-loading-skeleton/dist/skeleton.css"]}, {"file": "packages/webapp/src/index.tsx.backup", "hash": "15788333292914561996"}, {"file": "packages/webapp/src/mocks/factories/index.ts", "hash": "2445574468266290255"}, {"file": "packages/webapp/src/modules/auth/auth.types.ts", "hash": "16689086610163693272"}, {"file": "packages/webapp/src/routes/admin/__tests__/admin.component.spec.tsx", "hash": "13038255272094489737", "deps": ["webapp-core", "npm:@testing-library/react"]}, {"file": "packages/webapp/src/routes/admin/admin.component.tsx", "hash": "14862704579100811342", "deps": ["webapp-core", "npm:lucide-react", "npm:react-intl"]}, {"file": "packages/webapp/src/routes/admin/index.ts", "hash": "17039865437155984075"}, {"file": "packages/webapp/src/routes/auth/confirmEmail/__tests__/confirmEmail.component.spec.tsx", "hash": "16613919526364591444", "deps": ["npm:@apollo/client/testing", "webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:graphql/error/GraphQLError", "npm:ramda", "npm:react-router-dom"]}, {"file": "packages/webapp/src/routes/auth/confirmEmail/confirmEmail.component.tsx", "hash": "14337720126678122775", "deps": ["npm:@apollo/client", "webapp-core", "npm:react", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp/src/routes/auth/confirmEmail/confirmEmail.graphql.ts", "hash": "7866230503542962591", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/routes/auth/confirmEmail/index.ts", "hash": "1550998281686339820"}, {"file": "packages/webapp/src/routes/auth/login/index.ts", "hash": "15378891997306466394"}, {"file": "packages/webapp/src/routes/auth/login/login.component.tsx", "hash": "11949291033919102467", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/routes/auth/login/login.stories.tsx", "hash": "1010283675594171440", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/routes/auth/logout/index.ts", "hash": "1942366303495997141"}, {"file": "packages/webapp/src/routes/auth/logout/logout.component.tsx", "hash": "14957303354066218587", "deps": ["webapp-api-client", "webapp-core", "npm:react", "npm:react-router"]}, {"file": "packages/webapp/src/routes/auth/passwordReset/index.ts", "hash": "1506558027922419691"}, {"file": "packages/webapp/src/routes/auth/passwordReset/passwordReset.component.tsx", "hash": "12535569000951705260", "deps": ["webapp-core", "npm:react-router-dom"]}, {"file": "packages/webapp/src/routes/auth/passwordReset/passwordResetConfirm/__tests__/passwordResetConfirm.component.spec.tsx", "hash": "6914327754947788451", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:ramda", "npm:react-router-dom"]}, {"file": "packages/webapp/src/routes/auth/passwordReset/passwordResetConfirm/index.ts", "hash": "6585453694295584123"}, {"file": "packages/webapp/src/routes/auth/passwordReset/passwordResetConfirm/passwordResetConfirm.component.tsx", "hash": "9160704259895454451", "deps": ["webapp-core", "npm:react", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp/src/routes/auth/passwordReset/passwordResetConfirm/passwordResetConfirm.stories.tsx", "hash": "5593267676178379982", "deps": ["webapp-core", "npm:@storybook/react", "npm:react-router"]}, {"file": "packages/webapp/src/routes/auth/passwordReset/passwordResetRequest/index.ts", "hash": "10234737032570999331"}, {"file": "packages/webapp/src/routes/auth/passwordReset/passwordResetRequest/passwordResetRequest.component.tsx", "hash": "5684164554781091736", "deps": ["webapp-core", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp/src/routes/auth/passwordReset/passwordResetRequest/passwordResetRequest.stories.tsx", "hash": "12761074158808387704", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/routes/auth/signup/index.ts", "hash": "3852510269519573924"}, {"file": "packages/webapp/src/routes/auth/signup/signup.component.tsx", "hash": "8857131147969756571", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/routes/auth/signup/signup.stories.tsx", "hash": "9828867268808631400", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/routes/auth/validateOtp/index.ts", "hash": "8685512438980445639"}, {"file": "packages/webapp/src/routes/auth/validateOtp/validateOtp.component.tsx", "hash": "14754465055928478079"}, {"file": "packages/webapp/src/routes/home/<USER>/home.component.spec.tsx", "hash": "2084993789811016728", "deps": ["webapp-api-client", "webapp-tenants", "npm:@testing-library/react"]}, {"file": "packages/webapp/src/routes/home/<USER>", "hash": "8415137886565558153", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:lucide-react", "npm:react-helmet-async", "npm:react-intl"]}, {"file": "packages/webapp/src/routes/home/<USER>", "hash": "7858296268154233175"}, {"file": "packages/webapp/src/routes/notFound/index.ts", "hash": "1209997818501263259"}, {"file": "packages/webapp/src/routes/notFound/notFound.component.tsx", "hash": "8225344637303433824", "deps": ["webapp-core", "npm:react-helmet-async", "npm:react-intl"]}, {"file": "packages/webapp/src/routes/profile/__tests__/profile.component.spec.tsx", "hash": "9194489537087390034", "deps": ["webapp-api-client", "npm:@testing-library/react"]}, {"file": "packages/webapp/src/routes/profile/index.ts", "hash": "16181434465911136270"}, {"file": "packages/webapp/src/routes/profile/profile.component.tsx", "hash": "1128484793047287169", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/routes/profile/profile.stories.tsx", "hash": "1043969546766862483", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp/src/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp/src/shared/components/auth/addTwoFactorAuth/__tests__/addTwoFactorAuth.component.spec.tsx", "hash": "4204844854646113282", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql"]}, {"file": "packages/webapp/src/shared/components/auth/addTwoFactorAuth/addTwoFactorAuth.component.tsx", "hash": "10657578074742259295", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:qrcode", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/addTwoFactorAuth/addTwoFactorAuth.stories.tsx", "hash": "12258603768061518448", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/addTwoFactorAuth/addTwoFactorAuth.types.ts", "hash": "1978096593087219154"}, {"file": "packages/webapp/src/shared/components/auth/addTwoFactorAuth/index.ts", "hash": "2038692161048384240"}, {"file": "packages/webapp/src/shared/components/auth/avatarForm/__tests__/avatarForm.component.spec.tsx", "hash": "13543850502027687381", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:ramda"]}, {"file": "packages/webapp/src/shared/components/auth/avatarForm/avatarForm.component.tsx", "hash": "14350185179944287425", "deps": ["npm:@iconify-icons/ion/camera-outline", "webapp-core", "npm:react-hook-form"]}, {"file": "packages/webapp/src/shared/components/auth/avatarForm/avatarForm.constants.ts", "hash": "2247380441154091610"}, {"file": "packages/webapp/src/shared/components/auth/avatarForm/avatarForm.hooks.ts", "hash": "1843135143062481429", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/avatarForm/avatarForm.stories.tsx", "hash": "6034980906557412481", "deps": ["webapp-api-client", "npm:@storybook/react", "npm:styled-components"]}, {"file": "packages/webapp/src/shared/components/auth/avatarForm/avatarForm.types.ts", "hash": "17746004611757113556"}, {"file": "packages/webapp/src/shared/components/auth/avatarForm/index.ts", "hash": "5115665120971744828"}, {"file": "packages/webapp/src/shared/components/auth/changePasswordForm/__tests__/changePasswordForm.component.spec.tsx", "hash": "11407455242121800700", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error"]}, {"file": "packages/webapp/src/shared/components/auth/changePasswordForm/changePasswordForm.component.tsx", "hash": "5286990531062534783", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/changePasswordForm/changePasswordForm.graphql.ts", "hash": "8399525445703339955", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/components/auth/changePasswordForm/changePasswordForm.hooks.ts", "hash": "15187888288878305890", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/changePasswordForm/changePasswordForm.stories.tsx", "hash": "7103454268496889497", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/changePasswordForm/changePasswordForm.types.ts", "hash": "15237675224881384183"}, {"file": "packages/webapp/src/shared/components/auth/changePasswordForm/index.ts", "hash": "16031338019474587473"}, {"file": "packages/webapp/src/shared/components/auth/editProfileForm/__tests__/editProfileForm.component.spec.tsx", "hash": "3536589643383077445", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error/GraphQLError"]}, {"file": "packages/webapp/src/shared/components/auth/editProfileForm/editProfileForm.component.tsx", "hash": "7587156801040488611", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/editProfileForm/editProfileForm.constants.ts", "hash": "17045927962279508847"}, {"file": "packages/webapp/src/shared/components/auth/editProfileForm/editProfileForm.graphql.ts", "hash": "17892038825006583060", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/components/auth/editProfileForm/editProfileForm.hooks.ts", "hash": "15636846451313059963", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/editProfileForm/editProfileForm.stories.tsx", "hash": "2353688266774907118", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/editProfileForm/editProfileForm.types.ts", "hash": "12573240523316285229"}, {"file": "packages/webapp/src/shared/components/auth/editProfileForm/index.ts", "hash": "17970227073064498549"}, {"file": "packages/webapp/src/shared/components/auth/loginForm/__tests__/loginForm.component.spec.tsx", "hash": "14049131024506702504", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error/GraphQLError"]}, {"file": "packages/webapp/src/shared/components/auth/loginForm/index.ts", "hash": "1506297783868556065"}, {"file": "packages/webapp/src/shared/components/auth/loginForm/loginForm.component.tsx", "hash": "10178614874076654984", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/loginForm/loginForm.graphql.ts", "hash": "16148801069721715807", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/components/auth/loginForm/loginForm.hooks.ts", "hash": "12441970384092783911", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/auth/loginForm/loginForm.stories.tsx", "hash": "8713601883417862431", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/loginForm/loginForm.types.ts", "hash": "15211022682350129123"}, {"file": "packages/webapp/src/shared/components/auth/passwordResetConfirmForm/__tests__/passwordResetConfirmForm.component.spec.tsx", "hash": "9950310844452169249", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error/GraphQLError", "npm:ramda"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetConfirmForm/index.ts", "hash": "17171430524978872650"}, {"file": "packages/webapp/src/shared/components/auth/passwordResetConfirmForm/passwordResetConfirmForm.component.tsx", "hash": "6236165131971137521", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetConfirmForm/passwordResetConfirmForm.graphql.ts", "hash": "1927867820605535263", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetConfirmForm/passwordResetConfirmForm.hooks.ts", "hash": "8681076616268355463", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetConfirmForm/passwordResetConfirmForm.stories.tsx", "hash": "7490128405504071589", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetConfirmForm/passwordResetConfirmForm.types.ts", "hash": "10417606040575797200"}, {"file": "packages/webapp/src/shared/components/auth/passwordResetRequestForm/__tests__/passwordResetRequestForm.component.spec.tsx", "hash": "4866620625026299238", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error/GraphQLError", "npm:ramda"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetRequestForm/index.ts", "hash": "5390296498994496452"}, {"file": "packages/webapp/src/shared/components/auth/passwordResetRequestForm/passwordResetRequestForm.component.tsx", "hash": "11450522303511453209", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetRequestForm/passwordResetRequestForm.constants.tsx", "hash": "17817114685174781433"}, {"file": "packages/webapp/src/shared/components/auth/passwordResetRequestForm/passwordResetRequestForm.graphql.ts", "hash": "6067444958717697575", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetRequestForm/passwordResetRequestForm.hooks.ts", "hash": "572047923336305585", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetRequestForm/passwordResetRequestForm.stories.tsx", "hash": "6542394276675926027", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/passwordResetRequestForm/passwordResetRequestForm.types.ts", "hash": "15833435653228985234"}, {"file": "packages/webapp/src/shared/components/auth/signupForm/__tests__/signupForm.component.spec.tsx", "hash": "15863008504292633115", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error/GraphQLError"]}, {"file": "packages/webapp/src/shared/components/auth/signupForm/index.ts", "hash": "17041004771746726778"}, {"file": "packages/webapp/src/shared/components/auth/signupForm/signUpForm.graphql.ts", "hash": "12029955577777610865", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/components/auth/signupForm/signupForm.component.tsx", "hash": "2478903609267177323", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/signupForm/signupForm.hooks.ts", "hash": "455955809072308026", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/auth/signupForm/signupForm.stories.tsx", "hash": "2959341615135802023", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/signupForm/signupForm.types.ts", "hash": "5444532590635667698"}, {"file": "packages/webapp/src/shared/components/auth/socialLoginButtons/__tests__/socialLoginButtons.component.spec.tsx", "hash": "18004938351671060389", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp/src/shared/components/auth/socialLoginButtons/index.ts", "hash": "8303834962170313375"}, {"file": "packages/webapp/src/shared/components/auth/socialLoginButtons/socialLoginButtons.component.tsx", "hash": "8899229161342741149", "deps": ["webapp-api-client", "webapp-core", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/socialLoginButtons/socialLoginButtons.stories.tsx", "hash": "4093985292794472381", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/twoFactorAuthForm/__tests__/twoFactorAuthForm.component.spec.tsx", "hash": "699543795527075063", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp/src/shared/components/auth/twoFactorAuthForm/index.ts", "hash": "12945946294457435388"}, {"file": "packages/webapp/src/shared/components/auth/twoFactorAuthForm/twoFactorAuthForm.component.tsx", "hash": "823825612425969062", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/twoFactorAuthForm/twoFactorAuthForm.graphql.ts", "hash": "2382474044093951576", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/components/auth/twoFactorAuthForm/twoFactorAuthForm.stories.tsx", "hash": "2308496583827337814", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/auth/validateOtpForm/__tests__/validateOtpForm.component.spec.tsx", "hash": "16312539919300656861", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql", "npm:ramda"]}, {"file": "packages/webapp/src/shared/components/auth/validateOtpForm/index.ts", "hash": "5093196810282962392"}, {"file": "packages/webapp/src/shared/components/auth/validateOtpForm/validateOtpForm.component.tsx", "hash": "16882943714027632310", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/auth/validateOtpForm/validateOtpForm.stories.tsx", "hash": "775975530170274374", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/avatar/__tests__/avatar.component.spec.tsx", "hash": "17015964425436994235", "deps": ["webapp-api-client", "npm:@testing-library/react"]}, {"file": "packages/webapp/src/shared/components/avatar/avatar.component.tsx", "hash": "8403910858062791816", "deps": ["webapp-core", "npm:ramda", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/avatar/avatar.stories.tsx", "hash": "979372542593374481", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/avatar/index.ts", "hash": "15605642551591385722"}, {"file": "packages/webapp/src/shared/components/languageSwitcher/__tests__/languageSwitcher.component.spec.tsx", "hash": "17432110970737763091", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router"]}, {"file": "packages/webapp/src/shared/components/languageSwitcher/index.ts", "hash": "6853027497552491312"}, {"file": "packages/webapp/src/shared/components/languageSwitcher/languageSwitcher.component.tsx", "hash": "719385895973148763", "deps": ["webapp-core", "npm:react"]}, {"file": "packages/webapp/src/shared/components/languageSwitcher/languageSwitcher.hooks.ts", "hash": "8248739417951270188", "deps": ["webapp-core", "npm:react-router"]}, {"file": "packages/webapp/src/shared/components/languageSwitcher/languageSwitcher.stories.tsx", "hash": "17616998140695200071", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/layout/__tests__/layout.component.spec.tsx", "hash": "15154437276605146155", "deps": ["webapp-api-client", "webapp-core", "webapp-notifications", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:ramda", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/layout/header/__tests__/header.component.spec.tsx", "hash": "303987455978808194", "deps": ["webapp-api-client", "webapp-core", "webapp-notifications", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/layout/header/header.component.tsx", "hash": "11317201368907680159", "deps": ["webapp-api-client", "webapp-core", "webapp-notifications", "webapp-tenants", "npm:lucide-react", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/layout/header/header.stories.tsx", "hash": "10359827272680023310", "deps": ["webapp-api-client", "npm:@storybook/addon-actions", "npm:@storybook/react"]}, {"file": "packages/webapp/src/shared/components/layout/header/index.ts", "hash": "11656701165314479332"}, {"file": "packages/webapp/src/shared/components/layout/index.ts", "hash": "11679926947470109722"}, {"file": "packages/webapp/src/shared/components/layout/layout.component.tsx", "hash": "6648624556893116230", "deps": ["npm:ramda", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/layout/layout.context.ts", "hash": "15238673181841945093", "deps": ["npm:react"]}, {"file": "packages/webapp/src/shared/components/layout/layout.stories.tsx", "hash": "9769675593821394487", "deps": ["webapp-api-client", "webapp-notifications", "npm:@storybook/react", "npm:styled-components"]}, {"file": "packages/webapp/src/shared/components/layout/sidebar/__tests__/sidebar.component.spec.tsx", "hash": "17503826133864807817", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/layout/sidebar/index.ts", "hash": "3177555029116956509"}, {"file": "packages/webapp/src/shared/components/layout/sidebar/sidebar.component.tsx", "hash": "528864966452318232", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:lucide-react", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp/src/shared/components/layout/sidebar/sidebar.stories.tsx", "hash": "2335029240506174140", "deps": ["npm:@storybook/react", "npm:react"]}, {"file": "packages/webapp/src/shared/components/notifications/index.ts", "hash": "14150136831205548039"}, {"file": "packages/webapp/src/shared/components/profileInitial/__tests__/profileInitial.component.spec.tsx", "hash": "10968228213751827047", "deps": ["webapp-api-client", "npm:@testing-library/react"]}, {"file": "packages/webapp/src/shared/components/profileInitial/index.ts", "hash": "10887611789576798234"}, {"file": "packages/webapp/src/shared/components/profileInitial/profileInitial.component.tsx", "hash": "15289263007507320184", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/components/roleAccess/__tests__/roleAccess.component.spec.tsx", "hash": "13711730066018372343", "deps": ["webapp-api-client", "npm:@testing-library/react"]}, {"file": "packages/webapp/src/shared/components/roleAccess/index.ts", "hash": "9232250745516257740"}, {"file": "packages/webapp/src/shared/components/roleAccess/roleAccess.component.tsx", "hash": "5770135057548521408", "deps": ["npm:react"]}, {"file": "packages/webapp/src/shared/components/routes/anonymousRoute/__tests__/anonymousRoute.component.spec.tsx", "hash": "11421335829039271523", "deps": ["webapp-api-client", "npm:@testing-library/react", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/routes/anonymousRoute/anonymousRoute.component.tsx", "hash": "1341270905346071158", "deps": ["webapp-core", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/routes/anonymousRoute/index.ts", "hash": "16926191809135375317"}, {"file": "packages/webapp/src/shared/components/routes/authRoute/__tests__/authRoute.component.spec.tsx", "hash": "10366045465946096723", "deps": ["webapp-api-client", "npm:@testing-library/react", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/routes/authRoute/authRoute.component.tsx", "hash": "5945949599705414410", "deps": ["webapp-core", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/routes/authRoute/authRoute.hook.tsx", "hash": "1444847295918911730", "deps": ["webapp-core", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/components/routes/authRoute/index.ts", "hash": "12009405679042064845"}, {"file": "packages/webapp/src/shared/components/routes/index.ts", "hash": "2772771708188275709"}, {"file": "packages/webapp/src/shared/constants/index.ts", "hash": "17262223515568942380"}, {"file": "packages/webapp/src/shared/constants/notificationTemplates.ts", "hash": "788257902322886822", "deps": ["webapp-crud-demo", "webapp-notifications", "webapp-tenants", "npm:react"]}, {"file": "packages/webapp/src/shared/constants/regexes.ts", "hash": "15348326579078536451"}, {"file": "packages/webapp/src/shared/events/notificationEvents.ts", "hash": "8922000668509196043", "deps": ["webapp-notifications"]}, {"file": "packages/webapp/src/shared/hooks/index.ts", "hash": "909817993854891964"}, {"file": "packages/webapp/src/shared/hooks/useAuth/useAuth.ts", "hash": "3043430905924247011", "deps": ["webapp-api-client", "webapp-core", "npm:react", "npm:react-router"]}, {"file": "packages/webapp/src/shared/hooks/useRoleAccessCheck/__tests__/useRoleAccessCheck.hook.spec.tsx", "hash": "440189750551705671", "deps": ["webapp-api-client"]}, {"file": "packages/webapp/src/shared/hooks/useRoleAccessCheck/index.ts", "hash": "12530607704749920252"}, {"file": "packages/webapp/src/shared/hooks/useRoleAccessCheck/useRoleAccessCheck.hook.ts", "hash": "14860715310741281100"}, {"file": "packages/webapp/src/shared/hooks/useRouterScrollToTop/__tests__/useRouterScrollToTop.hook.spec.ts", "hash": "18346498013050541384", "deps": ["npm:@testing-library/react", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/hooks/useRouterScrollToTop/index.ts", "hash": "1199744392222271225"}, {"file": "packages/webapp/src/shared/hooks/useRouterScrollToTop/useRouterScrollToTop.hook.ts", "hash": "3450863600101973463", "deps": ["npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp/src/shared/utils/__tests__/reportError.spec.ts", "hash": "14503651689183651", "deps": ["npm:@sentry/react", "webapp-core"]}, {"file": "packages/webapp/src/shared/utils/storybook.tsx", "hash": "4759390087599767333", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp/src/simple-app.tsx", "hash": "15966294324417839609", "deps": ["npm:react", "npm:react-dom/client"]}, {"file": "packages/webapp/src/simple-debug.tsx", "hash": "3209345130992976372", "deps": ["npm:react", "npm:react-dom/client"]}, {"file": "packages/webapp/src/styles.css", "hash": "11277920134537160647"}, {"file": "packages/webapp/src/tests/svgMock.ts", "hash": "10511359698003564897"}, {"file": "packages/webapp/src/tests/utils/promise.ts", "hash": "12494170207595122332"}, {"file": "packages/webapp/src/tests/utils/rendering.tsx", "hash": "18222380518869097466", "deps": ["webapp-api-client", "webapp-core", "npm:@storybook/react", "npm:@testing-library/react", "npm:react", "npm:react-router"]}, {"file": "packages/webapp/src/types/formatjs.d.ts", "hash": "8049993190204988709"}, {"file": "packages/webapp/src/types/index.d.ts", "hash": "14144161717316236658", "deps": ["webapp-core", "webapp-generative-ai"]}, {"file": "packages/webapp/src/ultra-simple.tsx", "hash": "5368526068889329343", "deps": [["npm:react", "dynamic"], ["npm:react-dom/client", "dynamic"]]}, {"file": "packages/webapp/src/vite-env.d.ts", "hash": "12946363841406869089"}, {"file": "packages/webapp/tailwind.config.ts", "hash": "6738246730660974196", "deps": ["npm:path", "npm:@nx/react/tailwind", "npm:tailwindcss", "npm:../../tailwind.workspace-preset"]}, {"file": "packages/webapp/tsconfig.app.json", "hash": "4936732193140764439"}, {"file": "packages/webapp/tsconfig.infra.json", "hash": "7627737220562436501"}, {"file": "packages/webapp/tsconfig.json", "hash": "9327135865350310758"}, {"file": "packages/webapp/tsconfig.spec.json", "hash": "11407692358240977019"}, {"file": "packages/webapp/vite.config.ts", "hash": "11985936939877526791", "deps": ["npm:dns", "npm:path", "npm:@originjs/vite-plugin-commonjs", "npm:@vitejs/plugin-legacy", "npm:@vitejs/plugin-react", "npm:vite", "npm:vite-plugin-svgr", "npm:vite-tsconfig-paths", ["npm:tailwindcss", "dynamic"], ["npm:autoprefixer", "dynamic"], ["npm:rollup-plugin-node-builtins", "dynamic"]]}], "webapp-generative-ai": [{"file": "packages/webapp-libs/webapp-generative-ai/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-generative-ai/eslint.config.js", "hash": "4729949328900901634", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-generative-ai/graphql/codegen.ts", "hash": "85691977106619409", "deps": ["npm:@graphql-codegen/cli"]}, {"file": "packages/webapp-libs/webapp-generative-ai/jest.config.ts", "hash": "13337795934685313141"}, {"file": "packages/webapp-libs/webapp-generative-ai/package.json", "hash": "13267839981959625011", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-generative-ai/project.json", "hash": "8825242211212463025"}, {"file": "packages/webapp-libs/webapp-generative-ai/sonar-project.properties", "hash": "1339690022445472390"}, {"file": "packages/webapp-libs/webapp-generative-ai/src/config/routes.ts", "hash": "7019445451068238593"}, {"file": "packages/webapp-libs/webapp-generative-ai/src/routes/index.ts", "hash": "17733858760788327014", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-generative-ai/src/routes/saasIdeas/__tests__/saasIdeas.component.spec.tsx", "hash": "2056282236352656999", "deps": ["webapp-api-client", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-generative-ai/src/routes/saasIdeas/index.ts", "hash": "3355030418741946112"}, {"file": "packages/webapp-libs/webapp-generative-ai/src/routes/saasIdeas/saasIdeas.component.tsx", "hash": "4377193884884409068", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react", "npm:react-helmet-async", "npm:react-intl", "npm:typewriter-effect/dist/core"]}, {"file": "packages/webapp-libs/webapp-generative-ai/src/routes/saasIdeas/saasIdeas.graphql.ts", "hash": "5740076895575648569", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-generative-ai/src/routes/saasIdeas/saasIdeas.stories.tsx", "hash": "16941434261220919867", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-generative-ai/src/tests/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-generative-ai/src/tests/utils/rendering.tsx", "hash": "12234490356115050681", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-generative-ai/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-generative-ai/src/types/typewriter.d.ts", "hash": "1806955182622520766"}, {"file": "packages/webapp-libs/webapp-generative-ai/src/utils/storybook.tsx", "hash": "13167694795386376036", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-generative-ai/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-generative-ai/tsconfig.lib.json", "hash": "15861768067856467447"}, {"file": "packages/webapp-libs/webapp-generative-ai/tsconfig.spec.json", "hash": "6274810482038238705"}], "cli": [{"file": "packages/internal/cli/.editorconfig", "hash": "9315761047985002566"}, {"file": "packages/internal/cli/.gitignore", "hash": "12452434011988003763"}, {"file": "packages/internal/cli/README.md", "hash": "7799439619820789362"}, {"file": "packages/internal/cli/bin/dev", "hash": "9206302193048714183"}, {"file": "packages/internal/cli/bin/dev.cmd", "hash": "3816304123756722603"}, {"file": "packages/internal/cli/bin/run", "hash": "2469859297551817565"}, {"file": "packages/internal/cli/bin/run.cmd", "hash": "9317631649270309612"}, {"file": "packages/internal/cli/eslint.config.js", "hash": "15374786973408544826", "deps": ["npm:../../../eslint.config.js"]}, {"file": "packages/internal/cli/package.json", "hash": "1468129325764344220"}, {"file": "packages/internal/cli/project.json", "hash": "13031104429204096897"}, {"file": "packages/internal/cli/scripts/build.js", "hash": "178885065124739319", "deps": ["npm:node:path"]}, {"file": "packages/internal/cli/scripts/lib/runCommand.js", "hash": "9505671509989865055", "deps": ["npm:node:child_process"]}, {"file": "packages/internal/cli/src/baseCommand.ts", "hash": "4387867537483842459", "deps": ["npm:@oclif/core", "npm:@oclif/core/lib/errors", "npm:@opentelemetry/api"]}, {"file": "packages/internal/cli/src/commands/aws/get-env.ts", "hash": "17941069375224548770"}, {"file": "packages/internal/cli/src/commands/aws/login.ts", "hash": "7095573843790904868"}, {"file": "packages/internal/cli/src/commands/aws/set-env.ts", "hash": "17559158701405445542", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/aws/set-var.ts", "hash": "6650195196175583932", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/backend/black.ts", "hash": "11725271632008971046"}, {"file": "packages/internal/cli/src/commands/backend/build-docs.ts", "hash": "11059989620159321820"}, {"file": "packages/internal/cli/src/commands/backend/build.ts", "hash": "9902719741420720480", "deps": ["npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/backend/deploy/api.ts", "hash": "6051108511693906742", "deps": ["npm:@oclif/core", "npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/backend/deploy/celery.ts", "hash": "4490669938470350275", "deps": ["npm:@oclif/core", "npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/backend/deploy/migrations.ts", "hash": "13150575134932527758", "deps": ["npm:@oclif/core", "npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/backend/down.ts", "hash": "10322722039805061724"}, {"file": "packages/internal/cli/src/commands/backend/makemigrations.ts", "hash": "15387425177167922136"}, {"file": "packages/internal/cli/src/commands/backend/migrate.ts", "hash": "4354683033200578042"}, {"file": "packages/internal/cli/src/commands/backend/remote-shell.ts", "hash": "1467299613160722362", "deps": ["npm:@oclif/color", "npm:@aws-sdk/client-ecs"]}, {"file": "packages/internal/cli/src/commands/backend/ruff.ts", "hash": "14496494061053542556"}, {"file": "packages/internal/cli/src/commands/backend/secrets.ts", "hash": "2935568900259626482", "deps": ["npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/backend/shell.ts", "hash": "1589072741394506785"}, {"file": "packages/internal/cli/src/commands/backend/stripe/sync.ts", "hash": "1680746695015023607"}, {"file": "packages/internal/cli/src/commands/backend/test.ts", "hash": "390613294072514511"}, {"file": "packages/internal/cli/src/commands/backend/up.ts", "hash": "2626943249274484028"}, {"file": "packages/internal/cli/src/commands/build.ts", "hash": "8995791624677970480"}, {"file": "packages/internal/cli/src/commands/ci/get-artifacts-bucket.ts", "hash": "13341318686895915688", "deps": ["npm:@aws-sdk/client-cloudformation", "npm:ramda"]}, {"file": "packages/internal/cli/src/commands/db/shell.ts", "hash": "11357649526889025600"}, {"file": "packages/internal/cli/src/commands/deploy.ts", "hash": "15500569359406507825", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/docs/build.ts", "hash": "8770937862269906659", "deps": ["npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/docs/deploy.ts", "hash": "8676679706888308901", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/docs/up.ts", "hash": "14321129712919841375"}, {"file": "packages/internal/cli/src/commands/down.ts", "hash": "4807550827371304309"}, {"file": "packages/internal/cli/src/commands/emails/build.ts", "hash": "8429288070585179592", "deps": ["npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/emails/secrets.ts", "hash": "15593127260691807332"}, {"file": "packages/internal/cli/src/commands/emails/test.ts", "hash": "1226020787599560560"}, {"file": "packages/internal/cli/src/commands/infra/bootstrap.ts", "hash": "14245710940961517927"}, {"file": "packages/internal/cli/src/commands/infra/deploy.ts", "hash": "17589351830768554641", "deps": ["npm:@oclif/core", "npm:@aws-sdk/client-lambda"]}, {"file": "packages/internal/cli/src/commands/lint.ts", "hash": "14173828461783772594"}, {"file": "packages/internal/cli/src/commands/test.ts", "hash": "1320253956940983297"}, {"file": "packages/internal/cli/src/commands/up.ts", "hash": "15802582547725398924", "deps": ["npm:@oclif/core", "npm:node:util", "npm:node:child_process"]}, {"file": "packages/internal/cli/src/commands/webapp/build.ts", "hash": "17631218044730742000", "deps": ["npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/webapp/deploy.ts", "hash": "3762538101811566866", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/webapp/graphql/download-schema.ts", "hash": "3878955691597164011"}, {"file": "packages/internal/cli/src/commands/webapp/lint.ts", "hash": "275718988742800571", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/webapp/secrets.ts", "hash": "15785943414631893765", "deps": ["npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/webapp/storybook.ts", "hash": "2341068080355209313"}, {"file": "packages/internal/cli/src/commands/webapp/test.ts", "hash": "6694979918266838330", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/webapp/type-check.ts", "hash": "17549374267375132870", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/webapp/up.ts", "hash": "12391551764743148034"}, {"file": "packages/internal/cli/src/commands/workers/black.ts", "hash": "2861274437395924220"}, {"file": "packages/internal/cli/src/commands/workers/build.ts", "hash": "2863562324570184549", "deps": ["npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/workers/deploy.ts", "hash": "3534646960144713985", "deps": ["npm:@oclif/core", "npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/workers/invoke/local.ts", "hash": "3800076177107556801", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/commands/workers/lint.ts", "hash": "14140139683704470073"}, {"file": "packages/internal/cli/src/commands/workers/secrets.ts", "hash": "14439009940220970018", "deps": ["npm:@oclif/color"]}, {"file": "packages/internal/cli/src/commands/workers/shell.ts", "hash": "14121144055033202101"}, {"file": "packages/internal/cli/src/commands/workers/test.ts", "hash": "18063342451068708565"}, {"file": "packages/internal/cli/src/config/aws.ts", "hash": "11591452782265501641", "deps": ["npm:@aws-sdk/client-sts", "npm:@aws-sdk/client-ecr", "npm:@oclif/core", "npm:@oclif/color", "npm:@opentelemetry/api", "npm:child_process", "npm:util", "npm:dotenv"]}, {"file": "packages/internal/cli/src/config/env.ts", "hash": "3036723447524853929", "deps": ["npm:child_process", "npm:util", "npm:path", "npm:dotenv", "npm:envalid", "npm:@apptension/saas-boilerplate-telemetry"]}, {"file": "packages/internal/cli/src/config/init.ts", "hash": "10522457773927936588", "deps": ["npm:@oclif/core", "npm:@oclif/color", "npm:@opentelemetry/api"]}, {"file": "packages/internal/cli/src/config/storage.ts", "hash": "12490924813111105704", "deps": ["npm:node-persist", "npm:child_process", "npm:util", "npm:path"]}, {"file": "packages/internal/cli/src/config/telemetry.ts", "hash": "12555841190118248540", "deps": ["npm:node:os", "npm:@opentelemetry/sdk-trace-base", "npm:@opentelemetry/resources", "npm:@opentelemetry/sdk-trace-node", "npm:@opentelemetry/semantic-conventions", "npm:@opentelemetry/exporter-trace-otlp-proto"]}, {"file": "packages/internal/cli/src/hooks/init/instrumentation.ts", "hash": "11512421924220693569", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/index.ts", "hash": "8559578363244750466", "deps": ["npm:@oclif/core"]}, {"file": "packages/internal/cli/src/lib/awsVault.ts", "hash": "8014098368251503171", "deps": ["npm:@oclif/core", "npm:lookpath"]}, {"file": "packages/internal/cli/src/lib/chamber.ts", "hash": "8298173636922394870", "deps": ["npm:child_process", "npm:util", "npm:lookpath", "npm:@oclif/core", "npm:dotenv"]}, {"file": "packages/internal/cli/src/lib/docker.ts", "hash": "13178105753663064675", "deps": ["npm:@oclif/core", "npm:child_process", "npm:util", "npm:@opentelemetry/api"]}, {"file": "packages/internal/cli/src/lib/runCommand.ts", "hash": "2489473031002515800", "deps": ["npm:node:child_process"]}, {"file": "packages/internal/cli/src/lib/secretsEditor.ts", "hash": "13182119149426114445"}, {"file": "packages/internal/cli/tsconfig.json", "hash": "6739515257618434429"}], "ssm-editor": [{"file": "packages/internal/ssm-editor/Dockerfile", "hash": "2161122850607530407"}, {"file": "packages/internal/ssm-editor/project.json", "hash": "6546579584440536810"}, {"file": "packages/internal/ssm-editor/scripts/run.sh", "hash": "7134985008455718932"}], "webapp-crud-demo": [{"file": "packages/webapp-libs/webapp-crud-demo/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-crud-demo/eslint.config.js", "hash": "4729949328900901634", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-crud-demo/graphql/codegen.ts", "hash": "10126206112599730238", "deps": ["npm:@graphql-codegen/cli"]}, {"file": "packages/webapp-libs/webapp-crud-demo/jest.config.ts", "hash": "131844977986811970"}, {"file": "packages/webapp-libs/webapp-crud-demo/package.json", "hash": "7944878962701604225", "deps": ["webapp-api-client", "webapp-core", "webapp-notifications"]}, {"file": "packages/webapp-libs/webapp-crud-demo/project.json", "hash": "9412869327193066759"}, {"file": "packages/webapp-libs/webapp-crud-demo/sonar-project.properties", "hash": "10937655470788470330"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/config/routes.ts", "hash": "15074974065175502454", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/notifications/crudItemCreated/crudItemCreated.component.tsx", "hash": "2736461921252611532", "deps": ["webapp-core", "webapp-notifications", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/notifications/crudItemCreated/crudItemCreated.stories.tsx", "hash": "16968982867746596788", "deps": ["webapp-notifications", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/notifications/crudItemCreated/index.ts", "hash": "9176030801376623719"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/notifications/crudItemUpdated/crudItemUpdated.component.tsx", "hash": "5205705742682391653", "deps": ["webapp-core", "webapp-notifications", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/notifications/crudItemUpdated/crudItemUpdated.stories.tsx", "hash": "10545048084808227252", "deps": ["webapp-notifications", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/notifications/crudItemUpdated/index.ts", "hash": "14134869879707427091"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/notifications/index.ts", "hash": "2886515850772635584"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/addCrudDemoItem/__tests__/addCrudDemoItem.component.spec.tsx", "hash": "7892677775864651202", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/addCrudDemoItem/addCrudDemoItem.component.tsx", "hash": "3246292933799688947", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/addCrudDemoItem/addCrudDemoItem.stories.tsx", "hash": "5104847816264374516", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/addCrudDemoItem/index.tsx", "hash": "5118463631971720621"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItem.component.tsx", "hash": "1658906785630122354", "deps": ["webapp-core", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemDetails/__tests__/crudDemoItemDetails.component.spec.tsx", "hash": "12033081378942562941", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemDetails/crudDemoItemDetails.component.tsx", "hash": "9444802738728584278", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemDetails/crudDemoItemDetails.stories.tsx", "hash": "17975346134326359307", "deps": ["webapp-core", "npm:@storybook/react", "npm:ramda", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemDetails/index.tsx", "hash": "9689792850543411616"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemForm/__tests__/crudDemoItemForm.component.spec.tsx", "hash": "7642686550705018038", "deps": ["npm:@apollo/client", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error/GraphQLError"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemForm/crudDemoItemForm.component.tsx", "hash": "7434389330994149118", "deps": ["npm:@apollo/client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemForm/crudDemoItemForm.hook.ts", "hash": "13816083182673178918", "deps": ["webapp-api-client", "npm:react"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemForm/crudDemoItemForm.stories.tsx", "hash": "7428323789087003498", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemForm/index.tsx", "hash": "5293910190489999153"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/__tests__/crudDemoItemList.component.spec.tsx", "hash": "3542921446105245006", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemList.component.tsx", "hash": "11028319013748807371", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:lucide-react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemList.stories.tsx", "hash": "8209898075905096524", "deps": ["npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemListItem/__tests__/crudDemoItemListItem.component.spec.tsx", "hash": "17353090766632995765", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemListItem/crudDemoItemListItem.component.tsx", "hash": "2699091296198879115", "deps": ["npm:@apollo/client", "npm:@iconify-icons/ion/pencil-sharp", "npm:@iconify-icons/ion/trash-outline", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemListItem/crudDemoItemListItem.graphql.ts", "hash": "6084862636585453136", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemListItem/crudDemoItemListItem.stories.tsx", "hash": "1209158231286680794", "deps": ["npm:@apollo/client", "webapp-api-client", "npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemListItem/crudDropdownMenu/crudDropdownMenu.component.tsx", "hash": "11479644593862671640", "deps": ["npm:@iconify-icons/ion/pencil-sharp", "npm:@iconify-icons/ion/trash-outline", "webapp-core", "webapp-tenants", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemListItem/crudDropdownMenu/index.ts", "hash": "3792727977611152760"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/crudDemoItemListItem/index.tsx", "hash": "6868424028758452337"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/index.tsx", "hash": "14746490139228244396"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/listSkeleton/index.ts", "hash": "17843872686209254174"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/crudDemoItemList/listSkeleton/listSkeleton.component.tsx", "hash": "13233613104855018034", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/editCrudDemoItem/__tests__/editCrudDemoItem.component.spec.tsx", "hash": "15901178852496861628", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/editCrudDemoItem/editCrudDemoItem.component.tsx", "hash": "9491023516424140694", "deps": ["npm:@apollo/client", "webapp-core", "webapp-tenants", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/editCrudDemoItem/editCrudDemoItem.graphql.ts", "hash": "11894411374725582646", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/editCrudDemoItem/editCrudDemoItem.stories.tsx", "hash": "13993037101214991513", "deps": ["webapp-core", "npm:@storybook/react", "npm:ramda", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/editCrudDemoItem/index.tsx", "hash": "16822122779909481610"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/crudDemoItem/index.ts", "hash": "8512115438493555769"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/routes/index.ts", "hash": "12516385665405585962", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/tests/factories/crudDemoItem.ts", "hash": "156307069463007086", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/tests/factories/index.ts", "hash": "17964041948324171600"}, {"file": "packages/webapp-libs/webapp-crud-demo/src/tests/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/tests/utils/rendering.tsx", "hash": "10093101555685865026", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-crud-demo/src/utils/storybook.tsx", "hash": "13167694795386376036", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-crud-demo/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-crud-demo/tsconfig.lib.json", "hash": "11174001502938284996"}, {"file": "packages/webapp-libs/webapp-crud-demo/tsconfig.spec.json", "hash": "6274810482038238705"}], "docs": [{"file": "packages/internal/docs/.gitignore", "hash": "11103144270742185381"}, {"file": "packages/internal/docs/README.md", "hash": "8132999394106273583"}, {"file": "packages/internal/docs/babel.config.js", "hash": "5284868490292323786", "deps": ["npm:@docusaurus/core/lib/babel/preset"]}, {"file": "packages/internal/docs/cdk.json", "hash": "12635046677625749872"}, {"file": "packages/internal/docs/docs/api-reference/backend/commands.mdx", "hash": "16828774350184537427"}, {"file": "packages/internal/docs/docs/api-reference/backend/index.mdx", "hash": "10909691674172745717"}, {"file": "packages/internal/docs/docs/api-reference/env-files.mdx", "hash": "10839199540649245905"}, {"file": "packages/internal/docs/docs/api-reference/env.mdx", "hash": "14956262243945391508"}, {"file": "packages/internal/docs/docs/api-reference/index.mdx", "hash": "1161675751200170396"}, {"file": "packages/internal/docs/docs/api-reference/tools/commands.mdx", "hash": "1616310311901714144"}, {"file": "packages/internal/docs/docs/api-reference/tools/index.mdx", "hash": "564384636840230304"}, {"file": "packages/internal/docs/docs/api-reference/webapp-api-client/commands.mdx", "hash": "6284435440906589296"}, {"file": "packages/internal/docs/docs/api-reference/webapp-core/components.mdx", "hash": "18216928116526241310"}, {"file": "packages/internal/docs/docs/api-reference/webapp-emails/commands.mdx", "hash": "4273158795761410396"}, {"file": "packages/internal/docs/docs/api-reference/webapp-emails/index.mdx", "hash": "5633955402236447173"}, {"file": "packages/internal/docs/docs/api-reference/webapp-tenants/commands.mdx", "hash": "1803436054024146995"}, {"file": "packages/internal/docs/docs/api-reference/webapp/commands.mdx", "hash": "10174123268542118257"}, {"file": "packages/internal/docs/docs/api-reference/webapp/index.mdx", "hash": "15002067285924099979"}, {"file": "packages/internal/docs/docs/aws/architecture/cicd-architecture.mdx", "hash": "15714077366818973404"}, {"file": "packages/internal/docs/docs/aws/architecture/index.mdx", "hash": "3772151165446837691"}, {"file": "packages/internal/docs/docs/aws/architecture/system-architecture.mdx", "hash": "1986838695624804175"}, {"file": "packages/internal/docs/docs/aws/cicd/auto-deploy.mdx", "hash": "12732542647869147171"}, {"file": "packages/internal/docs/docs/aws/cicd/configure-cicd-notifications.mdx", "hash": "17471056439881937906"}, {"file": "packages/internal/docs/docs/aws/cicd/index.mdx", "hash": "17081204648254562401"}, {"file": "packages/internal/docs/docs/aws/cicd/setup-docker-hub.mdx", "hash": "8521947554706536538"}, {"file": "packages/internal/docs/docs/aws/cicd/trigger-cicd-manually.mdx", "hash": "11206439597742566695"}, {"file": "packages/internal/docs/docs/aws/deploy-to-aws/configure-aws-credentials.mdx", "hash": "15815091931057478297"}, {"file": "packages/internal/docs/docs/aws/deploy-to-aws/configure-hosted-zone.mdx", "hash": "8259710204976632522"}, {"file": "packages/internal/docs/docs/aws/deploy-to-aws/create-env-stage-in-repo.mdx", "hash": "10335834107096035951"}, {"file": "packages/internal/docs/docs/aws/deploy-to-aws/create-runtime-env-vars.mdx", "hash": "2586376290386024791"}, {"file": "packages/internal/docs/docs/aws/deploy-to-aws/deploy-infrastructure.mdx", "hash": "14713140672304876638"}, {"file": "packages/internal/docs/docs/aws/deploy-to-aws/index.mdx", "hash": "17322368045065917300"}, {"file": "packages/internal/docs/docs/aws/deploy-to-aws/run-deployment-commands.mdx", "hash": "5920480257108863567"}, {"file": "packages/internal/docs/docs/aws/guides/aws-exec.mdx", "hash": "12912555555047203556"}, {"file": "packages/internal/docs/docs/aws/guides/index.mdx", "hash": "10117934404485004239"}, {"file": "packages/internal/docs/docs/aws/index.mdx", "hash": "6380631317730141940"}, {"file": "packages/internal/docs/docs/aws/troubleshooting.mdx", "hash": "3312679258299398408"}, {"file": "packages/internal/docs/docs/getting-started/index.mdx", "hash": "7610881701216089967"}, {"file": "packages/internal/docs/docs/getting-started/run-project/_install-deps.mdx", "hash": "76986505280724101"}, {"file": "packages/internal/docs/docs/getting-started/run-project/_requirements.mdx", "hash": "6089640388452508577"}, {"file": "packages/internal/docs/docs/getting-started/run-project/_start-app.mdx", "hash": "4114323153112075740"}, {"file": "packages/internal/docs/docs/getting-started/run-project/run-existing-project.mdx", "hash": "10647505158803008446"}, {"file": "packages/internal/docs/docs/getting-started/run-project/run-new-project.mdx", "hash": "1815689506635715237"}, {"file": "packages/internal/docs/docs/index.mdx", "hash": "12213256251575423440"}, {"file": "packages/internal/docs/docs/introduction/architecture.mdx", "hash": "16360634831221769697"}, {"file": "packages/internal/docs/docs/introduction/coding-standards/formatters/black.mdx", "hash": "17794378004510748694"}, {"file": "packages/internal/docs/docs/introduction/coding-standards/formatters/index.mdx", "hash": "227562668677019769"}, {"file": "packages/internal/docs/docs/introduction/coding-standards/index.mdx", "hash": "14215356064518054049"}, {"file": "packages/internal/docs/docs/introduction/coding-standards/linters/index.mdx", "hash": "16600204372803686682"}, {"file": "packages/internal/docs/docs/introduction/coding-standards/linters/ruff.mdx", "hash": "15176562675716894620"}, {"file": "packages/internal/docs/docs/introduction/features/admin.mdx", "hash": "10094501753306220773"}, {"file": "packages/internal/docs/docs/introduction/features/assets.mdx", "hash": "8992647967688231374"}, {"file": "packages/internal/docs/docs/introduction/features/auth.mdx", "hash": "17157968979529877816"}, {"file": "packages/internal/docs/docs/introduction/features/cicd.mdx", "hash": "10833782708970280324"}, {"file": "packages/internal/docs/docs/introduction/features/cms.mdx", "hash": "13402352538786680529"}, {"file": "packages/internal/docs/docs/introduction/features/crud.mdx", "hash": "1600553265197549020"}, {"file": "packages/internal/docs/docs/introduction/features/dev-tools.mdx", "hash": "12517328403410854909"}, {"file": "packages/internal/docs/docs/introduction/features/emails.mdx", "hash": "15041672015342302258"}, {"file": "packages/internal/docs/docs/introduction/features/graphql.mdx", "hash": "3000471787218616790"}, {"file": "packages/internal/docs/docs/introduction/features/iac.mdx", "hash": "18128651735760368147"}, {"file": "packages/internal/docs/docs/introduction/features/index.mdx", "hash": "359766206277700159"}, {"file": "packages/internal/docs/docs/introduction/features/multi-tenancy.mdx", "hash": "13365628132650806668"}, {"file": "packages/internal/docs/docs/introduction/features/notifications.mdx", "hash": "1671511522655259414"}, {"file": "packages/internal/docs/docs/introduction/features/openai.mdx", "hash": "14778707759404361261"}, {"file": "packages/internal/docs/docs/introduction/features/payments.mdx", "hash": "850682272510092781"}, {"file": "packages/internal/docs/docs/introduction/features/workers.mdx", "hash": "2495429744403715833"}, {"file": "packages/internal/docs/docs/introduction/index.mdx", "hash": "13948919152388431061"}, {"file": "packages/internal/docs/docs/introduction/stack-description.mdx", "hash": "12034417180680957641"}, {"file": "packages/internal/docs/docs/introduction/the-problem.mdx", "hash": "640133471809562537"}, {"file": "packages/internal/docs/docs/shared/components/DisplayLocalUseInfo.component.tsx", "hash": "12415930635615290735", "deps": ["npm:react", "npm:@docusaurus/useDocusaurusContext", "npm:@theme/MDXContent"]}, {"file": "packages/internal/docs/docs/shared/components/ImgThemed.component.tsx", "hash": "17918767705643891500", "deps": ["npm:react", "npm:@docusaurus/theme-common"]}, {"file": "packages/internal/docs/docs/shared/components/ProjectName.component.tsx", "hash": "13013341706629455254", "deps": ["npm:@docusaurus/useDocusaurusContext"]}, {"file": "packages/internal/docs/docs/shared/partials/_barrel-exports.mdx", "hash": "2399363436362420048"}, {"file": "packages/internal/docs/docs/shared/partials/_component-structure.mdx", "hash": "18241197018585286918"}, {"file": "packages/internal/docs/docs/shared/partials/_freash-aws-account-info.mdx", "hash": "5801608664711173146"}, {"file": "packages/internal/docs/docs/shared/partials/_local-use-info.mdx", "hash": "7231311278277359155"}, {"file": "packages/internal/docs/docs/shared/partials/_run-on-ci-caution.mdx", "hash": "7328013072599032925"}, {"file": "packages/internal/docs/docs/shared/partials/_sb_description.mdx", "hash": "14325193120499327173"}, {"file": "packages/internal/docs/docs/shared/partials/_testing-component-loading-state.mdx", "hash": "13999105261344301043"}, {"file": "packages/internal/docs/docs/shared/partials/_testing-custom-render.mdx", "hash": "11369365803688340164"}, {"file": "packages/internal/docs/docs/shared/partials/_wsl_notice.mdx", "hash": "1189477494103928612"}, {"file": "packages/internal/docs/docs/shared/partials/architecture/_cicd_architecture.mdx", "hash": "5249518974678346734"}, {"file": "packages/internal/docs/docs/shared/partials/architecture/_system_architecture.mdx", "hash": "13979216224439387123"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_backend.mdx", "hash": "15307427098914641807"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_backend_email.mdx", "hash": "17870505625933510742"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_backend_optional.mdx", "hash": "9394992959598962986"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_backend_stripe.mdx", "hash": "12065857352230592349"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_webapp.mdx", "hash": "13131521334564481770"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_webapp_contentful.mdx", "hash": "7723011295260102780"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_webapp_optional.mdx", "hash": "12102937378724126674"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_webapp_stripe.mdx", "hash": "12542147247341834790"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_workers.mdx", "hash": "1950841425238233897"}, {"file": "packages/internal/docs/docs/shared/partials/env-vars/_workers_optional.mdx", "hash": "14246223891825864774"}, {"file": "packages/internal/docs/docs/working-with-sb/async-workers/create-workers-module.mdx", "hash": "11311043556853805626"}, {"file": "packages/internal/docs/docs/working-with-sb/async-workers/debug-async-job-celery.mdx", "hash": "10012818350749172469"}, {"file": "packages/internal/docs/docs/working-with-sb/async-workers/run-async-job-celery.mdx", "hash": "10559653639226217941"}, {"file": "packages/internal/docs/docs/working-with-sb/async-workers/run-async-job-lambda.mdx", "hash": "942872460792824641"}, {"file": "packages/internal/docs/docs/working-with-sb/contentful/sync-data.mdx", "hash": "6914787882922538304"}, {"file": "packages/internal/docs/docs/working-with-sb/dev-tools/mailcatcher.mdx", "hash": "19969115970418763"}, {"file": "packages/internal/docs/docs/working-with-sb/dev-tools/plop.mdx", "hash": "15919694586916495391"}, {"file": "packages/internal/docs/docs/working-with-sb/dev-tools/sentry.mdx", "hash": "4061527438610222933"}, {"file": "packages/internal/docs/docs/working-with-sb/dev-tools/ssh-into-container.mdx", "hash": "13794664263426332659"}, {"file": "packages/internal/docs/docs/working-with-sb/dev-tools/telemetry.mdx", "hash": "1877736585655942101"}, {"file": "packages/internal/docs/docs/working-with-sb/dev-tools/version-matrix.mdx", "hash": "7856912156372181214"}, {"file": "packages/internal/docs/docs/working-with-sb/emails/create-email-template.mdx", "hash": "18346227182669905375"}, {"file": "packages/internal/docs/docs/working-with-sb/emails/send-email.mdx", "hash": "12787083969668314264"}, {"file": "packages/internal/docs/docs/working-with-sb/graphql/backend/adding-new-mutation.mdx", "hash": "7690109622929214321"}, {"file": "packages/internal/docs/docs/working-with-sb/graphql/backend/working-with-serializers.mdx", "hash": "17897099596246878666"}, {"file": "packages/internal/docs/docs/working-with-sb/graphql/conventions/errors-format.mdx", "hash": "7152560362347131368"}, {"file": "packages/internal/docs/docs/working-with-sb/graphql/web-app/component-with-query.mdx", "hash": "7282827663212770164"}, {"file": "packages/internal/docs/docs/working-with-sb/graphql/web-app/form-with-mutation.mdx", "hash": "1796369248019180764"}, {"file": "packages/internal/docs/docs/working-with-sb/graphql/web-app/update-schema.mdx", "hash": "15030539483489795233"}, {"file": "packages/internal/docs/docs/working-with-sb/graphql/web-app/use-fragments.mdx", "hash": "9094703466572057470"}, {"file": "packages/internal/docs/docs/working-with-sb/guides/backend/backend-model.mdx", "hash": "11484405707355451888"}, {"file": "packages/internal/docs/docs/working-with-sb/guides/web-app/create-react-component.mdx", "hash": "17483931119952323839"}, {"file": "packages/internal/docs/docs/working-with-sb/guides/web-app/new-route.mdx", "hash": "14258702374133718763"}, {"file": "packages/internal/docs/docs/working-with-sb/index.mdx", "hash": "12250802003374321327"}, {"file": "packages/internal/docs/docs/working-with-sb/notifications/create-notification.mdx", "hash": "14835884832307761753"}, {"file": "packages/internal/docs/docs/working-with-sb/notifications/index.mdx", "hash": "13309126103880946440"}, {"file": "packages/internal/docs/docs/working-with-sb/payments/index.mdx", "hash": "17104460998043252561"}, {"file": "packages/internal/docs/docs/working-with-sb/payments/one-time-payment-form.mdx", "hash": "11530676794927700290"}, {"file": "packages/internal/docs/docs/working-with-sb/project-structure/create-web-lib.mdx", "hash": "6195811873630913386"}, {"file": "packages/internal/docs/docs/working-with-sb/shadcn.mdx", "hash": "12905515893666787379"}, {"file": "packages/internal/docs/docs/working-with-sb/tests/backend.mdx", "hash": "8837892323595977846"}, {"file": "packages/internal/docs/docs/working-with-sb/tests/webapp.mdx", "hash": "1419644359327792213"}, {"file": "packages/internal/docs/docs/working-with-sb/users/admin-page.mdx", "hash": "14347424494787470495"}, {"file": "packages/internal/docs/docs/working-with-sb/users/create-oauth-method.mdx", "hash": "13570671369191358468"}, {"file": "packages/internal/docs/docs/working-with-sb/users/create-profile-field.mdx", "hash": "16479954362675574459"}, {"file": "packages/internal/docs/docs/working-with-sb/users/create-role.mdx", "hash": "1697762944005561693"}, {"file": "packages/internal/docs/docs/working-with-sb/users/index.mdx", "hash": "16904672347344180089"}, {"file": "packages/internal/docs/docusaurus.config.js", "hash": "18182824239891408021"}, {"file": "packages/internal/docs/eslint.config.js", "hash": "7714374989722728373", "deps": ["npm:../../../eslint.config.js"]}, {"file": "packages/internal/docs/infra/main.ts", "hash": "3510885147099748520", "deps": ["npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/internal/docs/infra/stacks/docs/index.ts", "hash": "9125738283969576976"}, {"file": "packages/internal/docs/infra/stacks/docs/stack.ts", "hash": "9008019428628084007", "deps": ["npm:fs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-s3-deployment", "infra-core", "infra-shared"]}, {"file": "packages/internal/docs/package.json", "hash": "4978625259715724172", "deps": ["core", "infra-core", "infra-shared", "tools", "webapp-api-client", "webapp-contentful", "webapp-core", "webapp-emails", "webapp-tenants", "webapp"]}, {"file": "packages/internal/docs/project.json", "hash": "8065080239784106464"}, {"file": "packages/internal/docs/sidebars.js", "hash": "77031328015932977"}, {"file": "packages/internal/docs/src/assets/fonts/Inter-Regular.woff", "hash": "14663520851397818649"}, {"file": "packages/internal/docs/src/assets/fonts/Inter-SemiBold.woff", "hash": "13994330349323349095"}, {"file": "packages/internal/docs/src/css/custom.css", "hash": "664830935714098298"}, {"file": "packages/internal/docs/src/pages/styles.module.css", "hash": "12082006037655627093"}, {"file": "packages/internal/docs/src/types.d.ts", "hash": "12398773295673604119", "deps": ["npm:@docusaurus/module-type-aliases"]}, {"file": "packages/internal/docs/static/.nojekyll", "hash": "3244421341483603138"}, {"file": "packages/internal/docs/static/img/cicd-diagram-v4-dark.png", "hash": "4067027139862654953"}, {"file": "packages/internal/docs/static/img/cicd-diagram-v4.png", "hash": "884098609196423516"}, {"file": "packages/internal/docs/static/img/favicon.png", "hash": "3105044335528016683"}, {"file": "packages/internal/docs/static/img/features/assets/file.png", "hash": "17025367576921514292"}, {"file": "packages/internal/docs/static/img/features/assets/upload-form.png", "hash": "6783153595208275278"}, {"file": "packages/internal/docs/static/img/features/auth/2fa.png", "hash": "11673601634788377148"}, {"file": "packages/internal/docs/static/img/features/auth/login.png", "hash": "11171413725485104935"}, {"file": "packages/internal/docs/static/img/features/auth/password-forgot.png", "hash": "17567515070990350922"}, {"file": "packages/internal/docs/static/img/features/auth/profile.png", "hash": "4867624758475974680"}, {"file": "packages/internal/docs/static/img/features/auth/signup.png", "hash": "2194889330472138012"}, {"file": "packages/internal/docs/static/img/features/auth/verification-email.png", "hash": "9925233043155399216"}, {"file": "packages/internal/docs/static/img/features/contentful/items.png", "hash": "13582116982836726216"}, {"file": "packages/internal/docs/static/img/features/contentful/privacy-policy.png", "hash": "3182447607949234505"}, {"file": "packages/internal/docs/static/img/features/crud/form.png", "hash": "9767691008406437760"}, {"file": "packages/internal/docs/static/img/features/crud/list.png", "hash": "4599397621941101643"}, {"file": "packages/internal/docs/static/img/features/dev-tools/version-matrix.png", "hash": "94270077143084048"}, {"file": "packages/internal/docs/static/img/features/generative-ai/saas-ideas.png", "hash": "10756198426924114695"}, {"file": "packages/internal/docs/static/img/features/multi-tenancy/multi-tenancy1.png", "hash": "14812690171142897178"}, {"file": "packages/internal/docs/static/img/features/multi-tenancy/multi-tenancy2.png", "hash": "4921926153169207228"}, {"file": "packages/internal/docs/static/img/features/multi-tenancy/multi-tenancy3.png", "hash": "16910206727317563391"}, {"file": "packages/internal/docs/static/img/features/notifications/notification-open.png", "hash": "*******************"}, {"file": "packages/internal/docs/static/img/features/notifications/notification-unread.png", "hash": "6577931491165617993"}, {"file": "packages/internal/docs/static/img/features/notifications/notification.png", "hash": "8743959280184922855"}, {"file": "packages/internal/docs/static/img/features/payments/card-form.png", "hash": "1643138296967115884"}, {"file": "packages/internal/docs/static/img/features/payments/payment-form.png", "hash": "*******************"}, {"file": "packages/internal/docs/static/img/features/subscriptions/plans.png", "hash": "15478066332953103325"}, {"file": "packages/internal/docs/static/img/features/subscriptions/subscriptions.png", "hash": "1109495362393224702"}, {"file": "packages/internal/docs/static/img/features/subscriptions/transaction-history.png", "hash": "10975031445776362787"}, {"file": "packages/internal/docs/static/img/guides/cicd-slack-notifications/aws-chatbot-configure-new-client.png", "hash": "17188341289124477809"}, {"file": "packages/internal/docs/static/img/guides/cicd-slack-notifications/aws-chatbot-configured-client.png", "hash": "2839649587330134444"}, {"file": "packages/internal/docs/static/img/guides/cicd-slack-notifications/aws-chatbot-create-channel.png", "hash": "13958937501100147755"}, {"file": "packages/internal/docs/static/img/guides/cicd-slack-notifications/aws-chatbot-select-slack.png", "hash": "11049148065584969863"}, {"file": "packages/internal/docs/static/img/guides/cicd-slack-notifications/aws-chatbot-slack-authorization.png", "hash": "12028983386090232344"}, {"file": "packages/internal/docs/static/img/guides/cicd-slack-notifications/notify-settings.png", "hash": "3846626584306315536"}, {"file": "packages/internal/docs/static/img/guides/cicd-slack-notifications/notify-target.png", "hash": "16689419998264116773"}, {"file": "packages/internal/docs/static/img/guides/cicd-slack-notifications/notify-triggers.png", "hash": "17311368125194184751"}, {"file": "packages/internal/docs/static/img/initial-setup-mfa-serial-number.png", "hash": "4675418368312298373"}, {"file": "packages/internal/docs/static/img/logo.svg", "hash": "1160159703923746563"}, {"file": "packages/internal/docs/static/img/logoDark.svg", "hash": "660162332022730328"}, {"file": "packages/internal/docs/static/img/subscription-retry-rules.png", "hash": "14952464892858400873"}, {"file": "packages/internal/docs/static/img/system-diagram-v4-dark.png", "hash": "9744104389631733108"}, {"file": "packages/internal/docs/static/img/system-diagram-v4.png", "hash": "4744093163295385830"}, {"file": "packages/internal/docs/static/img/undraw_docusaurus_mountain.svg", "hash": "12580885955753741779"}, {"file": "packages/internal/docs/static/img/undraw_docusaurus_react.svg", "hash": "15724667530732833819"}, {"file": "packages/internal/docs/static/img/undraw_docusaurus_tree.svg", "hash": "9216166069443140769"}, {"file": "packages/internal/docs/static/img/version-matrix.png", "hash": "94270077143084048"}, {"file": "packages/internal/docs/tsconfig.infra.json", "hash": "9672256396962647196"}, {"file": "packages/internal/docs/tsconfig.json", "hash": "10682136784084702633"}, {"file": "packages/internal/docs/tsconfig.spec.json", "hash": "11777218610374105241"}], "webapp-core": [{"file": "packages/webapp-libs/webapp-core/.gitignore", "hash": "3075125998154361740"}, {"file": "packages/webapp-libs/webapp-core/.prettierrc", "hash": "5119092945703334273"}, {"file": "packages/webapp-libs/webapp-core/components.json", "hash": "291293181511859688"}, {"file": "packages/webapp-libs/webapp-core/eslint.config.js", "hash": "6915687371126497891", "deps": ["npm:@eslint/compat", "npm:@eslint/eslintrc", "npm:@nx/react", "npm:eslint-plugin-formatjs", "npm:eslint-plugin-react", "npm:eslint-plugin-react-hooks", "npm:eslint-plugin-testing-library", "npm:../../../eslint.config.js"]}, {"file": "packages/webapp-libs/webapp-core/jest.config.ts", "hash": "14563126454067529296"}, {"file": "packages/webapp-libs/webapp-core/package.json", "hash": "7521725831521409325"}, {"file": "packages/webapp-libs/webapp-core/project.json", "hash": "4151975662197410024"}, {"file": "packages/webapp-libs/webapp-core/sonar-project.properties", "hash": "5928625347388478705"}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/backButton/backButton.component.tsx", "hash": "6335047874091306925", "deps": ["npm:lucide-react", "npm:react", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/backButton/backButton.stories.tsx", "hash": "11809159875480239893", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/backButton/index.ts", "hash": "2506680267016707793"}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/button/__tests__/button.component.spec.tsx", "hash": "7432467223976716077", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/button/button.component.tsx", "hash": "11700654674757256194", "deps": ["npm:class-variance-authority", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/button/button.stories.tsx", "hash": "6535840729603491379", "deps": ["npm:@iconify-icons/ion/pencil-sharp", "npm:@storybook/addon-actions", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/button/button.types.ts", "hash": "10253535502452724344", "deps": ["npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/button/button.utils.tsx", "hash": "18430529923515514104", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/button/index.ts", "hash": "7834264022657840935"}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/index.ts", "hash": "17534286632841241924"}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/link/__tests__/link.component.spec.tsx", "hash": "6103232909261138303", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/link/index.ts", "hash": "13833648996222067995"}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/link/link.component.tsx", "hash": "15779978992082447303", "deps": ["npm:ramda", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/link/link.stories.tsx", "hash": "12246582261701107300", "deps": ["npm:@storybook/addon-actions", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/buttons/link/link.utils.ts", "hash": "7553560685789574509"}, {"file": "packages/webapp-libs/webapp-core/src/components/confirmDialog/__tests__/confirmDialog.component.spec.tsx", "hash": "3343890286928307194", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-core/src/components/confirmDialog/confirmDialog.component.tsx", "hash": "11938311408387060169", "deps": ["npm:class-variance-authority", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/components/confirmDialog/confirmDialog.stories.tsx", "hash": "15731345953062233550", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/confirmDialog/index.ts", "hash": "13751379998755504093"}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/formattedDate/__tests__/formattedDate.component.spec.tsx", "hash": "1069281029820169027", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/formattedDate/formattedDate.component.tsx", "hash": "6944664420735627569", "deps": ["npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/formattedDate/formattedDate.stories.tsx", "hash": "17815492461128532157", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/formattedDate/index.ts", "hash": "15521439854031886890"}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/index.ts", "hash": "4633204749501980753"}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/relativeDate/__tests__/relativeDate.component.spec.tsx", "hash": "17142019709382668577", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/relativeDate/index.ts", "hash": "17134855682071282769"}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/relativeDate/relativeDate.component.tsx", "hash": "10043843249720894075", "deps": ["npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/relativeDate/relativeDate.constants.ts", "hash": "16980069916424713758"}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/relativeDate/relativeDate.fixtures.ts", "hash": "2186518110556709877"}, {"file": "packages/webapp-libs/webapp-core/src/components/dateTime/relativeDate/relativeDate.stories.tsx", "hash": "8074175189844615521", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/emptyState/__tests__/emptyState.component.spec.tsx", "hash": "12631597774778258650", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/emptyState/emptyState.component.tsx", "hash": "11675367727357630715", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/emptyState/emptyState.stories.tsx", "hash": "3214358458493396881", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/emptyState/index.ts", "hash": "5422600534482333790"}, {"file": "packages/webapp-libs/webapp-core/src/components/fileSize/__tests__/fileSize.component.spec.tsx", "hash": "17968165245109750737", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/fileSize/fileSize.component.tsx", "hash": "7276055698047369620"}, {"file": "packages/webapp-libs/webapp-core/src/components/fileSize/fileSize.hooks.ts", "hash": "15484884832331991352", "deps": ["npm:react-intl", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/fileSize/fileSize.stories.tsx", "hash": "8839097666765737366", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/fileSize/index.ts", "hash": "3883583991075470093"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/checkbox/__tests__/checkbox.component.spec.tsx", "hash": "6844505002576404615", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/checkbox/checkbox.component.tsx", "hash": "13683763349922470957", "deps": ["npm:@radix-ui/react-checkbox", "npm:lucide-react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/checkbox/checkbox.stories.tsx", "hash": "6804506111594196523", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/checkbox/index.ts", "hash": "14931575877305168739"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/dropzone/__tests__/dropzone.component.spec.tsx", "hash": "17982287632421831008", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/dropzone/dropzone.component.tsx", "hash": "13596177137120482404", "deps": ["npm:lucide-react", "npm:react", "npm:react-dropzone", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/dropzone/dropzone.hooks.ts", "hash": "7608256178827982223", "deps": ["npm:react", "npm:react-dropzone", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/dropzone/dropzone.stories.tsx", "hash": "5026432153904562347", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/dropzone/dropzone.types.ts", "hash": "15182848541608445643"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/dropzone/index.ts", "hash": "15918313606016695933"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/__tests__/form.component.spec.tsx", "hash": "9934129198691865622", "deps": ["npm:@testing-library/react", "npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/form.component.tsx", "hash": "2255208177698683786", "deps": ["npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formControl/formControl.component.tsx", "hash": "15652232891486155371", "deps": ["npm:@radix-ui/react-slot", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formControl/index.ts", "hash": "4099353028101130509"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formDescription/formDescription.component.tsx", "hash": "11548702782417876268", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formDescription/index.ts", "hash": "4463626968263350887"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formField/formField.component.tsx", "hash": "9513289738268235119", "deps": ["npm:react", "npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formField/index.ts", "hash": "18296390497895867611"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formItem/formItem.component.tsx", "hash": "15466500504261692854", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formItem/index.ts", "hash": "5163816759296735588"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formLabel/formLabel.component.tsx", "hash": "13910908895567585360", "deps": ["npm:@radix-ui/react-label", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formLabel/index.ts", "hash": "11996889917801115626"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formMessage/formMessage.component.tsx", "hash": "7184340089845561033", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/formMessage/index.ts", "hash": "3354458127228138859"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/index.ts", "hash": "685487760243428516"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/useFormField/index.ts", "hash": "8321144621836421357"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/form/useFormField/useFormField.hook.ts", "hash": "4308139088774660843", "deps": ["npm:react", "npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/index.ts", "hash": "72802331693939264"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/input/__tests__/input.component.spec.tsx", "hash": "1869168689516369646", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/input/index.ts", "hash": "12169706402284252870"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/input/input.component.tsx", "hash": "3432046900643191127", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/input/input.stories.tsx", "hash": "16215408052918302502", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/input/input.types.ts", "hash": "11761098592429956441", "deps": ["npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/radioButton/__tests__/radioButton.component.spec.tsx", "hash": "14457995419777123018", "deps": ["npm:@testing-library/react", "npm:ramda", "npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/radioButton/index.ts", "hash": "7337154334622691100"}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/radioButton/radioButton.component.tsx", "hash": "12334242388368087380", "deps": ["npm:class-variance-authority", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/radioButton/radioButton.stories.tsx", "hash": "10405184462182208769", "deps": ["npm:@storybook/react", "npm:react-hook-form"]}, {"file": "packages/webapp-libs/webapp-core/src/components/forms/radioButton/radioButton.styles.ts", "hash": "6158196443224502148", "deps": ["npm:class-variance-authority"]}, {"file": "packages/webapp-libs/webapp-core/src/components/gtm/GTM.component.tsx", "hash": "6245280705446532448", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/gtm/index.ts", "hash": "1289131818879527165"}, {"file": "packages/webapp-libs/webapp-core/src/components/hiddenOnPlatform/__tests__/hiddenOnPlatform.component.spec.tsx", "hash": "3133215522810933249", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/hiddenOnPlatform/hiddenOnPlatform.component.tsx", "hash": "936520368348144817", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/hiddenOnPlatform/index.ts", "hash": "14396388271581984961"}, {"file": "packages/webapp-libs/webapp-core/src/components/icons/icon/icon.component.tsx", "hash": "2775398183137236151", "deps": ["npm:@iconify/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/icons/icon/icon.stories.tsx", "hash": "2851102208476778768", "deps": ["npm:@iconify-icons/ion/close-outline", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/icons/icon/index.ts", "hash": "14197642615837849563"}, {"file": "packages/webapp-libs/webapp-core/src/components/icons/index.ts", "hash": "15836822026367991313"}, {"file": "packages/webapp-libs/webapp-core/src/components/markdownPage/__tests__/markdownPage.component.spec.tsx", "hash": "8854712964770064082", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/markdownPage/index.ts", "hash": "9561232738590104918"}, {"file": "packages/webapp-libs/webapp-core/src/components/markdownPage/markdownPage.component.tsx", "hash": "16656380548585027105", "deps": ["npm:react-markdown"]}, {"file": "packages/webapp-libs/webapp-core/src/components/markdownPage/markdownPage.stories.tsx", "hash": "6447584781286931209", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pageHeadline/__tests__/pageHeadline.component.spec.tsx", "hash": "12314101612312892269", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pageHeadline/index.ts", "hash": "9099121547611891088"}, {"file": "packages/webapp-libs/webapp-core/src/components/pageHeadline/pageHeadline.component.tsx", "hash": "12262915381025941220", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pageLayout/__tests__/pageLayout.component.spec.tsx", "hash": "17424773667598894421", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pageLayout/index.ts", "hash": "2072700493785483349"}, {"file": "packages/webapp-libs/webapp-core/src/components/pageLayout/pageLayout.component.tsx", "hash": "10241277043950318776"}, {"file": "packages/webapp-libs/webapp-core/src/components/pagedPagination/__tests__/pagedPagination.component.spec.tsx", "hash": "15086178590691754724", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pagedPagination/index.ts", "hash": "5585229820803912637"}, {"file": "packages/webapp-libs/webapp-core/src/components/pagedPagination/pagedPagination.component.tsx", "hash": "12993242985070206334", "deps": ["npm:lucide-react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pagedPagination/pagedPagination.stories.tsx", "hash": "1765455196878938208", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pagination/__tests__/pagination.component.spec.tsx", "hash": "5197114270682950869", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pagination/index.ts", "hash": "1022089384759134217"}, {"file": "packages/webapp-libs/webapp-core/src/components/pagination/pagination.component.tsx", "hash": "6849065342684989322", "deps": ["npm:lucide-react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/pagination/pagination.stories.tsx", "hash": "2048197554483010977", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/portal/index.ts", "hash": "6500985327111764559"}, {"file": "packages/webapp-libs/webapp-core/src/components/portal/portal.component.tsx", "hash": "17026962433965676917", "deps": ["npm:react", "npm:react-dom"]}, {"file": "packages/webapp-libs/webapp-core/src/components/table/index.ts", "hash": "7693674693346323594"}, {"file": "packages/webapp-libs/webapp-core/src/components/table/table.constants.ts", "hash": "866301803632520995"}, {"file": "packages/webapp-libs/webapp-core/src/components/table/tableFooter/__tests__/tableFooter.component.spec.tsx", "hash": "17172857441537751187", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-core/src/components/table/tableFooter/index.ts", "hash": "17418278207421765186"}, {"file": "packages/webapp-libs/webapp-core/src/components/table/tableFooter/tableFooter.component.tsx", "hash": "11590644806167444304", "deps": ["npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/components/table/tableFooter/tableFooter.stories.tsx", "hash": "7665466889040743546", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/typography/__tests__/typography.spec.tsx", "hash": "13361329533618077685", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/typography/index.ts", "hash": "8676851206832036758"}, {"file": "packages/webapp-libs/webapp-core/src/components/typography/typography.stories.tsx", "hash": "7680547753894415693", "deps": ["npm:@storybook/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/typography/typography.tsx", "hash": "7601181865375749300", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/alert-dialog.stories.tsx", "hash": "13137801277440836143", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/alert.stories.tsx", "hash": "8368779870626879890", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/avatar.stories.tsx", "hash": "6832079159664053369", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/card.stories.tsx", "hash": "11612879059199799322", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/dialog.stories.tsx", "hash": "*******************", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/dropdown-menu.stories.tsx", "hash": "16465269819857956855", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/popover.stories.tsx", "hash": "15132860557597637219", "deps": ["npm:@radix-ui/react-popover", "npm:@storybook/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/radio-group.stories.tsx", "hash": "3082014738747397703", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/select.stories.tsx", "hash": "10425086098366979221", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/separator.stories.tsx", "hash": "634486920923230433", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/skeleton.stories.tsx", "hash": "3758532759464910939", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/table.stories.tsx", "hash": "5031456972078830305", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/tabs.stories.tsx", "hash": "16125826243165738734", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__stories__/tooltip.stories.tsx", "hash": "8593631604674636241", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/alert-dialog.component.spec.tsx", "hash": "3491780954854307477", "deps": ["npm:@testing-library/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/alert.component.spec.tsx", "hash": "17651415541523747729", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/avatar.component.spec.tsx", "hash": "209951271861142238", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/card.component.spec.tsx", "hash": "8891970027757320953", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/dialog.component.spec.tsx", "hash": "14881406995107614361", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/dropdown-menu.component.spec.tsx", "hash": "16684848834105689194", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/popover.component.spec.tsx", "hash": "6257185595059910956", "deps": ["npm:@radix-ui/react-popover", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/select.component.spec.tsx", "hash": "11035994344528632439", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/separator.component.spec.tsx", "hash": "12812467879683401404", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/skeleton.component.spec.tsx", "hash": "18108023960003721043", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/table.component.spec.tsx", "hash": "5504791974677166052", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/tabs.component.spec.tsx", "hash": "16033449162749504302", "deps": ["npm:@radix-ui/react-tabs/dist", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/__tests__/tooltip.component.spec.tsx", "hash": "4843285595158752992", "deps": ["npm:@testing-library/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/alert-dialog.tsx", "hash": "5755389812229407361", "deps": ["npm:@radix-ui/react-alert-dialog", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/alert.tsx", "hash": "11282175611287403970", "deps": ["npm:class-variance-authority", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/avatar.tsx", "hash": "18339491626591784405", "deps": ["npm:@radix-ui/react-avatar", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/button.tsx", "hash": "15922146278562092346", "deps": ["npm:@radix-ui/react-slot", "npm:class-variance-authority", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/card.tsx", "hash": "7477182988840737219", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/dialog.tsx", "hash": "10052725489287783271", "deps": ["npm:@radix-ui/react-dialog", "npm:lucide-react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/dropdown-menu.tsx", "hash": "713198615477798381", "deps": ["npm:@radix-ui/react-dropdown-menu", "npm:lucide-react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/popover.tsx", "hash": "1727836886578725705", "deps": ["npm:@radix-ui/react-popover", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/radio-group.tsx", "hash": "12585976580542914528", "deps": ["npm:react", "npm:@radix-ui/react-radio-group", "npm:lucide-react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/select.tsx", "hash": "*******************", "deps": ["npm:react", "npm:@radix-ui/react-select", "npm:lucide-react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/separator.tsx", "hash": "18226561253628257276", "deps": ["npm:@radix-ui/react-separator", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/skeleton.tsx", "hash": "10750781700646623982"}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/table.tsx", "hash": "5665966550481161015", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/tabs.tsx", "hash": "12732486127119507695", "deps": ["npm:react", "npm:@radix-ui/react-tabs"]}, {"file": "packages/webapp-libs/webapp-core/src/components/ui/tooltip.tsx", "hash": "2079480168202928837", "deps": ["npm:react", "npm:@radix-ui/react-tooltip"]}, {"file": "packages/webapp-libs/webapp-core/src/config/env.d.ts", "hash": "7028735049544840565"}, {"file": "packages/webapp-libs/webapp-core/src/config/env.ts", "hash": "6978479321497437998"}, {"file": "packages/webapp-libs/webapp-core/src/config/i18n.ts", "hash": "1806882406059641254", "deps": ["npm:ramda"]}, {"file": "packages/webapp-libs/webapp-core/src/config/routes.ts", "hash": "15903499544293784073"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/index.ts", "hash": "10198634272711751817"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useGenerateAbsoluteLocalePath/__tests__/useGenerateAbsoluteLocalePath.spec.ts", "hash": "6669535683333941760"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useGenerateAbsoluteLocalePath/index.ts", "hash": "15871599398836818207"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useGenerateAbsoluteLocalePath/useGenerateAbsoluteLocalePath.ts", "hash": "17782202241019373197", "deps": ["npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useGenerateLocalePath/index.ts", "hash": "8126594203015251913"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useGenerateLocalePath/useGenerateLocalePath.ts", "hash": "355295322801779928", "deps": ["npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useLocale/index.ts", "hash": "16700197685099760885"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useLocale/useLocale.hook.ts", "hash": "17880205416752767164", "deps": ["npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useLocales/__tests__/useLocales.hook.spec.tsx", "hash": "14359188377982185890", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useLocales/index.ts", "hash": "6076591776145521319"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useLocales/useLocales.hooks.ts", "hash": "14307716760414019899", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useMappedConnection/__tests__/useMappedConnection.hook.spec.ts", "hash": "2648656010490319638", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useMappedConnection/index.ts", "hash": "15207747412549562588"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useMappedConnection/useMappedConnection.hook.ts", "hash": "7815818734394861568", "deps": ["npm:ramda", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useMediaQuery/__tests__/useMediaQuery.hook.spec.tsx", "hash": "3759196007947098563", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useMediaQuery/index.ts", "hash": "12711917791744206538"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useMediaQuery/useMediaQuery.hook.ts", "hash": "12444682403512029793", "deps": ["npm:ramda"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useOpenState/__tests__/useOpenState.hook.spec.ts", "hash": "3001705400936706896", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useOpenState/index.ts", "hash": "11994585155562463943"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useOpenState/useOpenState.hook.ts", "hash": "13432028866416883476", "deps": ["npm:ramda", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useTheme/__tests__/useTheme.hook.spec.tsx", "hash": "510271573055717250", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useTheme/index.ts", "hash": "3075562711351653112"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useTheme/useTheme.ts", "hash": "14111217704073100300", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useWindowListener/__tests__/useWindowListener.hook.spec.ts", "hash": "3875613908983251977", "deps": ["npm:ramda", "npm:lodash.throttle", "npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useWindowListener/index.ts", "hash": "7043456062137883588"}, {"file": "packages/webapp-libs/webapp-core/src/hooks/useWindowListener/useWindowListener.hook.ts", "hash": "15138098900639133586", "deps": ["npm:lodash.throttle", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/lib/utils.ts", "hash": "4398841608861978635", "deps": ["npm:clsx", "npm:tailwind-merge"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/index.ts", "hash": "362204977049869243"}, {"file": "packages/webapp-libs/webapp-core/src/providers/localesProvider/index.ts", "hash": "11543097360626349941"}, {"file": "packages/webapp-libs/webapp-core/src/providers/localesProvider/localesProvider.context.ts", "hash": "13770081531071516555", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/localesProvider/localesProvider.reducer.ts", "hash": "17691224092050943857"}, {"file": "packages/webapp-libs/webapp-core/src/providers/localesProvider/localesProvider.tsx", "hash": "13184806346494007692", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/localesProvider/localesProvider.types.ts", "hash": "6165989905686686415", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/responsiveThemeProvider/__tests__/responsiveThemeProvider.component.spec.tsx", "hash": "2991295602115232551", "deps": ["npm:@testing-library/react", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/responsiveThemeProvider/index.ts", "hash": "16202960607701944791"}, {"file": "packages/webapp-libs/webapp-core/src/providers/responsiveThemeProvider/responsiveThemeProvider.component.tsx", "hash": "7681871934318164270", "deps": ["npm:react", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/themeProvider/__tests__/themeProvider.spec.tsx", "hash": "18250870088048913527", "deps": ["npm:@testing-library/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/themeProvider/index.ts", "hash": "8268379412133101414"}, {"file": "packages/webapp-libs/webapp-core/src/providers/themeProvider/themeProvider.context.ts", "hash": "4862164786344937129", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/themeProvider/themeProvider.reducer.ts", "hash": "12140142841098140"}, {"file": "packages/webapp-libs/webapp-core/src/providers/themeProvider/themeProvider.tsx", "hash": "16917523108591722504", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/themeProvider/themeProvider.types.ts", "hash": "5813035915333920309", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/providers/themeProvider/themeProvider.utils.ts", "hash": "17786131654344692814"}, {"file": "packages/webapp-libs/webapp-core/src/services/analytics/__tests__/analytics.spec.ts", "hash": "17815121086735571138"}, {"file": "packages/webapp-libs/webapp-core/src/services/analytics/analytics.ts", "hash": "3326244485640647569"}, {"file": "packages/webapp-libs/webapp-core/src/services/analytics/index.ts", "hash": "16474747928753636375"}, {"file": "packages/webapp-libs/webapp-core/src/tests/mocks/icons.tsx", "hash": "6938315243216468031"}, {"file": "packages/webapp-libs/webapp-core/src/tests/mocks/pointerEvent.ts", "hash": "16023429251739816139"}, {"file": "packages/webapp-libs/webapp-core/src/tests/mocks/reactIntl.tsx", "hash": "16172625133826154524", "deps": ["npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/tests/setupTests.ts", "hash": "15712303143145798642", "deps": ["npm:@testing-library/jest-dom", "npm:core-js/stable", "npm:isomorphic-fetch", "npm:mockdate", "npm:ramda", "npm:regenerator-runtime/runtime", "npm:resize-observer-polyfill"]}, {"file": "packages/webapp-libs/webapp-core/src/tests/utils/match.ts", "hash": "11789148194689813217"}, {"file": "packages/webapp-libs/webapp-core/src/tests/utils/rendering.tsx", "hash": "10516824267333883518", "deps": ["npm:@testing-library/dom", "npm:@testing-library/react", "npm:react", "npm:react-helmet-async", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-core/src/theme/color.ts", "hash": "7047466090139963422"}, {"file": "packages/webapp-libs/webapp-core/src/theme/font.ts", "hash": "15953160380724251187"}, {"file": "packages/webapp-libs/webapp-core/src/theme/index.ts", "hash": "1957329387862024172", "deps": ["npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/theme/media.ts", "hash": "313662703146052642", "deps": ["npm:ramda", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/theme/size.ts", "hash": "9820986395191991878", "deps": ["npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/theme/typography.ts", "hash": "6730102394816930481", "deps": ["npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/theme/utils/__tests__/colorScale.spec.ts", "hash": "9582414316435389701"}, {"file": "packages/webapp-libs/webapp-core/src/theme/utils/colorScale.ts", "hash": "9750552719985619247", "deps": ["npm:color"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/index.ts", "hash": "2236923144632937490"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/__tests__/toast.component.spec.tsx", "hash": "5277060257438643231", "deps": ["npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/index.ts", "hash": "9590389515107255990"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toast.component.tsx", "hash": "9151591244928942812", "deps": ["npm:@radix-ui/react-toast", "npm:class-variance-authority", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toast.stories.tsx", "hash": "13603206746043420167", "deps": ["npm:@storybook/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toast.styles.ts", "hash": "5565620648745340404", "deps": ["npm:class-variance-authority"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastAction/index.ts", "hash": "11122448182614058663"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastAction/toastAction.component.tsx", "hash": "13729452990412478840", "deps": ["npm:@radix-ui/react-toast", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastClose/index.ts", "hash": "17101800470198486270"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastClose/toastClose.component.tsx", "hash": "8909882644235742134", "deps": ["npm:@radix-ui/react-toast", "npm:lucide-react", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastDescription/index.ts", "hash": "5674304001746870382"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastDescription/toastDescription.component.tsx", "hash": "10963528982366450325", "deps": ["npm:@radix-ui/react-toast", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastTitle/index.ts", "hash": "4000701609730120196"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastTitle/toastTitle.component.tsx", "hash": "3931804898600152276", "deps": ["npm:@radix-ui/react-toast", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastViewport/index.ts", "hash": "15524089627071226855"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toast/toastViewport/viewport.component.tsx", "hash": "11457146277455197551", "deps": ["npm:@radix-ui/react-toast", "npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toastProvider/index.ts", "hash": "14912775967828131149"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toastProvider/toastProvider.context.tsx", "hash": "3643176584070393187", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toastProvider/toastProvider.reducer.ts", "hash": "7687888202018088867"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toastProvider/toastProvider.tsx", "hash": "12536550941187454276", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toastProvider/toastProvider.types.ts", "hash": "13180311212530929697", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/toaster/index.ts", "hash": "14912875010271441902"}, {"file": "packages/webapp-libs/webapp-core/src/toast/toaster/toaster.component.tsx", "hash": "12815498826754243070", "deps": ["npm:@radix-ui/react-toast", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-core/src/toast/useToast/index.ts", "hash": "294443397373918998"}, {"file": "packages/webapp-libs/webapp-core/src/toast/useToast/useToast.hook.ts", "hash": "17883557952233290955", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/translations/en.json", "hash": "15753941481085060538"}, {"file": "packages/webapp-libs/webapp-core/src/translations/pl.json", "hash": "15753941481085060538"}, {"file": "packages/webapp-libs/webapp-core/src/types/eslint.d.ts", "hash": "14186410512245304501"}, {"file": "packages/webapp-libs/webapp-core/src/types/styled.d.ts", "hash": "14401203734933869843", "deps": ["npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-core/src/utils/__tests__/asyncComponent.spec.tsx", "hash": "17614918835783341107", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-core/src/utils/__tests__/path.spec.ts", "hash": "14687180565056771716"}, {"file": "packages/webapp-libs/webapp-core/src/utils/assertUnreachable.tsx", "hash": "15486971702996125911"}, {"file": "packages/webapp-libs/webapp-core/src/utils/asyncComponent.tsx", "hash": "655336105484637736", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-core/src/utils/graphql.ts", "hash": "7439075624104081661"}, {"file": "packages/webapp-libs/webapp-core/src/utils/index.ts", "hash": "911672369878113762"}, {"file": "packages/webapp-libs/webapp-core/src/utils/object.ts", "hash": "3641334171025069508", "deps": ["npm:@supercharge/strings"]}, {"file": "packages/webapp-libs/webapp-core/src/utils/path.ts", "hash": "773004239894578875", "deps": ["npm:ramda"]}, {"file": "packages/webapp-libs/webapp-core/src/utils/reportError.ts", "hash": "16406085370029095900", "deps": ["npm:@sentry/react"]}, {"file": "packages/webapp-libs/webapp-core/src/utils/storybook.tsx", "hash": "5862579578839256748", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-core/src/utils/types.ts", "hash": "18237436108472473788"}, {"file": "packages/webapp-libs/webapp-core/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-core/tsconfig.lib.json", "hash": "16389780312870552074"}, {"file": "packages/webapp-libs/webapp-core/tsconfig.spec.json", "hash": "13067199624866918776"}], "backend": [{"file": "packages/backend/.coveragerc", "hash": "17801516584311622610"}, {"file": "packages/backend/.dockerignore", "hash": "5850250744641299836"}, {"file": "packages/backend/.env.shared", "hash": "900059411010596057"}, {"file": "packages/backend/.gitignore", "hash": "16242772843058600191"}, {"file": "packages/backend/.test.env", "hash": "15013038491948120437"}, {"file": "packages/backend/Dockerfile", "hash": "12915175023260453100"}, {"file": "packages/backend/README.md", "hash": "15241117536693558627"}, {"file": "packages/backend/apps/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/content/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/content/admin.py", "hash": "8002251754445762243"}, {"file": "packages/backend/apps/content/apps.py", "hash": "1977209023923477667"}, {"file": "packages/backend/apps/content/management/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/content/management/commands/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/content/management/commands/contentful_sync.py", "hash": "15000314903055951960"}, {"file": "packages/backend/apps/content/migrations/0001_initial.py", "hash": "15601273084521875574"}, {"file": "packages/backend/apps/content/migrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/content/models.py", "hash": "3375329499599903510"}, {"file": "packages/backend/apps/content/serializers.py", "hash": "13175526583188591555"}, {"file": "packages/backend/apps/content/tasks.py", "hash": "17089999708297294389"}, {"file": "packages/backend/apps/content/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/content/tests/factories.py", "hash": "674076996776895984"}, {"file": "packages/backend/apps/content/tests/fixtures.py", "hash": "2887781169321957668"}, {"file": "packages/backend/apps/content/urls.py", "hash": "12647637375275546020"}, {"file": "packages/backend/apps/content/views.py", "hash": "1514000650226513845"}, {"file": "packages/backend/apps/demo/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/demo/admin.py", "hash": "9707152003099577022"}, {"file": "packages/backend/apps/demo/apps.py", "hash": "3393149563641017464"}, {"file": "packages/backend/apps/demo/constants.py", "hash": "15231223805258533775"}, {"file": "packages/backend/apps/demo/migrations/0001_initial.py", "hash": "2025958804045191443"}, {"file": "packages/backend/apps/demo/migrations/0002_initial.py", "hash": "11818914858562895155"}, {"file": "packages/backend/apps/demo/migrations/0003_cruddemoitem_tenant.py", "hash": "9999964481994553264"}, {"file": "packages/backend/apps/demo/migrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/demo/models.py", "hash": "15798619476070632843"}, {"file": "packages/backend/apps/demo/notifications.py", "hash": "16089479929105426732"}, {"file": "packages/backend/apps/demo/schema.py", "hash": "4199671522422818439"}, {"file": "packages/backend/apps/demo/serializers.py", "hash": "13444607592013184978"}, {"file": "packages/backend/apps/demo/signals.py", "hash": "16597911962067126318"}, {"file": "packages/backend/apps/demo/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/demo/tests/factories.py", "hash": "14927317202738908986"}, {"file": "packages/backend/apps/demo/tests/fixtures.py", "hash": "1407047604714806986"}, {"file": "packages/backend/apps/demo/tests/test_schema.py", "hash": "5739644457972120362"}, {"file": "packages/backend/apps/demo/urls.py", "hash": "8799753601920673918"}, {"file": "packages/backend/apps/finances/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/finances/admin.py", "hash": "1737904898907073893"}, {"file": "packages/backend/apps/finances/apps.py", "hash": "10908998454858338983"}, {"file": "packages/backend/apps/finances/constants.py", "hash": "15132522718291320251"}, {"file": "packages/backend/apps/finances/email_serializers.py", "hash": "6627481402267741405"}, {"file": "packages/backend/apps/finances/exceptions.py", "hash": "4706735391300558588"}, {"file": "packages/backend/apps/finances/management/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/finances/management/commands/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/finances/management/commands/init_subscriptions.py", "hash": "1474119562209237657"}, {"file": "packages/backend/apps/finances/management/commands/stripe_migrate_json_fields.py", "hash": "290444017153862734"}, {"file": "packages/backend/apps/finances/managers.py", "hash": "8941728902589514305"}, {"file": "packages/backend/apps/finances/migrations/0001_initial.py", "hash": "6134525864583346089"}, {"file": "packages/backend/apps/finances/migrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/finances/models.py", "hash": "8850740821732003496"}, {"file": "packages/backend/apps/finances/notifications.py", "hash": "13210682484724020702"}, {"file": "packages/backend/apps/finances/schema.py", "hash": "6166591170296462283"}, {"file": "packages/backend/apps/finances/serializers.py", "hash": "506641330288606733"}, {"file": "packages/backend/apps/finances/services/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/finances/services/charges.py", "hash": "346383295972014906"}, {"file": "packages/backend/apps/finances/services/customers.py", "hash": "11600514357914545717"}, {"file": "packages/backend/apps/finances/services/subscriptions.py", "hash": "6997433076960351969"}, {"file": "packages/backend/apps/finances/signals.py", "hash": "4379724620936278666"}, {"file": "packages/backend/apps/finances/static/djstripe/jquery.modal.min.css", "hash": "15890804129064671698"}, {"file": "packages/backend/apps/finances/static/djstripe/jquery.modal.min.js", "hash": "5499804586946994154", "deps": [["npm:j<PERSON>y", "dynamic"]]}, {"file": "packages/backend/apps/finances/static/djstripe/refund.js", "hash": "14068835616344463942"}, {"file": "packages/backend/apps/finances/templates/djstripe/charge/admin/change_form.html", "hash": "11827483250371289045"}, {"file": "packages/backend/apps/finances/templates/djstripe/paymentintent/admin/change_form.html", "hash": "10414462067244735673"}, {"file": "packages/backend/apps/finances/templates/refund.html", "hash": "13781942931739301063"}, {"file": "packages/backend/apps/finances/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/finances/tests/factories.py", "hash": "8703066228786824582"}, {"file": "packages/backend/apps/finances/tests/fixtures.py", "hash": "3874918966472437446"}, {"file": "packages/backend/apps/finances/tests/test_admin_views.py", "hash": "4589751092454543365"}, {"file": "packages/backend/apps/finances/tests/test_schema.py", "hash": "99590875753981436"}, {"file": "packages/backend/apps/finances/tests/test_webhooks.py", "hash": "15803585713253177727"}, {"file": "packages/backend/apps/finances/tests/utils.py", "hash": "11427369783923621104"}, {"file": "packages/backend/apps/finances/urls.py", "hash": "16043207679002369294"}, {"file": "packages/backend/apps/finances/urls_admin.py", "hash": "14826815610597352275"}, {"file": "packages/backend/apps/finances/utils.py", "hash": "4347541240403761232"}, {"file": "packages/backend/apps/finances/views_admin.py", "hash": "4896103229056947098"}, {"file": "packages/backend/apps/finances/webhooks.py", "hash": "13074784829405727889"}, {"file": "packages/backend/apps/integrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/integrations/apps.py", "hash": "13562470937045522062"}, {"file": "packages/backend/apps/integrations/migrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/integrations/openai/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/integrations/openai/client.py", "hash": "6933241105409232392"}, {"file": "packages/backend/apps/integrations/openai/exceptions.py", "hash": "769339008570170465"}, {"file": "packages/backend/apps/integrations/openai/types.py", "hash": "15351399639984071433"}, {"file": "packages/backend/apps/integrations/schema.py", "hash": "8151756670792777590"}, {"file": "packages/backend/apps/integrations/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/integrations/tests/factories.py", "hash": "17775393332531404197"}, {"file": "packages/backend/apps/integrations/tests/fixtures.py", "hash": "3937383165337215002"}, {"file": "packages/backend/apps/integrations/tests/test_clients.py", "hash": "9104373943454104782"}, {"file": "packages/backend/apps/integrations/tests/test_schema.py", "hash": "5003234974013628155"}, {"file": "packages/backend/apps/multitenancy/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/multitenancy/admin.py", "hash": "11138292651062090532"}, {"file": "packages/backend/apps/multitenancy/apps.py", "hash": "6577074887304800651"}, {"file": "packages/backend/apps/multitenancy/constants.py", "hash": "11096284252560655886"}, {"file": "packages/backend/apps/multitenancy/email_serializers.py", "hash": "6862805121879642498"}, {"file": "packages/backend/apps/multitenancy/managers.py", "hash": "940228824679837969"}, {"file": "packages/backend/apps/multitenancy/middleware.py", "hash": "13625635093321628567"}, {"file": "packages/backend/apps/multitenancy/migrations/0001_initial.py", "hash": "5403945997141228530"}, {"file": "packages/backend/apps/multitenancy/migrations/0002_alter_tenant_name.py", "hash": "9321047992287243740"}, {"file": "packages/backend/apps/multitenancy/migrations/0003_alter_tenantmembership_unique_together_and_more.py", "hash": "8978845835029232939"}, {"file": "packages/backend/apps/multitenancy/migrations/0004_auto_20240318_1003.py", "hash": "15441832604502247678"}, {"file": "packages/backend/apps/multitenancy/migrations/0005_tenant_billing_email.py", "hash": "13799263989828886224"}, {"file": "packages/backend/apps/multitenancy/migrations/0006_alter_tenantmembership_role.py", "hash": "282512923455473984"}, {"file": "packages/backend/apps/multitenancy/migrations/0007_tenantmembership_creator.py", "hash": "5780488056831084225"}, {"file": "packages/backend/apps/multitenancy/migrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/multitenancy/models.py", "hash": "560476717583203395"}, {"file": "packages/backend/apps/multitenancy/notifications.py", "hash": "13568980663015527314"}, {"file": "packages/backend/apps/multitenancy/pipeline.py", "hash": "1178130914529931986"}, {"file": "packages/backend/apps/multitenancy/schema.py", "hash": "9990628912977045307"}, {"file": "packages/backend/apps/multitenancy/serializers.py", "hash": "3608125200793696017"}, {"file": "packages/backend/apps/multitenancy/services/membership.py", "hash": "5274010133037477237"}, {"file": "packages/backend/apps/multitenancy/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/multitenancy/tests/factories.py", "hash": "4019563329161675584"}, {"file": "packages/backend/apps/multitenancy/tests/fixtures.py", "hash": "6908091526900112064"}, {"file": "packages/backend/apps/multitenancy/tests/test_middleware.py", "hash": "7798496411389863213"}, {"file": "packages/backend/apps/multitenancy/tests/test_models.py", "hash": "1702354926976949901"}, {"file": "packages/backend/apps/multitenancy/tests/test_pipeline.py", "hash": "8234803320072268395"}, {"file": "packages/backend/apps/multitenancy/tests/test_schema.py", "hash": "13372000280631909355"}, {"file": "packages/backend/apps/multitenancy/tests/test_serializers.py", "hash": "14837226895295857233"}, {"file": "packages/backend/apps/multitenancy/tests/test_tokens.py", "hash": "598987313553886090"}, {"file": "packages/backend/apps/multitenancy/tokens.py", "hash": "17312780126210293532"}, {"file": "packages/backend/apps/notifications/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/notifications/admin.py", "hash": "2005374711296158134"}, {"file": "packages/backend/apps/notifications/apps.py", "hash": "9665877873033792965"}, {"file": "packages/backend/apps/notifications/constants.py", "hash": "9588287373970512892"}, {"file": "packages/backend/apps/notifications/exceptions.py", "hash": "11817425373746864086"}, {"file": "packages/backend/apps/notifications/managers.py", "hash": "5940151677910352158"}, {"file": "packages/backend/apps/notifications/migrations/0001_initial.py", "hash": "17130098770119186832"}, {"file": "packages/backend/apps/notifications/migrations/0002_notification_user.py", "hash": "2414553819871430611"}, {"file": "packages/backend/apps/notifications/migrations/0003_notification_issuer.py", "hash": "12277873758742748144"}, {"file": "packages/backend/apps/notifications/migrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/notifications/models.py", "hash": "5543469973773333476"}, {"file": "packages/backend/apps/notifications/schema.py", "hash": "6876452703483295610"}, {"file": "packages/backend/apps/notifications/sender.py", "hash": "4451242475624588658"}, {"file": "packages/backend/apps/notifications/serializers.py", "hash": "16170514706025609756"}, {"file": "packages/backend/apps/notifications/services.py", "hash": "4002366261263298571"}, {"file": "packages/backend/apps/notifications/signals.py", "hash": "11349717906403084388"}, {"file": "packages/backend/apps/notifications/strategies.py", "hash": "15101875277360461232"}, {"file": "packages/backend/apps/notifications/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/notifications/tests/factories.py", "hash": "3377724182917725301"}, {"file": "packages/backend/apps/notifications/tests/fixtures.py", "hash": "11987592074529408085"}, {"file": "packages/backend/apps/notifications/tests/test_schema/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/notifications/tests/test_schema/test_all_notifications.py", "hash": "5282724552182568104"}, {"file": "packages/backend/apps/notifications/tests/test_schema/test_has_unread_notifications.py", "hash": "1303018979052049946"}, {"file": "packages/backend/apps/notifications/tests/test_schema/test_mark_read_all_notifications.py", "hash": "7389661750237309796"}, {"file": "packages/backend/apps/notifications/tests/test_schema/test_update_notification.py", "hash": "6560855210096291236"}, {"file": "packages/backend/apps/users/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/users/admin.py", "hash": "3324670993551194449"}, {"file": "packages/backend/apps/users/apps.py", "hash": "17763793317447323442"}, {"file": "packages/backend/apps/users/authentication.py", "hash": "17151492105413039525"}, {"file": "packages/backend/apps/users/constants.py", "hash": "10556319851719277137"}, {"file": "packages/backend/apps/users/email_serializers.py", "hash": "9840198509257699088"}, {"file": "packages/backend/apps/users/exceptions.py", "hash": "9826952451995987537"}, {"file": "packages/backend/apps/users/jwt.py", "hash": "11773856074349037135"}, {"file": "packages/backend/apps/users/management/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/users/management/commands/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/users/management/commands/init_customers_plans.py", "hash": "18111877746668565894"}, {"file": "packages/backend/apps/users/migrations/0001_initial.py", "hash": "10800601654315531373"}, {"file": "packages/backend/apps/users/migrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/users/models.py", "hash": "11796256990691909357"}, {"file": "packages/backend/apps/users/notifications.py", "hash": "14916241428362251191"}, {"file": "packages/backend/apps/users/schema.py", "hash": "34626809163085502"}, {"file": "packages/backend/apps/users/serializers.py", "hash": "5963679361156234788"}, {"file": "packages/backend/apps/users/services/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/users/services/export/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/users/services/export/constants.py", "hash": "18001717637702516665"}, {"file": "packages/backend/apps/users/services/export/email_serializers.py", "hash": "9483323844139587253"}, {"file": "packages/backend/apps/users/services/export/emails.py", "hash": "552895186560604673"}, {"file": "packages/backend/apps/users/services/export/protocols.py", "hash": "115515236392292883"}, {"file": "packages/backend/apps/users/services/export/services/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/users/services/export/services/export.py", "hash": "10671069426460045014"}, {"file": "packages/backend/apps/users/services/export/services/user.py", "hash": "7898091665402887107"}, {"file": "packages/backend/apps/users/services/otp.py", "hash": "13977859763860603825"}, {"file": "packages/backend/apps/users/services/users.py", "hash": "7456364640290396182"}, {"file": "packages/backend/apps/users/strategy.py", "hash": "2376911597579029482"}, {"file": "packages/backend/apps/users/tasks.py", "hash": "10788934520275027652"}, {"file": "packages/backend/apps/users/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/users/tests/factories.py", "hash": "18061909580880672584"}, {"file": "packages/backend/apps/users/tests/fixtures.py", "hash": "15456089886130026262"}, {"file": "packages/backend/apps/users/tests/test_commands.py", "hash": "8191060111028991305"}, {"file": "packages/backend/apps/users/tests/test_models.py", "hash": "12110757011706560844"}, {"file": "packages/backend/apps/users/tests/test_schema.py", "hash": "4978105862243170032"}, {"file": "packages/backend/apps/users/tests/test_serializers.py", "hash": "3120050793893768022"}, {"file": "packages/backend/apps/users/tests/test_services.py", "hash": "5112740354518346822"}, {"file": "packages/backend/apps/users/tests/test_signals.py", "hash": "2341782613813734938"}, {"file": "packages/backend/apps/users/tests/test_views.py", "hash": "11168132814141283145"}, {"file": "packages/backend/apps/users/tokens.py", "hash": "9019147619191678132"}, {"file": "packages/backend/apps/users/urls.py", "hash": "3181895956865357443"}, {"file": "packages/backend/apps/users/utils.py", "hash": "8600017628503605924"}, {"file": "packages/backend/apps/users/views.py", "hash": "3201904540728937716"}, {"file": "packages/backend/apps/websockets/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/websockets/apps.py", "hash": "16002359460666781317"}, {"file": "packages/backend/apps/websockets/consumers.py", "hash": "9642192716504794515"}, {"file": "packages/backend/apps/websockets/migrations/0001_initial.py", "hash": "11775350683444875806"}, {"file": "packages/backend/apps/websockets/migrations/0002_alter_graphqlsubscription_relay_id.py", "hash": "16585035643843926601"}, {"file": "packages/backend/apps/websockets/migrations/0003_remove_websocketconnection_user_and_more.py", "hash": "8661183185973991975"}, {"file": "packages/backend/apps/websockets/migrations/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/apps/websockets/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/cdk.context.json", "hash": "18286558043377058891"}, {"file": "packages/backend/cdk.json", "hash": "12635046677625749872"}, {"file": "packages/backend/common/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/common/acl/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/common/acl/helpers.py", "hash": "1496105729119710037"}, {"file": "packages/backend/common/acl/policies.py", "hash": "6819449460941401929"}, {"file": "packages/backend/common/decorators.py", "hash": "6896274315525757898"}, {"file": "packages/backend/common/emails.py", "hash": "11158241852312809330"}, {"file": "packages/backend/common/exceptions.py", "hash": "3784222706492584685"}, {"file": "packages/backend/common/graphql/__init__.py", "hash": "8541102661390579678"}, {"file": "packages/backend/common/graphql/acl/__init__.py", "hash": "5178555651093829715"}, {"file": "packages/backend/common/graphql/acl/decorators.py", "hash": "6192795998281263690"}, {"file": "packages/backend/common/graphql/acl/types.py", "hash": "16390068060011580394"}, {"file": "packages/backend/common/graphql/acl/utils.py", "hash": "3256731528460369734"}, {"file": "packages/backend/common/graphql/acl/wrappers.py", "hash": "10763128211208008745"}, {"file": "packages/backend/common/graphql/constants.py", "hash": "13699010367218425590"}, {"file": "packages/backend/common/graphql/exceptions.py", "hash": "17526475958059283823"}, {"file": "packages/backend/common/graphql/field_conversions.py", "hash": "5234142951029686081"}, {"file": "packages/backend/common/graphql/mutations.py", "hash": "12207801804752693730"}, {"file": "packages/backend/common/graphql/pagination/fields.py", "hash": "12934954988713929188"}, {"file": "packages/backend/common/graphql/pagination/helpers.py", "hash": "96361504940875053"}, {"file": "packages/backend/common/graphql/pagination/pagination.py", "hash": "17427355486285766092"}, {"file": "packages/backend/common/graphql/ratelimit.py", "hash": "15186644518596544287"}, {"file": "packages/backend/common/graphql/utils.py", "hash": "3442767293249457585"}, {"file": "packages/backend/common/graphql/views.py", "hash": "8216389125788705970"}, {"file": "packages/backend/common/middleware.py", "hash": "18089108918350999941"}, {"file": "packages/backend/common/models.py", "hash": "10810289509814591174"}, {"file": "packages/backend/common/storages.py", "hash": "6337498507609360434"}, {"file": "packages/backend/common/tasks.py", "hash": "15597889351544464201"}, {"file": "packages/backend/common/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/common/tests/fixtures.py", "hash": "7696673204345923064"}, {"file": "packages/backend/common/tests/hashers.py", "hash": "4415256212824750401"}, {"file": "packages/backend/common/tests/matchers.py", "hash": "7736486780235664615"}, {"file": "packages/backend/common/tests/storages.py", "hash": "6052875166806347701"}, {"file": "packages/backend/common/tests/test_emails.py", "hash": "16614235785770890569"}, {"file": "packages/backend/common/tests/test_graphql/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/common/tests/test_graphql/test_acl.py", "hash": "17996692061075385383"}, {"file": "packages/backend/common/tests/test_tasks.py", "hash": "6889793659929454551"}, {"file": "packages/backend/common/utils.py", "hash": "6534276009933392424"}, {"file": "packages/backend/config/__init__.py", "hash": "15258298655880599798"}, {"file": "packages/backend/config/asgi.py", "hash": "4311648457512105652"}, {"file": "packages/backend/config/celery.py", "hash": "1891857106423844924"}, {"file": "packages/backend/config/gunicorn.py", "hash": "17026226878579391045"}, {"file": "packages/backend/config/hosts.py", "hash": "5152262776073212489"}, {"file": "packages/backend/config/monitoring.py", "hash": "309970589199115447"}, {"file": "packages/backend/config/schema.py", "hash": "9268119887034323597"}, {"file": "packages/backend/config/settings.py", "hash": "12109711266284885769"}, {"file": "packages/backend/config/urls_admin.py", "hash": "1587934529138855524"}, {"file": "packages/backend/config/urls_api.py", "hash": "6630873889123537997"}, {"file": "packages/backend/conftest.py", "hash": "8512337020366828018"}, {"file": "packages/backend/eslint.config.js", "hash": "2277064495508146178", "deps": ["npm:../../eslint.config.js"]}, {"file": "packages/backend/infra/.gitignore", "hash": "1271703533778212942"}, {"file": "packages/backend/infra/main.ts", "hash": "16544274455714575600", "deps": ["npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/backend/infra/stacks/api/index.ts", "hash": "17680043669700111993"}, {"file": "packages/backend/infra/stacks/api/monitoring.ts", "hash": "6946339607468900663", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-cloudwatch", "npm:aws-cdk-lib/aws-logs", "infra-core", "infra-shared"]}, {"file": "packages/backend/infra/stacks/api/names.ts", "hash": "12488519881934317752", "deps": ["infra-core"]}, {"file": "packages/backend/infra/stacks/api/stack.ts", "hash": "16060480830219814653", "deps": ["npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ecs", "npm:aws-cdk-lib/aws-elasticloadbalancingv2", "infra-core", "infra-shared"]}, {"file": "packages/backend/infra/stacks/celeryWorkers/index.ts", "hash": "8884164704016284736"}, {"file": "packages/backend/infra/stacks/celeryWorkers/names.ts", "hash": "1106178229532627609", "deps": ["infra-core"]}, {"file": "packages/backend/infra/stacks/celeryWorkers/stack.ts", "hash": "16379744653365094598", "deps": ["npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ecs", "npm:aws-cdk-lib/aws-iam", "infra-core", "infra-shared", "npm:aws-cdk-lib/aws-ec2", "npm:aws-cdk-lib/aws-logs", "npm:aws-cdk-lib/aws-elasticloadbalancingv2"]}, {"file": "packages/backend/infra/stacks/lib/backendTaskRole.ts", "hash": "9680761732523097801", "deps": ["npm:constructs", "npm:aws-cdk-lib", "infra-core", "npm:aws-cdk-lib/aws-iam", "npm:aws-cdk-lib/aws-s3", "npm:aws-cdk-lib/aws-events", "infra-shared"]}, {"file": "packages/backend/infra/stacks/lib/environment.ts", "hash": "14825522525087241878", "deps": ["infra-core", "infra-shared", "npm:aws-cdk-lib", "npm:constructs"]}, {"file": "packages/backend/infra/stacks/lib/names.ts", "hash": "17749525672876517984", "deps": ["infra-core"]}, {"file": "packages/backend/infra/stacks/lib/secrets.ts", "hash": "9690260268379988316", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-ecs", "npm:aws-cdk-lib/aws-secretsmanager", "infra-shared", "npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/backend/infra/stacks/migrations/index.ts", "hash": "7031234399890303796"}, {"file": "packages/backend/infra/stacks/migrations/stack.ts", "hash": "7133825131199347500", "deps": ["npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ecs", "npm:aws-cdk-lib/aws-stepfunctions-tasks", "npm:aws-cdk-lib/aws-stepfunctions", "npm:aws-cdk-lib/aws-logs", "infra-core", "infra-shared"]}, {"file": "packages/backend/manage.py", "hash": "16891492216457432088"}, {"file": "packages/backend/package.json", "hash": "4914190118675384317", "deps": ["core", "infra-core", "infra-shared", "tools", "webapp-emails"]}, {"file": "packages/backend/pdm.lock", "hash": "5768469562796420361"}, {"file": "packages/backend/pdm.toml", "hash": "13644113005095514202"}, {"file": "packages/backend/project.json", "hash": "5539804827365206356"}, {"file": "packages/backend/pydoc-markdown.yml", "hash": "1806323985695274998"}, {"file": "packages/backend/pyproject.toml", "hash": "17294528841353324993"}, {"file": "packages/backend/scripts/build.js", "hash": "38630265954623819", "deps": ["npm:@aws-sdk/client-sts"]}, {"file": "packages/backend/scripts/buildDocs.js", "hash": "15885237194546580509", "deps": ["npm:fs-extra", "npm:path"]}, {"file": "packages/backend/scripts/copyEmailRenderer.js", "hash": "11650378732108024897", "deps": ["npm:fs-extra", "npm:path"]}, {"file": "packages/backend/scripts/executeRemote.js", "hash": "9467528563674444013", "deps": ["npm:@aws-sdk/client-ecs"]}, {"file": "packages/backend/scripts/lib/runCommand.js", "hash": "10356850394873141624", "deps": ["npm:node:child_process"]}, {"file": "packages/backend/scripts/runtime/build_static.sh", "hash": "7358273084061719940"}, {"file": "packages/backend/scripts/runtime/email/index.js", "hash": "16875764067503898411"}, {"file": "packages/backend/scripts/runtime/install_localstack_fixtures.sh", "hash": "9089055622475514419"}, {"file": "packages/backend/scripts/runtime/run.sh", "hash": "4821054382337865162"}, {"file": "packages/backend/scripts/runtime/run_celery_beat.sh", "hash": "11483764769396253780"}, {"file": "packages/backend/scripts/runtime/run_celery_flower.sh", "hash": "11547645297546057175"}, {"file": "packages/backend/scripts/runtime/run_celery_worker_default.sh", "hash": "4133055086236330809"}, {"file": "packages/backend/scripts/runtime/run_local.sh", "hash": "18107305739955827640"}, {"file": "packages/backend/scripts/runtime/run_local_celery_flower.sh", "hash": "18260029887707014186"}, {"file": "packages/backend/scripts/runtime/run_local_celery_worker_default.sh", "hash": "16055933816959457924"}, {"file": "packages/backend/scripts/runtime/run_migrations.sh", "hash": "2599675731771678452"}, {"file": "packages/backend/scripts/runtime/run_tests.sh", "hash": "5657567201640552649"}, {"file": "packages/backend/scripts/triggerMigrationsJob.js", "hash": "3583165472306918317", "deps": ["npm:@aws-sdk/client-sts", "npm:@aws-sdk/client-sfn"]}, {"file": "packages/backend/secrets.example.json", "hash": "4167006063961069038"}, {"file": "packages/backend/secrets.json", "hash": "4167006063961069038"}, {"file": "packages/backend/setup.cfg", "hash": "2654653104551953702"}, {"file": "packages/backend/sonar-project.properties", "hash": "9205960360703734911"}, {"file": "packages/backend/static/.gitkeep", "hash": "3244421341483603138"}, {"file": "packages/backend/templates/admin/base.html", "hash": "7034438311824615592"}, {"file": "packages/backend/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/tests/aws_fixtures.py", "hash": "8795555373366719670"}, {"file": "packages/backend/tsconfig.infra.json", "hash": "2911187151943493405"}, {"file": "packages/backend/tsconfig.json", "hash": "8482874443211595072"}, {"file": "packages/backend/utils/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/backend/utils/hashid.py", "hash": "1192420279997091020"}], "infra-shared": [{"file": "packages/infra/infra-shared/cdk.json", "hash": "4688804288471580186"}, {"file": "packages/infra/infra-shared/eslint.config.js", "hash": "4176868129069762114", "deps": ["npm:../../../eslint.config.js"]}, {"file": "packages/infra/infra-shared/jest.config.ts", "hash": "16161110234545686359"}, {"file": "packages/infra/infra-shared/package.json", "hash": "16589542219831348563", "deps": ["core", "infra-core"]}, {"file": "packages/infra/infra-shared/project.json", "hash": "12523653951014154963"}, {"file": "packages/infra/infra-shared/src/bootstrap.ts", "hash": "3809932938990299645", "deps": ["npm:aws-cdk-lib"]}, {"file": "packages/infra/infra-shared/src/index.ts", "hash": "7827952613788658581"}, {"file": "packages/infra/infra-shared/src/main.ts", "hash": "801012467505298707", "deps": ["npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/infra/infra-shared/src/patterns/fargateServiceResources.ts", "hash": "1892745395063643484", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ec2", "npm:aws-cdk-lib/aws-ecs", "npm:aws-cdk-lib/aws-ecr", "npm:aws-cdk-lib/aws-elasticloadbalancingv2", "infra-core"]}, {"file": "packages/infra/infra-shared/src/patterns/webAppCloudFrontDistribution.ts", "hash": "8441577726845367948", "deps": ["infra-core", "npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/custom-resources", "npm:aws-cdk-lib/aws-s3", "npm:aws-cdk-lib/aws-lambda", "npm:aws-cdk-lib/aws-cloudfront", "npm:aws-cdk-lib/aws-route53", "npm:aws-cdk-lib/aws-route53-targets", "npm:aws-cdk-lib/aws-s3-deployment", "npm:aws-cdk-lib/aws-cloudfront-origins", "npm:aws-cdk-lib/aws-certificatemanager"]}, {"file": "packages/infra/infra-shared/src/stacks/bootstrap/index.ts", "hash": "13845460569399930176"}, {"file": "packages/infra/infra-shared/src/stacks/bootstrap/stack.ts", "hash": "5654377413292786268", "deps": ["npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-kms", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/ciBackend.ts", "hash": "6713028580248532095", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-codebuild", "npm:aws-cdk-lib/aws-codepipeline-actions", "npm:aws-cdk-lib/aws-codepipeline", "npm:aws-cdk-lib/aws-ecr", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/ciComponents.ts", "hash": "1988101989054554169", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-codebuild", "npm:aws-cdk-lib/aws-codepipeline-actions", "npm:aws-cdk-lib/aws-codepipeline", "infra-core", "npm:aws-cdk-lib/aws-iam"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/ciDocs.ts", "hash": "4880857818355196899", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-codebuild", "npm:aws-cdk-lib/aws-codepipeline", "npm:aws-cdk-lib/aws-codepipeline-actions", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/ciEntrypoint.ts", "hash": "6687721273504690781", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-s3", "npm:aws-cdk-lib/aws-cloudtrail", "npm:aws-cdk-lib/aws-iam", "npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/ciPipeline.ts", "hash": "3167989583956815488", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-codepipeline", "npm:aws-cdk-lib/aws-codepipeline-actions", "npm:aws-cdk-lib/aws-s3", "npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/ciServerless.ts", "hash": "11259719528111349216", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-codebuild", "npm:aws-cdk-lib/aws-codepipeline-actions", "npm:aws-cdk-lib/aws-codepipeline", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/ciUploadVersion.ts", "hash": "798879533334155841", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-codebuild", "npm:aws-cdk-lib/aws-codepipeline-actions", "npm:aws-cdk-lib/aws-codepipeline", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/ciWebApp.ts", "hash": "15668843742450983250", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-codebuild", "npm:aws-cdk-lib/aws-codepipeline-actions", "npm:aws-cdk-lib/aws-codepipeline", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/ci/index.ts", "hash": "9174045881721392672"}, {"file": "packages/infra/infra-shared/src/stacks/ci/stack.ts", "hash": "12014245221232792857", "deps": ["npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ecr", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/components/index.ts", "hash": "17557818537345125236"}, {"file": "packages/infra/infra-shared/src/stacks/components/stack.ts", "hash": "4159275652719251605", "deps": ["npm:aws-cdk-lib", "npm:@aws-cdk/aws-apigatewayv2-alpha", "npm:aws-cdk-lib/aws-events", "npm:aws-cdk-lib/aws-s3", "npm:aws-cdk-lib/aws-cloudfront", "npm:aws-cdk-lib/aws-cloudfront-origins", "npm:aws-cdk-lib/aws-certificatemanager", "npm:aws-cdk-lib/aws-route53", "npm:aws-cdk-lib/aws-route53-targets", "npm:cdk-ec2-key-pair", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/db/index.ts", "hash": "2666419852657859635"}, {"file": "packages/infra/infra-shared/src/stacks/db/mainDatabase.ts", "hash": "4714914372661229842", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-rds", "npm:aws-cdk-lib/aws-ec2", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/db/stack.ts", "hash": "12984100594973376440", "deps": ["npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ec2", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/global/index.ts", "hash": "17913654923507583531"}, {"file": "packages/infra/infra-shared/src/stacks/global/resources/globalBuildSecrets.ts", "hash": "4740919575971003464", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-secretsmanager", "npm:aws-cdk-lib"]}, {"file": "packages/infra/infra-shared/src/stacks/global/resources/globalECR.ts", "hash": "4443670203581403139", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-ecr", "infra-core", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-iam"]}, {"file": "packages/infra/infra-shared/src/stacks/global/resources/index.ts", "hash": "1365031930484573447", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/global/stack.ts", "hash": "6315543456400642893", "deps": ["npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/main/index.ts", "hash": "1825759132007559988"}, {"file": "packages/infra/infra-shared/src/stacks/main/mainCertificates.ts", "hash": "3496564125867007116", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-certificatemanager", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/main/mainEcsCluster.ts", "hash": "1381337689652257411", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ecs", "npm:aws-cdk-lib/aws-ec2", "npm:aws-cdk-lib/aws-elasticloadbalancingv2", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/main/mainKmsKey.ts", "hash": "5280809838528532582", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-kms", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/main/mainLambdaConfig.ts", "hash": "12560146352109903041", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ec2", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/main/mainRedisCluster.ts", "hash": "10227752973402461750", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-elasticache", "npm:aws-cdk-lib/aws-ec2", "infra-core", "npm:aws-cdk-lib"]}, {"file": "packages/infra/infra-shared/src/stacks/main/mainVpc.ts", "hash": "18276121768945580037", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-ec2", "npm:aws-cdk-lib", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/main/stack.ts", "hash": "4916682072930961770", "deps": ["npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/src/stacks/usEastResources/authLambda/index.js", "hash": "16654727435617323432"}, {"file": "packages/infra/infra-shared/src/stacks/usEastResources/index.ts", "hash": "17717357965782433967"}, {"file": "packages/infra/infra-shared/src/stacks/usEastResources/stack.ts", "hash": "1518443417575901041", "deps": ["npm:path", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-ssm", "npm:aws-cdk-lib/aws-lambda", "npm:aws-cdk-lib/aws-iam", "infra-core"]}, {"file": "packages/infra/infra-shared/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/infra/infra-shared/tsconfig.lib.json", "hash": "5554291030212586824"}, {"file": "packages/infra/infra-shared/tsconfig.spec.json", "hash": "9110660443763673899"}], "core": [{"file": "packages/internal/core/package.json", "hash": "6028576318350796070"}, {"file": "packages/internal/core/project.json", "hash": "16472516867078657421"}, {"file": "packages/internal/core/scripts/docker-create-volumes.js", "hash": "18380336967760377933", "deps": ["npm:node:util", "npm:node:child_process"]}, {"file": "packages/internal/core/scripts/get-version.js", "hash": "15827236822949496901", "deps": ["npm:node:child_process"]}, {"file": "packages/internal/core/scripts/lib/runCommand.js", "hash": "15244645740312680305", "deps": ["npm:node:child_process"]}], "tools": [{"file": "packages/internal/tools/eslint.config.js", "hash": "17295180727502154062", "deps": ["npm:../../../eslint.config.js", "npm:@eslint/eslintrc"]}, {"file": "packages/internal/tools/executors.json", "hash": "9180521154899626279"}, {"file": "packages/internal/tools/generators.json", "hash": "9780448122262825021"}, {"file": "packages/internal/tools/jest.config.ts", "hash": "16009310872784698295"}, {"file": "packages/internal/tools/package.json", "hash": "8858155360800643199"}, {"file": "packages/internal/tools/project.json", "hash": "4234393156643385426"}, {"file": "packages/internal/tools/src/executors/setup/executor.spec.ts", "hash": "4756282931244545146"}, {"file": "packages/internal/tools/src/executors/setup/executor.ts", "hash": "15893469596726355435", "deps": ["npm:path", "npm:fs/promises", "npm:@nx/devkit", "npm:ramda"]}, {"file": "packages/internal/tools/src/executors/setup/schema.d.ts", "hash": "6901089821052878659"}, {"file": "packages/internal/tools/src/executors/setup/schema.json", "hash": "9051944264188726885"}, {"file": "packages/internal/tools/src/generators/tools/files/src/index.ts__template__", "hash": "12182040164740174869"}, {"file": "packages/internal/tools/src/generators/tools/generator.spec.ts", "hash": "8793197449124597156", "deps": ["npm:@nx/devkit/testing", "npm:@nx/devkit"]}, {"file": "packages/internal/tools/src/generators/tools/generator.ts", "hash": "9973074060334578581", "deps": ["npm:@nx/devkit", "npm:path"]}, {"file": "packages/internal/tools/src/generators/tools/schema.d.ts", "hash": "3572171971939324800"}, {"file": "packages/internal/tools/src/generators/tools/schema.json", "hash": "12516047619738732168"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/.prettierrc__template__", "hash": "7153902956784250159"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/eslint.config.js__template__", "hash": "4729949328900901634"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/graphql/codegen.ts__template__", "hash": "6302758152617584194"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/jest.config.ts__template__", "hash": "3316899866117889168"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/package.json__template__", "hash": "1893068773442210661"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/src/tests/setupTests.ts__template__", "hash": "17691481316131620361"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/src/tests/utils/rendering.tsx__template__", "hash": "12234490356115050681"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/src/types/index.d.ts__template__", "hash": "17020631503230818110"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/src/utils/storybook.tsx__template__", "hash": "17293493594492340363"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/tsconfig.json__template__", "hash": "15714689034481668573"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/tsconfig.lib.json__template__", "hash": "5817262578345378739"}, {"file": "packages/internal/tools/src/generators/webapp-lib/files/tsconfig.spec.json__template__", "hash": "1971642386723685326"}, {"file": "packages/internal/tools/src/generators/webapp-lib/generator.spec.ts", "hash": "8793197449124597156", "deps": ["npm:@nx/devkit/testing", "npm:@nx/devkit"]}, {"file": "packages/internal/tools/src/generators/webapp-lib/generator.ts", "hash": "14412466719700852973", "deps": ["npm:@nx/devkit", "npm:path"]}, {"file": "packages/internal/tools/src/generators/webapp-lib/schema.d.ts", "hash": "5183573978492561759"}, {"file": "packages/internal/tools/src/generators/webapp-lib/schema.json", "hash": "12516047619738732168"}, {"file": "packages/internal/tools/src/upload-service-version.ts", "hash": "3499949034747118767", "deps": ["npm:@aws-sdk/client-s3"]}, {"file": "packages/internal/tools/src/upload-version.ts", "hash": "7753940972030783121", "deps": ["npm:@aws-sdk/client-s3"]}, {"file": "packages/internal/tools/tsconfig.json", "hash": "6693614989438379715"}, {"file": "packages/internal/tools/tsconfig.lib.json", "hash": "5554291030212586824"}, {"file": "packages/internal/tools/tsconfig.spec.json", "hash": "9110660443763673899"}], "webapp-tenants": [{"file": "packages/webapp-libs/webapp-tenants/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-tenants/eslint.config.js", "hash": "4729949328900901634", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-tenants/graphql/codegen.ts", "hash": "14998250961711388995", "deps": ["npm:@graphql-codegen/cli"]}, {"file": "packages/webapp-libs/webapp-tenants/jest.config.ts", "hash": "5270210462165449765"}, {"file": "packages/webapp-libs/webapp-tenants/package.json", "hash": "12888096684598101671", "deps": ["webapp-api-client", "webapp-core", "webapp-notifications"]}, {"file": "packages/webapp-libs/webapp-tenants/project.json", "hash": "18202632677744802377"}, {"file": "packages/webapp-libs/webapp-tenants/sonar-project.properties", "hash": "13573008728894065855"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/routes/tenantAuthRoute/__tests__/tenantAuthRoute.component.spec.tsx", "hash": "3093074574660329339", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/routes/tenantAuthRoute/index.ts", "hash": "18370374877524004573"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/routes/tenantAuthRoute/tenantAuthRoute.component.tsx", "hash": "13790119331573263777", "deps": ["webapp-api-client", "webapp-core", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantDangerZone/__tests__/tenantDangerZone.component.spec.tsx", "hash": "15557736722651241156", "deps": ["webapp-api-client", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantDangerZone/index.ts", "hash": "6043540856807166879"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantDangerZone/tenantDangerZone.component.tsx", "hash": "6785873143364222392", "deps": ["npm:@iconify-icons/ion/alert-circle-outline", "webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantDangerZone/tenantDangerZone.graphql.ts", "hash": "13386714575984605254", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantDangerZone/tenantDangerZone.hook.ts", "hash": "16545350063791344884", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantDangerZone/tenantDangerZone.stories.tsx", "hash": "2888885307793244907", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantForm/__tests__/tenantForm.component.spec.tsx", "hash": "10605828381511211237", "deps": ["npm:@apollo/client", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error/GraphQLError"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantForm/index.ts", "hash": "8538731630479679728"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantForm/tenantForm.component.tsx", "hash": "17754622842132397696", "deps": ["npm:@apollo/client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantForm/tenantForm.hook.ts", "hash": "11090008468386517630", "deps": ["webapp-api-client", "npm:react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantForm/tenantForm.stories.tsx", "hash": "3768441539532084505", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantInvitationForm/__tests__/tenantInvitationForm.component.spec.tsx", "hash": "870792657129816654", "deps": ["npm:@apollo/client", "webapp-api-client", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql/error/GraphQLError"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantInvitationForm/index.ts", "hash": "2049018817438077861"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantInvitationForm/tenantInvitationForm.component.tsx", "hash": "2443035865262729459", "deps": ["webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantInvitationForm/tenantInvitationForm.hook.ts", "hash": "5855412204108436597", "deps": ["npm:@apollo/client", "webapp-api-client", "npm:react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantInvitationForm/tenantInvitationForm.stories.tsx", "hash": "15472341071100747819", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/__tests__/__snapshots__/tenantMembersList.component.spec.tsx.snap", "hash": "5350499503779196262"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/__tests__/tenantMembersList.component.spec.tsx", "hash": "6819865426188548547", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/index.ts", "hash": "7316007872792224506"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/membershipEntry/__tests__/membershipEntry.component.spec.tsx", "hash": "17720403699442211236", "deps": ["webapp-api-client", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/membershipEntry/index.ts", "hash": "15095311614898141237"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/membershipEntry/membershipEntry.component.tsx", "hash": "3684457155245362225", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:lucide-react", "npm:ramda", "npm:react", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/membershipEntry/membershipEntry.graphql.ts", "hash": "11940672443873087946", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/tenantMembersList.component.tsx", "hash": "10570882856872817722", "deps": ["npm:@apollo/client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantMembersList/tenantMembersList.graphql.ts", "hash": "16273812909597443249", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantRoleAccess/__tests__/tenantRoleAccess.component.spec.tsx", "hash": "1130124443768883289", "deps": ["webapp-api-client", "npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantRoleAccess/index.ts", "hash": "13860696297774134010"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantRoleAccess/tenantRoleAccess.component.tsx", "hash": "16354699652928172677", "deps": ["webapp-api-client", "npm:react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantSwitch/__tests__/tenantSwitch.component.spec.tsx", "hash": "11811453657351820923", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantSwitch/index.ts", "hash": "5060424899353875974"}, {"file": "packages/webapp-libs/webapp-tenants/src/components/tenantSwitch/tenantSwitch.component.tsx", "hash": "18138869737348308577", "deps": ["webapp-api-client", "webapp-core", "npm:lucide-react", "npm:ramda", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/config/routes.ts", "hash": "2931620014098338068", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/index.ts", "hash": "6130872122252662810"}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useCurrentTenantMembership/__tests__/useCurrentTenantMembership.hook.spec.tsx", "hash": "754983749195670736", "deps": ["webapp-api-client", "webapp-core", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useCurrentTenantMembership/index.ts", "hash": "12307163194457590037"}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useCurrentTenantMembership/useCurrentTenantMembership.hook.ts", "hash": "11002127729166731326", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useCurrentTenantRole/__tests__/useCurrentTenantRole.hook.spec.tsx", "hash": "3054431520374570985", "deps": ["webapp-api-client", "webapp-core", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useCurrentTenantRole/index.ts", "hash": "13462577429219505869"}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useCurrentTenantRole/useCurrentTenantRole.hook.ts", "hash": "16200201033986836616"}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useGenerateTenantPath/__tests__/useGenerateTenantPath.hook.spec.tsx", "hash": "388158726121456717", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useGenerateTenantPath/index.ts", "hash": "16135852709172711065"}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useGenerateTenantPath/useGenerateTenantPath.hook.ts", "hash": "3400391719328146218", "deps": ["webapp-core", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useTenantRoleAccessCheck/__tests__/useTenantRoleAccessCheck.hook.spec.tsx", "hash": "9155708438774771104", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useTenantRoleAccessCheck/index.ts", "hash": "9455685615651498622"}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useTenantRoleAccessCheck/useTenantRoleAccessCheck.hook.ts", "hash": "8735981358487730995", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useTenantRoles/index.ts", "hash": "17593166458553711457"}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useTenantRoles/useTenantRoles.hook.ts", "hash": "11895428812130892817", "deps": ["webapp-api-client", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useTenants/__tests__/useTenants.hook.spec.tsx", "hash": "11853697686369222239", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useTenants/index.ts", "hash": "13237977893954628224"}, {"file": "packages/webapp-libs/webapp-tenants/src/hooks/useTenants/useTenants.hook.ts", "hash": "9250416756181186789", "deps": ["webapp-api-client", "npm:react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/index.ts", "hash": "2541191213875579925"}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationAccepted/index.ts", "hash": "11092260855941848897"}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationAccepted/tenantInvitationAccepted.component.tsx", "hash": "15756583377911264146", "deps": ["webapp-notifications", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationAccepted/tenantInvitationAccepted.stories.tsx", "hash": "1834067258064185801", "deps": ["webapp-notifications", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationCreated/index.ts", "hash": "7396903888151851583"}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationCreated/tenantInvitationCreated.component.tsx", "hash": "17830236003260022401", "deps": ["webapp-api-client", "webapp-core", "webapp-notifications", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationCreated/tenantInvitationCreated.stories.tsx", "hash": "13536925379111743937", "deps": ["webapp-notifications", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationDeclined/index.ts", "hash": "880206706456369853"}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationDeclined/tenantInvitationDeclined.component.tsx", "hash": "4047955479830029708", "deps": ["webapp-notifications", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/notifications/tenantInvitationDeclined/tenantInvitationDeclined.stories.tsx", "hash": "7047023940609890416", "deps": ["webapp-notifications", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.component.tsx", "hash": "13747827390144851431", "deps": ["webapp-api-client", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.context.ts", "hash": "15119915977355150167", "deps": ["webapp-api-client", "npm:react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.graphql.ts", "hash": "6134860252348228515", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.hook.ts", "hash": "14677082432334241745", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.storage.ts", "hash": "1687851713466316721", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.types.ts", "hash": "12982379755700741348", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.utils.ts", "hash": "1077807120367112530", "deps": ["webapp-api-client", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/index.ts", "hash": "12793381619347563056"}, {"file": "packages/webapp-libs/webapp-tenants/src/providers/index.ts", "hash": "5699535624850440698"}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/addTenantForm/__tests__/addTenantForm.component.spec.tsx", "hash": "14637342294491015903", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/addTenantForm/addTenantForm.component.tsx", "hash": "1214576479614840535", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/addTenantForm/addTenantForm.stories.tsx", "hash": "12894665419829197169", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/addTenantForm/index.ts", "hash": "17931992887457881447"}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/index.ts", "hash": "10006137077174255387", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantInvitation/__tests__/tenantInvitation.component.spec.tsx", "hash": "8417216805411928192", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantInvitation/index.ts", "hash": "1608570472536319750"}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantInvitation/tenantInvitation.component.tsx", "hash": "130806044986340148", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantInvitation/tenantInvitation.graphql.ts", "hash": "6212190387414148855", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantInvitation/tenantInvitation.stories.tsx", "hash": "7834108710774039472", "deps": ["webapp-api-client", "webapp-core", "npm:@storybook/react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/index.ts", "hash": "6304811295390218048"}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantGeneralSettings/__tests__/tenantGeneralSettings.component.spec.tsx", "hash": "16074375240201941317", "deps": ["webapp-api-client", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantGeneralSettings/index.ts", "hash": "15032234884290195986"}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantGeneralSettings/tenantGeneralSettings.component.tsx", "hash": "7870298809293949850", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantGeneralSettings/tenantGeneralSettings.graphql.ts", "hash": "12642418271437076940", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantMembers/__tests__/tenantMembers.component.spec.tsx", "hash": "13307984791736631146", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantMembers/index.ts", "hash": "14217585613868084987"}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantMembers/invitationForm/__tests__/invitationForm.component.spec.tsx", "hash": "3248024447857368647", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantMembers/invitationForm/index.ts", "hash": "5709794345290942889"}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantMembers/invitationForm/invitationForm.graphql.ts", "hash": "3182910370071349669", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantMembers/invitationForm/invotationForm.component.tsx", "hash": "16993538436143232993", "deps": ["npm:@apollo/client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantMembers/tenantMembers.component.tsx", "hash": "691858886712632736", "deps": ["webapp-api-client", "webapp-core", "npm:lucide-react", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/routes/tenantSettings/tenantSettings.component.tsx", "hash": "8634384611871132099", "deps": ["webapp-core", "webapp-finances", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/tests/factories/tenant.ts", "hash": "3805394232971413317", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-tenants/src/tests/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-tenants/src/tests/utils/rendering.tsx", "hash": "222735913593142104", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/dom", "npm:@testing-library/react", "npm:react", "npm:react-router", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-tenants/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-tenants/src/utils/storybook.tsx", "hash": "15054667008367204958", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-tenants/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-tenants/tsconfig.lib.json", "hash": "15861768067856467447"}, {"file": "packages/webapp-libs/webapp-tenants/tsconfig.spec.json", "hash": "6274810482038238705"}], "webapp-emails": [{"file": "packages/webapp-libs/webapp-emails/.gitignore", "hash": "6038205486305458566"}, {"file": "packages/webapp-libs/webapp-emails/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-emails/eslint.config.js", "hash": "15106167221962780055", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-emails/jest.config.ts", "hash": "17137187091435603426"}, {"file": "packages/webapp-libs/webapp-emails/package.json", "hash": "7562238683214062625", "deps": ["webapp-api-client", "webapp-core", "webapp-finances"]}, {"file": "packages/webapp-libs/webapp-emails/project.json", "hash": "871907316168571004"}, {"file": "packages/webapp-libs/webapp-emails/sonar-project.properties", "hash": "8669040293976002859"}, {"file": "packages/webapp-libs/webapp-emails/src/__tests__/email.spec.tsx", "hash": "12621285273856804647", "deps": ["webapp-core", "npm:react-dom/server"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/base.styles.ts", "hash": "1525612341300324726", "deps": ["webapp-core", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/button/__tests__/button.component.spec.tsx", "hash": "3087969131908460588", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/button/button.component.tsx", "hash": "14131703584936228306", "deps": ["webapp-core", "npm:react"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/button/button.styles.ts", "hash": "9946065791950033352", "deps": ["webapp-core", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/button/index.ts", "hash": "11800661255743706199"}, {"file": "packages/webapp-libs/webapp-emails/src/base/image/__tests__/image.component.spec.tsx", "hash": "1584621076396064043", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/image/image.component.tsx", "hash": "11158927953848436234", "deps": ["webapp-core", "npm:react"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/image/index.ts", "hash": "16111192034561583924"}, {"file": "packages/webapp-libs/webapp-emails/src/base/index.ts", "hash": "13345719535534378159"}, {"file": "packages/webapp-libs/webapp-emails/src/base/layout/__tests__/layout.component.spec.tsx", "hash": "11530294516704787785", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/layout/index.ts", "hash": "11679926947470109722"}, {"file": "packages/webapp-libs/webapp-emails/src/base/layout/layout.component.tsx", "hash": "16691384917802885519", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-emails/src/base/layout/layout.styles.ts", "hash": "489046435809578808", "deps": ["webapp-core", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-emails/src/email.tsx", "hash": "3390222748601042990", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-emails/src/emailStory/emailStory.component.tsx", "hash": "12119716981535467892", "deps": ["npm:axios", "npm:react"]}, {"file": "packages/webapp-libs/webapp-emails/src/emailStory/emailStory.styles.ts", "hash": "8677126730191919483", "deps": ["npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-emails/src/index.tsx", "hash": "5585696911679440090", "deps": ["webapp-core", "npm:juice", "npm:react-dom/server", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/accountActivation/accountActivation.component.tsx", "hash": "16190641756248979572", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/accountActivation/accountActivation.stories.tsx", "hash": "1272882476226635749", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/accountActivation/index.ts", "hash": "18103656672428210346"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/index.ts", "hash": "2051156667782378321"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/passwordReset/index.ts", "hash": "12032534630421919640"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/passwordReset/passwordReset.component.tsx", "hash": "2001545635903252460", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/passwordReset/passwordReset.stories.tsx", "hash": "5269382727943543121", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/subscriptionError/index.ts", "hash": "15054043806240441228"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/subscriptionError/subscriptionError.component.tsx", "hash": "17210857260866634371", "deps": ["webapp-core", "webapp-finances", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/subscriptionError/subscriptionError.stories.tsx", "hash": "4217386523528788103", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/templates.config.ts", "hash": "16332293985284752597"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/tenantInvitation/index.ts", "hash": "668841051520384776"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/tenantInvitation/tenantInvitation.component.tsx", "hash": "998816918761451687", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/tenantInvitation/tenantInvitation.stories.tsx", "hash": "2546159396593894750", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/trialExpiresSoon/index.ts", "hash": "4195979439320498550"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/trialExpiresSoon/trialExpiresSoon.component.tsx", "hash": "9133973086529722974", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/trialExpiresSoon/trialExpiresSoon.stories.tsx", "hash": "2037382921434564682", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/userExport/index.ts", "hash": "4582668060071654757"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/userExport/userExport.component.tsx", "hash": "1413857672913505903", "deps": ["npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/userExport/userExport.stories.tsx", "hash": "8239591575508060065", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/userExport/userExport.styles.ts", "hash": "15131259333272114467", "deps": ["webapp-core", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/userExportAdmin/index.ts", "hash": "2238430123406401330"}, {"file": "packages/webapp-libs/webapp-emails/src/templates/userExportAdmin/userExportAdmin.component.tsx", "hash": "16442543886328613435", "deps": ["npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-emails/src/templates/userExportAdmin/userExportAdmin.stories.tsx", "hash": "7017765061280528560", "deps": ["npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-emails/src/tests/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-emails/src/tests/utils/rendering.tsx", "hash": "1295893913725886629", "deps": ["webapp-api-client", "npm:@testing-library/react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-emails/src/types.ts", "hash": "533754323909751756", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-emails/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-emails/tsconfig.json", "hash": "9360446908454833767"}, {"file": "packages/webapp-libs/webapp-emails/tsconfig.lib.json", "hash": "11136640404014769561"}, {"file": "packages/webapp-libs/webapp-emails/tsconfig.spec.json", "hash": "3683674635599771462"}, {"file": "packages/webapp-libs/webapp-emails/vite.config.ts", "hash": "14156634846272048221", "deps": ["npm:path", "npm:@esbuild-plugins/node-globals-polyfill", "npm:@esbuild-plugins/node-modules-polyfill", "npm:@originjs/vite-plugin-commonjs", "npm:@vitejs/plugin-react", "npm:rollup-plugin-peer-deps-external", "npm:rollup-plugin-polyfill-node", "npm:vite", "npm:vite-plugin-svgr", "npm:vite-tsconfig-paths"]}, {"file": "packages/webapp-libs/webapp-emails/node_modules/.bin/juice", "hash": "8796040944565885807"}], "webapp-documents": [{"file": "packages/webapp-libs/webapp-documents/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-documents/eslint.config.js", "hash": "4729949328900901634", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-documents/graphql/codegen.ts", "hash": "16002266268981983218", "deps": ["npm:@graphql-codegen/cli"]}, {"file": "packages/webapp-libs/webapp-documents/jest.config.ts", "hash": "6856141966423621077"}, {"file": "packages/webapp-libs/webapp-documents/package.json", "hash": "2025917886675476127", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-documents/project.json", "hash": "5460763506549221559"}, {"file": "packages/webapp-libs/webapp-documents/sonar-project.properties", "hash": "15603166571687235246"}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/__tests__/documents.component.spec.tsx", "hash": "13386886574428098757", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/document/__tests__/document.component.spec.tsx", "hash": "562766196987751680", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/document/document.component.tsx", "hash": "11345423871176109415", "deps": ["webapp-api-client", "webapp-core", "npm:lucide-react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/document/document.stories.tsx", "hash": "16711196822507970924", "deps": ["webapp-api-client", "npm:@storybook/react", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/document/index.ts", "hash": "17893337853479655966"}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/document/skeleton/index.ts", "hash": "410057345795930592"}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/document/skeleton/skeleton.component.tsx", "hash": "16094725461640502910", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/document/skeleton/skeleton.stories.tsx", "hash": "16386911393497826421", "deps": ["npm:@storybook/react", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/documents.component.tsx", "hash": "13619307113972164895", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:ramda", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/documents.constants.ts", "hash": "240027980825321458"}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/documents.graphql.ts", "hash": "9513869435498351624", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/documents.hooks.ts", "hash": "3538097775290694755", "deps": ["npm:@apollo/client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/documents.stories.tsx", "hash": "7914531191014720228", "deps": ["npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-documents/src/routes/documents/index.ts", "hash": "3531990284138556169"}, {"file": "packages/webapp-libs/webapp-documents/src/routes/index.ts", "hash": "4477439406180387618", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-documents/src/tests/factories/document.ts", "hash": "15115080502188089969", "deps": ["webapp-api-client", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-documents/src/tests/factories/index.ts", "hash": "1606042815785214685"}, {"file": "packages/webapp-libs/webapp-documents/src/tests/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-documents/src/tests/utils/rendering.tsx", "hash": "12234490356115050681", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-documents/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-documents/src/utils/storybook.tsx", "hash": "13167694795386376036", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-documents/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-documents/tsconfig.lib.json", "hash": "15861768067856467447"}, {"file": "packages/webapp-libs/webapp-documents/tsconfig.spec.json", "hash": "6274810482038238705"}], "webapp-finances": [{"file": "packages/webapp-libs/webapp-finances/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-finances/eslint.config.js", "hash": "10050209892643385800", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-finances/graphql/codegen.ts", "hash": "1247313878327022834", "deps": ["npm:@graphql-codegen/cli"]}, {"file": "packages/webapp-libs/webapp-finances/jest.config.ts", "hash": "9666638361979918702"}, {"file": "packages/webapp-libs/webapp-finances/package.json", "hash": "2407777907391578012", "deps": ["webapp-api-client", "webapp-core", "webapp-notifications"]}, {"file": "packages/webapp-libs/webapp-finances/project.json", "hash": "4075921867571907391"}, {"file": "packages/webapp-libs/webapp-finances/sonar-project.properties", "hash": "13781100464300201898"}, {"file": "packages/webapp-libs/webapp-finances/src/components/activeSubscriptionContext/activeSubscriptionContext.component.tsx", "hash": "13498149882616594683", "deps": ["npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/activeSubscriptionContext/activeSubscriptionContext.hooks.ts", "hash": "13783466912812527993", "deps": ["webapp-api-client", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/activeSubscriptionContext/index.ts", "hash": "4050743575601540297"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/index.ts", "hash": "14018407502353940194"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePayment.hooks.ts", "hash": "8469431268039398095", "deps": ["npm:@apollo/client", "npm:@apollo/client/errors", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:graphql", "npm:react"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePayment.stripe.hook.ts", "hash": "9994813281437257974", "deps": ["webapp-api-client", "npm:@stripe/react-stripe-js", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentForm/__tests__/stripePaymentForm.component.spec.tsx", "hash": "11768944368135355678", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@stripe/react-stripe-js", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentForm/index.ts", "hash": "17126210687513733931"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentForm/stripePaymentForm.component.tsx", "hash": "16608802836014892411", "deps": ["webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentForm/stripePaymentForm.graphql.ts", "hash": "6669411267389261929", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentForm/stripePaymentForm.stories.tsx", "hash": "18392403323984542337", "deps": ["webapp-api-client", "npm:@storybook/addon-actions", "npm:@storybook/react", "npm:@stripe/react-stripe-js", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodInfo/__tests__/stripePaymentMethodInfo.component.spec.tsx", "hash": "2427946654524500239", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodInfo/index.ts", "hash": "16162848641220927816"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodInfo/stripePaymentMethodInfo.component.tsx", "hash": "12783944591418131613", "deps": ["webapp-api-client", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodInfo/stripePaymentMethodInfo.graphql.ts", "hash": "708590775290085553", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodInfo/stripePaymentMethodInfo.stories.tsx", "hash": "2870922393318298548", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/__tests__/stripePaymentMethodSelector.component.spec.tsx", "hash": "16860165923299982341", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@stripe/react-stripe-js", "npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/index.ts", "hash": "5873220269031712207"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/methods/index.ts", "hash": "3593956730396984885"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/methods/newCardInput/index.ts", "hash": "9011252582014776660"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/methods/newCardInput/newCardInput.component.tsx", "hash": "8795539997265719126", "deps": ["webapp-core", "npm:@stripe/react-stripe-js", "npm:@stripe/stripe-js", "npm:react", "npm:react-hook-form", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/methods/newCardInput/newCardInput.styles.ts", "hash": "778892494315903182", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/stripePaymentMethodSelector.component.tsx", "hash": "17710400320616666202", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:lucide-react", "npm:ramda", "npm:react-hook-form", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/stripePaymentMethodSelector.graphql.ts", "hash": "15069490690383736823", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/stripePaymentMethodSelector.stories.tsx", "hash": "2388570322804654925", "deps": ["webapp-api-client", "webapp-core", "npm:@storybook/react", "npm:@stripe/react-stripe-js", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/stripePaymentMethodSelector/stripePaymentMethodSelector.types.ts", "hash": "1103506432304117911", "deps": ["webapp-api-client", "npm:@stripe/stripe-js"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/transactionHistory/__tests__/transactionHistory.component.spec.tsx", "hash": "16941396176753801038", "deps": ["webapp-api-client", "webapp-tenants", "npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/transactionHistory/index.ts", "hash": "10894399314378761167"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/transactionHistory/transactionHistory.component.tsx", "hash": "14995105996599796130", "deps": ["npm:@apollo/client", "webapp-core", "webapp-tenants", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/transactionHistory/transactionHistory.stories.tsx", "hash": "15891042966637495321", "deps": ["webapp-api-client", "webapp-tenants", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/transactionHistory/transactionHistoryEntry/index.ts", "hash": "4293105957521451959"}, {"file": "packages/webapp-libs/webapp-finances/src/components/stripe/transactionHistory/transactionHistoryEntry/transactionHistoryEntry.component.tsx", "hash": "2249804993106178116", "deps": ["webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/config/routes.ts", "hash": "14113832139685855251", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-finances/src/hooks/index.ts", "hash": "86504538060434685"}, {"file": "packages/webapp-libs/webapp-finances/src/hooks/useActiveSubscriptionDetailsData/index.ts", "hash": "16367351119041050954"}, {"file": "packages/webapp-libs/webapp-finances/src/hooks/useActiveSubscriptionDetailsData/useActiveSubscriptionDetailsData.graphql.ts", "hash": "13020523759742422290", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/hooks/useActiveSubscriptionDetailsData/useActiveSubscriptionDetailsData.ts", "hash": "6542117087596708093", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/hooks/useSubscriptionPlanDetails/__tests__/useSubscriptionPlanDetails.hook.spec.tsx", "hash": "15523268213172046366", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/hooks/useSubscriptionPlanDetails/index.ts", "hash": "6383795585402209938"}, {"file": "packages/webapp-libs/webapp-finances/src/hooks/useSubscriptionPlanDetails/useSubscriptionPlanDetails.graphql.ts", "hash": "5463408219547124027", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/hooks/useSubscriptionPlanDetails/useSubscriptionPlanDetails.hook.ts", "hash": "11846139123036366781", "deps": ["npm:@apollo/client", "npm:@graphql-typed-document-node/core", "webapp-api-client", "webapp-tenants", "npm:ramda", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/cancelSubscription/__tests__/cancelSubscription.component.spec.tsx", "hash": "8953315441903759273", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/cancelSubscription/cancelSubscription.component.tsx", "hash": "7688571824749054301", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/cancelSubscription/cancelSubscription.graphql.ts", "hash": "1505902561952989046", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/cancelSubscription/cancelSubscription.hook.ts", "hash": "7996409720634237009", "deps": ["npm:@apollo/client", "webapp-core", "webapp-tenants", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/cancelSubscription/cancelSubscription.stories.tsx", "hash": "2317133298751770179", "deps": ["webapp-api-client", "npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/cancelSubscription/index.ts", "hash": "4764553230383651289"}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editPaymentMethod/editPaymentMethod.component.tsx", "hash": "1994238638449004388", "deps": ["webapp-core", "webapp-tenants", "npm:@stripe/react-stripe-js", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editPaymentMethod/editPaymentMethodForm/__tests__/editPaymentMethodForm.component.spec.tsx", "hash": "1008851504975775630", "deps": ["webapp-api-client", "webapp-tenants", "npm:@stripe/react-stripe-js", "npm:@stripe/stripe-js", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:ramda", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editPaymentMethod/editPaymentMethodForm/editPaymentMethodForm.component.tsx", "hash": "13311918844573133606", "deps": ["webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editPaymentMethod/editPaymentMethodForm/editPaymentMethodForm.graphql.ts", "hash": "11188875941860211821", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editPaymentMethod/editPaymentMethodForm/editPaymentMethodForm.hooks.tsx", "hash": "14828458004576449881", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-tenants", "npm:@stripe/react-stripe-js", "npm:graphql"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editPaymentMethod/editPaymentMethodForm/editPaymentMethodForm.stories.tsx", "hash": "6658648716366502813", "deps": ["webapp-api-client", "npm:@storybook/addon-actions", "npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editPaymentMethod/index.ts", "hash": "2091084706857667703"}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/__tests__/editSubscription.component.spec.tsx", "hash": "5599425351602526344", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:graphql", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/editSubscription.component.tsx", "hash": "1017381094951918323", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/editSubscription.graphql.ts", "hash": "8400260641541007576", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/editSubscription.hook.ts", "hash": "15715696712834076798", "deps": ["npm:@apollo/client", "webapp-core", "webapp-tenants", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/editSubscription.stories.tsx", "hash": "4960405470276164071", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/index.ts", "hash": "15237199928966973820"}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/subscriptionPlanItem/__tests__/subscriptionPlanItem.component.spec.tsx", "hash": "2067436490323765520", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/subscriptionPlanItem/index.ts", "hash": "16707871065481722519"}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/subscriptionPlanItem/subscriptionPlanItem.component.tsx", "hash": "11235248924082774652", "deps": ["webapp-api-client", "webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/subscriptionPlanItem/subscriptionPlanItem.stories.tsx", "hash": "397437271896323841", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:@storybook/react", "npm:react", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/subscriptionPlans/index.ts", "hash": "16316143814440973399"}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/subscriptionPlans/subscriptionPlans.component.tsx", "hash": "10681572636538675333", "deps": ["npm:@apollo/client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/editSubscription/subscriptionPlans/subscriptionPlans.graphql.ts", "hash": "18241909896158203673", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/index.tsx", "hash": "14653428510163100056", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/paymentConfirm/index.ts", "hash": "2519555557823008038"}, {"file": "packages/webapp-libs/webapp-finances/src/routes/paymentConfirm/paymentConfirm.component.tsx", "hash": "9350264331434017389", "deps": ["webapp-core", "npm:@stripe/react-stripe-js", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/paymentConfirm/paymentConfirm.stories.tsx", "hash": "3041998357938651977", "deps": ["webapp-api-client", "npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/subscriptions/__tests__/subscriptions.component.spec.tsx", "hash": "17171796349383272885", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:ramda", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/subscriptions/currentSubscription.component.tsx", "hash": "5170557100158420570", "deps": ["webapp-core", "webapp-tenants", "npm:react-intl", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/subscriptions/index.ts", "hash": "11561444442490474636"}, {"file": "packages/webapp-libs/webapp-finances/src/routes/subscriptions/paymentMethod.content.tsx", "hash": "1525252876355853131", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "webapp-tenants", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/subscriptions/subscriptions.content.tsx", "hash": "4713690382025490000", "deps": ["webapp-core", "webapp-tenants", "npm:lucide-react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/subscriptions/subscriptions.graphql.ts", "hash": "8297205379340897257", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/subscriptions/subscriptions.stories.tsx", "hash": "3955056368013611864", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/subscriptions/transactionsHistory.content.tsx", "hash": "18254427851853224213", "deps": ["npm:@apollo/client", "webapp-core", "webapp-tenants", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/transactionHistory/index.ts", "hash": "2574206014177408985"}, {"file": "packages/webapp-libs/webapp-finances/src/routes/transactionHistory/transactionHistory.component.tsx", "hash": "9468078046064962400", "deps": ["webapp-core", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-finances/src/routes/transactionHistory/transactionHistory.stories.tsx", "hash": "7567265988170115082", "deps": ["npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/services/stripe/client.ts", "hash": "5752149562242430584", "deps": ["webapp-core", "npm:@stripe/stripe-js"]}, {"file": "packages/webapp-libs/webapp-finances/src/services/stripe/index.ts", "hash": "5707686305591495747"}, {"file": "packages/webapp-libs/webapp-finances/src/tests/factories/index.ts", "hash": "2330722379383580197"}, {"file": "packages/webapp-libs/webapp-finances/src/tests/factories/stripe.ts", "hash": "6985828984169889564", "deps": ["webapp-api-client", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-finances/src/tests/factories/subscription.ts", "hash": "3796712965618611619", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-finances/src/tests/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-finances/src/tests/utils/rendering.tsx", "hash": "18190831504940231324", "deps": ["webapp-api-client", "webapp-core", "webapp-tenants", "npm:@testing-library/react", "npm:react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-finances/src/types.ts", "hash": "16097608223377941627"}, {"file": "packages/webapp-libs/webapp-finances/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-finances/src/utils/storybook.tsx", "hash": "1669341072490920226", "deps": ["npm:@storybook/react", "npm:@stripe/react-stripe-js", "npm:react-router-dom"]}, {"file": "packages/webapp-libs/webapp-finances/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-finances/tsconfig.lib.json", "hash": "15861768067856467447"}, {"file": "packages/webapp-libs/webapp-finances/tsconfig.spec.json", "hash": "6274810482038238705"}], "infra-core": [{"file": "packages/infra/infra-core/eslint.config.js", "hash": "9982398891306048414", "deps": ["npm:../../../eslint.config.js"]}, {"file": "packages/infra/infra-core/jest.config.ts", "hash": "8653250759564853277"}, {"file": "packages/infra/infra-core/package.json", "hash": "8686636284856435432"}, {"file": "packages/infra/infra-core/project.json", "hash": "10093362823510583081"}, {"file": "packages/infra/infra-core/src/index.ts", "hash": "5845880113310812581"}, {"file": "packages/infra/infra-core/src/lib/constructs.ts", "hash": "5063623052015555186"}, {"file": "packages/infra/infra-core/src/lib/domains.ts", "hash": "6262238801441315501", "deps": ["npm:constructs", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-route53"]}, {"file": "packages/infra/infra-core/src/lib/env-config.ts", "hash": "6006466584168143968"}, {"file": "packages/infra/infra-core/src/lib/internal-infra-core.ts", "hash": "13303162736544373689"}, {"file": "packages/infra/infra-core/src/lib/patterns/applicationMultipleTargetGroupsFargateService.ts", "hash": "1197637079227272805", "deps": ["npm:aws-cdk-lib/aws-ecs", "npm:aws-cdk-lib/aws-elasticloadbalancingv2", "npm:aws-cdk-lib", "npm:aws-cdk-lib/cx-api", "npm:constructs", "npm:aws-cdk-lib/aws-ec2", "npm:aws-cdk-lib/aws-iam", "npm:aws-cdk-lib/aws-logs"]}, {"file": "packages/infra/infra-core/src/lib/patterns/applicationMultipleTargetGroupsFargateServiceBase.ts", "hash": "11126453797140052432", "deps": ["npm:aws-cdk-lib/aws-certificatemanager", "npm:aws-cdk-lib/aws-ec2", "npm:aws-cdk-lib/aws-ecs", "npm:aws-cdk-lib/aws-elasticloadbalancingv2", "npm:aws-cdk-lib/aws-route53", "npm:aws-cdk-lib/aws-route53-targets", "npm:aws-cdk-lib", "npm:constructs"]}, {"file": "packages/infra/infra-core/src/lib/patterns/ecr-sync/ecr-sync.ts", "hash": "14516350811499683280", "deps": ["npm:fs", "npm:path", "npm:aws-cdk-lib", "npm:aws-cdk-lib/aws-codebuild", "npm:aws-cdk-lib/aws-codepipeline", "npm:aws-cdk-lib/aws-codepipeline-actions", "npm:aws-cdk-lib/aws-ecr", "npm:aws-cdk-lib/aws-events", "npm:aws-cdk-lib/aws-events-targets", "npm:aws-cdk-lib/aws-iam", "npm:aws-cdk-lib/aws-lambda", "npm:aws-cdk-lib/aws-lambda-nodejs", "npm:aws-cdk-lib/aws-logs", "npm:aws-cdk-lib/aws-s3", "npm:constructs"]}, {"file": "packages/infra/infra-core/src/lib/patterns/ecr-sync/image.ts", "hash": "17794520027586548001"}, {"file": "packages/infra/infra-core/src/lib/patterns/ecr-sync/index.ts", "hash": "4390977161255408509"}, {"file": "packages/infra/infra-core/src/lib/patterns/ecr-sync/lambda/docker-adapter.ts", "hash": "11875267185715317661", "deps": ["npm:https", "npm:url"]}, {"file": "packages/infra/infra-core/src/lib/patterns/ecr-sync/lambda/ecr-adapter.ts", "hash": "14689668847490281832", "deps": ["npm:aws-sdk"]}, {"file": "packages/infra/infra-core/src/lib/patterns/ecr-sync/lambda/get-image-tags-handler.ts", "hash": "3022458094823689752", "deps": ["npm:stream", "npm:aws-sdk", "npm:aws-sdk/clients/s3", ["npm:jszip", "dynamic"]]}, {"file": "packages/infra/infra-core/src/lib/patterns/serviceCiConfig.ts", "hash": "18072741304488915395", "deps": ["npm:constructs", "npm:aws-cdk-lib/aws-codebuild", "npm:aws-cdk-lib/aws-codepipeline"]}, {"file": "packages/infra/infra-core/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/infra/infra-core/tsconfig.lib.json", "hash": "5554291030212586824"}, {"file": "packages/infra/infra-core/tsconfig.spec.json", "hash": "9110660443763673899"}], "webapp-notifications": [{"file": "packages/webapp-libs/webapp-notifications/.prettierrc", "hash": "7153902956784250159"}, {"file": "packages/webapp-libs/webapp-notifications/eslint.config.js", "hash": "4729949328900901634", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-notifications/graphql/codegen.ts", "hash": "10668594598358616332", "deps": ["npm:@graphql-codegen/cli"]}, {"file": "packages/webapp-libs/webapp-notifications/jest.config.ts", "hash": "3995274792037072474"}, {"file": "packages/webapp-libs/webapp-notifications/package.json", "hash": "2671152693286511625", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-notifications/project.json", "hash": "9727571557504445820"}, {"file": "packages/webapp-libs/webapp-notifications/sonar-project.properties", "hash": "5275769536945331838"}, {"file": "packages/webapp-libs/webapp-notifications/src/__tests__/notifications.component.spec.tsx", "hash": "6710591789990371192", "deps": ["webapp-api-client", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:react"]}, {"file": "packages/webapp-libs/webapp-notifications/src/index.ts", "hash": "4278038979444385858"}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/__tests__/notification.component.spec.tsx", "hash": "2536333899564528538", "deps": ["npm:@testing-library/react"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/button/button.component.tsx", "hash": "6122198676370601815", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/button/index.ts", "hash": "11800661255743706199"}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/index.ts", "hash": "3053626983934480027"}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/notification.component.tsx", "hash": "7797979176032690983", "deps": ["webapp-api-client", "webapp-core", "npm:lucide-react", "npm:react"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/notification.fixtures.ts", "hash": "3387268189945044385", "deps": ["npm:ramda"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/notification.graphql.ts", "hash": "4884981394554210244", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/notification.hooks.ts", "hash": "14981645992065803664", "deps": ["npm:@apollo/client", "webapp-api-client", "npm:react"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notification/notification.stories.tsx", "hash": "6445299393235322660", "deps": ["npm:@storybook/addon-actions", "npm:@storybook/react", "npm:styled-components"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notifications.component.tsx", "hash": "9406199398178225294", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core", "npm:react"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notifications.graphql.ts", "hash": "6423006097385216319", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notifications.types.ts", "hash": "6081916392284007051", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsButton/__tests__/notificationsButton.component.spec.tsx", "hash": "17779410689949992386", "deps": ["npm:@apollo/client", "npm:@testing-library/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsButton/index.ts", "hash": "157276811583317569"}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsButton/notificationsButton.component.tsx", "hash": "8322118167060623831", "deps": ["webapp-api-client", "webapp-core", "npm:lucide-react", "npm:react", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsButton/notificationsButton.stories.tsx", "hash": "3766346999577850554", "deps": ["npm:@storybook/addon-actions", "npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/__tests__/notificationsList.component.spec.tsx", "hash": "263554416290910436", "deps": ["npm:@apollo/client", "webapp-api-client", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/index.ts", "hash": "9958626020264844719"}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/notificationErrorBoundary/index.ts", "hash": "14756662523877896530"}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/notificationErrorBoundary/notificationErrorBoundary.component.tsx", "hash": "5329801141766655839", "deps": ["npm:react"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/notificationsList.component.tsx", "hash": "4434621187681307092", "deps": ["webapp-api-client", "webapp-core", "npm:lucide-react", "npm:ramda", "npm:react", "npm:react-infinite-scroll-hook", "npm:react-intl"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/notificationsList.constants.ts", "hash": "18322416224644000167"}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/notificationsList.graphql.ts", "hash": "4841809934027322206", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/notificationsList.hooks.ts", "hash": "3477476277298261779", "deps": ["npm:@apollo/client", "webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-notifications/src/notificationsList/notificationsList.stories.tsx", "hash": "8434052332971717977", "deps": ["npm:@storybook/react", "npm:ramda"]}, {"file": "packages/webapp-libs/webapp-notifications/src/tests/factories/index.ts", "hash": "10834978349186723928"}, {"file": "packages/webapp-libs/webapp-notifications/src/tests/factories/notification.ts", "hash": "15160390373138493108", "deps": ["webapp-api-client"]}, {"file": "packages/webapp-libs/webapp-notifications/src/tests/setupTests.ts", "hash": "17691481316131620361", "deps": ["webapp-api-client", "webapp-core"]}, {"file": "packages/webapp-libs/webapp-notifications/src/tests/utils/rendering.tsx", "hash": "12036270486422826268", "deps": ["webapp-api-client", "webapp-core", "npm:@testing-library/react", "npm:react", "npm:react-router"]}, {"file": "packages/webapp-libs/webapp-notifications/src/types/index.d.ts", "hash": "17020631503230818110", "deps": ["webapp-core"]}, {"file": "packages/webapp-libs/webapp-notifications/src/utils/storybook.tsx", "hash": "13167694795386376036", "deps": ["webapp-api-client", "npm:@storybook/react"]}, {"file": "packages/webapp-libs/webapp-notifications/tsconfig.json", "hash": "1098465750514412960"}, {"file": "packages/webapp-libs/webapp-notifications/tsconfig.lib.json", "hash": "15861768067856467447"}, {"file": "packages/webapp-libs/webapp-notifications/tsconfig.spec.json", "hash": "6274810482038238705"}], "workers": [{"file": "packages/workers/.coveragerc", "hash": "8586425818800392533"}, {"file": "packages/workers/.dockerignore", "hash": "7454509859718225753"}, {"file": "packages/workers/.env.shared", "hash": "13114053142628364609"}, {"file": "packages/workers/.gitignore", "hash": "7128980903207710946"}, {"file": "packages/workers/.pdm-python", "hash": "3586531252584402967"}, {"file": "packages/workers/Dockerfile", "hash": "5500495895482943483"}, {"file": "packages/workers/README.md", "hash": "2457829044539259772"}, {"file": "packages/workers/common/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/common/emails.py", "hash": "9628580997895266356"}, {"file": "packages/workers/common/protocols.py", "hash": "12714503627873847447"}, {"file": "packages/workers/common/types.py", "hash": "3198367696217713601"}, {"file": "packages/workers/conftest.py", "hash": "11855581657702864819"}, {"file": "packages/workers/content/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/content/client.py", "hash": "11013309529100821960"}, {"file": "packages/workers/content/handlers.py", "hash": "10734661714209127048"}, {"file": "packages/workers/content/models.py", "hash": "10790777524116139971"}, {"file": "packages/workers/content/services.py", "hash": "8449238070694627428"}, {"file": "packages/workers/content/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/content/tests/conftest.py", "hash": "12069537344746906120"}, {"file": "packages/workers/content/tests/test_sync.py", "hash": "10276092415715496188"}, {"file": "packages/workers/dao/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/dao/db/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/dao/db/connection.py", "hash": "12991089055591770939"}, {"file": "packages/workers/dao/db/models.py", "hash": "935415652366954095"}, {"file": "packages/workers/dao/db/session.py", "hash": "15787723487865100848"}, {"file": "packages/workers/demo/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/demo/factories.py", "hash": "11830043515655562907"}, {"file": "packages/workers/demo/models.py", "hash": "6183978440837114891"}, {"file": "packages/workers/demo/services/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/demo/services/export.py", "hash": "15924208528024091684"}, {"file": "packages/workers/demo/types.py", "hash": "6609986803748661166"}, {"file": "packages/workers/eslint.config.js", "hash": "16344080421713080551", "deps": ["npm:../../eslint.config.js"]}, {"file": "packages/workers/package.json", "hash": "11153815005431152417", "deps": ["core", "tools", "webapp-emails"]}, {"file": "packages/workers/pdm.lock", "hash": "7114628765493238305"}, {"file": "packages/workers/pdm.toml", "hash": "13644113005095514202"}, {"file": "packages/workers/project.json", "hash": "2869087472981604883"}, {"file": "packages/workers/pyproject.toml", "hash": "9877427894645240620"}, {"file": "packages/workers/scheduler/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/scheduler/handlers.py", "hash": "7212801561637941056"}, {"file": "packages/workers/scheduler/services.py", "hash": "13116147674850572792"}, {"file": "packages/workers/scripts/runtime/local-trigger-server.js", "hash": "3155309008136650341", "deps": ["npm:express", "npm:body-parser", "npm:child_process", "npm:yaml", "npm:fs"]}, {"file": "packages/workers/scripts/runtime/run_build.sh", "hash": "6500833399672429523"}, {"file": "packages/workers/scripts/runtime/run_deploy.sh", "hash": "829876106838769128"}, {"file": "packages/workers/scripts/runtime/run_lint.sh", "hash": "3754831448894108805"}, {"file": "packages/workers/scripts/runtime/run_tests.sh", "hash": "324973586762979614"}, {"file": "packages/workers/scripts/stop-task-scheduling-executions.sh", "hash": "923646828928696681"}, {"file": "packages/workers/serverless.yml", "hash": "11771820992027132190"}, {"file": "packages/workers/settings/__init__.py", "hash": "16427829282224445790"}, {"file": "packages/workers/settings/base.py", "hash": "14552184778950658548"}, {"file": "packages/workers/setup.cfg", "hash": "10541863738047390738"}, {"file": "packages/workers/setup.py", "hash": "3468278279359526613"}, {"file": "packages/workers/sonar-project.properties", "hash": "15251993645838651765"}, {"file": "packages/workers/tsconfig.app.json", "hash": "7354138925616812570"}, {"file": "packages/workers/tsconfig.json", "hash": "15118015970505881288"}, {"file": "packages/workers/tsconfig.spec.json", "hash": "16514665113400902316"}, {"file": "packages/workers/userauth/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/userauth/factories.py", "hash": "9218471404197239252"}, {"file": "packages/workers/userauth/models.py", "hash": "10854496979814097450"}, {"file": "packages/workers/userauth/types.py", "hash": "5494625041283414182"}, {"file": "packages/workers/utils/__init__.py", "hash": "3244421341483603138"}, {"file": "packages/workers/utils/hashid.py", "hash": "6022800672404748017"}, {"file": "packages/workers/utils/logging.py", "hash": "2790447431522257513"}, {"file": "packages/workers/utils/monitoring.py", "hash": "16873952942223102360"}, {"file": "packages/workers/workers.conf.local.yml", "hash": "16252143155640598191"}, {"file": "packages/workers/workers.conf.yml", "hash": "7322699445866979464"}]}}}