> pnpm vite -c vite.config.ts build

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[36mvite v5.4.19 [32mbuilding for production...[36m[39m
transforming...
[1m[33m[plugin:vite:resolve][39m[22m [33m[plugin vite:resolve] Module "crypto" has been externalized for browser compatibility, imported by "/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/@supercharge+strings@2.0.0/node_modules/@supercharge/strings/dist/random-string-generator.js". See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.[39m
[32m✓[39m 849 modules transformed.
rendering chunks...
computing gzip size...
[2mbuild/email-renderer/[22m[36mindex.umd.js  [39m[1m[33m835.54 kB[39m[22m[2m │ gzip: 256.29 kB[22m
[32m✓ built in 2.32s[39m
