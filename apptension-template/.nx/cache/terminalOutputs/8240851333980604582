[2m> [22mdocker compose build workers

time="2025-06-28T14:01:03+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
time="2025-06-28T14:01:03+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.local.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
Compose can now delegate builds to bake for better performance.
 To do so, set COMPOSE_BAKE=true.
#0 building with "desktop-linux" instance using docker driver

#1 [workers internal] load build definition from Dockerfile
#1 transferring dockerfile: 2.08kB done
#1 DONE 0.0s

#2 [workers internal] load metadata for docker.io/segment/chamber:2
#2 ...

#3 [workers auth] library/python:pull token for registry-1.docker.io
#3 DONE 0.0s

#4 [workers auth] segment/chamber:pull token for registry-1.docker.io
#4 DONE 0.0s

#5 [workers internal] load metadata for docker.io/library/python:3.11-slim-bullseye
#5 DONE 2.3s

#2 [workers internal] load metadata for docker.io/segment/chamber:2
#2 DONE 3.4s

#6 [workers internal] load .dockerignore
#6 transferring context: 206B done
#6 DONE 0.0s

#7 [workers stage-1  1/20] FROM docker.io/library/python:3.11-slim-bullseye@sha256:121862c3e24a272af7f8d79ceb595feaf39e8dcbd10a2fd2639033f613dc10f5
#7 CACHED

#8 [workers chamber 1/1] FROM docker.io/segment/chamber:2@sha256:a73252f72ab4cc5b0d544430314f937b25abf0ea6390eebdc3a9b4cad7b5e635
#8 resolve docker.io/segment/chamber:2@sha256:a73252f72ab4cc5b0d544430314f937b25abf0ea6390eebdc3a9b4cad7b5e635 0.0s done
#8 sha256:a73252f72ab4cc5b0d544430314f937b25abf0ea6390eebdc3a9b4cad7b5e635 1.61kB / 1.61kB done
#8 sha256:8ec56f219bf82b6726dad7ed4ff7196f46dba7bc3d481e60740f4dccd590dc95 672B / 672B done
#8 sha256:fbc57005d35a65f1d59bf7ec7de386b23db341a1e52e303c77820897cc368ab5 891B / 891B done
#8 sha256:1db3f0979abbf1193fc21f3539029dcdf722e4bcd6b0b016e3a500f73d65d015 0B / 123.56kB 0.1s
#8 sha256:336865ab97e1d30b1113e724abc4582c381ee02a7dde480f3641b19318401261 0B / 10.04MB 0.1s
#8 extracting sha256:1db3f0979abbf1193fc21f3539029dcdf722e4bcd6b0b016e3a500f73d65d015
#8 ...

#9 [workers internal] load build context
#9 transferring context: 2.15MB 0.3s done
#9 DONE 0.4s

#8 [workers chamber 1/1] FROM docker.io/segment/chamber:2@sha256:a73252f72ab4cc5b0d544430314f937b25abf0ea6390eebdc3a9b4cad7b5e635
#8 sha256:1db3f0979abbf1193fc21f3539029dcdf722e4bcd6b0b016e3a500f73d65d015 123.56kB / 123.56kB 0.3s done
#8 extracting sha256:1db3f0979abbf1193fc21f3539029dcdf722e4bcd6b0b016e3a500f73d65d015 done
#8 sha256:336865ab97e1d30b1113e724abc4582c381ee02a7dde480f3641b19318401261 2.10MB / 10.04MB 0.8s
#8 sha256:336865ab97e1d30b1113e724abc4582c381ee02a7dde480f3641b19318401261 3.15MB / 10.04MB 1.0s
#8 sha256:336865ab97e1d30b1113e724abc4582c381ee02a7dde480f3641b19318401261 5.24MB / 10.04MB 1.1s
#8 sha256:336865ab97e1d30b1113e724abc4582c381ee02a7dde480f3641b19318401261 6.29MB / 10.04MB 1.2s
#8 sha256:336865ab97e1d30b1113e724abc4582c381ee02a7dde480f3641b19318401261 10.04MB / 10.04MB 1.4s done
#8 extracting sha256:336865ab97e1d30b1113e724abc4582c381ee02a7dde480f3641b19318401261
#8 extracting sha256:336865ab97e1d30b1113e724abc4582c381ee02a7dde480f3641b19318401261 0.1s done
#8 DONE 1.6s

#10 [workers stage-1  2/20] RUN apt-get update && apt-get install -y wget gnupg curl unzip make xz-utils build-essential
#10 0.384 Get:1 http://deb.debian.org/debian bullseye InRelease [116 kB]
#10 0.612 Get:2 http://deb.debian.org/debian-security bullseye-security InRelease [27.2 kB]
#10 0.639 Get:3 http://deb.debian.org/debian bullseye-updates InRelease [44.0 kB]
#10 0.690 Get:4 http://deb.debian.org/debian bullseye/main arm64 Packages [7955 kB]
#10 2.868 Get:5 http://deb.debian.org/debian-security bullseye-security/main arm64 Packages [378 kB]
#10 2.934 Get:6 http://deb.debian.org/debian bullseye-updates/main arm64 Packages [16.3 kB]
#10 3.632 Fetched 8537 kB in 3s (2568 kB/s)
#10 3.632 Reading package lists...
#10 3.950 Reading package lists...
#10 4.205 Building dependency tree...
#10 4.283 Reading state information...
#10 4.364 The following additional packages will be installed:
#10 4.364   binutils binutils-aarch64-linux-gnu binutils-common bzip2 cpp cpp-10 dirmngr
#10 4.364   dpkg-dev fakeroot fontconfig-config fonts-dejavu-core g++ g++-10 gcc gcc-10
#10 4.364   gnupg-l10n gnupg-utils gpg gpg-agent gpg-wks-client gpg-wks-server gpgconf
#10 4.364   gpgsm libalgorithm-diff-perl libalgorithm-diff-xs-perl
#10 4.364   libalgorithm-merge-perl libasan6 libassuan0 libatomic1 libbinutils
#10 4.364   libbrotli1 libbsd0 libc-dev-bin libc-devtools libc6-dev libcc1-0
#10 4.364   libcrypt-dev libctf-nobfd0 libctf0 libcurl4 libdeflate0 libdpkg-perl
#10 4.364   libexpat1 libfakeroot libfile-fcntllock-perl libfontconfig1 libfreetype6
#10 4.364   libgcc-10-dev libgd3 libgdbm-compat4 libgomp1 libisl23 libitm1 libjbig0
#10 4.364   libjpeg62-turbo libksba8 libldap-2.4-2 libldap-common liblocale-gettext-perl
#10 4.365   liblsan0 libmd0 libmpc3 libmpfr6 libnghttp2-14 libnpth0 libnsl-dev
#10 4.365   libperl5.32 libpng16-16 libpsl5 librtmp1 libsasl2-2 libsasl2-modules
#10 4.365   libsasl2-modules-db libssh2-1 libstdc++-10-dev libtiff5 libtirpc-dev
#10 4.365   libtsan0 libubsan1 libwebp6 libx11-6 libx11-data libxau6 libxcb1 libxdmcp6
#10 4.365   libxpm4 linux-libc-dev manpages manpages-dev patch perl perl-modules-5.32
#10 4.365   pinentry-curses publicsuffix sensible-utils ucf
#10 4.365 Suggested packages:
#10 4.365   binutils-doc bzip2-doc cpp-doc gcc-10-locales dbus-user-session
#10 4.365   libpam-systemd pinentry-gnome3 tor debian-keyring gcc-10-doc gcc-multilib
#10 4.365   autoconf automake libtool flex bison gdb gcc-doc parcimonie xloadimage
#10 4.365   scdaemon glibc-doc git bzr libgd-tools libsasl2-modules-gssapi-mit
#10 4.365   | libsasl2-modules-gssapi-heimdal libsasl2-modules-ldap libsasl2-modules-otp
#10 4.365   libsasl2-modules-sql libstdc++-10-doc make-doc man-browser ed diffutils-doc
#10 4.365   perl-doc libterm-readline-gnu-perl | libterm-readline-perl-perl
#10 4.365   libtap-harness-archive-perl pinentry-doc zip
#10 4.653 The following NEW packages will be installed:
#10 4.653   binutils binutils-aarch64-linux-gnu binutils-common build-essential bzip2
#10 4.653   cpp cpp-10 curl dirmngr dpkg-dev fakeroot fontconfig-config
#10 4.654   fonts-dejavu-core g++ g++-10 gcc gcc-10 gnupg gnupg-l10n gnupg-utils gpg
#10 4.654   gpg-agent gpg-wks-client gpg-wks-server gpgconf gpgsm libalgorithm-diff-perl
#10 4.654   libalgorithm-diff-xs-perl libalgorithm-merge-perl libasan6 libassuan0
#10 4.654   libatomic1 libbinutils libbrotli1 libbsd0 libc-dev-bin libc-devtools
#10 4.654   libc6-dev libcc1-0 libcrypt-dev libctf-nobfd0 libctf0 libcurl4 libdeflate0
#10 4.654   libdpkg-perl libexpat1 libfakeroot libfile-fcntllock-perl libfontconfig1
#10 4.654   libfreetype6 libgcc-10-dev libgd3 libgdbm-compat4 libgomp1 libisl23 libitm1
#10 4.654   libjbig0 libjpeg62-turbo libksba8 libldap-2.4-2 libldap-common
#10 4.654   liblocale-gettext-perl liblsan0 libmd0 libmpc3 libmpfr6 libnghttp2-14
#10 4.654   libnpth0 libnsl-dev libperl5.32 libpng16-16 libpsl5 librtmp1 libsasl2-2
#10 4.654   libsasl2-modules libsasl2-modules-db libssh2-1 libstdc++-10-dev libtiff5
#10 4.654   libtirpc-dev libtsan0 libubsan1 libwebp6 libx11-6 libx11-data libxau6
#10 4.654   libxcb1 libxdmcp6 libxpm4 linux-libc-dev make manpages manpages-dev patch
#10 4.654   perl perl-modules-5.32 pinentry-curses publicsuffix sensible-utils ucf unzip
#10 4.654   wget xz-utils
#10 4.692 0 upgraded, 103 newly installed, 0 to remove and 0 not upgraded.
#10 4.692 Need to get 82.7 MB of archives.
#10 4.692 After this operation, 299 MB of additional disk space will be used.
#10 4.692 Get:1 http://deb.debian.org/debian-security bullseye-security/main arm64 perl-modules-5.32 all 5.32.1-4+deb11u4 [2824 kB]
#10 4.958 Get:2 http://deb.debian.org/debian bullseye/main arm64 libgdbm-compat4 arm64 1.19-2 [44.6 kB]
#10 4.972 Get:3 http://deb.debian.org/debian-security bullseye-security/main arm64 libperl5.32 arm64 5.32.1-4+deb11u4 [3917 kB]
#10 5.304 Get:4 http://deb.debian.org/debian-security bullseye-security/main arm64 perl arm64 5.32.1-4+deb11u4 [293 kB]
#10 5.316 Get:5 http://deb.debian.org/debian bullseye/main arm64 liblocale-gettext-perl arm64 1.07-4+b1 [18.9 kB]
#10 5.316 Get:6 http://deb.debian.org/debian bullseye/main arm64 sensible-utils all 0.0.14 [14.8 kB]
#10 5.325 Get:7 http://deb.debian.org/debian bullseye/main arm64 bzip2 arm64 1.0.8-4 [48.4 kB]
#10 5.347 Get:8 http://deb.debian.org/debian bullseye/main arm64 manpages all 5.10-1 [1412 kB]
#10 5.443 Get:9 http://deb.debian.org/debian-security bullseye-security/main arm64 ucf all 3.0043+deb11u2 [74.3 kB]
#10 5.459 Get:10 http://deb.debian.org/debian bullseye/main arm64 libpsl5 arm64 0.21.0-1.2 [57.1 kB]
#10 5.476 Get:11 http://deb.debian.org/debian-security bullseye-security/main arm64 wget arm64 1.21-1+deb11u2 [946 kB]
#10 5.559 Get:12 http://deb.debian.org/debian bullseye/main arm64 xz-utils arm64 5.2.5-2.1~deb11u1 [219 kB]
#10 5.787 Get:13 http://deb.debian.org/debian bullseye/main arm64 binutils-common arm64 2.35.2-2 [2220 kB]
#10 5.985 Get:14 http://deb.debian.org/debian bullseye/main arm64 libbinutils arm64 2.35.2-2 [599 kB]
#10 6.038 Get:15 http://deb.debian.org/debian bullseye/main arm64 libctf-nobfd0 arm64 2.35.2-2 [108 kB]
#10 6.057 Get:16 http://deb.debian.org/debian bullseye/main arm64 libctf0 arm64 2.35.2-2 [51.0 kB]
#10 6.086 Get:17 http://deb.debian.org/debian bullseye/main arm64 binutils-aarch64-linux-gnu arm64 2.35.2-2 [2472 kB]
#10 6.302 Get:18 http://deb.debian.org/debian bullseye/main arm64 binutils arm64 2.35.2-2 [61.2 kB]
#10 6.324 Get:19 http://deb.debian.org/debian-security bullseye-security/main arm64 libc-dev-bin arm64 2.31-13+deb11u13 [273 kB]
#10 6.496 Get:20 http://deb.debian.org/debian-security bullseye-security/main arm64 linux-libc-dev arm64 5.10.237-1 [1800 kB]
#10 6.661 Get:21 http://deb.debian.org/debian bullseye/main arm64 libcrypt-dev arm64 1:4.4.18-4 [108 kB]
#10 6.688 Get:22 http://deb.debian.org/debian bullseye/main arm64 libtirpc-dev arm64 1.3.1-1+deb11u1 [193 kB]
#10 6.706 Get:23 http://deb.debian.org/debian bullseye/main arm64 libnsl-dev arm64 1.3.0-2 [66.1 kB]
#10 6.722 Get:24 http://deb.debian.org/debian-security bullseye-security/main arm64 libc6-dev arm64 2.31-13+deb11u13 [2029 kB]
#10 6.901 Get:25 http://deb.debian.org/debian bullseye/main arm64 libisl23 arm64 0.23-1 [590 kB]
#10 6.970 Get:26 http://deb.debian.org/debian bullseye/main arm64 libmpfr6 arm64 4.1.0-3 [829 kB]
#10 7.005 Get:27 http://deb.debian.org/debian bullseye/main arm64 libmpc3 arm64 1.2.0-1 [43.2 kB]
#10 7.023 Get:28 http://deb.debian.org/debian bullseye/main arm64 cpp-10 arm64 10.2.1-6 [7756 kB]
#10 7.792 Get:29 http://deb.debian.org/debian bullseye/main arm64 cpp arm64 4:10.2.1-1 [19.7 kB]
#10 7.794 Get:30 http://deb.debian.org/debian bullseye/main arm64 libcc1-0 arm64 10.2.1-6 [45.1 kB]
#10 7.800 Get:31 http://deb.debian.org/debian bullseye/main arm64 libgomp1 arm64 10.2.1-6 [91.6 kB]
#10 7.820 Get:32 http://deb.debian.org/debian bullseye/main arm64 libitm1 arm64 10.2.1-6 [23.8 kB]
#10 7.823 Get:33 http://deb.debian.org/debian bullseye/main arm64 libatomic1 arm64 10.2.1-6 [9468 B]
#10 7.854 Get:34 http://deb.debian.org/debian bullseye/main arm64 libasan6 arm64 10.2.1-6 [2002 kB]
#10 8.090 Get:35 http://deb.debian.org/debian bullseye/main arm64 liblsan0 arm64 10.2.1-6 [794 kB]
#10 8.161 Get:36 http://deb.debian.org/debian bullseye/main arm64 libtsan0 arm64 10.2.1-6 [1957 kB]
#10 8.350 Get:37 http://deb.debian.org/debian bullseye/main arm64 libubsan1 arm64 10.2.1-6 [759 kB]
#10 8.432 Get:38 http://deb.debian.org/debian bullseye/main arm64 libgcc-10-dev arm64 10.2.1-6 [876 kB]
#10 8.661 Get:39 http://deb.debian.org/debian bullseye/main arm64 gcc-10 arm64 10.2.1-6 [15.4 MB]
#10 11.92 Get:40 http://deb.debian.org/debian bullseye/main arm64 gcc arm64 4:10.2.1-1 [5208 B]
#10 12.28 Get:41 http://deb.debian.org/debian bullseye/main arm64 libstdc++-10-dev arm64 10.2.1-6 [1704 kB]
#10 12.45 Get:42 http://deb.debian.org/debian bullseye/main arm64 g++-10 arm64 10.2.1-6 [8557 kB]
#10 13.12 Get:43 http://deb.debian.org/debian bullseye/main arm64 g++ arm64 4:10.2.1-1 [1628 B]
#10 13.14 Get:44 http://deb.debian.org/debian bullseye/main arm64 make arm64 4.3-4.1 [391 kB]
#10 13.25 Get:45 http://deb.debian.org/debian bullseye/main arm64 libdpkg-perl all 1.20.13 [1552 kB]
#10 13.38 Get:46 http://deb.debian.org/debian bullseye/main arm64 patch arm64 2.7.6-7 [121 kB]
#10 13.38 Get:47 http://deb.debian.org/debian bullseye/main arm64 dpkg-dev all 1.20.13 [2314 kB]
#10 13.59 Get:48 http://deb.debian.org/debian bullseye/main arm64 build-essential arm64 12.9 [7704 B]
#10 13.62 Get:49 http://deb.debian.org/debian bullseye/main arm64 libbrotli1 arm64 1.0.9-2+b2 [267 kB]
#10 13.64 Get:50 http://deb.debian.org/debian bullseye/main arm64 libsasl2-modules-db arm64 2.1.27+dfsg-2.1+deb11u1 [69.4 kB]
#10 13.64 Get:51 http://deb.debian.org/debian bullseye/main arm64 libsasl2-2 arm64 2.1.27+dfsg-2.1+deb11u1 [105 kB]
#10 13.67 Get:52 http://deb.debian.org/debian bullseye/main arm64 libldap-2.4-2 arm64 2.4.57+dfsg-3+deb11u1 [222 kB]
#10 13.69 Get:53 http://deb.debian.org/debian-security bullseye-security/main arm64 libnghttp2-14 arm64 1.43.0-1+deb11u2 [74.0 kB]
#10 13.69 Get:54 http://deb.debian.org/debian bullseye/main arm64 librtmp1 arm64 2.4+20151223.gitfa8646d.1-2+b2 [59.4 kB]
#10 13.71 Get:55 http://deb.debian.org/debian bullseye/main arm64 libssh2-1 arm64 1.9.0-2+deb11u1 [150 kB]
#10 13.74 Get:56 http://deb.debian.org/debian-security bullseye-security/main arm64 libcurl4 arm64 7.74.0-1.3+deb11u15 [327 kB]
#10 13.76 Get:57 http://deb.debian.org/debian-security bullseye-security/main arm64 curl arm64 7.74.0-1.3+deb11u15 [266 kB]
#10 13.79 Get:58 http://deb.debian.org/debian bullseye/main arm64 libassuan0 arm64 2.5.3-7.1 [48.0 kB]
#10 13.82 Get:59 http://deb.debian.org/debian bullseye/main arm64 gpgconf arm64 2.2.27-2+deb11u2 [540 kB]
#10 13.87 Get:60 http://deb.debian.org/debian bullseye/main arm64 libksba8 arm64 1.5.0-3+deb11u2 [115 kB]
#10 13.88 Get:61 http://deb.debian.org/debian bullseye/main arm64 libnpth0 arm64 1.6-3 [18.6 kB]
#10 13.90 Get:62 http://deb.debian.org/debian bullseye/main arm64 dirmngr arm64 2.2.27-2+deb11u2 [743 kB]
#10 14.06 Get:63 http://deb.debian.org/debian bullseye/main arm64 libfakeroot arm64 1.25.3-1.1 [47.9 kB]
#10 14.09 Get:64 http://deb.debian.org/debian bullseye/main arm64 fakeroot arm64 1.25.3-1.1 [86.8 kB]
#10 14.12 Get:65 http://deb.debian.org/debian bullseye/main arm64 fonts-dejavu-core all 2.37-2 [1069 kB]
#10 14.23 Get:66 http://deb.debian.org/debian bullseye/main arm64 fontconfig-config all 2.13.1-4.2 [281 kB]
#10 14.24 Get:67 http://deb.debian.org/debian bullseye/main arm64 gnupg-l10n all 2.2.27-2+deb11u2 [1086 kB]
#10 14.35 Get:68 http://deb.debian.org/debian bullseye/main arm64 gnupg-utils arm64 2.2.27-2+deb11u2 [864 kB]
#10 14.45 Get:69 http://deb.debian.org/debian bullseye/main arm64 gpg arm64 2.2.27-2+deb11u2 [884 kB]
#10 14.51 Get:70 http://deb.debian.org/debian bullseye/main arm64 pinentry-curses arm64 1.1.0-4 [63.0 kB]
#10 14.53 Get:71 http://deb.debian.org/debian bullseye/main arm64 gpg-agent arm64 2.2.27-2+deb11u2 [651 kB]
#10 14.58 Get:72 http://deb.debian.org/debian bullseye/main arm64 gpg-wks-client arm64 2.2.27-2+deb11u2 [517 kB]
#10 14.65 Get:73 http://deb.debian.org/debian bullseye/main arm64 gpg-wks-server arm64 2.2.27-2+deb11u2 [510 kB]
#10 14.70 Get:74 http://deb.debian.org/debian bullseye/main arm64 gpgsm arm64 2.2.27-2+deb11u2 [627 kB]
#10 14.75 Get:75 http://deb.debian.org/debian bullseye/main arm64 gnupg all 2.2.27-2+deb11u2 [825 kB]
#10 14.81 Get:76 http://deb.debian.org/debian bullseye/main arm64 libalgorithm-diff-perl all 1.201-1 [43.3 kB]
#10 14.81 Get:77 http://deb.debian.org/debian bullseye/main arm64 libalgorithm-diff-xs-perl arm64 0.04-6+b1 [11.7 kB]
#10 14.81 Get:78 http://deb.debian.org/debian bullseye/main arm64 libalgorithm-merge-perl all 0.08-3 [12.7 kB]
#10 14.82 Get:79 http://deb.debian.org/debian bullseye/main arm64 libmd0 arm64 1.0.3-3 [27.9 kB]
#10 14.82 Get:80 http://deb.debian.org/debian bullseye/main arm64 libbsd0 arm64 0.11.3-1+deb11u1 [106 kB]
#10 14.85 Get:81 http://deb.debian.org/debian-security bullseye-security/main arm64 libexpat1 arm64 2.2.10-2+deb11u7 [85.3 kB]
#10 14.86 Get:82 http://deb.debian.org/debian bullseye/main arm64 libpng16-16 arm64 1.6.37-3 [289 kB]
#10 14.89 Get:83 http://deb.debian.org/debian-security bullseye-security/main arm64 libfreetype6 arm64 2.10.4+dfsg-1+deb11u2 [393 kB]
#10 14.95 Get:84 http://deb.debian.org/debian bullseye/main arm64 libfontconfig1 arm64 2.13.1-4.2 [344 kB]
#10 14.97 Get:85 http://deb.debian.org/debian bullseye/main arm64 libjpeg62-turbo arm64 1:2.0.6-4 [133 kB]
#10 14.99 Get:86 http://deb.debian.org/debian bullseye/main arm64 libdeflate0 arm64 1.7-1 [47.7 kB]
#10 14.99 Get:87 http://deb.debian.org/debian bullseye/main arm64 libjbig0 arm64 2.1-3.1+b2 [27.8 kB]
#10 15.02 Get:88 http://deb.debian.org/debian bullseye/main arm64 libwebp6 arm64 0.6.1-2.1+deb11u2 [244 kB]
#10 15.09 Get:89 http://deb.debian.org/debian-security bullseye-security/main arm64 libtiff5 arm64 4.2.0-1+deb11u6 [278 kB]
#10 15.09 Get:90 http://deb.debian.org/debian bullseye/main arm64 libxau6 arm64 1:1.0.9-1 [19.7 kB]
#10 15.09 Get:91 http://deb.debian.org/debian bullseye/main arm64 libxdmcp6 arm64 1:1.1.2-3 [25.4 kB]
#10 15.09 Get:92 http://deb.debian.org/debian bullseye/main arm64 libxcb1 arm64 1.14-3 [138 kB]
#10 15.09 Get:93 http://deb.debian.org/debian bullseye/main arm64 libx11-data all 2:1.7.2-1+deb11u2 [311 kB]
#10 15.12 Get:94 http://deb.debian.org/debian bullseye/main arm64 libx11-6 arm64 2:1.7.2-1+deb11u2 [744 kB]
#10 15.18 Get:95 http://deb.debian.org/debian bullseye/main arm64 libxpm4 arm64 1:3.5.12-1.1+deb11u1 [47.4 kB]
#10 15.18 Get:96 http://deb.debian.org/debian bullseye/main arm64 libgd3 arm64 2.3.0-2 [127 kB]
#10 15.19 Get:97 http://deb.debian.org/debian-security bullseye-security/main arm64 libc-devtools arm64 2.31-13+deb11u13 [246 kB]
#10 15.21 Get:98 http://deb.debian.org/debian bullseye/main arm64 libfile-fcntllock-perl arm64 0.22-3+b7 [35.4 kB]
#10 15.22 Get:99 http://deb.debian.org/debian bullseye/main arm64 libldap-common all 2.4.57+dfsg-3+deb11u1 [95.8 kB]
#10 15.25 Get:100 http://deb.debian.org/debian bullseye/main arm64 libsasl2-modules arm64 2.1.27+dfsg-2.1+deb11u1 [101 kB]
#10 15.27 Get:101 http://deb.debian.org/debian bullseye/main arm64 manpages-dev all 5.10-1 [2309 kB]
#10 15.47 Get:102 http://deb.debian.org/debian bullseye/main arm64 publicsuffix all 20220811.1734-0+deb11u1 [127 kB]
#10 15.50 Get:103 http://deb.debian.org/debian bullseye/main arm64 unzip arm64 6.0-26+deb11u1 [165 kB]
#10 15.67 debconf: delaying package configuration, since apt-utils is not installed
#10 15.69 Fetched 82.7 MB in 11s (7634 kB/s)
#10 15.71 Selecting previously unselected package perl-modules-5.32.
#10 15.71 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 7026 files and directories currently installed.)
#10 15.75 Preparing to unpack .../000-perl-modules-5.32_5.32.1-4+deb11u4_all.deb ...
#10 15.75 Unpacking perl-modules-5.32 (5.32.1-4+deb11u4) ...
#10 15.99 Selecting previously unselected package libgdbm-compat4:arm64.
#10 15.99 Preparing to unpack .../001-libgdbm-compat4_1.19-2_arm64.deb ...
#10 15.99 Unpacking libgdbm-compat4:arm64 (1.19-2) ...
#10 16.01 Selecting previously unselected package libperl5.32:arm64.
#10 16.01 Preparing to unpack .../002-libperl5.32_5.32.1-4+deb11u4_arm64.deb ...
#10 16.01 Unpacking libperl5.32:arm64 (5.32.1-4+deb11u4) ...
#10 16.33 Selecting previously unselected package perl.
#10 16.33 Preparing to unpack .../003-perl_5.32.1-4+deb11u4_arm64.deb ...
#10 16.33 Unpacking perl (5.32.1-4+deb11u4) ...
#10 16.36 Selecting previously unselected package liblocale-gettext-perl.
#10 16.36 Preparing to unpack .../004-liblocale-gettext-perl_1.07-4+b1_arm64.deb ...
#10 16.36 Unpacking liblocale-gettext-perl (1.07-4+b1) ...
#10 16.37 Selecting previously unselected package sensible-utils.
#10 16.37 Preparing to unpack .../005-sensible-utils_0.0.14_all.deb ...
#10 16.37 Unpacking sensible-utils (0.0.14) ...
#10 16.38 Selecting previously unselected package bzip2.
#10 16.38 Preparing to unpack .../006-bzip2_1.0.8-4_arm64.deb ...
#10 16.39 Unpacking bzip2 (1.0.8-4) ...
#10 16.40 Selecting previously unselected package manpages.
#10 16.40 Preparing to unpack .../007-manpages_5.10-1_all.deb ...
#10 16.40 Unpacking manpages (5.10-1) ...
#10 16.49 Selecting previously unselected package ucf.
#10 16.49 Preparing to unpack .../008-ucf_3.0043+deb11u2_all.deb ...
#10 16.50 Moving old data out of the way
#10 16.50 Unpacking ucf (3.0043+deb11u2) ...
#10 16.51 Selecting previously unselected package libpsl5:arm64.
#10 16.51 Preparing to unpack .../009-libpsl5_0.21.0-1.2_arm64.deb ...
#10 16.51 Unpacking libpsl5:arm64 (0.21.0-1.2) ...
#10 16.53 Selecting previously unselected package wget.
#10 16.53 Preparing to unpack .../010-wget_1.21-1+deb11u2_arm64.deb ...
#10 16.53 Unpacking wget (1.21-1+deb11u2) ...
#10 16.60 Selecting previously unselected package xz-utils.
#10 16.60 Preparing to unpack .../011-xz-utils_5.2.5-2.1~deb11u1_arm64.deb ...
#10 16.60 Unpacking xz-utils (5.2.5-2.1~deb11u1) ...
#10 16.62 Selecting previously unselected package binutils-common:arm64.
#10 16.62 Preparing to unpack .../012-binutils-common_2.35.2-2_arm64.deb ...
#10 16.62 Unpacking binutils-common:arm64 (2.35.2-2) ...
#10 16.78 Selecting previously unselected package libbinutils:arm64.
#10 16.78 Preparing to unpack .../013-libbinutils_2.35.2-2_arm64.deb ...
#10 16.79 Unpacking libbinutils:arm64 (2.35.2-2) ...
#10 16.83 Selecting previously unselected package libctf-nobfd0:arm64.
#10 16.83 Preparing to unpack .../014-libctf-nobfd0_2.35.2-2_arm64.deb ...
#10 16.83 Unpacking libctf-nobfd0:arm64 (2.35.2-2) ...
#10 16.85 Selecting previously unselected package libctf0:arm64.
#10 16.85 Preparing to unpack .../015-libctf0_2.35.2-2_arm64.deb ...
#10 16.85 Unpacking libctf0:arm64 (2.35.2-2) ...
#10 16.87 Selecting previously unselected package binutils-aarch64-linux-gnu.
#10 16.87 Preparing to unpack .../016-binutils-aarch64-linux-gnu_2.35.2-2_arm64.deb ...
#10 16.87 Unpacking binutils-aarch64-linux-gnu (2.35.2-2) ...
#10 17.04 Selecting previously unselected package binutils.
#10 17.04 Preparing to unpack .../017-binutils_2.35.2-2_arm64.deb ...
#10 17.04 Unpacking binutils (2.35.2-2) ...
#10 17.05 Selecting previously unselected package libc-dev-bin.
#10 17.05 Preparing to unpack .../018-libc-dev-bin_2.31-13+deb11u13_arm64.deb ...
#10 17.06 Unpacking libc-dev-bin (2.31-13+deb11u13) ...
#10 17.08 Selecting previously unselected package linux-libc-dev:arm64.
#10 17.08 Preparing to unpack .../019-linux-libc-dev_5.10.237-1_arm64.deb ...
#10 17.08 Unpacking linux-libc-dev:arm64 (5.10.237-1) ...
#10 17.17 Selecting previously unselected package libcrypt-dev:arm64.
#10 17.17 Preparing to unpack .../020-libcrypt-dev_1%3a4.4.18-4_arm64.deb ...
#10 17.18 Unpacking libcrypt-dev:arm64 (1:4.4.18-4) ...
#10 17.20 Selecting previously unselected package libtirpc-dev:arm64.
#10 17.20 Preparing to unpack .../021-libtirpc-dev_1.3.1-1+deb11u1_arm64.deb ...
#10 17.20 Unpacking libtirpc-dev:arm64 (1.3.1-1+deb11u1) ...
#10 17.22 Selecting previously unselected package libnsl-dev:arm64.
#10 17.22 Preparing to unpack .../022-libnsl-dev_1.3.0-2_arm64.deb ...
#10 17.23 Unpacking libnsl-dev:arm64 (1.3.0-2) ...
#10 17.24 Selecting previously unselected package libc6-dev:arm64.
#10 17.24 Preparing to unpack .../023-libc6-dev_2.31-13+deb11u13_arm64.deb ...
#10 17.24 Unpacking libc6-dev:arm64 (2.31-13+deb11u13) ...
#10 17.41 Selecting previously unselected package libisl23:arm64.
#10 17.41 Preparing to unpack .../024-libisl23_0.23-1_arm64.deb ...
#10 17.42 Unpacking libisl23:arm64 (0.23-1) ...
#10 17.49 Selecting previously unselected package libmpfr6:arm64.
#10 17.49 Preparing to unpack .../025-libmpfr6_4.1.0-3_arm64.deb ...
#10 17.49 Unpacking libmpfr6:arm64 (4.1.0-3) ...
#10 17.52 Selecting previously unselected package libmpc3:arm64.
#10 17.52 Preparing to unpack .../026-libmpc3_1.2.0-1_arm64.deb ...
#10 17.53 Unpacking libmpc3:arm64 (1.2.0-1) ...
#10 17.54 Selecting previously unselected package cpp-10.
#10 17.54 Preparing to unpack .../027-cpp-10_10.2.1-6_arm64.deb ...
#10 17.54 Unpacking cpp-10 (10.2.1-6) ...
#10 17.95 Selecting previously unselected package cpp.
#10 17.95 Preparing to unpack .../028-cpp_4%3a10.2.1-1_arm64.deb ...
#10 17.96 Unpacking cpp (4:10.2.1-1) ...
#10 17.97 Selecting previously unselected package libcc1-0:arm64.
#10 17.97 Preparing to unpack .../029-libcc1-0_10.2.1-6_arm64.deb ...
#10 17.97 Unpacking libcc1-0:arm64 (10.2.1-6) ...
#10 17.99 Selecting previously unselected package libgomp1:arm64.
#10 17.99 Preparing to unpack .../030-libgomp1_10.2.1-6_arm64.deb ...
#10 18.00 Unpacking libgomp1:arm64 (10.2.1-6) ...
#10 18.01 Selecting previously unselected package libitm1:arm64.
#10 18.01 Preparing to unpack .../031-libitm1_10.2.1-6_arm64.deb ...
#10 18.01 Unpacking libitm1:arm64 (10.2.1-6) ...
#10 18.03 Selecting previously unselected package libatomic1:arm64.
#10 18.03 Preparing to unpack .../032-libatomic1_10.2.1-6_arm64.deb ...
#10 18.03 Unpacking libatomic1:arm64 (10.2.1-6) ...
#10 18.04 Selecting previously unselected package libasan6:arm64.
#10 18.04 Preparing to unpack .../033-libasan6_10.2.1-6_arm64.deb ...
#10 18.05 Unpacking libasan6:arm64 (10.2.1-6) ...
#10 18.18 Selecting previously unselected package liblsan0:arm64.
#10 18.18 Preparing to unpack .../034-liblsan0_10.2.1-6_arm64.deb ...
#10 18.18 Unpacking liblsan0:arm64 (10.2.1-6) ...
#10 18.23 Selecting previously unselected package libtsan0:arm64.
#10 18.23 Preparing to unpack .../035-libtsan0_10.2.1-6_arm64.deb ...
#10 18.24 Unpacking libtsan0:arm64 (10.2.1-6) ...
#10 18.36 Selecting previously unselected package libubsan1:arm64.
#10 18.36 Preparing to unpack .../036-libubsan1_10.2.1-6_arm64.deb ...
#10 18.37 Unpacking libubsan1:arm64 (10.2.1-6) ...
#10 18.42 Selecting previously unselected package libgcc-10-dev:arm64.
#10 18.42 Preparing to unpack .../037-libgcc-10-dev_10.2.1-6_arm64.deb ...
#10 18.42 Unpacking libgcc-10-dev:arm64 (10.2.1-6) ...
#10 18.51 Selecting previously unselected package gcc-10.
#10 18.51 Preparing to unpack .../038-gcc-10_10.2.1-6_arm64.deb ...
#10 18.51 Unpacking gcc-10 (10.2.1-6) ...
#10 19.32 Selecting previously unselected package gcc.
#10 19.32 Preparing to unpack .../039-gcc_4%3a10.2.1-1_arm64.deb ...
#10 19.32 Unpacking gcc (4:10.2.1-1) ...
#10 19.33 Selecting previously unselected package libstdc++-10-dev:arm64.
#10 19.33 Preparing to unpack .../040-libstdc++-10-dev_10.2.1-6_arm64.deb ...
#10 19.33 Unpacking libstdc++-10-dev:arm64 (10.2.1-6) ...
#10 19.46 Selecting previously unselected package g++-10.
#10 19.46 Preparing to unpack .../041-g++-10_10.2.1-6_arm64.deb ...
#10 19.46 Unpacking g++-10 (10.2.1-6) ...
#10 19.93 Selecting previously unselected package g++.
#10 19.94 Preparing to unpack .../042-g++_4%3a10.2.1-1_arm64.deb ...
#10 19.94 Unpacking g++ (4:10.2.1-1) ...
#10 19.96 Selecting previously unselected package make.
#10 19.96 Preparing to unpack .../043-make_4.3-4.1_arm64.deb ...
#10 19.97 Unpacking make (4.3-4.1) ...
#10 20.02 Selecting previously unselected package libdpkg-perl.
#10 20.03 Preparing to unpack .../044-libdpkg-perl_1.20.13_all.deb ...
#10 20.03 Unpacking libdpkg-perl (1.20.13) ...
#10 20.09 Selecting previously unselected package patch.
#10 20.09 Preparing to unpack .../045-patch_2.7.6-7_arm64.deb ...
#10 20.09 Unpacking patch (2.7.6-7) ...
#10 20.11 Selecting previously unselected package dpkg-dev.
#10 20.12 Preparing to unpack .../046-dpkg-dev_1.20.13_all.deb ...
#10 20.12 Unpacking dpkg-dev (1.20.13) ...
#10 20.20 Selecting previously unselected package build-essential.
#10 20.20 Preparing to unpack .../047-build-essential_12.9_arm64.deb ...
#10 20.20 Unpacking build-essential (12.9) ...
#10 20.22 Selecting previously unselected package libbrotli1:arm64.
#10 20.23 Preparing to unpack .../048-libbrotli1_1.0.9-2+b2_arm64.deb ...
#10 20.23 Unpacking libbrotli1:arm64 (1.0.9-2+b2) ...
#10 20.26 Selecting previously unselected package libsasl2-modules-db:arm64.
#10 20.26 Preparing to unpack .../049-libsasl2-modules-db_2.1.27+dfsg-2.1+deb11u1_arm64.deb ...
#10 20.26 Unpacking libsasl2-modules-db:arm64 (2.1.27+dfsg-2.1+deb11u1) ...
#10 20.28 Selecting previously unselected package libsasl2-2:arm64.
#10 20.28 Preparing to unpack .../050-libsasl2-2_2.1.27+dfsg-2.1+deb11u1_arm64.deb ...
#10 20.28 Unpacking libsasl2-2:arm64 (2.1.27+dfsg-2.1+deb11u1) ...
#10 20.29 Selecting previously unselected package libldap-2.4-2:arm64.
#10 20.29 Preparing to unpack .../051-libldap-2.4-2_2.4.57+dfsg-3+deb11u1_arm64.deb ...
#10 20.30 Unpacking libldap-2.4-2:arm64 (2.4.57+dfsg-3+deb11u1) ...
#10 20.34 Selecting previously unselected package libnghttp2-14:arm64.
#10 20.34 Preparing to unpack .../052-libnghttp2-14_1.43.0-1+deb11u2_arm64.deb ...
#10 20.35 Unpacking libnghttp2-14:arm64 (1.43.0-1+deb11u2) ...
#10 20.38 Selecting previously unselected package librtmp1:arm64.
#10 20.38 Preparing to unpack .../053-librtmp1_2.4+20151223.gitfa8646d.1-2+b2_arm64.deb ...
#10 20.39 Unpacking librtmp1:arm64 (2.4+20151223.gitfa8646d.1-2+b2) ...
#10 20.43 Selecting previously unselected package libssh2-1:arm64.
#10 20.43 Preparing to unpack .../054-libssh2-1_1.9.0-2+deb11u1_arm64.deb ...
#10 20.43 Unpacking libssh2-1:arm64 (1.9.0-2+deb11u1) ...
#10 20.47 Selecting previously unselected package libcurl4:arm64.
#10 20.47 Preparing to unpack .../055-libcurl4_7.74.0-1.3+deb11u15_arm64.deb ...
#10 20.48 Unpacking libcurl4:arm64 (7.74.0-1.3+deb11u15) ...
#10 20.51 Selecting previously unselected package curl.
#10 20.51 Preparing to unpack .../056-curl_7.74.0-1.3+deb11u15_arm64.deb ...
#10 20.51 Unpacking curl (7.74.0-1.3+deb11u15) ...
#10 20.54 Selecting previously unselected package libassuan0:arm64.
#10 20.54 Preparing to unpack .../057-libassuan0_2.5.3-7.1_arm64.deb ...
#10 20.54 Unpacking libassuan0:arm64 (2.5.3-7.1) ...
#10 20.56 Selecting previously unselected package gpgconf.
#10 20.56 Preparing to unpack .../058-gpgconf_2.2.27-2+deb11u2_arm64.deb ...
#10 20.57 Unpacking gpgconf (2.2.27-2+deb11u2) ...
#10 20.60 Selecting previously unselected package libksba8:arm64.
#10 20.60 Preparing to unpack .../059-libksba8_1.5.0-3+deb11u2_arm64.deb ...
#10 20.60 Unpacking libksba8:arm64 (1.5.0-3+deb11u2) ...
#10 20.62 Selecting previously unselected package libnpth0:arm64.
#10 20.65 Preparing to unpack .../060-libnpth0_1.6-3_arm64.deb ...
#10 20.65 Unpacking libnpth0:arm64 (1.6-3) ...
#10 20.68 Selecting previously unselected package dirmngr.
#10 20.68 Preparing to unpack .../061-dirmngr_2.2.27-2+deb11u2_arm64.deb ...
#10 20.69 Unpacking dirmngr (2.2.27-2+deb11u2) ...
#10 20.72 Selecting previously unselected package libfakeroot:arm64.
#10 20.72 Preparing to unpack .../062-libfakeroot_1.25.3-1.1_arm64.deb ...
#10 20.72 Unpacking libfakeroot:arm64 (1.25.3-1.1) ...
#10 20.73 Selecting previously unselected package fakeroot.
#10 20.74 Preparing to unpack .../063-fakeroot_1.25.3-1.1_arm64.deb ...
#10 20.74 Unpacking fakeroot (1.25.3-1.1) ...
#10 20.76 Selecting previously unselected package fonts-dejavu-core.
#10 20.76 Preparing to unpack .../064-fonts-dejavu-core_2.37-2_all.deb ...
#10 20.77 Unpacking fonts-dejavu-core (2.37-2) ...
#10 20.84 Selecting previously unselected package fontconfig-config.
#10 20.84 Preparing to unpack .../065-fontconfig-config_2.13.1-4.2_all.deb ...
#10 20.90 Unpacking fontconfig-config (2.13.1-4.2) ...
#10 20.93 Selecting previously unselected package gnupg-l10n.
#10 20.94 Preparing to unpack .../066-gnupg-l10n_2.2.27-2+deb11u2_all.deb ...
#10 20.94 Unpacking gnupg-l10n (2.2.27-2+deb11u2) ...
#10 21.02 Selecting previously unselected package gnupg-utils.
#10 21.02 Preparing to unpack .../067-gnupg-utils_2.2.27-2+deb11u2_arm64.deb ...
#10 21.02 Unpacking gnupg-utils (2.2.27-2+deb11u2) ...
#10 21.06 Selecting previously unselected package gpg.
#10 21.06 Preparing to unpack .../068-gpg_2.2.27-2+deb11u2_arm64.deb ...
#10 21.06 Unpacking gpg (2.2.27-2+deb11u2) ...
#10 21.11 Selecting previously unselected package pinentry-curses.
#10 21.11 Preparing to unpack .../069-pinentry-curses_1.1.0-4_arm64.deb ...
#10 21.11 Unpacking pinentry-curses (1.1.0-4) ...
#10 21.14 Selecting previously unselected package gpg-agent.
#10 21.14 Preparing to unpack .../070-gpg-agent_2.2.27-2+deb11u2_arm64.deb ...
#10 21.14 Unpacking gpg-agent (2.2.27-2+deb11u2) ...
#10 21.16 Selecting previously unselected package gpg-wks-client.
#10 21.17 Preparing to unpack .../071-gpg-wks-client_2.2.27-2+deb11u2_arm64.deb ...
#10 21.17 Unpacking gpg-wks-client (2.2.27-2+deb11u2) ...
#10 21.19 Selecting previously unselected package gpg-wks-server.
#10 21.19 Preparing to unpack .../072-gpg-wks-server_2.2.27-2+deb11u2_arm64.deb ...
#10 21.19 Unpacking gpg-wks-server (2.2.27-2+deb11u2) ...
#10 21.21 Selecting previously unselected package gpgsm.
#10 21.22 Preparing to unpack .../073-gpgsm_2.2.27-2+deb11u2_arm64.deb ...
#10 21.22 Unpacking gpgsm (2.2.27-2+deb11u2) ...
#10 21.24 Selecting previously unselected package gnupg.
#10 21.24 Preparing to unpack .../074-gnupg_2.2.27-2+deb11u2_all.deb ...
#10 21.24 Unpacking gnupg (2.2.27-2+deb11u2) ...
#10 21.27 Selecting previously unselected package libalgorithm-diff-perl.
#10 21.27 Preparing to unpack .../075-libalgorithm-diff-perl_1.201-1_all.deb ...
#10 21.27 Unpacking libalgorithm-diff-perl (1.201-1) ...
#10 21.28 Selecting previously unselected package libalgorithm-diff-xs-perl.
#10 21.28 Preparing to unpack .../076-libalgorithm-diff-xs-perl_0.04-6+b1_arm64.deb ...
#10 21.28 Unpacking libalgorithm-diff-xs-perl (0.04-6+b1) ...
#10 21.29 Selecting previously unselected package libalgorithm-merge-perl.
#10 21.30 Preparing to unpack .../077-libalgorithm-merge-perl_0.08-3_all.deb ...
#10 21.30 Unpacking libalgorithm-merge-perl (0.08-3) ...
#10 21.32 Selecting previously unselected package libmd0:arm64.
#10 21.32 Preparing to unpack .../078-libmd0_1.0.3-3_arm64.deb ...
#10 21.32 Unpacking libmd0:arm64 (1.0.3-3) ...
#10 21.34 Selecting previously unselected package libbsd0:arm64.
#10 21.34 Preparing to unpack .../079-libbsd0_0.11.3-1+deb11u1_arm64.deb ...
#10 21.34 Unpacking libbsd0:arm64 (0.11.3-1+deb11u1) ...
#10 21.36 Selecting previously unselected package libexpat1:arm64.
#10 21.36 Preparing to unpack .../080-libexpat1_2.2.10-2+deb11u7_arm64.deb ...
#10 21.36 Unpacking libexpat1:arm64 (2.2.10-2+deb11u7) ...
#10 21.37 Selecting previously unselected package libpng16-16:arm64.
#10 21.38 Preparing to unpack .../081-libpng16-16_1.6.37-3_arm64.deb ...
#10 21.38 Unpacking libpng16-16:arm64 (1.6.37-3) ...
#10 21.40 Selecting previously unselected package libfreetype6:arm64.
#10 21.40 Preparing to unpack .../082-libfreetype6_2.10.4+dfsg-1+deb11u2_arm64.deb ...
#10 21.40 Unpacking libfreetype6:arm64 (2.10.4+dfsg-1+deb11u2) ...
#10 21.43 Selecting previously unselected package libfontconfig1:arm64.
#10 21.44 Preparing to unpack .../083-libfontconfig1_2.13.1-4.2_arm64.deb ...
#10 21.44 Unpacking libfontconfig1:arm64 (2.13.1-4.2) ...
#10 21.46 Selecting previously unselected package libjpeg62-turbo:arm64.
#10 21.47 Preparing to unpack .../084-libjpeg62-turbo_1%3a2.0.6-4_arm64.deb ...
#10 21.47 Unpacking libjpeg62-turbo:arm64 (1:2.0.6-4) ...
#10 21.49 Selecting previously unselected package libdeflate0:arm64.
#10 21.49 Preparing to unpack .../085-libdeflate0_1.7-1_arm64.deb ...
#10 21.49 Unpacking libdeflate0:arm64 (1.7-1) ...
#10 21.50 Selecting previously unselected package libjbig0:arm64.
#10 21.50 Preparing to unpack .../086-libjbig0_2.1-3.1+b2_arm64.deb ...
#10 21.50 Unpacking libjbig0:arm64 (2.1-3.1+b2) ...
#10 21.53 Selecting previously unselected package libwebp6:arm64.
#10 21.53 Preparing to unpack .../087-libwebp6_0.6.1-2.1+deb11u2_arm64.deb ...
#10 21.54 Unpacking libwebp6:arm64 (0.6.1-2.1+deb11u2) ...
#10 21.56 Selecting previously unselected package libtiff5:arm64.
#10 21.56 Preparing to unpack .../088-libtiff5_4.2.0-1+deb11u6_arm64.deb ...
#10 21.56 Unpacking libtiff5:arm64 (4.2.0-1+deb11u6) ...
#10 21.58 Selecting previously unselected package libxau6:arm64.
#10 21.59 Preparing to unpack .../089-libxau6_1%3a1.0.9-1_arm64.deb ...
#10 21.59 Unpacking libxau6:arm64 (1:1.0.9-1) ...
#10 21.60 Selecting previously unselected package libxdmcp6:arm64.
#10 21.60 Preparing to unpack .../090-libxdmcp6_1%3a1.1.2-3_arm64.deb ...
#10 21.60 Unpacking libxdmcp6:arm64 (1:1.1.2-3) ...
#10 21.63 Selecting previously unselected package libxcb1:arm64.
#10 21.63 Preparing to unpack .../091-libxcb1_1.14-3_arm64.deb ...
#10 21.63 Unpacking libxcb1:arm64 (1.14-3) ...
#10 21.67 Selecting previously unselected package libx11-data.
#10 21.67 Preparing to unpack .../092-libx11-data_2%3a1.7.2-1+deb11u2_all.deb ...
#10 21.68 Unpacking libx11-data (2:1.7.2-1+deb11u2) ...
#10 21.72 Selecting previously unselected package libx11-6:arm64.
#10 21.72 Preparing to unpack .../093-libx11-6_2%3a1.7.2-1+deb11u2_arm64.deb ...
#10 21.73 Unpacking libx11-6:arm64 (2:1.7.2-1+deb11u2) ...
#10 21.78 Selecting previously unselected package libxpm4:arm64.
#10 21.78 Preparing to unpack .../094-libxpm4_1%3a3.5.12-1.1+deb11u1_arm64.deb ...
#10 21.78 Unpacking libxpm4:arm64 (1:3.5.12-1.1+deb11u1) ...
#10 21.80 Selecting previously unselected package libgd3:arm64.
#10 21.80 Preparing to unpack .../095-libgd3_2.3.0-2_arm64.deb ...
#10 21.80 Unpacking libgd3:arm64 (2.3.0-2) ...
#10 21.82 Selecting previously unselected package libc-devtools.
#10 21.82 Preparing to unpack .../096-libc-devtools_2.31-13+deb11u13_arm64.deb ...
#10 21.82 Unpacking libc-devtools (2.31-13+deb11u13) ...
#10 21.84 Selecting previously unselected package libfile-fcntllock-perl.
#10 21.84 Preparing to unpack .../097-libfile-fcntllock-perl_0.22-3+b7_arm64.deb ...
#10 21.84 Unpacking libfile-fcntllock-perl (0.22-3+b7) ...
#10 21.85 Selecting previously unselected package libldap-common.
#10 21.85 Preparing to unpack .../098-libldap-common_2.4.57+dfsg-3+deb11u1_all.deb ...
#10 21.86 Unpacking libldap-common (2.4.57+dfsg-3+deb11u1) ...
#10 21.89 Selecting previously unselected package libsasl2-modules:arm64.
#10 21.89 Preparing to unpack .../099-libsasl2-modules_2.1.27+dfsg-2.1+deb11u1_arm64.deb ...
#10 21.89 Unpacking libsasl2-modules:arm64 (2.1.27+dfsg-2.1+deb11u1) ...
#10 21.90 Selecting previously unselected package manpages-dev.
#10 21.90 Preparing to unpack .../100-manpages-dev_5.10-1_all.deb ...
#10 21.91 Unpacking manpages-dev (5.10-1) ...
#10 22.06 Selecting previously unselected package publicsuffix.
#10 22.06 Preparing to unpack .../101-publicsuffix_20220811.1734-0+deb11u1_all.deb ...
#10 22.07 Unpacking publicsuffix (20220811.1734-0+deb11u1) ...
#10 22.09 Selecting previously unselected package unzip.
#10 22.10 Preparing to unpack .../102-unzip_6.0-26+deb11u1_arm64.deb ...
#10 22.10 Unpacking unzip (6.0-26+deb11u1) ...
#10 22.12 Setting up libksba8:arm64 (1.5.0-3+deb11u2) ...
#10 22.12 Setting up libexpat1:arm64 (2.2.10-2+deb11u7) ...
#10 22.13 Setting up libxau6:arm64 (1:1.0.9-1) ...
#10 22.13 Setting up libpsl5:arm64 (0.21.0-1.2) ...
#10 22.13 Setting up wget (1.21-1+deb11u2) ...
#10 22.14 Setting up manpages (5.10-1) ...
#10 22.15 Setting up unzip (6.0-26+deb11u1) ...
#10 22.17 Setting up perl-modules-5.32 (5.32.1-4+deb11u4) ...
#10 22.17 Setting up libbrotli1:arm64 (1.0.9-2+b2) ...
#10 22.18 Setting up libsasl2-modules:arm64 (2.1.27+dfsg-2.1+deb11u1) ...
#10 22.18 Setting up binutils-common:arm64 (2.35.2-2) ...
#10 22.19 Setting up libnghttp2-14:arm64 (1.43.0-1+deb11u2) ...
#10 22.19 Setting up libdeflate0:arm64 (1.7-1) ...
#10 22.20 Setting up linux-libc-dev:arm64 (5.10.237-1) ...
#10 22.20 Setting up libctf-nobfd0:arm64 (2.35.2-2) ...
#10 22.21 Setting up libnpth0:arm64 (1.6-3) ...
#10 22.21 Setting up libassuan0:arm64 (2.5.3-7.1) ...
#10 22.21 Setting up libgomp1:arm64 (10.2.1-6) ...
#10 22.22 Setting up bzip2 (1.0.8-4) ...
#10 22.22 Setting up libldap-common (2.4.57+dfsg-3+deb11u1) ...
#10 22.22 Setting up libjbig0:arm64 (2.1-3.1+b2) ...
#10 22.23 Setting up libfakeroot:arm64 (1.25.3-1.1) ...
#10 22.23 Setting up libasan6:arm64 (10.2.1-6) ...
#10 22.24 Setting up libsasl2-modules-db:arm64 (2.1.27+dfsg-2.1+deb11u1) ...
#10 22.25 Setting up fakeroot (1.25.3-1.1) ...
#10 22.27 update-alternatives: using /usr/bin/fakeroot-sysv to provide /usr/bin/fakeroot (fakeroot) in auto mode
#10 22.27 update-alternatives: warning: skip creation of /usr/share/man/man1/fakeroot.1.gz because associated file /usr/share/man/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
#10 22.27 update-alternatives: warning: skip creation of /usr/share/man/man1/faked.1.gz because associated file /usr/share/man/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
#10 22.27 update-alternatives: warning: skip creation of /usr/share/man/es/man1/fakeroot.1.gz because associated file /usr/share/man/es/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
#10 22.27 update-alternatives: warning: skip creation of /usr/share/man/es/man1/faked.1.gz because associated file /usr/share/man/es/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
#10 22.27 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/fakeroot.1.gz because associated file /usr/share/man/fr/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
#10 22.27 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/faked.1.gz because associated file /usr/share/man/fr/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
#10 22.27 update-alternatives: warning: skip creation of /usr/share/man/sv/man1/fakeroot.1.gz because associated file /usr/share/man/sv/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
#10 22.27 update-alternatives: warning: skip creation of /usr/share/man/sv/man1/faked.1.gz because associated file /usr/share/man/sv/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
#10 22.27 Setting up libtirpc-dev:arm64 (1.3.1-1+deb11u1) ...
#10 22.28 Setting up libjpeg62-turbo:arm64 (1:2.0.6-4) ...
#10 22.28 Setting up libx11-data (2:1.7.2-1+deb11u2) ...
#10 22.29 Setting up make (4.3-4.1) ...
#10 22.29 Setting up libmpfr6:arm64 (4.1.0-3) ...
#10 22.30 Setting up gnupg-l10n (2.2.27-2+deb11u2) ...
#10 22.30 Setting up librtmp1:arm64 (2.4+20151223.gitfa8646d.1-2+b2) ...
#10 22.30 Setting up xz-utils (5.2.5-2.1~deb11u1) ...
#10 22.31 update-alternatives: using /usr/bin/xz to provide /usr/bin/lzma (lzma) in auto mode
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzma.1.gz because associated file /usr/share/man/man1/xz.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/unlzma.1.gz because associated file /usr/share/man/man1/unxz.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzcat.1.gz because associated file /usr/share/man/man1/xzcat.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzmore.1.gz because associated file /usr/share/man/man1/xzmore.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzless.1.gz because associated file /usr/share/man/man1/xzless.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzdiff.1.gz because associated file /usr/share/man/man1/xzdiff.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzcmp.1.gz because associated file /usr/share/man/man1/xzcmp.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzgrep.1.gz because associated file /usr/share/man/man1/xzgrep.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzegrep.1.gz because associated file /usr/share/man/man1/xzegrep.1.gz (of link group lzma) doesn't exist
#10 22.31 update-alternatives: warning: skip creation of /usr/share/man/man1/lzfgrep.1.gz because associated file /usr/share/man/man1/xzfgrep.1.gz (of link group lzma) doesn't exist
#10 22.31 Setting up libpng16-16:arm64 (1.6.37-3) ...
#10 22.31 Setting up libmpc3:arm64 (1.2.0-1) ...
#10 22.32 Setting up libatomic1:arm64 (10.2.1-6) ...
#10 22.32 Setting up patch (2.7.6-7) ...
#10 22.32 Setting up libwebp6:arm64 (0.6.1-2.1+deb11u2) ...
#10 22.33 Setting up fonts-dejavu-core (2.37-2) ...
#10 22.36 Setting up libgdbm-compat4:arm64 (1.19-2) ...
#10 22.37 Setting up libperl5.32:arm64 (5.32.1-4+deb11u4) ...
#10 22.37 Setting up libsasl2-2:arm64 (2.1.27+dfsg-2.1+deb11u1) ...
#10 22.39 Setting up libubsan1:arm64 (10.2.1-6) ...
#10 22.39 Setting up libmd0:arm64 (1.0.3-3) ...
#10 22.40 Setting up libnsl-dev:arm64 (1.3.0-2) ...
#10 22.40 Setting up sensible-utils (0.0.14) ...
#10 22.40 Setting up libcrypt-dev:arm64 (1:4.4.18-4) ...
#10 22.41 Setting up gpgconf (2.2.27-2+deb11u2) ...
#10 22.41 Setting up libssh2-1:arm64 (1.9.0-2+deb11u1) ...
#10 22.42 Setting up libtiff5:arm64 (4.2.0-1+deb11u6) ...
#10 22.42 Setting up libbinutils:arm64 (2.35.2-2) ...
#10 22.42 Setting up libisl23:arm64 (0.23-1) ...
#10 22.43 Setting up libc-dev-bin (2.31-13+deb11u13) ...
#10 22.43 Setting up libbsd0:arm64 (0.11.3-1+deb11u1) ...
#10 22.44 Setting up publicsuffix (20220811.1734-0+deb11u1) ...
#10 22.44 Setting up libcc1-0:arm64 (10.2.1-6) ...
#10 22.44 Setting up liblocale-gettext-perl (1.07-4+b1) ...
#10 22.45 Setting up gpg (2.2.27-2+deb11u2) ...
#10 22.46 Setting up liblsan0:arm64 (10.2.1-6) ...
#10 22.47 Setting up cpp-10 (10.2.1-6) ...
#10 22.48 Setting up libitm1:arm64 (10.2.1-6) ...
#10 22.48 Setting up gnupg-utils (2.2.27-2+deb11u2) ...
#10 22.49 Setting up libtsan0:arm64 (10.2.1-6) ...
#10 22.49 Setting up libctf0:arm64 (2.35.2-2) ...
#10 22.50 Setting up pinentry-curses (1.1.0-4) ...
#10 22.50 Setting up manpages-dev (5.10-1) ...
#10 22.51 Setting up libxdmcp6:arm64 (1:1.1.2-3) ...
#10 22.51 Setting up libxcb1:arm64 (1.14-3) ...
#10 22.52 Setting up gpg-agent (2.2.27-2+deb11u2) ...
#10 22.72 Setting up libgcc-10-dev:arm64 (10.2.1-6) ...
#10 22.72 Setting up gpgsm (2.2.27-2+deb11u2) ...
#10 22.73 Setting up libldap-2.4-2:arm64 (2.4.57+dfsg-3+deb11u1) ...
#10 22.73 Setting up binutils-aarch64-linux-gnu (2.35.2-2) ...
#10 22.74 Setting up binutils (2.35.2-2) ...
#10 22.74 Setting up dirmngr (2.2.27-2+deb11u2) ...
#10 22.80 Setting up perl (5.32.1-4+deb11u4) ...
#10 22.81 Setting up libfreetype6:arm64 (2.10.4+dfsg-1+deb11u2) ...
#10 22.81 Setting up ucf (3.0043+deb11u2) ...
#10 22.86 debconf: unable to initialize frontend: Dialog
#10 22.86 debconf: (TERM is not set, so the dialog frontend is not usable.)
#10 22.86 debconf: falling back to frontend: Readline
#10 22.89 Setting up gcc-10 (10.2.1-6) ...
#10 22.89 Setting up libdpkg-perl (1.20.13) ...
#10 22.90 Setting up gpg-wks-server (2.2.27-2+deb11u2) ...
#10 22.90 Setting up cpp (4:10.2.1-1) ...
#10 22.95 Setting up libcurl4:arm64 (7.74.0-1.3+deb11u15) ...
#10 23.04 Setting up libc6-dev:arm64 (2.31-13+deb11u13) ...
#10 23.05 Setting up libx11-6:arm64 (2:1.7.2-1+deb11u2) ...
#10 23.09 Setting up curl (7.74.0-1.3+deb11u15) ...
#10 23.18 Setting up libstdc++-10-dev:arm64 (10.2.1-6) ...
#10 23.18 Setting up g++-10 (10.2.1-6) ...
#10 23.19 Setting up libxpm4:arm64 (1:3.5.12-1.1+deb11u1) ...
#10 23.19 Setting up gpg-wks-client (2.2.27-2+deb11u2) ...
#10 23.20 Setting up libfile-fcntllock-perl (0.22-3+b7) ...
#10 23.20 Setting up libalgorithm-diff-perl (1.201-1) ...
#10 23.20 Setting up fontconfig-config (2.13.1-4.2) ...
#10 23.24 debconf: unable to initialize frontend: Dialog
#10 23.24 debconf: (TERM is not set, so the dialog frontend is not usable.)
#10 23.24 debconf: falling back to frontend: Readline
#10 23.32 Setting up gcc (4:10.2.1-1) ...
#10 23.33 Setting up dpkg-dev (1.20.13) ...
#10 23.34 Setting up g++ (4:10.2.1-1) ...
#10 23.35 update-alternatives: using /usr/bin/g++ to provide /usr/bin/c++ (c++) in auto mode
#10 23.35 Setting up gnupg (2.2.27-2+deb11u2) ...
#10 23.36 Setting up build-essential (12.9) ...
#10 23.37 Setting up libfontconfig1:arm64 (2.13.1-4.2) ...
#10 23.37 Setting up libalgorithm-diff-xs-perl (0.04-6+b1) ...
#10 23.38 Setting up libalgorithm-merge-perl (0.08-3) ...
#10 23.38 Setting up libgd3:arm64 (2.3.0-2) ...
#10 23.39 Setting up libc-devtools (2.31-13+deb11u13) ...
#10 23.39 Processing triggers for libc-bin (2.31-13+deb11u13) ...
#10 DONE 24.0s

#11 [workers stage-1  3/20] RUN   echo "deb https://deb.nodesource.com/node_18.x buster main" > /etc/apt/sources.list.d/nodesource.list &&   wget -qO- https://deb.nodesource.com/gpgkey/nodesource.gpg.key | apt-key add - &&   apt-get update &&   apt-get install -yqq nodejs &&   pip install -U pip~=23.0.1 && pip install "urllib3<2" pdm~=2.5.2 &&   npm i -g npm@^8 pnpm@~9.5.0 &&   rm -rf /var/lib/apt/lists/*
#11 0.195 Warning: apt-key is deprecated. Manage keyring files in trusted.gpg.d instead (see apt-key(8)).
#11 1.629 OK
#11 1.702 Hit:1 http://deb.debian.org/debian bullseye InRelease
#11 1.703 Hit:2 http://deb.debian.org/debian-security bullseye-security InRelease
#11 1.719 Hit:3 http://deb.debian.org/debian bullseye-updates InRelease
#11 2.060 Get:4 https://deb.nodesource.com/node_18.x buster InRelease [4584 B]
#11 2.473 Get:5 https://deb.nodesource.com/node_18.x buster/main arm64 Packages [776 B]
#11 2.493 Fetched 5360 B in 1s (6438 B/s)
#11 2.493 Reading package lists...
#11 6.684 debconf: delaying package configuration, since apt-utils is not installed
#11 6.725 Selecting previously unselected package libpython3.9-minimal:arm64.
#11 6.725 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 16380 files and directories currently installed.)
#11 6.730 Preparing to unpack .../libpython3.9-minimal_3.9.2-1+deb11u3_arm64.deb ...
#11 6.736 Unpacking libpython3.9-minimal:arm64 (3.9.2-1+deb11u3) ...
#11 6.816 Selecting previously unselected package python3.9-minimal.
#11 6.817 Preparing to unpack .../python3.9-minimal_3.9.2-1+deb11u3_arm64.deb ...
#11 6.824 Unpacking python3.9-minimal (3.9.2-1+deb11u3) ...
#11 6.949 Setting up libpython3.9-minimal:arm64 (3.9.2-1+deb11u3) ...
#11 6.965 Setting up python3.9-minimal (3.9.2-1+deb11u3) ...
#11 7.270 Selecting previously unselected package python3-minimal.
#11 7.270 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 16665 files and directories currently installed.)
#11 7.279 Preparing to unpack .../0-python3-minimal_3.9.2-3_arm64.deb ...
#11 7.285 Unpacking python3-minimal (3.9.2-3) ...
#11 7.311 Selecting previously unselected package media-types.
#11 7.312 Preparing to unpack .../1-media-types_4.0.0_all.deb ...
#11 7.317 Unpacking media-types (4.0.0) ...
#11 7.351 Selecting previously unselected package libmpdec3:arm64.
#11 7.352 Preparing to unpack .../2-libmpdec3_2.5.1-1_arm64.deb ...
#11 7.360 Unpacking libmpdec3:arm64 (2.5.1-1) ...
#11 7.390 Selecting previously unselected package libpython3.9-stdlib:arm64.
#11 7.391 Preparing to unpack .../3-libpython3.9-stdlib_3.9.2-1+deb11u3_arm64.deb ...
#11 7.394 Unpacking libpython3.9-stdlib:arm64 (3.9.2-1+deb11u3) ...
#11 7.506 Selecting previously unselected package nodejs.
#11 7.507 Preparing to unpack .../4-nodejs_18.17.1-deb-1nodesource1_arm64.deb ...
#11 7.514 Unpacking nodejs (18.17.1-deb-1nodesource1) ...
#11 9.532 Selecting previously unselected package python3.9.
#11 9.534 Preparing to unpack .../5-python3.9_3.9.2-1+deb11u3_arm64.deb ...
#11 9.540 Unpacking python3.9 (3.9.2-1+deb11u3) ...
#11 9.576 Setting up media-types (4.0.0) ...
#11 9.592 Setting up libmpdec3:arm64 (2.5.1-1) ...
#11 9.600 Setting up python3-minimal (3.9.2-3) ...
#11 9.673 Setting up libpython3.9-stdlib:arm64 (3.9.2-1+deb11u3) ...
#11 9.682 Setting up nodejs (18.17.1-deb-1nodesource1) ...
#11 9.695 Setting up python3.9 (3.9.2-1+deb11u3) ...
#11 9.998 Processing triggers for libc-bin (2.31-13+deb11u13) ...
#11 13.34 Collecting pip~=23.0.1
#11 13.42   Downloading pip-23.0.1-py3-none-any.whl.metadata (4.1 kB)
#11 13.44 Downloading pip-23.0.1-py3-none-any.whl (2.1 MB)
#11 13.65    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.1/2.1 MB 10.1 MB/s eta 0:00:00
#11 13.69 Installing collected packages: pip
#11 13.69   Attempting uninstall: pip
#11 13.69     Found existing installation: pip 24.0
#11 13.79     Uninstalling pip-24.0:
#11 13.99       Successfully uninstalled pip-24.0
#11 14.54 Successfully installed pip-23.0.1
#11 14.54 WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
#11 14.61 
#11 14.61 [notice] A new release of pip is available: 23.0.1 -> 25.1.1
#11 14.61 [notice] To update, run: pip install --upgrade pip
#11 15.42 Collecting urllib3<2
#11 15.46   Downloading urllib3-1.26.20-py2.py3-none-any.whl (144 kB)
#11 15.50      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 144.2/144.2 kB 3.6 MB/s eta 0:00:00
#11 15.82 Collecting pdm~=2.5.2
#11 15.83   Downloading pdm-2.5.6-py3-none-any.whl (216 kB)
#11 15.86      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 216.3/216.3 kB 12.4 MB/s eta 0:00:00
#11 15.89 Collecting blinker
#11 15.90   Downloading blinker-1.9.0-py3-none-any.whl (8.5 kB)
#11 15.93 Collecting certifi
#11 15.94   Downloading certifi-2025.6.15-py3-none-any.whl (157 kB)
#11 15.95      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 157.7/157.7 kB 12.5 MB/s eta 0:00:00
#11 15.98 Collecting packaging!=22.0,>=20.9
#11 15.99   Downloading packaging-25.0-py3-none-any.whl (66 kB)
#11 16.00      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 66.5/66.5 kB 12.3 MB/s eta 0:00:00
#11 16.03 Collecting platformdirs
#11 16.04   Downloading platformdirs-4.3.8-py3-none-any.whl (18 kB)
#11 16.09 Collecting rich>=12.3.0
#11 16.11   Downloading rich-14.0.0-py3-none-any.whl (243 kB)
#11 16.13      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 243.2/243.2 kB 11.3 MB/s eta 0:00:00
#11 16.19 Collecting virtualenv>=20
#11 16.21   Downloading virtualenv-20.31.2-py3-none-any.whl (6.1 MB)
#11 16.76      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.1/6.1 MB 11.1 MB/s eta 0:00:00
#11 17.03 Collecting pyproject-hooks
#11 17.04   Downloading pyproject_hooks-1.2.0-py3-none-any.whl (10 kB)
#11 17.09 Collecting requests-toolbelt
#11 17.10   Downloading requests_toolbelt-1.0.0-py2.py3-none-any.whl (54 kB)
#11 17.12      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 54.5/54.5 kB 4.1 MB/s eta 0:00:00
#11 17.40 Collecting unearth>=0.9.0
#11 17.41   Downloading unearth-0.17.5-py3-none-any.whl (47 kB)
#11 17.43      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 47.4/47.4 kB 2.9 MB/s eta 0:00:00
#11 17.70 Collecting findpython>=0.2.2
#11 17.71   Downloading findpython-0.6.3-py3-none-any.whl (20 kB)
#11 17.80 Collecting tomlkit<1,>=0.11.1
#11 17.81   Downloading tomlkit-0.13.3-py3-none-any.whl (38 kB)
#11 17.85 Collecting shellingham>=1.3.2
#11 17.86   Downloading shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
#11 17.89 Collecting python-dotenv>=0.15
#11 17.90   Downloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)
#11 17.95 Collecting resolvelib>=1.0.1
#11 17.97   Downloading resolvelib-1.2.0-py3-none-any.whl (18 kB)
#11 18.30 Collecting installer<0.8,>=0.7
#11 18.33   Downloading installer-0.7.0-py3-none-any.whl (453 kB)
#11 18.36      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 453.8/453.8 kB 13.4 MB/s eta 0:00:00
#11 18.39 Collecting cachecontrol[filecache]>=0.12.11
#11 18.41   Downloading cachecontrol-0.14.3-py3-none-any.whl (21 kB)
#11 18.47 Collecting requests>=2.16.0
#11 18.48   Downloading requests-2.32.4-py3-none-any.whl (64 kB)
#11 18.49      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 64.8/64.8 kB 4.0 MB/s eta 0:00:00
#11 18.59 Collecting msgpack<2.0.0,>=0.5.2
#11 18.62   Downloading msgpack-1.1.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl (423 kB)
#11 18.65      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 423.9/423.9 kB 13.4 MB/s eta 0:00:00
#11 18.68 Collecting filelock>=3.8.0
#11 18.69   Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
#11 18.74 Collecting markdown-it-py>=2.2.0
#11 18.75   Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
#11 18.76      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 87.5/87.5 kB 12.3 MB/s eta 0:00:00
#11 18.79 Collecting pygments<3.0.0,>=2.13.0
#11 18.82   Downloading pygments-2.19.2-py3-none-any.whl (1.2 MB)
#11 18.93      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 11.1 MB/s eta 0:00:00
#11 18.98 Collecting httpx<1,>=0.27.0
#11 19.00   Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
#11 19.00      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.5/73.5 kB 16.9 MB/s eta 0:00:00
#11 19.03 Collecting distlib<1,>=0.3.7
#11 19.05   Downloading distlib-0.3.9-py2.py3-none-any.whl (468 kB)
#11 19.08      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 14.3 MB/s eta 0:00:00
#11 19.15 Collecting anyio
#11 19.17   Downloading anyio-4.9.0-py3-none-any.whl (100 kB)
#11 19.17      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100.9/100.9 kB 45.5 MB/s eta 0:00:00
#11 19.20 Collecting httpcore==1.*
#11 19.22   Downloading httpcore-1.0.9-py3-none-any.whl (78 kB)
#11 19.22      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.8/78.8 kB 13.2 MB/s eta 0:00:00
#11 19.25 Collecting idna
#11 19.27   Downloading idna-3.10-py3-none-any.whl (70 kB)
#11 19.27      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 70.4/70.4 kB 9.5 MB/s eta 0:00:00
#11 19.30 Collecting h11>=0.16
#11 19.31   Downloading h11-0.16.0-py3-none-any.whl (37 kB)
#11 19.34 Collecting mdurl~=0.1
#11 19.36   Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)
#11 19.48 Collecting charset_normalizer<4,>=2
#11 19.50   Downloading charset_normalizer-3.4.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl (142 kB)
#11 19.51      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 142.8/142.8 kB 19.6 MB/s eta 0:00:00
#11 19.58 Collecting sniffio>=1.1
#11 19.59   Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
#11 19.64 Collecting typing_extensions>=4.5
#11 19.66   Downloading typing_extensions-4.14.0-py3-none-any.whl (43 kB)
#11 19.67      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.8/43.8 kB 6.8 MB/s eta 0:00:00
#11 19.84 Installing collected packages: distlib, urllib3, typing_extensions, tomlkit, sniffio, shellingham, resolvelib, python-dotenv, pyproject-hooks, pygments, platformdirs, packaging, msgpack, mdurl, installer, idna, h11, filelock, charset_normalizer, certifi, blinker, virtualenv, requests, markdown-it-py, httpcore, findpython, anyio, rich, requests-toolbelt, httpx, cachecontrol, unearth, pdm
#11 21.29 Successfully installed anyio-4.9.0 blinker-1.9.0 cachecontrol-0.14.3 certifi-2025.6.15 charset_normalizer-3.4.2 distlib-0.3.9 filelock-3.18.0 findpython-0.6.3 h11-0.16.0 httpcore-1.0.9 httpx-0.28.1 idna-3.10 installer-0.7.0 markdown-it-py-3.0.0 mdurl-0.1.2 msgpack-1.1.1 packaging-25.0 pdm-2.5.6 platformdirs-4.3.8 pygments-2.19.2 pyproject-hooks-1.2.0 python-dotenv-1.1.1 requests-2.32.4 requests-toolbelt-1.0.0 resolvelib-1.2.0 rich-14.0.0 shellingham-1.5.4 sniffio-1.3.1 tomlkit-0.13.3 typing_extensions-4.14.0 unearth-0.17.5 urllib3-1.26.20 virtualenv-20.31.2
#11 21.29 WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
#11 21.31 WARNING: There was an error checking the latest version of pip.
#11 23.45 
#11 23.45 added 1 package, removed 65 packages, and changed 101 packages in 2s
#11 23.45 
#11 23.45 12 packages are looking for funding
#11 23.45   run `npm fund` for details
#11 DONE 24.0s

#12 [workers stage-1  4/20] COPY --from=chamber /chamber /bin/chamber
#12 DONE 0.1s

#13 [workers stage-1  5/20] WORKDIR /pkgs
#13 DONE 0.0s

#14 [workers stage-1  6/20] COPY packages/workers/pdm.lock packages/workers/pyproject.toml packages/workers/pdm.toml packages/workers/.pdm-python /pkgs/
#14 DONE 0.0s

#15 [workers stage-1  7/20] RUN pdm sync
#15 0.938 STATUS: Resolving packages from lockfile...
#15 1.273 STATUS: Fetching hashes for resolved packages...
#15 1.319 Synchronizing working set with lock file: 53 to add, 0 to update, 0 to remove
#15 1.319 
#15 1.410   ✔ Install charset-normalizer 2.1.0 successful
#15 1.450   ✔ Install certifi 2022.6.15 successful
#15 1.462   ✔ Install click 8.1.3 successful
#15 1.518   ✔ Install black 22.12.0 successful
#15 1.529   ✔ Install boto3 1.29.6 successful
#15 1.556   ✔ Install cffi 1.15.1 successful
#15 1.573   ✔ Install environs 9.5.0 successful
#15 1.590   ✔ Install coverage 7.1.0 successful
#15 1.640   ✔ Install factory-boy 3.2.1 successful
#15 1.782   ✔ Install hashids 1.3.1 successful
#15 1.911   ✔ Install freezegun 1.2.2 successful
#15 1.942   ✔ Install idna 3.3 successful
#15 1.984   ✔ Install greenlet 3.0.1 successful
#15 2.189   ✔ Install faker 13.15.1 successful
#15 2.572   ✔ Install cryptography 37.0.4 successful
#15 2.875   ✔ Install botocore 1.32.6 successful
#15 4.902   ✔ Install iniconfig 1.1.1 successful
#15 4.904   ✔ Install inflection 0.5.1 successful
#15 4.921   ✔ Install jmespath 1.0.1 successful
#15 4.932   ✔ Install marshmallow 3.17.0 successful
#15 4.941   ✔ Install jinja2 3.1.2 successful
#15 5.382   ✔ Install mypy-extensions 0.4.3 successful
#15 5.400   ✔ Install pluggy 1.0.0 successful
#15 5.413   ✔ Install platformdirs 2.5.2 successful
#15 5.430   ✔ Install packaging 21.3 successful
#15 5.435   ✔ Install pathspec 0.9.0 successful
#15 5.603   ✔ Install pyjwt 2.8.0 successful
#15 5.715   ✔ Install pyparsing 3.0.9 successful
#15 5.733   ✔ Install pycparser 2.21 successful
#15 5.747   ✔ Install pydantic 1.10.13 successful
#15 5.833   ✔ Install pytest-dotenv 0.5.2 successful
#15 5.841   ✔ Install pytest-cov 4.1.0 successful
#15 5.858   ✔ Install pytest-factoryboy 2.6.0 successful
#15 5.866   ✔ Install moto 4.2.9 successful
#15 5.930   ✔ Install pytest-freezegun 0.4.2 successful
#15 5.950   ✔ Install pytest-mock 3.12.0 successful
#15 6.004   ✔ Install python-dotenv 0.21.1 successful
#15 6.063   ✔ Install pytest 7.4.3 successful
#15 6.133   ✔ Install python-dateutil 2.8.2 successful
#15 6.233   ✔ Install responses 0.21.0 successful
#15 6.260   ✔ Install requests 2.28.1 successful
#15 6.290   ✔ Install s3transfer 0.7.0 successful
#15 6.335   ✔ Install six 1.16.0 successful
#15 6.399   ✔ Install typing-extensions 4.8.0 successful
#15 6.467   ✔ Install sentry-sdk 1.36.0 successful
#15 6.553   ✔ Install urllib3 1.26.11 successful
#15 6.577   ✔ Install psycopg2-binary 2.9.9 successful
#15 6.593   ✔ Install xmltodict 0.13.0 successful
#15 6.663   ✔ Install werkzeug 2.1.2 successful
#15 7.002   ✔ Install sqlalchemy 2.0.23 successful
#15 7.182   ✔ Install contentful 1.13.1 successful
#15 7.378   ✔ Install markupsafe 2.1.1 successful
#15 7.457   ✔ Install ruff 0.1.6 successful
#15 7.457 Installing the project as an editable package...
#15 7.872   ✔ Install sb_workers 1.0.0 successful
#15 7.872 
#15 7.872 🎉 All complete!
#15 7.873 
#15 8.027 
#15 8.027 PDM 2.5.6 is installed, while 2.25.3 is available.
#15 8.027 Please run `pdm self update` to upgrade.
#15 8.027 Run `pdm config check_update false` to disable the check.
#15 DONE 8.6s

#16 [workers stage-1  8/20] WORKDIR /app
#16 DONE 0.0s

#17 [workers stage-1  9/20] COPY /patches/ /app/patches/
#17 DONE 0.0s

#18 [workers stage-1 10/20] COPY package.json pnpm*.yaml /app/
#18 DONE 0.0s

#19 [workers stage-1 11/20] COPY packages/internal/core/package.json /app/packages/internal/core/
#19 DONE 0.0s

#20 [workers stage-1 12/20] COPY packages/workers/package.json /app/packages/workers/
#20 DONE 0.0s

#21 [workers stage-1 13/20] COPY tsconfig* /app/
#21 DONE 0.0s

#22 [workers stage-1 14/20] COPY packages/internal/cli /app/packages/internal/cli/
#22 DONE 0.0s

#23 [workers stage-1 15/20] RUN pnpm install --include-workspace-root --frozen-lockfile --filter=workers... --filter=cli...
#23 0.441 Scope: 3 of 4 workspace projects
#23 0.591  ERR_PNPM_LOCKFILE_CONFIG_MISMATCH  Cannot proceed with the frozen installation. The current "patchedDependencies" configuration doesn't match the value found in the lockfile
#23 0.591 
#23 0.591 Update your lockfile using "pnpm install --no-frozen-lockfile"
#23 ERROR: process "/bin/sh -c pnpm install --include-workspace-root --frozen-lockfile --filter=workers... --filter=cli..." did not complete successfully: exit code: 1
------
 > [workers stage-1 15/20] RUN pnpm install --include-workspace-root --frozen-lockfile --filter=workers... --filter=cli...:
0.441 Scope: 3 of 4 workspace projects
0.591  ERR_PNPM_LOCKFILE_CONFIG_MISMATCH  Cannot proceed with the frozen installation. The current "patchedDependencies" configuration doesn't match the value found in the lockfile
0.591 
0.591 Update your lockfile using "pnpm install --no-frozen-lockfile"
------
failed to solve: process "/bin/sh -c pnpm install --include-workspace-root --frozen-lockfile --filter=workers... --filter=cli..." did not complete successfully: exit code: 1
