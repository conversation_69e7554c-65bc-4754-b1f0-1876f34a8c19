> pnpm vite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
Re-optimizing dependencies because vite config has changed

  [32m[1mVITE[22m v5.4.14[39m  [2mready in [0m[1m771[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3000[22m/[39m
Browserslist: caniuse-lite is outdated. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
[2m10:53:24 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:24 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:24 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:24 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:24 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:24 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:25 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:25 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:27 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:29 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:36 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:53:56 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:06 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:16 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:20 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:20 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:21 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:21 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:23 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:30 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:31 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:41 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:54:51 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:55:01 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:55:10 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:55:11 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:55:21 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:55:21 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:55:31 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:55:41 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:55:51 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:56:01 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:56:11 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:56:13 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/index.tsx[22m
[2m10:56:13 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "./initApp" from "src/debug-app.tsx". Does the file exist?
[2m10:56:13 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./initApp" from "src/debug-app.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36m/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp/src/debug-app.tsx[39m:46:39
[33m  51 |    const loadRealApp = async () => {
  52 |      try {
  53 |        const { initApp } = await import("./initApp");
     |                                         ^
  54 |        const container2 = document.getElementById("root");
  55 |        if (container2) {[39m
      at TransformPluginContext._formatError (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49257:41)
      at TransformPluginContext.error (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49252:16)
      at normalizeUrl (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64199:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64331:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64258:7)
      at async PluginContainer.transform (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49098:18)
      at async loadAndTransform (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:51931:27)
[2m10:56:13 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "./initApp" from "src/debug-app.tsx". Does the file exist?
[2m10:56:13 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "./initApp" from "src/debug-app.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36m/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp/src/debug-app.tsx[39m:46:39
[33m  51 |    const loadRealApp = async () => {
  52 |      try {
  53 |        const { initApp } = await import("./initApp");
     |                                         ^
  54 |        const container2 = document.getElementById("root");
  55 |        if (container2) {[39m
      at TransformPluginContext._formatError (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49257:41)
      at TransformPluginContext.error (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49252:16)
      at normalizeUrl (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64199:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64331:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64258:7)
      at async PluginContainer.transform (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49098:18)
      at async loadAndTransform (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:51931:27)
      at async viteTransformMiddleware (file:///Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/vite@5.4.14_@types+node@20.12.12_less@4.2.0_sass@1.77.2_stylus@0.63.0_terser@5.31.0/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:62031:24)
> pnpm nx run webapp:graphql:generate-types:watch


[2m> [22m[2mnx run[22m webapp:graphql:generate-types:watch

[2m> [22mpnpm nx run webapp-api-client:graphql:generate-types:watch


[2m> [22m[2mnx run[22m webapp-api-client:graphql:generate-types:watch

[2m> [22mpnpm run graphql-codegen -w -c ./graphql/codegen.ts


> @sb/webapp-api-client@4.1.1 graphql-codegen /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp-libs/webapp-api-client
> graphql-codegen -w -c ./graphql/codegen.ts

[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  Parcel watcher not found. To use this feature, please make sure to provide @parcel/watcher as a peer dependency.



[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target graphql for project webapp-api-client[39m





[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target graphql for project webapp[39m


Warning: command "pnpm vite" exited with non-zero status code