> pnpm vite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  [32m[1mVITE[22m v5.4.14[39m  [2mready in [0m[1m623[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3000[22m/[39m
Browserslist: caniuse-lite is outdated. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
[2m10:43:09 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:09 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:09 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:10 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:11 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:13 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:20 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:33 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:33 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:34 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:34 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:35 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:37 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:44 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:44 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:44 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:45 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:45 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:46 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:43:51 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:44:03 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:44:13 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:44:23 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:44:33 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:44:43 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:44:53 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:45:03 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:45:13 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:45:23 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:45:33 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:45:43 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:45:53 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:46:03 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:46:13 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:46:23 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:46:34 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:46:44 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:46:54 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:47:04 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:47:14 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
[2m10:47:24 PM[22m [31m[1m[vite][22m[39m [31mws proxy socket error:[39m
Error: read ECONNRESET
    at TCP.onStreamRead (node:internal/stream_base_commons:216:20)
> pnpm nx run webapp:graphql:generate-types:watch


[2m> [22m[2mnx run[22m webapp:graphql:generate-types:watch

[2m> [22mpnpm nx run webapp-api-client:graphql:generate-types:watch


[2m> [22m[2mnx run[22m webapp-api-client:graphql:generate-types:watch

[2m> [22mpnpm run graphql-codegen -w -c ./graphql/codegen.ts


> @sb/webapp-api-client@4.1.1 graphql-codegen /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp-libs/webapp-api-client
> graphql-codegen -w -c ./graphql/codegen.ts

[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  Parcel watcher not found. To use this feature, please make sure to provide @parcel/watcher as a peer dependency.



[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target graphql for project webapp-api-client[39m





[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target graphql for project webapp[39m


Warning: command "pnpm vite" exited with non-zero status code