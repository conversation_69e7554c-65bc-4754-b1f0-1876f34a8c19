[2m> [22mdocker compose build workers

time="2025-06-27T23:53:12+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
time="2025-06-27T23:53:12+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.local.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
Compose can now delegate builds to bake for better performance.
 To do so, set COMPOSE_BAKE=true.
#0 building with "desktop-linux" instance using docker driver

#1 [workers internal] load build definition from Dockerfile
#1 transferring dockerfile: 2.08kB done
#1 DONE 0.0s

#2 [workers internal] load metadata for docker.io/library/python:3.11-slim-bullseye
#2 DONE 1.0s

#3 [workers internal] load metadata for docker.io/segment/chamber:2
#3 DONE 1.0s

#4 [workers internal] load .dockerignore
#4 transferring context: 206B done
#4 DONE 0.0s

#5 [workers stage-1  1/20] FROM docker.io/library/python:3.11-slim-bullseye@sha256:121862c3e24a272af7f8d79ceb595feaf39e8dcbd10a2fd2639033f613dc10f5
#5 DONE 0.0s

#6 [workers chamber 1/1] FROM docker.io/segment/chamber:2@sha256:a73252f72ab4cc5b0d544430314f937b25abf0ea6390eebdc3a9b4cad7b5e635
#6 DONE 0.0s

#7 [workers internal] load build context
#7 transferring context: 58.65kB 0.2s done
#7 DONE 0.2s

#8 [workers stage-1  7/20] RUN pdm sync
#8 CACHED

#9 [workers stage-1 10/20] COPY package.json pnpm*.yaml /app/
#9 CACHED

#10 [workers stage-1 12/20] COPY packages/workers/package.json /app/packages/workers/
#10 CACHED

#11 [workers stage-1  3/20] RUN   echo "deb https://deb.nodesource.com/node_18.x buster main" > /etc/apt/sources.list.d/nodesource.list &&   wget -qO- https://deb.nodesource.com/gpgkey/nodesource.gpg.key | apt-key add - &&   apt-get update &&   apt-get install -yqq nodejs &&   pip install -U pip~=23.0.1 && pip install "urllib3<2" pdm~=2.5.2 &&   npm i -g npm@^8 pnpm@~9.5.0 &&   rm -rf /var/lib/apt/lists/*
#11 CACHED

#12 [workers stage-1 13/20] COPY tsconfig* /app/
#12 CACHED

#13 [workers stage-1  2/20] RUN apt-get update && apt-get install -y wget gnupg curl unzip make xz-utils build-essential
#13 CACHED

#14 [workers stage-1  8/20] WORKDIR /app
#14 CACHED

#15 [workers stage-1 11/20] COPY packages/internal/core/package.json /app/packages/internal/core/
#15 CACHED

#16 [workers stage-1  6/20] COPY packages/workers/pdm.lock packages/workers/pyproject.toml packages/workers/pdm.toml packages/workers/.pdm-python /pkgs/
#16 CACHED

#17 [workers stage-1  4/20] COPY --from=chamber /chamber /bin/chamber
#17 CACHED

#18 [workers stage-1  9/20] COPY /patches/ /app/patches/
#18 CACHED

#19 [workers stage-1  5/20] WORKDIR /pkgs
#19 CACHED

#20 [workers stage-1 14/20] COPY packages/internal/cli /app/packages/internal/cli/
#20 CACHED

#21 [workers stage-1 15/20] RUN pnpm install --include-workspace-root --frozen-lockfile --filter=workers... --filter=cli...
#21 0.548 Scope: 3 of 4 workspace projects
#21 0.695  ERR_PNPM_LOCKFILE_CONFIG_MISMATCH  Cannot proceed with the frozen installation. The current "patchedDependencies" configuration doesn't match the value found in the lockfile
#21 0.695 
#21 0.695 Update your lockfile using "pnpm install --no-frozen-lockfile"
#21 ERROR: process "/bin/sh -c pnpm install --include-workspace-root --frozen-lockfile --filter=workers... --filter=cli..." did not complete successfully: exit code: 1
------
 > [workers stage-1 15/20] RUN pnpm install --include-workspace-root --frozen-lockfile --filter=workers... --filter=cli...:
0.548 Scope: 3 of 4 workspace projects
0.695  ERR_PNPM_LOCKFILE_CONFIG_MISMATCH  Cannot proceed with the frozen installation. The current "patchedDependencies" configuration doesn't match the value found in the lockfile
0.695 
0.695 Update your lockfile using "pnpm install --no-frozen-lockfile"
------
failed to solve: process "/bin/sh -c pnpm install --include-workspace-root --frozen-lockfile --filter=workers... --filter=cli..." did not complete successfully: exit code: 1
