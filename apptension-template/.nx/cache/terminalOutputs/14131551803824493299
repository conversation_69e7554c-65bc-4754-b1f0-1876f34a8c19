[2m> [22mpnpm vite -c vite.config.ts build

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[36mvite v5.4.19 [32mbuilding for production...[36m[39m
[2K[1Gtransforming (1) [2msrc/index.tsx[22m[2K[1Gtransforming (4) [2msrc/templates/index.ts[22m[2K[1Gtransforming (35) [2m../../../node_modules/.pnpm/react-intl@6.8.9_react@18.3.1_typescript@5.4.5/node_[2K[1Gtransforming (69) [2m../../../node_modules/.pnpm/intl-messageformat@10.7.7/node_modules/intl-messagef[2K[1Gtransforming (113) [2m../../../node_modules/.pnpm/parse5-htmlparser2-tree-adapter@6.0.1/node_modules/[2K[1Gtransforming (300) [2m../../../node_modules/.pnpm/ramda@0.28.0/node_modules/ramda/es/mergeDeepWith.js[2K[1G[2K[1G[1m[33m[plugin:vite:resolve][39m[22m [33m[plugin vite:resolve] Module "crypto" has been externalized for browser compatibility, imported by "/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/@supercharge+strings@2.0.0/node_modules/@supercharge/strings/dist/random-string-generator.js". See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.[39m
[2K[1Gtransforming (476) [2m../../../node_modules/.pnpm/ramda@0.28.0/node_modules/ramda/es/internal/_apertu[2K[1Gtransforming (609) [2m../../../node_modules/.pnpm/@formatjs+intl-localematcher@0.5.8/node_modules/@fo[2K[1Gtransforming (821) [2m../../../node_modules/.pnpm/color-name@1.1.4/node_modules/color-name/index.js[[2K[1Gtransforming (833) [2m../../../node_modules/.pnpm/color@4.2.3/node_modules/color/index.js[22m[2K[1G[2K[1G[32m✓[39m 849 modules transformed.
[2K[1Grendering chunks (1)...[2K[1G[2K[1Gcomputing gzip size (0)...[2K[1Gcomputing gzip size (1)...[2K[1G[2mbuild/email-renderer/[22m[36mindex.umd.js  [39m[1m[33m835.35 kB[39m[22m[2m │ gzip: 256.21 kB[22m
[32m✓ built in 2.21s[39m
