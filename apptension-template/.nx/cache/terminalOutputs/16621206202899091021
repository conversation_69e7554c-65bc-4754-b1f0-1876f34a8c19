
> webapp@4.1.1 start /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp
> pnpm nx start:app


[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1mstart:app[22m for project webapp and [1m1[22m task it depends on:[39m



[2m> [22m[2mnx run[22m webapp:setup  [2m[existing outputs match the cache, left as is][22m

[1m[33m[7m[1m[36m NX [39m[33m[22m[1m[27m Falling back to ts-node for local typescript execution. This may be a little slower.[39m[22m
[1m[33m  - To fix this, ensure @swc-node/register and @swc/core have been installed[39m[22m

[2m> [22m[2mnx run[22m webapp:"start:app"

[2m> [22mNODE_NO_WARNINGS=1 pnpm vite

[2m> [22mpnpm nx run webapp:graphql:generate-types:watch

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

[2m> [22m[2mnx run[22m webapp:graphql:generate-types:watch

[2m> [22mpnpm nx run webapp-api-client:graphql:generate-types:watch

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  [32m[1mVITE[22m v5.4.14[39m  [2mready in [0m[1m618[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3000[22m/[39m

[2m> [22m[2mnx run[22m webapp-api-client:graphql:generate-types:watch

[2m> [22mpnpm run graphql-codegen -w -c ./graphql/codegen.ts


> @sb/webapp-api-client@4.1.1 graphql-codegen /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp-libs/webapp-api-client
> graphql-codegen -w -c ./graphql/codegen.ts

[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  [34mℹ[39m Watching for changes in /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages...
