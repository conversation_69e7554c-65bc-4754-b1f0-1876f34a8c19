[2m> [22mnx run-many --target=setup

[?25l[0K
[0J[1A
   [32m✔[39m  [2mnx run[22m webapp:setup  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 2/6 remaining tasks in parallel...[22m[0K
[0K
   [2m[36m⠙[39m[22m  [2mnx run[22m core:setup-env[0K
   [2m[36m⠙[39m[22m  [2mnx run[22m core:docker-create-volumes[0K
[0K
   [32m✔[39m  1/1 succeeded [2m[1 read from cache][22m[0K
[0K
[0J[12A   [32m✔[39m  [2mnx run[22m ssm-editor:setup  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 2/5 remaining tasks in parallel...[22m[0K
[0K
   [2m[36m⠹[39m[22m  [2mnx run[22m core:setup-env[0K
   [2m[36m⠹[39m[22m  [2mnx run[22m core:docker-create-volumes[0K
[0K
   [32m✔[39m  2/2 succeeded [2m[2 read from cache][22m[0K
[0K
[0J[12A   [32m✔[39m  [2mnx run[22m backend:setup  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 2/4 remaining tasks in parallel...[22m[0K
[0K
   [2m[36m⠸[39m[22m  [2mnx run[22m core:setup-env[0K
   [2m[36m⠸[39m[22m  [2mnx run[22m core:docker-create-volumes[0K
[0K
   [32m✔[39m  3/3 succeeded [2m[3 read from cache][22m[0K
[0K
[0J[12A   [32m✔[39m  [2mnx run[22m workers:setup  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 2/3 remaining tasks in parallel...[22m[0K
[0K
   [2m[36m⠼[39m[22m  [2mnx run[22m core:setup-env[0K
   [2m[36m⠼[39m[22m  [2mnx run[22m core:docker-create-volumes[0K
[0K
   [32m✔[39m  4/4 succeeded [2m[4 read from cache][22m[0K
[0K
[0J[12A   [32m✔[39m  [2mnx run[22m core:docker-create-volumes[2m (80ms)[22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 1/2 remaining tasks...[22m[0K
[0K
   [2m[36m⠴[39m[22m  [2mnx run[22m core:setup-env[0K
[0K
   [32m✔[39m  5/5 succeeded [2m[4 read from cache][22m[0K
[0K
[0J[11A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 1/2 remaining tasks...[22m[0K
[0K
   [2m[36m⠦[39m[22m  [2mnx run[22m core:setup-env[0K
[0K
   [32m✔[39m  5/5 succeeded [2m[4 read from cache][22m[0K
[0K
[0J[11A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 1/2 remaining tasks...[22m[0K
[0K
   [2m[36m⠧[39m[22m  [2mnx run[22m core:setup-env[0K
[0K
   [32m✔[39m  5/5 succeeded [2m[4 read from cache][22m[0K
[0K
[0J[11A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 1/2 remaining tasks...[22m[0K
[0K
   [2m[36m⠇[39m[22m  [2mnx run[22m core:setup-env[0K
[0K
   [32m✔[39m  5/5 succeeded [2m[4 read from cache][22m[0K
[0K
[0J[11A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 1/2 remaining tasks...[22m[0K
[0K
   [2m[36m⠏[39m[22m  [2mnx run[22m core:setup-env[0K
[0K
   [32m✔[39m  5/5 succeeded [2m[4 read from cache][22m[0K
[0K
[0J[11A   [32m✔[39m  [2mnx run[22m core:setup-env[2m (421ms)[22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[0K
   [32m✔[39m  6/6 succeeded [2m[4 read from cache][22m[0K
[0K
[0J[8A   [32m✔[39m  [2mnx run[22m core:setup  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[0K
[0K
[0K
   [32m✔[39m  7/7 succeeded [2m[5 read from cache][22m[0K
[0K
[0J[8A[0K
[2m[32m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target [1msetup[22m for 5 projects and [1m2[22m tasks they depend on[39m[2m[37m (439ms)[39m[22m[0K
[2m[22m
[2mNx read the output from the cache instead of running the command for 5 out of 7 tasks.[22m[0K
[0K
[0J[?25h[?25h[?25h[2m> [22mnx run-many --target=compose-build-image --projects=backend,workers

[?25l[0K
[0J[1A
   [32m✔[39m  [2mnx run[22m workers:setup  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1mcompose-build-image[22m for 2 projects and [1m4[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 2/5 remaining tasks in parallel...[22m[0K
[0K
   [2m[36m⠙[39m[22m  [2mnx run[22m backend:setup[0K
   [2m[36m⠙[39m[22m  [2mnx run[22m webapp-emails:build:default[0K
[0K
   [32m✔[39m  1/1 succeeded [2m[1 read from cache][22m[0K
[0K
[0J[12A   [32m✔[39m  [2mnx run[22m backend:setup  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1mcompose-build-image[22m for 2 projects and [1m4[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 2/4 remaining tasks in parallel...[22m[0K
[0K
   [2m[36m⠹[39m[22m  [2mnx run[22m workers:compose-build-image[0K
   [2m[36m⠹[39m[22m  [2mnx run[22m webapp-emails:build:default[0K
[0K
   [32m✔[39m  2/2 succeeded [2m[2 read from cache][22m[0K
[0K
[0J[12A   [32m✔[39m  [2mnx run[22m webapp-emails:build:default  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1mcompose-build-image[22m for 2 projects and [1m4[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 1/3 remaining tasks...[22m[0K
[0K
   [2m[36m⠸[39m[22m  [2mnx run[22m workers:compose-build-image[0K
[0K
   [32m✔[39m  3/3 succeeded [2m[3 read from cache][22m[0K
[0K
[0J[11A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1mcompose-build-image[22m for 2 projects and [1m4[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 2/3 remaining tasks in parallel...[22m[0K
[0K
   [2m[36m⠼[39m[22m  [2mnx run[22m workers:compose-build-image[0K
   [2m[36m⠼[39m[22m  [2mnx run[22m backend:build-email-renderer[0K
[0K
   [32m✔[39m  3/3 succeeded [2m[3 read from cache][22m[0K
[0K
[0J[12A   [32m✔[39m  [2mnx run[22m backend:build-email-renderer[2m (60ms)[22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1mcompose-build-image[22m for 2 projects and [1m4[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 1/2 remaining tasks...[22m[0K
[0K
   [2m[36m⠴[39m[22m  [2mnx run[22m workers:compose-build-image[0K
[0K
   [32m✔[39m  4/4 succeeded [2m[3 read from cache][22m[0K
[0K
[0J[11A   [32m✔[39m  [2mnx run[22m backend:compose-build-image  [2m[existing outputs match the cache, left as is][22m[0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1mcompose-build-image[22m for 2 projects and [1m4[22m tasks they depend on[39m[0K
[0K
[2m   [36m→[39m  Executing 1/1 remaining tasks...[22m[0K
[0K
   [2m[36m⠦[39m[22m  [2mnx run[22m workers:compose-build-image[0K
[0K
   [32m✔[39m  5/5 succeeded [2m[4 read from cache][22m[0K
[0K
[0J[11A

   [31m✖[39m  [2mnx run[22m [31mworkers:compose-build-image[39m[0K
      [2m> [22mdocker compose build workers
[0K
      
[0K
      time="2025-06-28T18:02:59+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"[0K
      time="2025-06-28T18:02:59+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.local.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"[0K
      no such service: workers[0K
      [0K
      [0K
[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0K
[0J[4A[0K
[2m[36m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[36m NX [39m[22m[27m[0m  [36mRunning target [1mcompose-build-image[22m for 2 projects and [1m4[22m tasks they depend on[39m[0K
[0K
[0K
   [32m✔[39m  5/6 succeeded [2m[4 read from cache][22m[0K
   [31m✖[39m  1/6 failed[0K
[0K
[0J[9A[0K
[2m[31m——————————————————————————————————————————————————————————————————————————————————————————————————————[39m[22m[0K
[0K
[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mRan target [1mcompose-build-image[22m for 2 projects and [1m4[22m tasks they depend on[39m[2m[37m (210ms)[39m[22m[0K
[0K
[2m   [2m✔[22m[2m  5/6 succeeded [2m[4 read from cache][22m[2m[22m[0K
[0K
   [31m✖[39m  1/6 targets failed, including the following:[0K
[0K
      [31m-[39m [2mnx run[22m workers:compose-build-image[0K
[0K
[0J[?25h[?25h[?25h