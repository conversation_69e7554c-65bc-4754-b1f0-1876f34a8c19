[2m> [22mNODE_NO_WARNINGS=1 pnpm vite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
Port 3000 is in use, trying another one...

  [32m[1mVITE[22m v5.4.14[39m  [2mready in [0m[1m3961[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3001[22m/[39m
[2m12:03:00 AM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/index.tsx[22m
[2m12:04:44 AM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/index.tsx[22m
[2m12:10:33 AM[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m12:10:34 AM[22m [36m[1m[vite][22m[39m server restarted.
[2m12:17:39 AM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/styles.css, /@fs/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp-libs/webapp-notifications/src/notifications.component.tsx[22m
[2m12:19:35 AM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/shared/components/layout/header/header.component.tsx, /src/styles.css[22m
[2m12:21:34 AM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/shared/components/layout/header/header.component.tsx, /src/styles.css[22m
[2m12:23:38 AM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/shared/components/layout/header/header.component.tsx, /src/styles.css[22m
[2m12:32:43 AM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/styles.css, /@fs/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.component.tsx[22m
[2m12:36:57 AM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/styles.css, /@fs/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp-libs/webapp-tenants/src/providers/currentTenantProvider/currentTenantProvider.component.tsx[22m
[2m3:10:22 AM[22m [36m[1m[vite][22m[39m [32m.env changed, restarting server...[39m
[2m3:10:22 AM[22m [36m[1m[vite][22m[39m server restarted.
[2m11:31:23 AM[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m11:31:23 AM[22m [36m[1m[vite][22m[39m server restarted.
[2m11:32:41 AM[22m [36m[1m[vite][22m[39m [32mvite.config.ts changed, restarting server...[39m
[2m11:32:41 AM[22m [36m[1m[vite][22m[39m server restarted.
[2m> [22mpnpm nx run webapp:graphql:generate-types:watch


[2m> [22m[2mnx run[22m webapp:graphql:generate-types:watch

[2m> [22mpnpm nx run webapp-api-client:graphql:generate-types:watch


[2m> [22m[2mnx run[22m webapp-api-client:graphql:generate-types:watch

[2m> [22mpnpm run graphql-codegen -w -c ./graphql/codegen.ts


> @sb/webapp-api-client@4.1.1 graphql-codegen /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp-libs/webapp-api-client
> graphql-codegen -w -c ./graphql/codegen.ts

[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  [34mℹ[39m Watching for changes in /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages...
[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  [34mℹ[39m Watching for changes in /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages...
[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  [34mℹ[39m Watching for changes in /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages...
[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  [34mℹ[39m Watching for changes in /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages...
[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  [34mℹ[39m Watching for changes in /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages...
[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  [34mℹ[39m Watching for changes in /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages...
  Shutting down watch...



[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target graphql for project webapp-api-client[39m





[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target graphql for project webapp[39m


