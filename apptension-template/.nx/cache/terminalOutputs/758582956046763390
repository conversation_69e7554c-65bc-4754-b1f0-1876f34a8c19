[2m> [22mpnpm run graphql-codegen -w -c ./graphql/codegen.ts


> @sb/webapp-api-client@4.1.1 graphql-codegen /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp-libs/webapp-api-client
> graphql-codegen -w -c ./graphql/codegen.ts

[STARTED] Parse Configuration
[SUCCESS] Parse Configuration
[STARTED] Generate outputs
[STARTED] Generate to src/graphql/__generated/gql/
[STARTED] Load GraphQL schemas
[SUCCESS] Load GraphQL schemas
[STARTED] Load GraphQL documents
[SUCCESS] Load GraphQL documents
[STARTED] Generate
[SUCCESS] Generate
[SUCCESS] Generate to src/graphql/__generated/gql/
[SUCCESS] Generate outputs
  Parcel watcher not found. To use this feature, please make sure to provide @parcel/watcher as a peer dependency.
