[2m> [22mdocker compose build backend

time="2025-06-27T23:53:56+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
time="2025-06-27T23:53:56+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.local.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
Compose can now delegate builds to bake for better performance.
 To do so, set COMPOSE_BAKE=true.
#0 building with "desktop-linux" instance using docker driver

#1 [backend internal] load build definition from Dockerfile
#1 transferring dockerfile: 1.71kB done
#1 DONE 0.0s

#2 [backend internal] load metadata for docker.io/segment/chamber:2
#2 ...

#3 [backend internal] load metadata for docker.io/library/python:3.11-slim-bullseye
#3 DONE 1.0s

#2 [backend internal] load metadata for docker.io/segment/chamber:2
#2 DONE 1.0s

#4 [backend internal] load .dockerignore
#4 transferring context: 198B done
#4 DONE 0.0s

#5 [backend backend_build  1/10] FROM docker.io/library/python:3.11-slim-bullseye@sha256:121862c3e24a272af7f8d79ceb595feaf39e8dcbd10a2fd2639033f613dc10f5
#5 DONE 0.0s

#6 [backend chamber 1/1] FROM docker.io/segment/chamber:2@sha256:a73252f72ab4cc5b0d544430314f937b25abf0ea6390eebdc3a9b4cad7b5e635
#6 DONE 0.0s

#7 [backend internal] load build context
#7 transferring context: 901.08kB 0.1s done
#7 DONE 0.1s

#8 [backend backend_build  7/10] RUN pdm sync   && apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false   && rm -rf /var/lib/apt/lists/*
#8 CACHED

#9 [backend backend_build  4/10] COPY --from=chamber /chamber /bin/chamber
#9 CACHED

#10 [backend backend_build  3/10] RUN curl -fsS https://deb.nodesource.com/setup_20.x | bash -   && apt-get --no-install-recommends install -y nodejs
#10 CACHED

#11 [backend backend_build  6/10] COPY pdm.lock pyproject.toml pdm.toml /pkgs/
#11 CACHED

#12 [backend backend_build  5/10] WORKDIR /pkgs
#12 CACHED

#13 [backend backend_build  2/10] RUN apt-get update && apt-get install -y gcc postgresql-client ca-certificates jq curl   && update-ca-certificates   && pip install --upgrade pip   && pip install --no-cache-dir setuptools pdm~=2.5.2 awscli==1.32.24
#13 CACHED

#14 [backend backend_build  8/10] WORKDIR /app
#14 CACHED

#15 [backend backend_build  9/10] COPY . /app/
#15 DONE 0.1s

#16 [backend backend_build 10/10] RUN chmod +x /app/scripts/runtime/*.sh
#16 DONE 0.2s

#17 [backend static_files 1/1] RUN ./scripts/runtime/build_static.sh
#17 8.121 
#17 8.121 202 static files copied to '/app/static', 580 post-processed.
#17 DONE 8.6s

#18 [backend backend 1/1] COPY --from=static_files /app/static /app/static
#18 DONE 0.3s

#19 [backend] exporting to image
#19 exporting layers
#19 exporting layers 0.2s done
#19 writing image sha256:542e7e8314bbb5e851bde73c31fe38770cdd1163ac3e2e1181ceeeacb2c3a315
#19 writing image sha256:542e7e8314bbb5e851bde73c31fe38770cdd1163ac3e2e1181ceeeacb2c3a315 done
#19 naming to docker.io/saas/backend done
#19 DONE 0.2s

#20 [backend] resolving provenance for metadata file
#20 DONE 0.0s
 backend  Built
