
> webapp@4.1.1 storybook /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/packages/webapp
> pnpm nx run webapp-emails:build && /bin/bash scripts/start-storybook.sh


[2m> [22m[2mnx run[22m webapp-emails:build:default

[2m> [22mpnpm vite -c vite.config.ts build

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[36mvite v5.4.14 [32mbuilding for production...[36m[39m
transforming...
[1m[33m[plugin:vite:resolve][39m[22m [33m[plugin vite:resolve] Module "crypto" has been externalized for browser compatibility, imported by "/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/@supercharge+strings@2.0.0/node_modules/@supercharge/strings/dist/random-string-generator.js". See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.[39m
[32m✓[39m 844 modules transformed.
rendering chunks...
computing gzip size...
[2mbuild/email-renderer/[22m[36mindex.umd.js  [39m[1m[33m832.50 kB[39m[22m[2m │ gzip: 255.48 kB[22m
[32m✓ built in 2.18s[39m



[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target build for project webapp-emails[39m


Adding --openssl-legacy-provider to NODE_OPTIONS env var for Node version greater or equal 17
[1m@storybook/cli v8.1.3[22m[0m[0m
[0m[0m
[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
WARN The following packages are incompatible with Storybook [1m8.1.3[22m as they depend on different major versions of Storybook packages:
WARN - [36m@storybook/client-api[39m@[36m7.6.17[39m
WARN  Repo: [33mhttps://github.com/storybookjs/storybook/tree/next/code/deprecated/client-api[39m
WARN - [36m@storybook/react-vite[39m@[36m8.5.3[39m
WARN  Repo: [33mhttps://github.com/storybookjs/storybook/tree/next/code/frameworks/react-vite[39m
WARN - [36mstorybook-react-router[39m@[36m1.0.8[39m
WARN  Repo: [33mhttps://github.com/gvaldambrini/storybook-router[39m
WARN 
WARN 
WARN Please consider updating your packages or contacting the maintainers for compatibility details.
WARN For more on Storybook 8 compatibility, see the linked GitHub issue:
WARN [33mhttps://github.com/storybookjs/storybook/issues/26031[39m
[31mError: Cannot find module 'storybook/internal/server-errors'[39m
[31mRequire stack:[39m
[31m- /Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/@storybook+builder-vite@8.5.3_storybook@8.1.3_@babel+preset-env@7.24.6_@babel+core@7.26_8eb4c54ed6cef4f99e4d97912420563d/node_modules/@storybook/builder-vite/dist/index.js[39m
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at Module._resolveFilename (/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/esbuild-register@3.5.0_esbuild@0.20.2/node_modules/esbuild-register/dist/node.js:4799:36)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/node_modules/.pnpm/@storybook+builder-vite@8.5.3_storybook@8.1.3_@babel+preset-env@7.24.6_@babel+core@7.26_8eb4c54ed6cef4f99e4d97912420563d/node_modules/@storybook/builder-vite/dist/index.js:1:40296)

WARN Broken build, fix the error above.
WARN You may need to refresh the browser.

[?25l[2K[1G[36m?[39m [1mWould you like to help improve Storybook by sending anonymous crash reports?[22m [90m›[39m [90m(Y/n)[39m