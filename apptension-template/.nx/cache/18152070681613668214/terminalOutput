[2m> [22mdocker compose build backend

time="2025-06-27T23:50:52+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
time="2025-06-27T23:50:52+02:00" level=warning msg="/Users/<USER>/Documents/GitHub/bid_bees_full_project/apptension-template/docker-compose.local.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
Compose can now delegate builds to bake for better performance.
 To do so, set COMPOSE_BAKE=true.
#0 building with "desktop-linux" instance using docker driver

#1 [backend internal] load build definition from Dockerfile
#1 transferring dockerfile: 1.71kB done
#1 DONE 0.0s

#2 [backend internal] load metadata for docker.io/segment/chamber:2
#2 ...

#3 [backend internal] load metadata for docker.io/library/python:3.11-slim-bullseye
#3 DONE 2.2s

#2 [backend internal] load metadata for docker.io/segment/chamber:2
#2 DONE 2.3s

#4 [backend internal] load .dockerignore
#4 transferring context: 198B done
#4 DONE 0.0s

#5 [backend backend_build  1/10] FROM docker.io/library/python:3.11-slim-bullseye@sha256:121862c3e24a272af7f8d79ceb595feaf39e8dcbd10a2fd2639033f613dc10f5
#5 CACHED

#6 [backend chamber 1/1] FROM docker.io/segment/chamber:2@sha256:a73252f72ab4cc5b0d544430314f937b25abf0ea6390eebdc3a9b4cad7b5e635
#6 CACHED

#7 [backend internal] load build context
#7 transferring context: 902.50kB 0.1s done
#7 DONE 0.1s

#8 [backend backend_build  2/10] RUN apt-get update && apt-get install -y gcc postgresql-client ca-certificates jq curl   && update-ca-certificates   && pip install --upgrade pip   && pip install --no-cache-dir setuptools pdm~=2.5.2 awscli==1.32.24
#8 CACHED

#9 [backend backend_build  3/10] RUN curl -fsS https://deb.nodesource.com/setup_20.x | bash -   && apt-get --no-install-recommends install -y nodejs
#9 CACHED

#10 [backend backend_build  4/10] COPY --from=chamber /chamber /bin/chamber
#10 CACHED

#11 [backend backend_build  5/10] WORKDIR /pkgs
#11 CACHED

#12 [backend backend_build  6/10] COPY pdm.lock pyproject.toml pdm.toml /pkgs/
#12 CACHED

#13 [backend backend_build  7/10] RUN pdm sync   && apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false   && rm -rf /var/lib/apt/lists/*
#13 3.348 STATUS: Resolving packages from lockfile...
#13 7.130 STATUS: Fetching hashes for resolved packages...
#13 7.164 Synchronizing working set with lock file: 195 to add, 0 to update, 0 to remove
#13 7.164 
#13 7.475   ✔ Install setuptools 63.2.0 successful
#13 7.557   ✔ Install aiosignal 1.3.1 successful
#13 7.567   ✔ Install async-timeout 4.0.2 successful
#13 7.582   ✔ Install asgiref 3.8.1 successful
#13 7.588   ✔ Install astor 0.8.1 successful
#13 7.591   ✔ Install amqp 5.2.0 successful
#13 7.599   ✔ Install anyio 4.2.0 successful
#13 7.637   ✔ Install aniso8601 9.0.1 successful
#13 7.656   ✔ Install attrs 21.4.0 successful
#13 7.727   ✔ Install billiard 4.2.0 successful
#13 7.784   ✔ Install aiohttp 3.8.4 successful
#13 7.797   ✔ Install black 23.1.0 successful
#13 7.804   ✔ Install aws-xray-sdk 2.11.0 successful
#13 7.817   ✔ Install boto3 1.26.58 successful
#13 7.826   ✔ Install aws-sam-translator 1.47.0 successful
#13 7.911   ✔ Install certifi 2022.6.15 successful
#13 7.971   ✔ Install channels 4.0.0 successful
#13 7.979   ✔ Install cffi 1.15.1 successful
#13 8.081   ✔ Install channels-redis 4.2.0 successful
#13 8.086   ✔ Install charset-normalizer 2.1.0 successful
#13 8.156   ✔ Install click-didyoumean 0.3.1 successful
#13 8.168   ✔ Install click 8.1.3 successful
#13 8.221   ✔ Install click-plugins 1.1.1 successful
#13 8.228   ✔ Install click-repl 0.3.0 successful
#13 8.361   ✔ Install constantly 23.10.4 successful
#13 8.377   ✔ Install coreapi 2.3.3 successful
#13 8.601   ✔ Install coverage 7.1.0 successful
#13 8.661   ✔ Install automat 22.10.0 successful
#13 8.673   ✔ Install craftr-dsl 0.7.7 successful
#13 8.770   ✔ Install cron-descriptor 1.4.3 successful
#13 9.549   ✔ Install daphne 4.0.0 successful
#13 9.668   ✔ Install databind-core 4.4.0 successful
#13 9.865   ✔ Install databind-json 4.4.0 successful
#13 10.24   ✔ Install defusedxml 0.7.1 successful
#13 10.38   ✔ Install deprecated 1.2.13 successful
#13 10.39   ✔ Install celery 5.4.0 successful
#13 10.69   ✔ Install dj-stripe 2.8.1 successful
#13 11.04   ✔ Install cryptography 42.0.0 successful
#13 11.18   ✔ Install django-celery-results 2.5.1 successful
#13 12.51   ✔ Install calleee 0.4.0 successful
#13 12.66   ✔ Install django-environ 0.9.0 successful
#13 12.68   ✔ Install coreschema 0.0.4 successful
#13 12.82   ✔ Install django-hashid-field 3.3.7 successful
#13 12.97   ✔ Install django-hosts 5.2 successful
#13 13.09   ✔ Install django-ratelimit 4.0.0 successful
#13 13.13   ✔ Install django-extensions 3.2.1 successful
#13 13.27   ✔ Install django-redis 5.4.0 successful
#13 13.28   ✔ Install django-ses 4.1.0 successful
#13 13.34   ✔ Install django-celery-beat 2.6.0 successful
#13 13.39   ✔ Install django-timezone-field 6.1.0 successful
#13 13.47   ✔ Install django-storages 1.13.2 successful
#13 13.61   ✔ Install autobahn 23.6.2 successful
#13 13.73   ✔ Install djangorestframework-simplejwt 5.2.2 successful
#13 13.73   ✔ Install docker 5.0.3 successful
#13 13.74   ✔ Install docspec 2.2.1 successful
#13 13.82   ✔ Install docspec-python 2.2.1 successful
#13 14.20   ✔ Install djangorestframework 3.14.0 successful
#13 14.42   ✔ Install django-channels-graphql-ws 1.0.0rc6 successful
#13 14.54   ✔ Install drf-access-policy 1.3.0 successful
#13 14.55   ✔ Install ecdsa 0.18.0 successful
#13 14.57   ✔ Install docstring-parser 0.11 successful
#13 14.58   ✔ Install factory-boy 3.2.1 successful
#13 14.67   ✔ Install freezegun 1.2.2 successful
#13 14.71   ✔ Install cfn-lint 0.61.3 successful
#13 14.75   ✔ Install frozenlist 1.3.3 successful
#13 14.89   ✔ Install flower 2.0.1 successful
#13 15.03   ✔ Install graphene-file-upload 1.3.0 successful
#13 15.09   ✔ Install graphene 3.2.1 successful
#13 15.12   ✔ Install graphene-django 3.0.0 successful
#13 15.24   ✔ Install graphql-relay 3.2.0 successful
#13 15.52   ✔ Install graphql-core 3.2.3 successful
#13 15.53   ✔ Install gunicorn 22.0.0 successful
#13 15.59   ✔ Install greenlet 2.0.1 successful
#13 15.64   ✔ Install hashids 1.3.1 successful
#13 15.66   ✔ Install h11 0.14.0 successful
#13 15.79   ✔ Install hyperlink 21.0.0 successful
#13 15.84   ✔ Install httptools 0.6.1 successful
#13 15.88   ✔ Install drf-yasg 1.21.4 successful
#13 15.94   ✔ Install incremental 22.10.0 successful
#13 15.95   ✔ Install idna 3.3 successful
#13 15.97   ✔ Install inflection 0.5.1 successful
#13 15.98   ✔ Install humanize 4.9.0 successful
#13 16.01   ✔ Install iniconfig 1.1.1 successful
#13 16.03   ✔ Install itypes 1.2.0 successful
#13 16.09   ✔ Install jmespath 1.0.1 successful
#13 16.11   ✔ Install botocore 1.29.58 successful
#13 16.12   ✔ Install jsondiff 2.0.0 successful
#13 16.13   ✔ Install jschema-to-python 1.2.3 successful
#13 16.16   ✔ Install jinja2 3.1.2 successful
#13 16.17   ✔ Install jsonpatch 1.32 successful
#13 16.19   ✔ Install jsonpickle 2.2.0 successful
#13 16.20   ✔ Install jsonpointer 2.3 successful
#13 16.22   ✔ Install junit-xml 1.9 successful
#13 16.24   ✔ Install jsonschema 3.2.0 successful
#13 16.36   ✔ Install markdown 3.4.3 successful
#13 16.39   ✔ Install faker 13.15.0 successful
#13 16.42   ✔ Install kombu 5.3.7 successful
#13 16.44   ✔ Install msgpack 1.0.7 successful
#13 16.45   ✔ Install mypy-extensions 0.4.3 successful
#13 16.50   ✔ Install multidict 6.0.4 successful
#13 16.56   ✔ Install novella 0.2.6 successful
#13 16.57   ✔ Install nr-date 2.0.0 successful
#13 16.69   ✔ Install gevent 22.10.2 successful
#13 16.79   ✔ Install nr-util 0.8.12 successful
#13 16.83   ✔ Install oauthlib 3.2.0 successful
#13 16.87   ✔ Install openapi-schema-validator 0.2.3 successful
#13 16.88   ✔ Install openai 0.27.2 successful
#13 17.00   ✔ Install pathspec 0.9.0 successful
#13 17.00   ✔ Install openapi-spec-validator 0.4.0 successful
#13 17.02   ✔ Install packaging 23.1 successful
#13 17.12   ✔ Install platformdirs 2.5.2 successful
#13 17.20   ✔ Install pbr 5.9.0 successful
#13 17.26   ✔ Install pluggy 1.5.0 successful
#13 17.41   ✔ Install prometheus-client 0.20.0 successful
#13 17.62   ✔ Install nr-stream 1.1.5 successful
#13 17.70   ✔ Install networkx 2.8.5 successful
#13 17.81   ✔ Install prompt-toolkit 3.0.47 successful
#13 17.89   ✔ Install django 5.0.6 successful
#13 17.99   ✔ Install pillow 9.4.0 successful
#13 18.00   ✔ Install moto 4.1.1 successful
#13 18.11   ✔ Install pycparser 2.21 successful
#13 18.12   ✔ Install pyasn1 0.4.8 successful
#13 18.14   ✔ Install pydoc-markdown 4.8.2 successful
#13 18.20   ✔ Install pyasn1-modules 0.3.0 successful
#13 18.20   ✔ Install pydantic 1.10.7 successful
#13 18.25   ✔ Install pyjwt 2.4.0 successful
#13 18.31   ✔ Install pyotp 2.8.0 successful
#13 18.32   ✔ Install pyopenssl 24.0.0 successful
#13 18.35   ✔ Install markupsafe 2.1.1 successful
#13 18.37   ✔ Install pyparsing 3.0.9 successful
#13 18.40   ✔ Install pytest-cov 4.0.0 successful
#13 18.42   ✔ Install pytest-django 4.5.2 successful
#13 18.43   ✔ Install pytest-dotenv 0.5.2 successful
#13 18.44   ✔ Install pytest-factoryboy 2.7.0 successful
#13 18.51   ✔ Install pytest-freezer 0.4.6 successful
#13 18.51   ✔ Install pytest-mock 3.10.0 successful
#13 18.53   ✔ Install python-crontab 3.2.0 successful
#13 18.54   ✔ Install pytest 8.2.1 successful
#13 18.71   ✔ Install psycopg2-binary 2.9.9 successful
#13 18.84   ✔ Install python-dotenv 0.20.0 successful
#13 18.85   ✔ Install promise 2.3 successful
#13 18.92   ✔ Install python-jose 3.3.0 successful
#13 18.94   ✔ Install python-dateutil 2.8.2 successful
#13 18.98   ✔ Install python3-openid 3.2.0 successful
#13 19.05   ✔ Install requests 2.28.2 successful
#13 19.09   ✔ Install requests-oauthlib 1.3.1 successful
#13 19.11   ✔ Install responses 0.21.0 successful
#13 19.16   ✔ Install pyyaml 6.0.1 successful
#13 19.21   ✔ Install rsa 4.9 successful
#13 19.24   ✔ Install redis 5.0.1 successful
#13 19.29   ✔ Install ruamel-yaml 0.17.21 successful
#13 19.38   ✔ Install sarif-om 1.0.4 successful
#13 19.42   ✔ Install service-identity 24.1.0 successful
#13 19.45   ✔ Install six 1.16.0 successful
#13 19.50   ✔ Install sniffio 1.3.0 successful
#13 19.60   ✔ Install social-auth-app-django 5.0.0 successful
#13 19.76   ✔ Install s3transfer 0.6.0 successful
#13 19.83   ✔ Install sentry-sdk 1.45.0 successful
#13 19.89   ✔ Install sshpubkeys 3.3.1 successful
#13 19.89   ✔ Install sqlparse 0.4.2 successful
#13 20.06   ✔ Install pytest-faker 2.0.0 successful
#13 20.07   ✔ Install text-unidecode 1.3 successful
#13 20.09   ✔ Install pytz 2022.1 successful
#13 20.15   ✔ Install tomli 2.0.1 successful
#13 20.16   ✔ Install tomli-w 1.0.0 successful
#13 20.18   ✔ Install stripe 3.5.0 successful
#13 20.25   ✔ Install ruff 0.0.261 successful
#13 20.32   ✔ Install social-auth-core 4.3.0 successful
#13 20.33   ✔ Install tornado 6.4.1 successful
#13 20.34   ✔ Install txaio 23.1.1 successful
#13 20.35   ✔ Install tqdm 4.65.0 successful
#13 20.37   ✔ Install typeapi 2.1.1 successful
#13 20.39   ✔ Install typing-extensions 4.3.0 successful
#13 20.42   ✔ Install uritemplate 4.1.1 successful
#13 20.53   ✔ Install uvicorn 0.27.1 successful
#13 20.56   ✔ Install vine 5.1.0 successful
#13 20.59   ✔ Install urllib3 1.26.14 successful
#13 20.68   ✔ Install wcwidth 0.2.13 successful
#13 20.70   ✔ Install tzdata 2022.7 successful
#13 20.77   ✔ Install websocket-client 1.3.3 successful
#13 20.81   ✔ Install websockets 12.0 successful
#13 20.87   ✔ Install uvloop 0.19.0 successful
#13 20.89   ✔ Install whitenoise 6.3.0 successful
#13 20.95   ✔ Install xmltodict 0.13.0 successful
#13 21.00   ✔ Install werkzeug 2.1.2 successful
#13 21.07   ✔ Install watchfiles 0.21.0 successful
#13 21.08   ✔ Install wrapt 1.14.1 successful
#13 21.18   ✔ Install zope-event 4.6 successful
#13 21.23   ✔ Install yapf 0.33.0 successful
#13 21.34   ✔ Install zope-interface 6.0 successful
#13 21.51   ✔ Install yarl 1.8.2 successful
#13 21.75   ✔ Install watchdog 2.3.1 successful
#13 24.45   ✔ Install termcolor 1.1.0 successful
#13 24.49   ✔ Install twisted 23.10.0 successful
#13 28.02   ✔ Install pyrsistent 0.18.1 successful
#13 28.02 
#13 28.02 🎉 All complete!
#13 28.02 
#13 28.50 
#13 28.50 PDM 2.5.6 is installed, while 2.25.3 is available.
#13 28.50 Please run `pdm self update` to upgrade.
#13 28.50 Run `pdm config check_update false` to disable the check.
#13 28.83 Reading package lists...
#13 29.43 Building dependency tree...
#13 29.55 Reading state information...
#13 30.37 The following packages will be REMOVED:
#13 30.37   fontconfig-config* fonts-dejavu-core* libbsd0* libc-dev-bin* libc-devtools*
#13 30.37   libc6-dev* libcrypt-dev* libdeflate0* libfontconfig1* libfreetype6* libgd3*
#13 30.37   libjbig0* libjpeg62-turbo* libldap-common* libmd0* libnsl-dev* libpng16-16*
#13 30.37   libsasl2-modules* libtiff5* libtirpc-dev* libwebp6* libx11-6* libx11-data*
#13 30.37   libxau6* libxcb1* libxdmcp6* libxpm4* linux-libc-dev* manpages*
#13 30.37   manpages-dev* publicsuffix* ucf*
#13 30.66 0 upgraded, 0 newly installed, 32 to remove and 0 not upgraded.
#13 30.66 After this operation, 39.1 MB disk space will be freed.
#13 30.71 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 19227 files and directories currently installed.)
#13 30.80 Removing libc-devtools (2.31-13+deb11u13) ...
#13 30.86 Removing libgd3:arm64 (2.3.0-2) ...
#13 30.91 Removing libfontconfig1:arm64 (2.13.1-4.2) ...
#13 30.95 Removing fontconfig-config (2.13.1-4.2) ...
#13 31.11 Removing fonts-dejavu-core (2.37-2) ...
#13 31.14 Removing libxpm4:arm64 (1:3.5.12-1.1+deb11u1) ...
#13 31.19 Removing libx11-6:arm64 (2:1.7.2-1+deb11u2) ...
#13 31.23 Removing libxcb1:arm64 (1.14-3) ...
#13 31.28 Removing libxdmcp6:arm64 (1:1.1.2-3) ...
#13 31.32 Removing libbsd0:arm64 (0.11.3-1+deb11u1) ...
#13 31.36 Removing libc6-dev:arm64 (2.31-13+deb11u13) ...
#13 31.52 Removing libc-dev-bin (2.31-13+deb11u13) ...
#13 31.53 Removing libcrypt-dev:arm64 (1:4.4.18-4) ...
#13 31.55 Removing libtiff5:arm64 (4.2.0-1+deb11u6) ...
#13 31.57 Removing libdeflate0:arm64 (1.7-1) ...
#13 31.59 Removing libfreetype6:arm64 (2.10.4+dfsg-1+deb11u2) ...
#13 31.60 Removing libjbig0:arm64 (2.1-3.1+b2) ...
#13 31.62 Removing libjpeg62-turbo:arm64 (1:2.0.6-4) ...
#13 31.63 Removing libldap-common (2.4.57+dfsg-3+deb11u1) ...
#13 31.64 Removing libmd0:arm64 (1.0.3-3) ...
#13 31.66 Removing libnsl-dev:arm64 (1.3.0-2) ...
#13 31.67 Removing libpng16-16:arm64 (1.6.37-3) ...
#13 31.69 Removing libsasl2-modules:arm64 (2.1.27+dfsg-2.1+deb11u1) ...
#13 31.70 Removing libtirpc-dev:arm64 (1.3.1-1+deb11u1) ...
#13 31.71 Removing libwebp6:arm64 (0.6.1-2.1+deb11u2) ...
#13 31.73 Removing libx11-data (2:1.7.2-1+deb11u2) ...
#13 31.81 Removing libxau6:arm64 (1:1.0.9-1) ...
#13 31.83 Removing linux-libc-dev:arm64 (5.10.237-1) ...
#13 31.92 Removing manpages-dev (5.10-1) ...
#13 31.95 Removing manpages (5.10-1) ...
#13 31.97 Removing publicsuffix (20220811.1734-0+deb11u1) ...
#13 31.99 Removing ucf (3.0043+deb11u2) ...
#13 32.01 Processing triggers for libc-bin (2.31-13+deb11u13) ...
#13 32.04 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 14614 files and directories currently installed.)
#13 32.04 Purging configuration files for fontconfig-config (2.13.1-4.2) ...
#13 32.10 debconf: unable to initialize frontend: Dialog
#13 32.10 debconf: (TERM is not set, so the dialog frontend is not usable.)
#13 32.10 debconf: falling back to frontend: Readline
#13 32.10 debconf: unable to initialize frontend: Readline
#13 32.10 debconf: (Can't locate Term/ReadLine.pm in @INC (you may need to install the Term::ReadLine module) (@INC contains: /etc/perl /usr/local/lib/aarch64-linux-gnu/perl/5.32.1 /usr/local/share/perl/5.32.1 /usr/lib/aarch64-linux-gnu/perl5/5.32 /usr/share/perl5 /usr/lib/aarch64-linux-gnu/perl-base /usr/lib/aarch64-linux-gnu/perl/5.32 /usr/share/perl/5.32 /usr/local/lib/site_perl) at /usr/share/perl5/Debconf/FrontEnd/Readline.pm line 7.)
#13 32.10 debconf: falling back to frontend: Teletype
#13 32.19 Purging configuration files for libsasl2-modules:arm64 (2.1.27+dfsg-2.1+deb11u1) ...
#13 32.23 Purging configuration files for libldap-common (2.4.57+dfsg-3+deb11u1) ...
#13 32.26 Purging configuration files for fonts-dejavu-core (2.37-2) ...
#13 32.28 Purging configuration files for ucf (3.0043+deb11u2) ...
#13 32.32 debconf: unable to initialize frontend: Dialog
#13 32.32 debconf: (TERM is not set, so the dialog frontend is not usable.)
#13 32.32 debconf: falling back to frontend: Readline
#13 32.32 debconf: unable to initialize frontend: Readline
#13 32.32 debconf: (Can't locate Term/ReadLine.pm in @INC (you may need to install the Term::ReadLine module) (@INC contains: /etc/perl /usr/local/lib/aarch64-linux-gnu/perl/5.32.1 /usr/local/share/perl/5.32.1 /usr/lib/aarch64-linux-gnu/perl5/5.32 /usr/share/perl5 /usr/lib/aarch64-linux-gnu/perl-base /usr/lib/aarch64-linux-gnu/perl/5.32 /usr/share/perl/5.32 /usr/local/lib/site_perl) at /usr/share/perl5/Debconf/FrontEnd/Readline.pm line 7.)
#13 32.32 debconf: falling back to frontend: Teletype
#13 DONE 32.4s

#14 [backend backend_build  8/10] WORKDIR /app
#14 DONE 0.0s

#15 [backend backend_build  9/10] COPY . /app/
#15 DONE 0.1s

#16 [backend backend_build 10/10] RUN chmod +x /app/scripts/runtime/*.sh
#16 DONE 0.1s

#17 [backend static_files 1/1] RUN ./scripts/runtime/build_static.sh
#17 7.823 
#17 7.823 202 static files copied to '/app/static', 580 post-processed.
#17 DONE 9.3s

#18 [backend backend 1/1] COPY --from=static_files /app/static /app/static
#18 DONE 0.4s

#19 [backend] exporting to image
#19 exporting layers
#19 exporting layers 4.9s done
#19 writing image sha256:7c585d44ccfa3c26469773ab76af59f8ef4dff7128f21b38d7aba63e036758ad
#19 writing image sha256:7c585d44ccfa3c26469773ab76af59f8ef4dff7128f21b38d7aba63e036758ad done
#19 naming to docker.io/saas/backend done
#19 DONE 5.0s

#20 [backend] resolving provenance for metadata file
#20 DONE 0.0s
 backend  Built
