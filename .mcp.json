{"mcpServers": {"filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/GitHub/bid_bees_full_project"], "env": {}}, "github": {"command": "npx", "args": ["@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token-here"}}, "postgres": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres", "postgresql://localhost/bidbees"], "env": {}}, "memory": {"command": "npx", "args": ["@modelcontextprotocol/server-memory"], "env": {}}, "web-search": {"command": "npx", "args": ["@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key-here"}}, "puppeteer": {"command": "npx", "args": ["@modelcontextprotocol/server-puppeteer"], "env": {}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest"], "env": {"SUPABASE_URL": "https://uvksgkpxeyyssvdsxbts.supabase.co", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAzNTg0MiwiZXhwIjoyMDYxNjExODQyfQ.i3R6lZOWC3Z60L326m20w-NrGh4Nasj9bi1qI6DipDY"}}}}