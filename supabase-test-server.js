import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3000;

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Simple Supabase REST API client
async function supabaseQuery(table, options = {}) {
  const { select = '*', limit = 10 } = options;
  const url = `${supabaseUrl}/rest/v1/${table}?select=${select}&limit=${limit}`;

  const response = await fetch(url, {
    headers: {
      'apikey': supabase<PERSON>ey,
      'Authorization': `Bearer ${supabaseKey}`,
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return await response.json();
}

// Middleware
app.use(express.static('public'));
app.use(express.json());

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// API Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'BidBees Supabase Test Server',
    timestamp: new Date().toISOString(),
    supabase: {
      url: supabaseUrl,
      connected: true
    }
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'BidBees Supabase Test Server',
    timestamp: new Date().toISOString(),
    supabase: {
      url: supabaseUrl,
      connected: true
    }
  });
});

app.get('/api/test', (req, res) => {
  res.json({
    message: 'API endpoint is working correctly',
    timestamp: new Date().toISOString(),
    service: 'BidBees Backend API',
    version: '1.0.0'
  });
});

app.get('/api/test/database', async (req, res) => {
  try {
    // Test Supabase connection by querying a simple table
    const data = await supabaseQuery('users', { limit: 1 });
    res.json({
      type: 'Supabase PostgreSQL',
      status: 'connected',
      message: 'Database connection successful',
      timestamp: new Date().toISOString(),
      test_query: 'SELECT from users LIMIT 1',
      result_count: data?.length || 0
    });
  } catch (error) {
    res.status(500).json({
      type: 'Supabase PostgreSQL',
      status: 'error',
      message: 'Database connection failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

app.get('/api/env', (req, res) => {
  res.json({
    node_env: process.env.NODE_ENV || 'development',
    service: 'BidBees Backend',
    supabase_configured: !!(supabaseUrl && supabaseKey),
    supabase_url: supabaseUrl,
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Get real data from Supabase tables
app.get('/api/data/tenders', async (req, res) => {
  try {
    const data = await supabaseQuery('tenders');

    res.json({
      success: true,
      count: data?.length || 0,
      data: data || []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      table: 'tenders'
    });
  }
});

app.get('/api/data/users', async (req, res) => {
  try {
    const data = await supabaseQuery('users', {
      select: 'id,username,name,profile_complete,win_streak,created_at'
    });

    res.json({
      success: true,
      count: data?.length || 0,
      data: data || []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      table: 'users'
    });
  }
});

app.get('/api/data/quotes', async (req, res) => {
  try {
    const data = await supabaseQuery('quotes');

    res.json({
      success: true,
      count: data?.length || 0,
      data: data || []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      table: 'quotes'
    });
  }
});

// Get all available tables
app.get('/api/tables', async (req, res) => {
  try {
    // Try common tables
    const tables = ['users', 'tenders', 'quotes', 'bee_tasks', 'site_meetings', 'smart_contracts'];
    const results = [];

    for (const table of tables) {
      try {
        const data = await supabaseQuery(table, { limit: 1 });
        results.push({
          table_name: table,
          row_count: data?.length || 0,
          status: 'accessible'
        });
      } catch (e) {
        results.push({
          table_name: table,
          row_count: 0,
          status: 'error',
          error: e.message
        });
      }
    }

    res.json({
      success: true,
      tables: results
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Serve the main HTML page
app.get('/', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBees - Real Supabase Data Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
        .data-section {
            margin: 30px 0;
        }
        .data-section h3 {
            color: #333;
            border-bottom: 2px solid #f093fb;
            padding-bottom: 10px;
        }
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .data-card {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .data-card h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.2em;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th,
        .data-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐝 BidBees Dashboard</h1>
            <p>Real-time data from Supabase Database</p>
        </div>
        
        <div class="content">
            <div class="status-card">
                <h3>🔗 Connection Status</h3>
                <div id="connection-status">Checking connection...</div>
            </div>
            
            <div class="data-section">
                <h3>📊 Database Tables</h3>
                <button class="btn" onclick="loadAllData()">🔄 Refresh All Data</button>
                <button class="btn" onclick="loadTenders()">📋 Load Tenders</button>
                <button class="btn" onclick="loadUsers()">👥 Load Users</button>
                <button class="btn" onclick="loadQuotes()">💰 Load Quotes</button>
                
                <div class="data-grid" id="data-container">
                    <div class="loading">Click a button above to load data...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        async function checkConnection() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                document.getElementById('connection-status').innerHTML = \`
                    <div class="success">
                        ✅ Connected to Supabase<br>
                        <strong>URL:</strong> \${data.supabase.url}<br>
                        <strong>Service:</strong> \${data.service}<br>
                        <strong>Time:</strong> \${new Date(data.timestamp).toLocaleString()}
                    </div>
                \`;
            } catch (error) {
                document.getElementById('connection-status').innerHTML = \`
                    <div class="error">❌ Connection failed: \${error.message}</div>
                \`;
            }
        }

        async function loadTenders() {
            showLoading('Loading tenders...');
            try {
                const response = await fetch('/api/data/tenders');
                const result = await response.json();
                
                if (result.success) {
                    displayData('Tenders', result.data, ['id', 'title', 'status', 'issuer', 'win_chance']);
                } else {
                    showError('Failed to load tenders: ' + result.error);
                }
            } catch (error) {
                showError('Error loading tenders: ' + error.message);
            }
        }

        async function loadUsers() {
            showLoading('Loading users...');
            try {
                const response = await fetch('/api/data/users');
                const result = await response.json();
                
                if (result.success) {
                    displayData('Users', result.data, ['id', 'username', 'name', 'profile_complete', 'win_streak']);
                } else {
                    showError('Failed to load users: ' + result.error);
                }
            } catch (error) {
                showError('Error loading users: ' + error.message);
            }
        }

        async function loadQuotes() {
            showLoading('Loading quotes...');
            try {
                const response = await fetch('/api/data/quotes');
                const result = await response.json();
                
                if (result.success) {
                    displayData('Quotes', result.data, ['id', 'supplier_id', 'amount', 'submission_risk']);
                } else {
                    showError('Failed to load quotes: ' + result.error);
                }
            } catch (error) {
                showError('Error loading quotes: ' + error.message);
            }
        }

        async function loadAllData() {
            await loadTenders();
            await loadUsers();
            await loadQuotes();
        }

        function showLoading(message) {
            document.getElementById('data-container').innerHTML = \`
                <div class="loading">\${message}</div>
            \`;
        }

        function showError(message) {
            document.getElementById('data-container').innerHTML = \`
                <div class="error">\${message}</div>
            \`;
        }

        function displayData(title, data, columns) {
            const container = document.getElementById('data-container');
            
            if (data.length === 0) {
                container.innerHTML = \`
                    <div class="data-card">
                        <h4>\${title}</h4>
                        <p>No data found in this table.</p>
                    </div>
                \`;
                return;
            }

            const tableHTML = \`
                <div class="data-card">
                    <h4>\${title} (\${data.length} records)</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                \${columns.map(col => \`<th>\${col}</th>\`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            \${data.map(row => \`
                                <tr>
                                    \${columns.map(col => \`<td>\${row[col] || 'N/A'}</td>\`).join('')}
                                </tr>
                            \`).join('')}
                        </tbody>
                    </table>
                </div>
            \`;
            
            container.innerHTML = tableHTML;
        }

        // Initialize
        window.addEventListener('load', () => {
            checkConnection();
        });
    </script>
</body>
</html>
  `);
});

app.listen(PORT, () => {
  console.log(`🐝 BidBees Supabase Test Server running on http://localhost:${PORT}`);
  console.log(`📊 Dashboard: http://localhost:${PORT}`);
  console.log(`🔗 Supabase URL: ${supabaseUrl}`);
});
