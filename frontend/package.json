{"name": "bidbeez-dashboard", "version": "1.0.0", "description": "BidBeez AI-Powered Bidding Dashboard", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@mui/x-charts": "^7.18.0", "@mui/x-data-grid": "^7.18.0", "@reduxjs/toolkit": "^2.8.2", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.56.2", "axios": "^1.10.0", "chart.js": "^4.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "i18next": "^23.16.0", "lodash": "^4.17.21", "lucide-react": "^0.516.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.9", "react-hook-form": "^7.53.0", "react-hotkeys-hook": "^4.5.1", "react-i18next": "^15.0.2", "react-intersection-observer": "^9.13.1", "react-redux": "^9.2.0", "react-router-dom": "^6.30.1", "react-virtualized": "^9.22.5", "react-window": "^1.8.10", "recharts": "^2.12.7", "socket.io-client": "^4.7.5", "tailwind-merge": "^3.3.1", "tesseract.js": "^5.1.0", "workbox-precaching": "^7.1.0", "yup": "^1.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.10", "@types/node": "^24.0.10", "@types/react": "^18.3.8", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.21", "eslint": "^9.12.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.12", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "typescript": "^5.6.2", "vite": "^5.4.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}