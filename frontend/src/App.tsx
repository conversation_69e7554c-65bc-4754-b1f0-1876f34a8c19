import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Providers
import { QueryProvider } from '@/providers/QueryProvider';

// Enterprise Components
import { EnterpriseDashboard } from '@/components/enterprise/Dashboard';
import { TendersPage } from '@/components/enterprise/TendersPage';
// import { MapPage } from '@/components/enterprise/MapPage';
import { CalendarPage } from '@/components/enterprise/CalendarPage';

// Layout components
import { EnterpriseLayout } from '@/components/layout/EnterpriseLayout';

const App: React.FC = () => {
  return (
    <QueryProvider>
      <Router>
        <Routes>
          {/* Enterprise TMS Routes */}
          <Route path="/" element={<EnterpriseLayout />}>
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<EnterpriseDashboard />} />
            <Route path="tenders" element={<TendersPage />} />
            {/* <Route path="map" element={<MapPage />} /> */}
            <Route path="calendar" element={<CalendarPage />} />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </QueryProvider>
  );
};

export default App;
