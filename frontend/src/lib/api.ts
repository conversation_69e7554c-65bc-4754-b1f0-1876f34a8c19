import { supabase } from './supabase'

// API Configuration
const API_BASE_URL = 'http://localhost:3000/api'
const TENDER_SERVICE_URL = 'http://localhost:3012/api' // Tender microservice

// Types for Enterprise TMS
export interface EnterpriseUser {
  id: string
  username: string
  name: string
  email?: string
  profile_complete: number
  win_streak: number
  tenant_id?: string
  created_at: string
  role?: 'admin' | 'manager' | 'user'
  department?: string
  permissions?: string[]
}

export interface EnterpriseTender {
  id: string
  ocid?: string
  title: string
  description: string
  status: 'Open' | 'Closed' | 'Draft' | 'Awarded' | 'Cancelled'
  issuer: string
  win_chance: number
  due_date: string
  location: string
  value: string
  lng: number
  lat: number
  main_procurement_category: string
  tender_start_date?: string
  enquiry_end_date?: string
  created_at: string
  // Enterprise specific fields
  priority?: 'high' | 'medium' | 'low'
  assigned_team?: string[]
  compliance_score?: number
  risk_assessment?: string
  estimated_effort_hours?: number
  required_certifications?: string[]
}

export interface EnterpriseBid {
  id: string
  tender_id: string
  bid_number: string
  amount: number
  currency: string
  status: 'draft' | 'submitted' | 'under_review' | 'shortlisted' | 'awarded' | 'rejected'
  win_probability: number
  submission_date: string
  last_updated: string
  // Enterprise specific
  team_lead?: string
  estimated_margin?: number
  risk_factors?: string[]
  compliance_checklist?: boolean
  technical_score?: number
  commercial_score?: number
}

export interface SiteMeeting {
  id: string
  title: string
  date: string
  location: string
  status: 'scheduled' | 'completed' | 'cancelled'
  tender_id?: string
  attendees?: string[]
  meeting_type?: 'site_visit' | 'clarification' | 'presentation'
  notes?: string
  created_at: string
}

export interface TenderStats {
  total: number
  open: number
  closed: number
  totalValue: number
  categories: Record<string, number>
  winRate?: number
  averageBidValue?: number
  monthlyTrends?: Array<{
    month: string
    tenders: number
    bids: number
    wins: number
  }>
}

// API Functions
export const api = {
  // Tender Management
  async getTenders(filters?: {
    search?: string
    status?: string
    location?: string
    category?: string
    minValue?: number
    maxValue?: number
    dateFrom?: string
    dateTo?: string
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }): Promise<{
    tenders: EnterpriseTender[]
    total: number
    page: number
    totalPages: number
    hasNextPage: boolean
    hasPreviousPage: boolean
  }> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString())
        }
      })
    }

    const response = await fetch(`${TENDER_SERVICE_URL}/etenders?${params}`)
    if (!response.ok) {
      throw new Error('Failed to fetch tenders')
    }
    return response.json()
  },

  async getTenderStats(): Promise<TenderStats> {
    const response = await fetch(`${TENDER_SERVICE_URL}/etenders/stats`)
    if (!response.ok) {
      throw new Error('Failed to fetch tender stats')
    }
    const data = await response.json()
    return data.stats
  },

  async getTenderById(id: string): Promise<EnterpriseTender> {
    const { data, error } = await supabase
      .from('etenders_releases')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  // Bid Management
  async getBids(tenderId?: string): Promise<EnterpriseBid[]> {
    let query = supabase
      .from('supplier_quotes')
      .select(`
        id,
        quote_id,
        amount,
        status,
        trust_score,
        currency,
        created_at,
        updated_at,
        tenders (
          title,
          issuer_name,
          closing_date,
          category_code,
          description
        )
      `)

    if (tenderId) {
      query = query.eq('tender_id', tenderId)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    if (error) throw error
    return data || []
  },

  async createBid(bid: Partial<EnterpriseBid>): Promise<EnterpriseBid> {
    const { data, error } = await supabase
      .from('supplier_quotes')
      .insert(bid)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async updateBid(id: string, updates: Partial<EnterpriseBid>): Promise<EnterpriseBid> {
    const { data, error } = await supabase
      .from('supplier_quotes')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Site Meetings
  async getSiteMeetings(filters?: {
    tenderId?: string
    dateFrom?: string
    dateTo?: string
    status?: string
  }): Promise<SiteMeeting[]> {
    let query = supabase
      .from('site_meetings')
      .select('*')

    if (filters?.tenderId) {
      query = query.eq('tender_id', filters.tenderId)
    }
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.dateFrom) {
      query = query.gte('date', filters.dateFrom)
    }
    if (filters?.dateTo) {
      query = query.lte('date', filters.dateTo)
    }

    const { data, error } = await query.order('date', { ascending: true })
    if (error) throw error
    return data || []
  },

  async createSiteMeeting(meeting: Partial<SiteMeeting>): Promise<SiteMeeting> {
    const { data, error } = await supabase
      .from('site_meetings')
      .insert(meeting)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async updateSiteMeeting(id: string, updates: Partial<SiteMeeting>): Promise<SiteMeeting> {
    const { data, error } = await supabase
      .from('site_meetings')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // User Management
  async getCurrentUser(): Promise<EnterpriseUser | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .limit(1)
      .single()

    if (error) return null
    return data
  },

  async getUsers(): Promise<EnterpriseUser[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  // Dashboard Analytics
  async getDashboardData(): Promise<{
    user: EnterpriseUser
    recentTenders: EnterpriseTender[]
    recentBids: EnterpriseBid[]
    upcomingMeetings: SiteMeeting[]
    stats: TenderStats
  }> {
    const [user, tenders, bids, meetings, stats] = await Promise.all([
      this.getCurrentUser(),
      this.getTenders({ limit: 5 }),
      this.getBids(),
      this.getSiteMeetings({ dateFrom: new Date().toISOString() }),
      this.getTenderStats()
    ])

    return {
      user: user!,
      recentTenders: tenders.tenders,
      recentBids: bids.slice(0, 5),
      upcomingMeetings: meetings.slice(0, 5),
      stats
    }
  }
}
