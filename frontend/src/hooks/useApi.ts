import React from 'react'
import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query'
import { api, type EnterpriseTender, type EnterpriseBid, type SiteMeeting } from '@/lib/api'

// Query Keys
export const queryKeys = {
  tenders: ['tenders'] as const,
  tender: (id: string) => ['tender', id] as const,
  tenderStats: ['tender-stats'] as const,
  bids: ['bids'] as const,
  bidsByTender: (tenderId: string) => ['bids', 'tender', tenderId] as const,
  siteMeetings: ['site-meetings'] as const,
  users: ['users'] as const,
  currentUser: ['current-user'] as const,
  dashboard: ['dashboard'] as const,
}

// Tender Hooks
export function useTenders(filters?: Parameters<typeof api.getTenders>[0]) {
  return useQuery({
    queryKey: [...queryKeys.tenders, filters],
    queryFn: () => api.getTenders(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  })
}

export function useTender(id: string) {
  return useQuery({
    queryKey: queryKeys.tender(id),
    queryFn: () => api.getTenderById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useTenderStats() {
  return useQuery({
    queryKey: queryKeys.tenderStats,
    queryFn: api.getTenderStats,
    staleTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
  })
}

// Bid Hooks
export function useBids(tenderId?: string) {
  return useQuery({
    queryKey: tenderId ? queryKeys.bidsByTender(tenderId) : queryKeys.bids,
    queryFn: () => api.getBids(tenderId),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useCreateBid() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: api.createBid,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.bids })
    },
  })
}

export function useUpdateBid() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<EnterpriseBid> }) =>
      api.updateBid(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.bids })
    },
  })
}

// Site Meeting Hooks
export function useSiteMeetings(filters?: Parameters<typeof api.getSiteMeetings>[0]) {
  return useQuery({
    queryKey: [...queryKeys.siteMeetings, filters],
    queryFn: () => api.getSiteMeetings(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useCreateSiteMeeting() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: api.createSiteMeeting,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.siteMeetings })
    },
  })
}

export function useUpdateSiteMeeting() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<SiteMeeting> }) =>
      api.updateSiteMeeting(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.siteMeetings })
    },
  })
}

// User Hooks
export function useCurrentUser() {
  return useQuery({
    queryKey: queryKeys.currentUser,
    queryFn: api.getCurrentUser,
    staleTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
  })
}

export function useUsers() {
  return useQuery({
    queryKey: queryKeys.users,
    queryFn: api.getUsers,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Dashboard Hook
export function useDashboard() {
  return useQuery({
    queryKey: queryKeys.dashboard,
    queryFn: api.getDashboardData,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true,
  })
}

// Real-time hooks for live updates
export function useRealTimeTenders() {
  const queryClient = useQueryClient()
  
  // Simulate real-time updates every 30 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tenders })
      queryClient.invalidateQueries({ queryKey: queryKeys.tenderStats })
    }, 30000)
    
    return () => clearInterval(interval)
  }, [queryClient])
}

// Optimistic updates for better UX
export function useOptimisticBidUpdate() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<EnterpriseBid> }) =>
      api.updateBid(id, updates),
    onMutate: async ({ id, updates }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.bids })
      
      // Snapshot previous value
      const previousBids = queryClient.getQueryData(queryKeys.bids)
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.bids, (old: EnterpriseBid[] | undefined) => {
        if (!old) return []
        return old.map(bid => bid.id === id ? { ...bid, ...updates } : bid)
      })
      
      return { previousBids }
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousBids) {
        queryClient.setQueryData(queryKeys.bids, context.previousBids)
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.bids })
    },
  })
}

// Prefetch hooks for better performance
export function usePrefetchTender() {
  const queryClient = useQueryClient()
  
  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.tender(id),
      queryFn: () => api.getTenderById(id),
      staleTime: 10 * 60 * 1000,
    })
  }
}

// Custom hook for infinite scroll tenders
export function useInfiniteTenders(filters?: Parameters<typeof api.getTenders>[0]) {
  return useInfiniteQuery({
    queryKey: [...queryKeys.tenders, 'infinite', filters],
    queryFn: ({ pageParam = 1 }) => 
      api.getTenders({ ...filters, page: pageParam, limit: 12 }),
    getNextPageParam: (lastPage) => 
      lastPage.hasNextPage ? lastPage.page + 1 : undefined,
    staleTime: 5 * 60 * 1000,
  })
}
