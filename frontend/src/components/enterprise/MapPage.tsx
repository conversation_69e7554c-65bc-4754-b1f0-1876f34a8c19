import React, { useState, useRef, useCallback, useMemo } from 'react'
import Map, { Marker, Popup, NavigationControl, FullscreenControl, ScaleControl } from 'react-map-gl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useTenders } from '@/hooks/useApi'
import { formatCurrency, formatRelativeTime, cn } from '@/lib/utils'
import { 
  MapPin, 
  Search, 
  Filter, 
  Target, 
  Building, 
  DollarSign, 
  Clock, 
  Star,
  Layers,
  Maximize,
  Navigation,
  Eye,
  Plus,
  Settings
} from 'lucide-react'

// Mapbox token - In production, this should be in environment variables
const MAPBOX_TOKEN = process.env.REACT_APP_MAPBOX_TOKEN || 'pk.eyJ1IjoiYmlkYmVlcyIsImEiOiJjbHNkZjEyM3MwMDFjMmxwZzJxYnVqOGNkIn0.example'

// South Africa viewport
const INITIAL_VIEWPORT = {
  longitude: 24.7461,
  latitude: -28.8166,
  zoom: 5.5
}

interface MapFilters {
  search: string
  status: string
  category: string
  minValue: string
  maxValue: string
}

export function MapPage() {
  const [viewport, setViewport] = useState(INITIAL_VIEWPORT)
  const [selectedTender, setSelectedTender] = useState<any>(null)
  const [filters, setFilters] = useState<MapFilters>({
    search: '',
    status: '',
    category: '',
    minValue: '',
    maxValue: ''
  })
  const [mapStyle, setMapStyle] = useState('mapbox://styles/mapbox/light-v11')
  const [showSidebar, setShowSidebar] = useState(true)
  
  const mapRef = useRef<any>(null)

  // Fetch tenders with current filters
  const apiFilters = useMemo(() => ({
    search: filters.search || undefined,
    status: filters.status || undefined,
    category: filters.category || undefined,
    minValue: filters.minValue ? parseFloat(filters.minValue) : undefined,
    maxValue: filters.maxValue ? parseFloat(filters.maxValue) : undefined,
    limit: 100 // Get more for map view
  }), [filters])

  const { data: tendersData, isLoading } = useTenders(apiFilters)

  const handleFilterChange = (key: keyof MapFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const flyToTender = useCallback((tender: any) => {
    if (mapRef.current && tender.lng && tender.lat) {
      mapRef.current.flyTo({
        center: [tender.lng, tender.lat],
        zoom: 12,
        duration: 2000
      })
      setSelectedTender(tender)
    }
  }, [])

  const getMarkerColor = (tender: any) => {
    if (tender.status === 'Open') return '#10B981' // Green
    if (tender.status === 'Closed') return '#EF4444' // Red
    if (tender.win_chance > 70) return '#F59E0B' // Amber
    return '#6B7280' // Gray
  }

  const getMarkerSize = (tender: any) => {
    if (!tender.value || typeof tender.value !== 'string') return 20
    const numericValue = parseFloat(tender.value.replace(/[^\d.]/g, ''))
    if (numericValue > 10000000) return 30 // Large
    if (numericValue > 1000000) return 25 // Medium
    return 20 // Small
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="bg-white border-b p-4 z-10">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div>
            <h1 className="text-2xl font-bold">Tender Map</h1>
            <p className="text-sm text-muted-foreground">
              Explore tender opportunities across South Africa
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSidebar(!showSidebar)}
            >
              <Layers className="h-4 w-4 mr-2" />
              {showSidebar ? 'Hide' : 'Show'} Sidebar
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex relative">
        {/* Sidebar */}
        {showSidebar && (
          <div className="w-96 bg-white border-r flex flex-col z-10">
            {/* Filters */}
            <div className="p-4 border-b">
              <h3 className="font-semibold mb-3">Filters</h3>
              <div className="space-y-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search tenders..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full h-9 px-3 rounded-md border border-input bg-background text-sm"
                >
                  <option value="">All Statuses</option>
                  <option value="Open">Open</option>
                  <option value="Closed">Closed</option>
                </select>

                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    placeholder="Min Value"
                    value={filters.minValue}
                    onChange={(e) => handleFilterChange('minValue', e.target.value)}
                  />
                  <Input
                    type="number"
                    placeholder="Max Value"
                    value={filters.maxValue}
                    onChange={(e) => handleFilterChange('maxValue', e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Map Controls */}
            <div className="p-4 border-b">
              <h3 className="font-semibold mb-3">Map Style</h3>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant={mapStyle.includes('light') ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setMapStyle('mapbox://styles/mapbox/light-v11')}
                >
                  Light
                </Button>
                <Button
                  variant={mapStyle.includes('dark') ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setMapStyle('mapbox://styles/mapbox/dark-v11')}
                >
                  Dark
                </Button>
                <Button
                  variant={mapStyle.includes('satellite') ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setMapStyle('mapbox://styles/mapbox/satellite-v9')}
                >
                  Satellite
                </Button>
                <Button
                  variant={mapStyle.includes('streets') ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setMapStyle('mapbox://styles/mapbox/streets-v12')}
                >
                  Streets
                </Button>
              </div>
            </div>

            {/* Tender List */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold">
                    Tenders ({tendersData?.tenders.length || 0})
                  </h3>
                  {isLoading && (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                  )}
                </div>
                
                <div className="space-y-3">
                  {tendersData?.tenders.map((tender) => (
                    <TenderMapCard
                      key={tender.id}
                      tender={tender}
                      isSelected={selectedTender?.id === tender.id}
                      onClick={() => flyToTender(tender)}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Map */}
        <div className="flex-1 relative">
          <Map
            ref={mapRef}
            {...viewport}
            onMove={evt => setViewport(evt.viewState)}
            mapboxAccessToken={MAPBOX_TOKEN}
            style={{ width: '100%', height: '100%' }}
            mapStyle={mapStyle}
            attributionControl={false}
          >
            {/* Navigation Controls */}
            <NavigationControl position="top-right" />
            <FullscreenControl position="top-right" />
            <ScaleControl position="bottom-left" />

            {/* Tender Markers */}
            {tendersData?.tenders.map((tender) => (
              tender.lng && tender.lat && (
                <Marker
                  key={tender.id}
                  longitude={tender.lng}
                  latitude={tender.lat}
                  anchor="bottom"
                  onClick={(e) => {
                    e.originalEvent.stopPropagation()
                    setSelectedTender(tender)
                  }}
                >
                  <div
                    className={cn(
                      "rounded-full border-2 border-white shadow-lg cursor-pointer transition-transform hover:scale-110",
                      selectedTender?.id === tender.id && "ring-2 ring-blue-500 ring-offset-2"
                    )}
                    style={{
                      backgroundColor: getMarkerColor(tender),
                      width: getMarkerSize(tender),
                      height: getMarkerSize(tender)
                    }}
                  >
                    <div className="w-full h-full rounded-full flex items-center justify-center">
                      <Target className="h-3 w-3 text-white" />
                    </div>
                  </div>
                </Marker>
              )
            ))}

            {/* Popup */}
            {selectedTender && selectedTender.lng && selectedTender.lat && (
              <Popup
                longitude={selectedTender.lng}
                latitude={selectedTender.lat}
                anchor="top"
                onClose={() => setSelectedTender(null)}
                closeButton={true}
                closeOnClick={false}
                className="max-w-sm"
              >
                <TenderPopup tender={selectedTender} />
              </Popup>
            )}
          </Map>

          {/* Legend */}
          <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10">
            <h4 className="font-semibold text-sm mb-2">Legend</h4>
            <div className="space-y-1 text-xs">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Open Tenders</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span>Closed Tenders</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span>High Win Chance</span>
              </div>
              <div className="text-xs text-muted-foreground mt-2">
                Marker size indicates tender value
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface TenderMapCardProps {
  tender: any
  isSelected: boolean
  onClick: () => void
}

function TenderMapCard({ tender, isSelected, onClick }: TenderMapCardProps) {
  return (
    <Card 
      className={cn(
        "cursor-pointer transition-all hover:shadow-md",
        isSelected && "ring-2 ring-blue-500 bg-blue-50"
      )}
      onClick={onClick}
    >
      <CardContent className="p-3">
        <div className="space-y-2">
          <div className="flex items-start justify-between">
            <h4 className="text-sm font-medium line-clamp-2">{tender.title}</h4>
            <Badge 
              variant={tender.status === 'Open' ? 'success' : 'secondary'}
              className="ml-2 shrink-0 text-xs"
            >
              {tender.status}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <Building className="h-3 w-3" />
            <span className="truncate">{tender.issuer}</span>
          </div>
          
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <MapPin className="h-3 w-3" />
            <span className="truncate">{tender.location}</span>
          </div>

          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-1">
              <DollarSign className="h-3 w-3 text-green-600" />
              <span className="font-medium">{tender.value}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Star className="h-3 w-3 text-yellow-500" />
              <span>{tender.win_chance}%</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function TenderPopup({ tender }: { tender: any }) {
  return (
    <div className="p-3 min-w-[280px]">
      <div className="space-y-3">
        <div>
          <h3 className="font-semibold text-sm line-clamp-2">{tender.title}</h3>
          <p className="text-xs text-muted-foreground mt-1">{tender.issuer}</p>
        </div>
        
        <div className="space-y-2 text-xs">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Status:</span>
            <Badge variant={tender.status === 'Open' ? 'success' : 'secondary'} className="text-xs">
              {tender.status}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Value:</span>
            <span className="font-medium">{tender.value}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Win Chance:</span>
            <span className="font-medium text-green-600">{tender.win_chance}%</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Due Date:</span>
            <span className="font-medium">{formatRelativeTime(tender.due_date)}</span>
          </div>
        </div>

        <div className="flex items-center space-x-2 pt-2">
          <Button size="sm" className="flex-1 text-xs">
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button size="sm" variant="outline" className="flex-1 text-xs">
            <Plus className="h-3 w-3 mr-1" />
            Bid
          </Button>
        </div>
      </div>
    </div>
  )
}
