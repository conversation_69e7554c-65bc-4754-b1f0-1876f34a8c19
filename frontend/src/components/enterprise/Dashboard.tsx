import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useDashboard, useTenderStats } from '@/hooks/useApi'
import { formatCurrency, formatRelativeTime, cn } from '@/lib/utils'
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  MapPin, 
  DollarSign, 
  Target,
  Users,
  Clock,
  Award,
  AlertTriangle,
  BarChart3,
  Activity
} from 'lucide-react'

export function EnterpriseDashboard() {
  const { data: dashboardData, isLoading, error } = useDashboard()
  const { data: stats } = useTenderStats()

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              <span>Failed to load dashboard data</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const { user, recentTenders, recentBids, upcomingMeetings } = dashboardData || {}

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome back, {user?.name || 'User'}
          </h1>
          <p className="text-muted-foreground">
            Here's what's happening with your tenders today.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-sm">
            <Activity className="h-3 w-3 mr-1" />
            Live Data
          </Badge>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Active Tenders"
          value={stats?.open || 0}
          change="+12%"
          trend="up"
          icon={Target}
          description="Currently open for bidding"
        />
        <MetricCard
          title="Total Value"
          value={formatCurrency(stats?.totalValue || 0)}
          change="+8%"
          trend="up"
          icon={DollarSign}
          description="Combined tender value"
        />
        <MetricCard
          title="Win Rate"
          value="68%"
          change="+5%"
          trend="up"
          icon={Award}
          description="Success rate this quarter"
        />
        <MetricCard
          title="Upcoming Meetings"
          value={upcomingMeetings?.length || 0}
          change="2 today"
          trend="neutral"
          icon={Calendar}
          description="Site visits & presentations"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Tenders */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Recent Tenders</span>
            </CardTitle>
            <CardDescription>
              Latest opportunities matching your criteria
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentTenders?.slice(0, 5).map((tender) => (
              <TenderCard key={tender.id} tender={tender} />
            ))}
            <Button variant="outline" className="w-full">
              View All Tenders
            </Button>
          </CardContent>
        </Card>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start">
                <Target className="h-4 w-4 mr-2" />
                Create New Bid
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Meeting
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <MapPin className="h-4 w-4 mr-2" />
                View Map
              </Button>
            </CardContent>
          </Card>

          {/* Upcoming Meetings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Today's Schedule</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {upcomingMeetings?.slice(0, 3).map((meeting) => (
                <div key={meeting.id} className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                  <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{meeting.title}</p>
                    <p className="text-xs text-muted-foreground">{meeting.location}</p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {formatRelativeTime(meeting.date)}
                  </Badge>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

interface MetricCardProps {
  title: string
  value: string | number
  change: string
  trend: 'up' | 'down' | 'neutral'
  icon: React.ElementType
  description: string
}

function MetricCard({ title, value, change, trend, icon: Icon, description }: MetricCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <div className="flex items-center space-x-1 text-xs text-muted-foreground">
          {trend === 'up' && <TrendingUp className="h-3 w-3 text-green-500" />}
          {trend === 'down' && <TrendingDown className="h-3 w-3 text-red-500" />}
          <span className={cn(
            trend === 'up' && 'text-green-500',
            trend === 'down' && 'text-red-500'
          )}>
            {change}
          </span>
          <span>from last month</span>
        </div>
        <p className="text-xs text-muted-foreground mt-1">{description}</p>
      </CardContent>
    </Card>
  )
}

interface TenderCardProps {
  tender: any
}

function TenderCard({ tender }: TenderCardProps) {
  return (
    <div className="flex items-center space-x-4 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium truncate">{tender.title}</h4>
        <div className="flex items-center space-x-2 mt-1">
          <Badge variant="outline" className="text-xs">
            {tender.status}
          </Badge>
          <span className="text-xs text-muted-foreground">{tender.issuer}</span>
        </div>
        <p className="text-xs text-muted-foreground mt-1">{tender.value}</p>
      </div>
      <div className="text-right">
        <div className="text-sm font-medium text-green-600">
          {tender.win_chance}% win chance
        </div>
        <div className="text-xs text-muted-foreground">
          {formatRelativeTime(tender.due_date)}
        </div>
      </div>
    </div>
  )
}
