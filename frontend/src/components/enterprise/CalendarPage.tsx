import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useSiteMeetings, useTenders } from '@/hooks/useApi'
import { formatRelativeTime, cn } from '@/lib/utils'
import { 
  Calendar as CalendarIcon, 
  Clock, 
  MapPin, 
  Users, 
  Plus,
  ChevronLeft,
  ChevronRight,
  Filter,
  Eye,
  Edit,
  Target,
  Building,
  Navigation
} from 'lucide-react'

// Calendar utilities
const DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
]

interface CalendarEvent {
  id: string
  title: string
  date: string
  time?: string
  type: 'meeting' | 'deadline' | 'presentation'
  status: 'scheduled' | 'completed' | 'cancelled'
  location?: string
  tender_id?: string
  description?: string
}

export function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month')
  const [filterType, setFilterType] = useState<string>('all')

  // Fetch data
  const { data: meetings = [] } = useSiteMeetings()
  const { data: tendersData } = useTenders({ limit: 50 })

  // Transform data into calendar events
  const events = useMemo(() => {
    const calendarEvents: CalendarEvent[] = []

    // Add site meetings
    meetings.forEach(meeting => {
      calendarEvents.push({
        id: meeting.id,
        title: meeting.title,
        date: meeting.date,
        type: 'meeting',
        status: meeting.status,
        location: meeting.location,
        tender_id: meeting.tender_id,
        description: meeting.notes
      })
    })

    // Add tender deadlines
    tendersData?.tenders.forEach(tender => {
      if (tender.due_date) {
        calendarEvents.push({
          id: `deadline-${tender.id}`,
          title: `Deadline: ${tender.title}`,
          date: tender.due_date,
          type: 'deadline',
          status: 'scheduled',
          tender_id: tender.id,
          description: `Tender submission deadline for ${tender.issuer}`
        })
      }
    })

    return calendarEvents
  }, [meetings, tendersData])

  // Filter events
  const filteredEvents = useMemo(() => {
    if (filterType === 'all') return events
    return events.filter(event => event.type === filterType)
  }, [events, filterType])

  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0]
    return filteredEvents.filter(event => 
      event.date.startsWith(dateStr)
    )
  }

  // Calendar navigation
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const goToToday = () => {
    setCurrentDate(new Date())
    setSelectedDate(new Date())
  }

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())
    
    const days = []
    const currentDateObj = new Date(startDate)
    
    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentDateObj))
      currentDateObj.setDate(currentDateObj.getDate() + 1)
    }
    
    return days
  }

  const calendarDays = generateCalendarDays()
  const today = new Date()
  const isToday = (date: Date) => 
    date.toDateString() === today.toDateString()
  const isCurrentMonth = (date: Date) => 
    date.getMonth() === currentDate.getMonth()

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Calendar & Planner</h1>
          <p className="text-muted-foreground">
            Manage your tender deadlines, site meetings, and presentations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={goToToday}>
            Today
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Meeting
          </Button>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => navigateMonth('prev')}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-xl font-semibold min-w-[200px] text-center">
              {MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}
            </h2>
            <Button variant="outline" size="sm" onClick={() => navigateMonth('next')}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="h-9 px-3 rounded-md border border-input bg-background text-sm"
            >
              <option value="all">All Events</option>
              <option value="meeting">Meetings</option>
              <option value="deadline">Deadlines</option>
              <option value="presentation">Presentations</option>
            </select>
          </div>

          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'month' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('month')}
              className="rounded-r-none"
            >
              Month
            </Button>
            <Button
              variant={viewMode === 'week' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('week')}
              className="rounded-none"
            >
              Week
            </Button>
            <Button
              variant={viewMode === 'day' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('day')}
              className="rounded-l-none"
            >
              Day
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Calendar */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CalendarIcon className="h-5 w-5" />
              <span>Calendar View</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Calendar Grid */}
            <div className="space-y-4">
              {/* Day Headers */}
              <div className="grid grid-cols-7 gap-1">
                {DAYS.map(day => (
                  <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar Days */}
              <div className="grid grid-cols-7 gap-1">
                {calendarDays.map((date, index) => {
                  const dayEvents = getEventsForDate(date)
                  const isSelected = selectedDate?.toDateString() === date.toDateString()
                  
                  return (
                    <div
                      key={index}
                      className={cn(
                        "min-h-[100px] p-2 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50",
                        !isCurrentMonth(date) && "text-muted-foreground bg-gray-50/50",
                        isToday(date) && "bg-blue-50 border-blue-200",
                        isSelected && "ring-2 ring-blue-500"
                      )}
                      onClick={() => setSelectedDate(date)}
                    >
                      <div className={cn(
                        "text-sm font-medium mb-1",
                        isToday(date) && "text-blue-600"
                      )}>
                        {date.getDate()}
                      </div>
                      
                      <div className="space-y-1">
                        {dayEvents.slice(0, 3).map(event => (
                          <div
                            key={event.id}
                            className={cn(
                              "text-xs p-1 rounded truncate",
                              event.type === 'meeting' && "bg-blue-100 text-blue-800",
                              event.type === 'deadline' && "bg-red-100 text-red-800",
                              event.type === 'presentation' && "bg-green-100 text-green-800"
                            )}
                          >
                            {event.title}
                          </div>
                        ))}
                        {dayEvents.length > 3 && (
                          <div className="text-xs text-muted-foreground">
                            +{dayEvents.length - 3} more
                          </div>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Selected Date Events */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {selectedDate ? selectedDate.toLocaleDateString() : 'Select a Date'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedDate ? (
                <div className="space-y-3">
                  {getEventsForDate(selectedDate).map(event => (
                    <EventCard key={event.id} event={event} />
                  ))}
                  {getEventsForDate(selectedDate).length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      No events scheduled
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  Click on a date to view events
                </p>
              )}
            </CardContent>
          </Card>

          {/* Upcoming Events */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Upcoming</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredEvents
                  .filter(event => new Date(event.date) >= today)
                  .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
                  .slice(0, 5)
                  .map(event => (
                    <EventCard key={event.id} event={event} compact />
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>This Month</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Total Events</span>
                <Badge variant="outline">{filteredEvents.length}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Meetings</span>
                <Badge variant="outline">
                  {filteredEvents.filter(e => e.type === 'meeting').length}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Deadlines</span>
                <Badge variant="outline">
                  {filteredEvents.filter(e => e.type === 'deadline').length}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

interface EventCardProps {
  event: CalendarEvent
  compact?: boolean
}

function EventCard({ event, compact = false }: EventCardProps) {
  const getEventIcon = () => {
    switch (event.type) {
      case 'meeting': return <Users className="h-4 w-4" />
      case 'deadline': return <Target className="h-4 w-4" />
      case 'presentation': return <Building className="h-4 w-4" />
      default: return <CalendarIcon className="h-4 w-4" />
    }
  }

  const getEventColor = () => {
    switch (event.type) {
      case 'meeting': return 'blue'
      case 'deadline': return 'red'
      case 'presentation': return 'green'
      default: return 'gray'
    }
  }

  if (compact) {
    return (
      <div className="flex items-center space-x-2 p-2 rounded-lg border hover:bg-gray-50">
        <div className={`text-${getEventColor()}-600`}>
          {getEventIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{event.title}</p>
          <p className="text-xs text-muted-foreground">
            {formatRelativeTime(event.date)}
          </p>
        </div>
      </div>
    )
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              <div className={`text-${getEventColor()}-600`}>
                {getEventIcon()}
              </div>
              <h4 className="font-medium">{event.title}</h4>
            </div>
            <Badge 
              variant={event.status === 'completed' ? 'success' : 'outline'}
              className="text-xs"
            >
              {event.status}
            </Badge>
          </div>

          {event.location && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span>{event.location}</span>
            </div>
          )}

          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>{formatRelativeTime(event.date)}</span>
          </div>

          <div className="flex items-center space-x-2 pt-2">
            <Button size="sm" variant="outline" className="flex-1">
              <Eye className="h-4 w-4 mr-2" />
              View
            </Button>
            <Button size="sm" variant="outline" className="flex-1">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            {event.tender_id && (
              <Button size="sm" variant="outline">
                <Navigation className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
