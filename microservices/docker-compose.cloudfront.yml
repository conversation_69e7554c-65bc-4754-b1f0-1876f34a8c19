version: '3.8'

services:
  # API Gateway - Main entry point
  api-gateway:
    build: ./services/api-gateway
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net,https://bidbees.com
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - KAFKA_BROKERS=kafka:9092
    depends_on:
      - redis
      - kafka
    networks:
      - bidbees-network

  # Auth Service
  auth-service:
    build: ./services/auth-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
      - redis
    networks:
      - bidbees-network

  # User Service
  user-service:
    build: ./services/user-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
      - redis
    networks:
      - bidbees-network

  # Tender Service
  tender-service:
    build: ./services/tender-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
      - redis
    networks:
      - bidbees-network

  # Bidding Service
  bidding-service:
    build: ./services/bidding-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
      - KAFKA_BROKERS=kafka:9092
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
      - redis
      - kafka
    networks:
      - bidbees-network

  # Payment Service
  payment-service:
    build: ./services/payment-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - STRIPE_API_KEY=${STRIPE_API_KEY}
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
    networks:
      - bidbees-network

  # Notification Service
  notification-service:
    build: ./services/notification-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - KAFKA_BROKERS=kafka:9092
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
      - kafka
    networks:
      - bidbees-network

  # Analytics Service
  analytics-service:
    build: ./services/analytics-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
      - redis
    networks:
      - bidbees-network

  # ML Service
  ml-service:
    build: ./services/ml-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
      - redis
    networks:
      - bidbees-network

  # Document Service
  document-service:
    build: ./services/document-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - S3_BUCKET=${S3_BUCKET}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
    networks:
      - bidbees-network

  # Supplier Service
  supplier-service:
    build: ./services/supplier-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
    networks:
      - bidbees-network

  # Transport Service
  transport-service:
    build: ./services/transport-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
    networks:
      - bidbees-network

  # Courier Service
  courier-service:
    build: ./services/courier-service
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
    depends_on:
      - postgres
    networks:
      - bidbees-network

  # Infrastructure Services
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bidbees
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - bidbees-network

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - bidbees-network

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
    depends_on:
      - zookeeper
    networks:
      - bidbees-network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      - ZOOKEEPER_TICK_TIME=2000
    networks:
      - bidbees-network

  # Nginx for local testing (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway
    networks:
      - bidbees-network

networks:
  bidbees-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data: