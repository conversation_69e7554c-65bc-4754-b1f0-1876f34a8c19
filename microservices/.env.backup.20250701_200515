# Database Configuration
# Note: You need to replace 'your-password' with your actual database password
# Get it from: Supabase Dashboard > Settings > Database > Connection string
DATABASE_URL=postgresql://postgres.uvksgkpxeyyssvdsxbts:[YOUR-PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:6543/postgres

# Supabase Configuration
SUPABASE_URL=https://uvksgkpxeyyssvdsxbts.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzU4NDIsImV4cCI6MjA2MTYxMTg0Mn0.AF1fLlULlM-_NUJYFEL092WETAXvpKKpawUsOidHQ70
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAzNTg0MiwiZXhwIjoyMDYxNjExODQyfQ.i3R6lZOWC3Z60L326m20w-NrGh4Nasj9bi1qI6DipDY
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAzNTg0MiwiZXhwIjoyMDYxNjExODQyfQ.i3R6lZOWC3Z60L326m20w-NrGh4Nasj9bi1qI6DipDY
SUPABASE_DB_HOST=aws-0-eu-central-1.pooler.supabase.com
SUPABASE_DB_PASSWORD=your-password-here

# Mapbox Configuration
VITE_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoiYmlkYmVlcyIsImEiOiJjbWJiOGh0cXQwOXJvMmtzb2x1Ymc0NGF6In0.rdxv8HdMok6ZgmXe392Aaw
MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoiYmlkYmVlcyIsImEiOiJjbWJiOGh0cXQwOXJvMmtzb2x1Ymc0NGF6In0.rdxv8HdMok6ZgmXe392Aaw

# Session Configuration
SESSION_SECRET=bid-bees-super-secure-session-secret-key-2024-production-ready-32-chars

# Environment Configuration
NODE_ENV=development
USE_DATABASE=false

# Frontend Environment Variables (for Vite)
VITE_SUPABASE_URL=https://uvksgkpxeyyssvdsxbts.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzU4NDIsImV4cCI6MjA2MTYxMTg0Mn0.AF1fLlULlM-_NUJYFEL092WETAXvpKKpawUsOidHQ70

# Upstash Redis Configuration (Cloud Redis)
UPSTASH_REDIS_REST_URL=your-upstash-redis-url-here
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token-here
REDIS_URL=redis://localhost:6379

# Email Configuration (Configure with your actual SMTP details)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-gmail-app-password-here
SMTP_FROM=<EMAIL>

# Optional: File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Database Pool Configuration
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000

# Redis Configuration (Enhanced)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=bid-bees-jwt-secret-key-2024-super-secure-production-ready-32-characters
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=bid-bees-refresh-jwt-secret-key-2024-super-secure-production-ready

# API Configuration
API_BASE_URL=http://localhost:5000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,https://d58ser5n68qmv.cloudfront.net
FRONTEND_URL=https://d58ser5n68qmv.cloudfront.net

# Analytics Configuration
ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn-here
LOG_LEVEL=info
SLACK_WEBHOOK_URL=*****************************************************************************
DATADOG_API_KEY=your_datadog_api_key_here

# CDN Configuration
CDN_URL=https://your-cdn-domain.com
STATIC_ASSETS_URL=https://your-cdn-domain.com/assets

# Performance Configuration
ENABLE_COMPRESSION=true
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# AWS SQS/SNS Configuration (replaces Kafka)
AWS_ACCOUNT_ID=************
AWS_REGION=us-east-1
AWS_SQS_TENDER_EVENTS_URL=https://sqs.us-east-1.amazonaws.com/************/bidbees-tender-events
AWS_SQS_PAYMENT_EVENTS_URL=https://sqs.us-east-1.amazonaws.com/************/bidbees-payment-events
AWS_SQS_TASK_EVENTS_URL=https://sqs.us-east-1.amazonaws.com/************/bidbees-task-events
AWS_SQS_USER_EVENTS_URL=https://sqs.us-east-1.amazonaws.com/************/bidbees-user-events
AWS_SQS_NOTIFICATIONS_URL=https://sqs.us-east-1.amazonaws.com/************/bidbees-notifications
AWS_SQS_SUPPLIER_EVENTS_URL=https://sqs.us-east-1.amazonaws.com/************/bidbees-supplier-events
AWS_SNS_TENDER_NOTIFICATIONS_ARN=arn:aws:sns:us-east-1:************:bidbees-tender-notifications
AWS_SNS_SYSTEM_EVENTS_ARN=arn:aws:sns:us-east-1:************:bidbees-system-events

# Legacy Kafka Configuration (deprecated - use AWS SQS/SNS above)
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=bidbeez-client
KAFKA_GROUP_ID=bidbeez-group

# QueenBee Configuration
QUEENBEE_ANCHOR_URL=http://localhost:8000
BLOCKCHAIN_ENABLED=true

# Service Ports Configuration
PORT=3006

# =============================================================================
# MICROSERVICES CONFIGURATION
# =============================================================================

# MongoDB Configuration (for ML Service and Docling Processor)
MONGODB_URL=mongodb://localhost:27017/bidbeez

# ML Service Configuration
ML_SERVICE_PORT=5000
ML_SERVICE_API_KEY=ml-service-api-key-change-in-production

# Docling Processor Configuration
DOCLING_SERVICE_PORT=5001
ENVIRONMENT=development
MASTER_API_KEY=master-api-key-change-in-production
ENCRYPTION_KEY=encryption-key-change-in-production

# Courier Service Configuration
COURIER_SERVICE_PORT=4000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bidbeez_courier
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_SSL=false

# External API Keys
ETENDERS_API_KEY=your-etenders-api-key
WEB_APP_API_KEY=your-web-app-api-key
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
STRIPE_SECRET_KEY=sk_test_5**********abcdefghijklmnopqrstuvwxyz
STRIPE_PUBLISHABLE_KEY=pk_test_5**********abcdefghijklmnopqrstuvwxyz

# Email Service Configuration
EMAIL_SERVICE=sendgrid
EMAIL_API_KEY=your-email-api-key-here
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=BidBees Notifications

# SMS Service Configuration (Twilio)
SMS_SERVICE=twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Push Notifications (Firebase)
FCM_SERVER_KEY=your-fcm-server-key
FCM_PROJECT_ID=your-firebase-project-id

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
METRICS_PORT=9090

# File Storage Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=AKIAQ3EGUR5HXEXAMPLE
AWS_SECRET_ACCESS_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
AWS_S3_BUCKET=bidbeez-files

# Security Configuration
API_KEY_SALT=your-api-key-salt
CORS_ORIGIN=http://localhost:3000,http://localhost:8080,https://d58ser5n68qmv.cloudfront.net
VALID_API_KEYS=api-key-1,api-key-2

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# Service Discovery
SERVICE_REGISTRY_URL=http://localhost:8500
SERVICE_NAME=bidbeez-main
