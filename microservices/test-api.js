const express = require('express');
const cors = require('cors');
const app = express();
const port = 3000;

// CORS configuration for frontend
const corsOptions = {
  origin: [
    'http://localhost:3000',
    'http://localhost:5000', 
    'https://d58ser5n68qmv.cloudfront.net'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'test-api',
    environment: process.env.NODE_ENV || 'development'
  });
});

// Test endpoint for frontend connectivity
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Backend is connected!',
    timestamp: new Date().toISOString(),
    frontend_url: process.env.FRONTEND_URL || 'https://d58ser5n68qmv.cloudfront.net',
    cors_enabled: true
  });
});

// Test database connectivity
app.get('/api/test/database', async (req, res) => {
  try {
    // Test MongoDB connection
    const { MongoClient } = require('mongodb');
    const mongoUrl = process.env.MONGODB_URI || 'mongodb://localhost:27017/bidbeez';
    const client = new MongoClient(mongoUrl);
    
    await client.connect();
    await client.db().admin().ping();
    await client.close();
    
    res.json({
      database: 'connected',
      type: 'mongodb',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      database: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Test Redis connectivity
app.get('/api/test/redis', async (req, res) => {
  try {
    const redis = require('redis');
    const client = redis.createClient({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379
    });
    
    await client.connect();
    await client.ping();
    await client.disconnect();
    
    res.json({
      redis: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      redis: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Test Supabase connectivity
app.get('/api/test/supabase', async (req, res) => {
  try {
    const { createClient } = require('@supabase/supabase-js');
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase credentials not configured');
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test connection with a simple query
    const { data, error } = await supabase.from('_realtime_schema').select('*').limit(1);
    
    if (error && error.code !== 'PGRST116') { // PGRST116 is "table not found" which is expected
      throw error;
    }
    
    res.json({
      supabase: 'connected',
      url: supabaseUrl,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      supabase: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Environment info endpoint
app.get('/api/env', (req, res) => {
  res.json({
    node_env: process.env.NODE_ENV,
    frontend_url: process.env.FRONTEND_URL,
    supabase_url: process.env.SUPABASE_URL,
    mongodb_uri: process.env.MONGODB_URI ? 'configured' : 'not configured',
    redis_host: process.env.REDIS_HOST || 'localhost',
    cors_origins: corsOptions.origin,
    timestamp: new Date().toISOString()
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`Test API server running on port ${port}`);
  console.log(`Health check: http://localhost:${port}/health`);
  console.log(`Test endpoint: http://localhost:${port}/api/test`);
  console.log(`Frontend URL: ${process.env.FRONTEND_URL || 'https://d58ser5n68qmv.cloudfront.net'}`);
});
