/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZza2lsbHN5bmMtc2VydmljZSUyRmZyb250ZW5kJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFvSyIsInNvdXJjZXMiOlsid2VicGFjazovL3NraWxsc3luYy1mcm9udGVuZC8/OTlmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9icnlhbnRhdS9Eb2N1bWVudHMvR2l0SHViL2JpZF9iZWVzX2Z1bGxfcHJvamVjdC9taWNyb3NlcnZpY2VzL3NlcnZpY2VzL3NraWxsc3luYy1zZXJ2aWNlL2Zyb250ZW5kL2FwcC9kYXNoYm9hcmQvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,Bell,ChevronDown,Download,Eye,FileText,Heart,MapPin,Menu,Plus,Search,Share2,Shield,Star,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Dashboard Layout Component\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const navigation = [\n        {\n            name: \"Overview\",\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            current: true\n        },\n        {\n            name: \"Skill Providers\",\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            current: false\n        },\n        {\n            name: \"Verifications\",\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            current: false\n        },\n        {\n            name: \"Recruitment\",\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            current: false\n        },\n        {\n            name: \"Analytics\",\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            current: false\n        },\n        {\n            name: \"Contracts\",\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            current: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-transparent\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 lg:hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm\",\n                            onClick: ()=>setSidebarOpen(false)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                x: -300\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: -300\n                            },\n                            className: \"fixed left-0 top-0 h-full w-80 glass border-r border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                                navigation: navigation\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-80 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass border-r border-white/10 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                        navigation: navigation\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-80\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 glass border-b border-white/10 backdrop-blur-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(true),\n                                    className: \"lg:hidden p-2 rounded-xl glass-hover\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/40\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search providers...\",\n                                                    className: \"w-64 pl-10 pr-4 py-2 glass rounded-xl text-white placeholder-white/40 border-0 focus:ring-2 focus:ring-primary-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            className: \"relative p-2 glass rounded-xl glass-hover\",\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                notifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.span, {\n                                                    initial: {\n                                                        scale: 0\n                                                    },\n                                                    animate: {\n                                                        scale: 1\n                                                    },\n                                                    className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs text-white flex items-center justify-center\",\n                                                    children: notifications\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            className: \"flex items-center space-x-2 p-2 glass rounded-xl glass-hover\",\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white/60\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-4 sm:p-6 lg:p-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n// Sidebar Content Component\nfunction SidebarContent({ navigation }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3 p-6 border-b border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-8 h-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"absolute inset-0 bg-primary-500/20 rounded-full blur-lg\",\n                                animate: {\n                                    scale: [\n                                        1,\n                                        1.2,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: [\n                            \"Skill\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gradient\",\n                                children: \"Sync\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 16\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-6 space-y-2\",\n                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                        href: \"#\",\n                        className: `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all ${item.current ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\" : \"text-white/60 hover:text-white hover:bg-white/5\"}`,\n                        whileHover: {\n                            scale: 1.02,\n                            x: 4\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, item.name, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-t border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                    className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl text-white font-semibold button-glow\",\n                    whileHover: {\n                        scale: 1.02\n                    },\n                    whileTap: {\n                        scale: 0.98\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Add Provider\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n// Stats Card Component\nfunction StatsCard({ title, value, change, icon: Icon, color, trend }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        className: \"glass rounded-3xl p-6 card-hover\",\n        whileHover: {\n            y: -5\n        },\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-2xl bg-gradient-to-r ${color}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"w-6 h-6 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: value\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-sm flex items-center ${trend === \"up\" ? \"text-green-400\" : \"text-red-400\"}`,\n                                children: [\n                                    trend === \"up\" ? \"↗\" : \"↘\",\n                                    \" \",\n                                    change,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-white/60 text-sm font-medium\",\n                children: title\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n// Provider Card Component\nfunction ProviderCard({ provider, index }) {\n    const [liked, setLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        className: \"glass rounded-3xl overflow-hidden card-hover group\",\n        initial: {\n            opacity: 0,\n            y: 30\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            delay: index * 0.1,\n            duration: 0.5\n        },\n        whileHover: {\n            y: -8\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative p-6 bg-gradient-to-r from-primary-500/20 to-secondary-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: provider.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: provider.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        className: \"p-2 glass rounded-full\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        onClick: ()=>setLiked(!liked),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: `w-5 h-5 ${liked ? \"text-red-500 fill-current\" : \"text-white\"}`\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        className: \"p-2 glass rounded-full\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-4 right-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `px-3 py-1 text-xs font-semibold rounded-full ${provider.status === \"verified\" ? \"bg-green-500 text-white\" : provider.status === \"pending\" ? \"bg-yellow-500 text-black\" : \"bg-red-500 text-white\"}`,\n                            children: provider.status\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-white font-medium mb-2\",\n                                children: \"Skills\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: provider.skills.map((skill, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-primary-500/20 text-primary-400 text-xs rounded-full border border-primary-500/30\",\n                                        children: skill\n                                    }, idx, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-white\",\n                                        children: provider.rating\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-white/60\",\n                                        children: \"Rating\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-white\",\n                                        children: provider.projects\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-white/60\",\n                                        children: \"Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-white\",\n                                        children: [\n                                            \"R\",\n                                            provider.rate\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-white/60\",\n                                        children: \"Per Hour\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 text-white/60 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: provider.location\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-400 fill-current\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: provider.rating\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                className: \"flex-1 py-2 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl text-white font-semibold button-glow\",\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                children: \"Hire Now\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                className: \"px-4 py-2 glass rounded-xl text-white\",\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n// Main Dashboard Component\nfunction DashboardPage() {\n    const stats = [\n        {\n            title: \"Total Providers\",\n            value: \"2,543\",\n            change: 12,\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"from-blue-500 to-cyan-500\",\n            trend: \"up\"\n        },\n        {\n            title: \"Verified Skills\",\n            value: \"8,921\",\n            change: 8,\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"from-green-500 to-emerald-500\",\n            trend: \"up\"\n        },\n        {\n            title: \"Active Contracts\",\n            value: \"156\",\n            change: 23,\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"from-purple-500 to-pink-500\",\n            trend: \"up\"\n        },\n        {\n            title: \"Success Rate\",\n            value: \"98.2%\",\n            change: 5,\n            icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"from-yellow-500 to-orange-500\",\n            trend: \"up\"\n        }\n    ];\n    const featuredProviders = [\n        {\n            id: 1,\n            name: \"Sarah Johnson\",\n            title: \"Senior Electrical Engineer\",\n            skills: [\n                \"Electrical Design\",\n                \"CIDB Grade 9\",\n                \"Project Management\"\n            ],\n            rating: 4.9,\n            projects: 47,\n            rate: 850,\n            location: \"Cape Town\",\n            status: \"verified\"\n        },\n        {\n            id: 2,\n            name: \"Michael Chen\",\n            title: \"Civil Engineering Specialist\",\n            skills: [\n                \"Structural Design\",\n                \"AutoCAD\",\n                \"Site Management\"\n            ],\n            rating: 4.8,\n            projects: 32,\n            rate: 750,\n            location: \"Johannesburg\",\n            status: \"verified\"\n        },\n        {\n            id: 3,\n            name: \"Nomsa Mthembu\",\n            title: \"Quantity Surveyor\",\n            skills: [\n                \"Cost Estimation\",\n                \"SACQSP\",\n                \"Contract Management\"\n            ],\n            rating: 4.7,\n            projects: 28,\n            rate: 650,\n            location: \"Durban\",\n            status: \"pending\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white mb-2\",\n                                    children: [\n                                        \"Welcome back, \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gradient\",\n                                            children: \"Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Here's what's happening with your skill providers today\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                            className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl text-white font-semibold button-glow\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Quick Match\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                            ...stat\n                        }, stat.title, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Top Skill Providers\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                    className: \"flex items-center space-x-2 text-primary-400 hover:text-primary-300 transition-colors\",\n                                    whileHover: {\n                                        x: 4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"View All\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: featuredProviders.map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProviderCard, {\n                                    provider: provider,\n                                    index: index\n                                }, provider.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"glass rounded-3xl p-8\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-white mb-6\",\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                    label: \"Add Provider\",\n                                    color: \"from-green-500 to-emerald-500\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    label: \"Find Skills\",\n                                    color: \"from-blue-500 to-cyan-500\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                    label: \"Verify\",\n                                    color: \"from-purple-500 to-pink-500\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                                    label: \"Analytics\",\n                                    color: \"from-yellow-500 to-orange-500\"\n                                }\n                            ].map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                    className: `flex flex-col items-center space-y-2 p-6 glass rounded-2xl glass-hover`,\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -5\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.4 + index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `p-3 rounded-xl bg-gradient-to-r ${action.color}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-medium\",\n                                            children: action.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, action.label, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"glass rounded-3xl p-8\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"Recent Activity\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                    className: \"flex items-center space-x-2 px-4 py-2 glass rounded-xl glass-hover\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white\",\n                                            children: \"Export\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                {\n                                    user: \"Sarah Johnson\",\n                                    action: \"Completed verification\",\n                                    detail: \"CIDB Grade 9 Certificate\",\n                                    time: \"2 minutes ago\",\n                                    type: \"verification\"\n                                },\n                                {\n                                    user: \"Michael Chen\",\n                                    action: \"Updated profile\",\n                                    detail: \"Added new skills\",\n                                    time: \"15 minutes ago\",\n                                    type: \"profile\"\n                                },\n                                {\n                                    user: \"Nomsa Mthembu\",\n                                    action: \"Applied for contract\",\n                                    detail: \"Highway Construction Project\",\n                                    time: \"1 hour ago\",\n                                    type: \"contract\"\n                                },\n                                {\n                                    user: \"David Williams\",\n                                    action: \"Skill assessment\",\n                                    detail: \"Passed electrical safety test\",\n                                    time: \"2 hours ago\",\n                                    type: \"assessment\"\n                                }\n                            ].map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    className: \"flex items-center space-x-4 p-4 glass rounded-2xl glass-hover\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: 0.6 + index * 0.1\n                                    },\n                                    whileHover: {\n                                        x: 4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `p-2 rounded-xl ${activity.type === \"verification\" ? \"bg-green-500/20 text-green-400\" : activity.type === \"profile\" ? \"bg-blue-500/20 text-blue-400\" : activity.type === \"contract\" ? \"bg-purple-500/20 text-purple-400\" : \"bg-yellow-500/20 text-yellow-400\"}`,\n                                            children: [\n                                                activity.type === \"verification\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 56\n                                                }, this),\n                                                activity.type === \"profile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 51\n                                                }, this),\n                                                activity.type === \"contract\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 52\n                                                }, this),\n                                                activity.type === \"assessment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_Bell_ChevronDown_Download_Eye_FileText_Heart_MapPin_Menu_Plus_Search_Share2_Shield_Star_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 54\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: activity.user\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/60 text-sm\",\n                                                    children: [\n                                                        activity.action,\n                                                        \": \",\n                                                        activity.detail\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white/40 text-sm\",\n                                            children: activity.time\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n            lineNumber: 403,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 402,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"30572b2a7cf8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9za2lsbHN5bmMtZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/ZjUxZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMwNTcyYjJhN2NmOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/dashboard/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"SkillSync - AI-Powered Skill Provider Recruitment\",\n    description: \"Ultra-modern skill provider recruitment platform with AI matching, SAQA verification, and blockchain licensing\",\n    keywords: \"skills, recruitment, AI, verification, SAQA, blockchain, South Africa, tender, compliance\",\n    authors: [\n        {\n            name: \"BidBees Team\"\n        }\n    ],\n    creator: \"BidBees\",\n    publisher: \"BidBees\",\n    robots: \"index, follow\",\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#3b82f6\",\n    colorScheme: \"dark\",\n    openGraph: {\n        type: \"website\",\n        locale: \"en_ZA\",\n        url: \"https://skillsync.bidbees.com\",\n        title: \"SkillSync - AI-Powered Skill Provider Recruitment\",\n        description: \"Ultra-modern skill provider recruitment platform with AI matching, SAQA verification, and blockchain licensing\",\n        siteName: \"SkillSync\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SkillSync - AI-Powered Skill Provider Recruitment\",\n        description: \"Ultra-modern skill provider recruitment platform with AI matching, SAQA verification, and blockchain licensing\",\n        creator: \"@bidbees\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();