/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZza2lsbHN5bmMtc2VydmljZSUyRmZyb250ZW5kJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUEwSiIsInNvdXJjZXMiOlsid2VicGFjazovL3NraWxsc3luYy1mcm9udGVuZC8/MmUyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9icnlhbnRhdS9Eb2N1bWVudHMvR2l0SHViL2JpZF9iZWVzX2Z1bGxfcHJvamVjdC9taWNyb3NlcnZpY2VzL3NlcnZpY2VzL3NraWxsc3luYy1zZXJ2aWNlL2Zyb250ZW5kL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Brain,Briefcase,Clock,Play,Shield,Sparkles,Target,TrendingUp,UserCheck,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Hero Section Component\nfunction HeroSection() {\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const slides = [\n        {\n            title: \"Find Skills\",\n            subtitle: \"Instantly\",\n            description: \"AI-powered skill matching connects you with verified professionals in seconds\",\n            color: \"from-blue-500 to-cyan-500\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            title: \"Verify Talent\",\n            subtitle: \"Automatically\",\n            description: \"SAQA-integrated verification ensures authentic qualifications and compliance\",\n            color: \"from-green-500 to-emerald-500\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            title: \"Scale Teams\",\n            subtitle: \"Efficiently\",\n            description: \"Smart contracts and blockchain licensing for seamless team expansion\",\n            color: \"from-purple-500 to-pink-500\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentSlide((prev)=>(prev + 1) % slides.length);\n        }, 4000);\n        return ()=>clearInterval(timer);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 1.1\n                        },\n                        animate: {\n                            opacity: 0.3,\n                            scale: 1\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        transition: {\n                            duration: 1\n                        },\n                        className: `absolute inset-0 bg-gradient-to-br ${slides[currentSlide].color}`\n                    }, currentSlide, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"flex items-center justify-center space-x-3\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 300\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-12 h-12 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"absolute inset-0 bg-white/20 rounded-full blur-xl\",\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.2,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 2,\n                                                repeat: Infinity\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white font-display\",\n                                    children: [\n                                        \"Skill\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gradient\",\n                                            children: \"Sync\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 20\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-40 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30,\n                                        rotateX: -90\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0,\n                                        rotateX: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        y: -30,\n                                        rotateX: 90\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(slides[currentSlide].icon, {\n                                                    className: \"w-16 h-16 text-white mr-4\"\n                                                }),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-6xl md:text-8xl font-black text-white mb-2\",\n                                                        children: [\n                                                            slides[currentSlide].title,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"block text-gradient text-glow\",\n                                                                children: slides[currentSlide].subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl md:text-2xl text-white/80 max-w-3xl mx-auto\",\n                                            children: slides[currentSlide].description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, currentSlide, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.4,\n                                duration: 0.6\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                        className: \"group relative px-8 py-4 bg-white text-black font-semibold rounded-2xl overflow-hidden button-glow\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Enter Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    className: \"group flex items-center space-x-2 px-8 py-4 glass rounded-2xl text-white font-semibold glass-hover\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Watch Demo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.6,\n                                duration: 0.6\n                            },\n                            children: [\n                                {\n                                    label: \"Verified Professionals\",\n                                    value: \"25K+\",\n                                    icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                },\n                                {\n                                    label: \"Skills Matched\",\n                                    value: \"500K+\",\n                                    icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                },\n                                {\n                                    label: \"Success Rate\",\n                                    value: \"98%\",\n                                    icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                },\n                                {\n                                    label: \"Avg Response Time\",\n                                    value: \"2min\",\n                                    icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                }\n                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"glass rounded-2xl p-6 text-center\",\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -5\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"w-8 h-8 text-white mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-white mb-1\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, stat.label, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2\",\n                children: slides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setCurrentSlide(index),\n                        \"aria-label\": `Go to slide ${index + 1}`,\n                        className: `w-3 h-3 rounded-full transition-all duration-300 ${index === currentSlide ? \"bg-white scale-125\" : \"bg-white/30 hover:bg-white/50\"}`\n                    }, index, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n// Features Section\nfunction FeaturesSection() {\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"AI-Powered Matching\",\n            description: \"Advanced NLP algorithms match skills to requirements with 98% accuracy\",\n            color: \"text-blue-400\",\n            stats: \"500K+ matches\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"SAQA Verification\",\n            description: \"Automated qualification verification through official South African channels\",\n            color: \"text-green-400\",\n            stats: \"25K+ verified\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"Real-time Recruitment\",\n            description: \"Instant skill provider sourcing from multiple platforms and databases\",\n            color: \"text-yellow-400\",\n            stats: \"2min avg response\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            title: \"B-BBEE Compliance\",\n            description: \"Automated compliance scoring and regulatory requirement matching\",\n            color: \"text-purple-400\",\n            stats: \"100% compliant\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            title: \"Smart Contracts\",\n            description: \"Blockchain-powered licensing with transparent terms and payments\",\n            color: \"text-cyan-400\",\n            stats: \"10K+ contracts\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Analytics Dashboard\",\n            description: \"Real-time insights into recruitment metrics and performance data\",\n            color: \"text-pink-400\",\n            stats: \"Live tracking\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl font-bold text-white mb-4\",\n                            children: [\n                                \"Why Choose \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient\",\n                                    children: \"SkillSync\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 24\n                                }, this),\n                                \"?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/60 max-w-3xl mx-auto\",\n                            children: \"Experience the future of skill recruitment with cutting-edge AI, blockchain technology, and regulatory compliance\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"glass rounded-3xl p-8 text-center card-hover group\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1,\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            whileHover: {\n                                y: -10\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `inline-flex p-4 rounded-2xl bg-black/20 mb-6 ${feature.color} group-hover:scale-110 transition-transform`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"w-8 h-8\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-4\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gradient font-semibold\",\n                                    children: feature.stats\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, feature.title, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, this);\n}\n// Process Section\nfunction ProcessSection() {\n    const steps = [\n        {\n            number: \"01\",\n            title: \"Skill Requirements\",\n            description: \"AI analyzes tender documents and extracts skill requirements automatically\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            number: \"02\",\n            title: \"Provider Sourcing\",\n            description: \"Multi-platform sourcing from LinkedIn, Indeed, and internal databases\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"from-green-500 to-emerald-500\"\n        },\n        {\n            number: \"03\",\n            title: \"Verification Process\",\n            description: \"SAQA API integration for automatic qualification verification\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"from-purple-500 to-pink-500\"\n        },\n        {\n            number: \"04\",\n            title: \"Smart Licensing\",\n            description: \"Blockchain contracts deployed for transparent skill provider licensing\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            color: \"from-yellow-500 to-orange-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl font-bold text-white mb-4\",\n                            children: [\n                                \"How \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient\",\n                                    children: \"SkillSync\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, this),\n                                \" Works\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/60 max-w-2xl mx-auto\",\n                            children: \"Our automated recruitment process ensures quality, compliance, and efficiency\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"relative\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.2,\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass rounded-3xl p-8 text-center card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-16 h-16 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center mx-auto mb-6`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl font-bold text-gradient mb-4\",\n                                            children: step.number\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, this),\n                                index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-white/30 to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, step.number, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n            lineNumber: 335,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n}\n// Main Page Component\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesSection, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProcessSection, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                className: \"fixed bottom-8 right-8 z-50\",\n                initial: {\n                    scale: 0,\n                    rotate: -180\n                },\n                animate: {\n                    scale: 1,\n                    rotate: 0\n                },\n                transition: {\n                    delay: 1,\n                    type: \"spring\",\n                    stiffness: 200\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: \"/dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                        className: \"w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-white shadow-2xl neon-glow\",\n                        whileHover: {\n                            scale: 1.1,\n                            rotate: 360\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 300\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Brain_Briefcase_Clock_Play_Shield_Sparkles_Target_TrendingUp_UserCheck_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"w-8 h-8\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx\",\n        lineNumber: 385,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"30572b2a7cf8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9za2lsbHN5bmMtZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/ZjUxZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMwNTcyYjJhN2NmOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"SkillSync - AI-Powered Skill Provider Recruitment\",\n    description: \"Ultra-modern skill provider recruitment platform with AI matching, SAQA verification, and blockchain licensing\",\n    keywords: \"skills, recruitment, AI, verification, SAQA, blockchain, South Africa, tender, compliance\",\n    authors: [\n        {\n            name: \"BidBees Team\"\n        }\n    ],\n    creator: \"BidBees\",\n    publisher: \"BidBees\",\n    robots: \"index, follow\",\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#3b82f6\",\n    colorScheme: \"dark\",\n    openGraph: {\n        type: \"website\",\n        locale: \"en_ZA\",\n        url: \"https://skillsync.bidbees.com\",\n        title: \"SkillSync - AI-Powered Skill Provider Recruitment\",\n        description: \"Ultra-modern skill provider recruitment platform with AI matching, SAQA verification, and blockchain licensing\",\n        siteName: \"SkillSync\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SkillSync - AI-Powered Skill Provider Recruitment\",\n        description: \"Ultra-modern skill provider recruitment platform with AI matching, SAQA verification, and blockchain licensing\",\n        creator: \"@bidbees\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/layout.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/skillsync-service/frontend/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Fskillsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();