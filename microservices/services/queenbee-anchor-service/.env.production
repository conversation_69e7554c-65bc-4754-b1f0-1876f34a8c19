# QueenBee Anchor Service Environment Configuration

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration (Supabase)
SUPABASE_DB_HOST=your-supabase-host
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres
SUPABASE_DB_PASSWORD=your-supabase-password
SUPABASE_DB_PORT=5432

# Supabase API Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=queenbee-anchor-service
KAFKA_GROUP_ID=queenbee-anchor-group

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080

# QueenBee Anchor Configuration
ANCHOR_BATCH_SIZE=100
BLOCKCHAIN_NETWORK=testnet

# Blockchain Credentials
POLYGON_RPC_URL=https://polygon-rpc.com
POLYGON_SIGNER_KEY=your-polygon-private-key

HEDERA_ACCOUNT_ID=0.0.123456
HEDERA_PRIVATE_KEY=your-hedera-private-key

TSA_ENDPOINT_URL=https://freetsa.org/tsr
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
