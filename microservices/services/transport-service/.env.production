# Server Configuration
PORT=3008
NODE_ENV=production
SERVICE_VERSION=1.0.0

# Database Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=transport-service
KAFKA_GROUP_ID=transport-service-group

# External API Keys
MAPBOX_ACCESS_TOKEN=your-mapbox-token
GOOGLE_MAPS_API_KEY=your-google-maps-key

# Transport Provider API Keys
UBER_API_KEY=your-uber-api-key
BOLT_API_KEY=your-bolt-api-key
INDRIVE_API_KEY=your-indrive-api-key

# Security
JWT_SECRET=your-jwt-secret-key-minimum-32-characters
API_KEY_SECRET=your-api-key-secret-minimum-32-characters

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# Features
ENABLE_REAL_TIME_TRACKING=true
ENABLE_ROUTE_OPTIMIZATION=true
ENABLE_PROVIDER_INTEGRATION=true
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net,https://bidbees.com
