# Bidbeez Courier Service - Environment Configuration

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=production
PORT=3007
SERVICE_VERSION=1.0.0
API_BASE_URL=http://localhost:3007

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bidbeez_courier
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_SSL=false
DB_SSL_REJECT_UNAUTHORIZED=true

# Connection Pool Settings
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000
DB_STATEMENT_TIMEOUT=30000
DB_QUERY_TIMEOUT=30000

# Test Database (for testing environment)
TEST_DB_NAME=bidbeez_courier_test

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
REDIS_RETRY_DELAY=100
REDIS_MAX_RETRIES=3
REDIS_KEEP_ALIVE=30000
REDIS_KEY_PREFIX=courier:
REDIS_FAMILY=4

# Test Redis (for testing environment)
TEST_REDIS_DB=1

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=your_jwt_secret_here_make_it_very_long_and_secure
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# API Keys for external integrations
API_KEY_SALT=your_api_key_salt_here

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Mapbox (for route optimization and geocoding)
MAPBOX_ACCESS_TOKEN=pk.your_mapbox_token_here

# Google Maps (fallback for route optimization)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================
# Email Service (SendGrid, AWS SES, etc.)
EMAIL_SERVICE=sendgrid
EMAIL_API_KEY=your_email_api_key_here
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Bidbeez Courier Service

# SMS Service (Twilio, AWS SNS, etc.)
SMS_SERVICE=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=+**********

# Push Notifications (Firebase Cloud Messaging)
FCM_SERVER_KEY=your_fcm_server_key_here
FCM_PROJECT_ID=your_firebase_project_id_here

# =============================================================================
# MESSAGING & EVENTS
# =============================================================================
# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=courier-service
KAFKA_GROUP_ID=courier-service-group
KAFKA_USERNAME=
KAFKA_PASSWORD=
KAFKA_SSL=false

# Kafka Topics
KAFKA_TOPIC_COURIER_EVENTS=courier.events
KAFKA_TOPIC_NOTIFICATIONS=notifications
KAFKA_TOPIC_TRACKING_UPDATES=tracking.updates

# =============================================================================
# CORS & SECURITY
# =============================================================================
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://app.bidbeez.com
MAX_REQUEST_SIZE=10mb

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# LOGGING
# =============================================================================
LOG_LEVEL=debug
LOG_FORMAT=json

# File Logging (production)
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs
LOG_FILE_MAX_SIZE=20m
LOG_FILE_MAX_FILES=14d

# =============================================================================
# MONITORING & HEALTH CHECKS
# =============================================================================
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
METRICS_PORT=9090

# Sentry (Error Monitoring)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=development

# =============================================================================
# FILE STORAGE
# =============================================================================
# AWS Configuration
AWS_REGION=us-east-1
AWS_DEFAULT_REGION=us-east-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=ix6wzw70jWwjP2d+286+dtJgLg+AnlFmsgcxKVJo
S3_BUCKET=bidbees-enterprise-tms-**********

# =============================================================================
# PAYMENT PROCESSING
# =============================================================================
# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================
# Development Settings
DEV_MOCK_EXTERNAL_APIS=false
DEV_SKIP_AUTH=false
DEV_SEED_DATA=false

# Testing Settings
TEST_TIMEOUT=30000
TEST_PARALLEL=false

# =============================================================================
# PERFORMANCE & OPTIMIZATION
# =============================================================================
# Caching
CACHE_TTL_DEFAULT=300
CACHE_TTL_ROUTES=3600
CACHE_TTL_GEOCODING=86400

# Request Timeouts
HTTP_TIMEOUT=30000
EXTERNAL_API_TIMEOUT=10000

# =============================================================================
# BUSINESS LOGIC CONFIGURATION
# =============================================================================
# Bee Assignment
BEE_ASSIGNMENT_MAX_DISTANCE_KM=20
BEE_ASSIGNMENT_TIMEOUT_MINUTES=30
BEE_ASSIGNMENT_RETRY_ATTEMPTS=3

# Delivery Timeouts
DELIVERY_SLA_BUFFER_HOURS=2
DELIVERY_TRACKING_STALE_HOURS=4

# Pricing
DEFAULT_CURRENCY=ZAR
FUEL_SURCHARGE_RATE=0.05
INSURANCE_RATE=0.01

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_AI_BEE_ASSIGNMENT=true
FEATURE_REAL_TIME_TRACKING=true
FEATURE_WEBHOOK_NOTIFICATIONS=true
FEATURE_BATCH_OPERATIONS=true
FEATURE_EXTERNAL_COURIER_INTEGRATION=true
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net,https://bidbees.com
