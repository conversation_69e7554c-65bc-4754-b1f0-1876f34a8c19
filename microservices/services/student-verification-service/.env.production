# BidBeez Student Verification Service Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3006
SERVICE_NAME=student-verification-service

# =============================================================================
# DATABASE CONFIGURATION (SUPABASE)
# =============================================================================
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
DATABASE_URL=postgresql://postgres:password@localhost:5432/bidbeez

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# KAFKA CONFIGURATION
# =============================================================================
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=student-verification-service
KAFKA_GROUP_ID=student-verification-group

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-at-least-32-characters-long
JWT_REFRESH_EXPIRES_IN=7d

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
STORAGE_PROVIDER=supabase
STORAGE_BUCKET=student-verification-documents

# AWS S3 Configuration (if using S3)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=ix6wzw70jWwjP2d+286+dtJgLg+AnlFmsgcxKVJo
AWS_REGION=us-east-1
AWS_S3_BUCKET=bidbees-enterprise-tms-**********

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
NOTIFICATION_SERVICE_URL=http://localhost:3007
QUEENBEE_SERVICE_URL=http://localhost:3008
DOCUMENT_PROCESSOR_URL=http://localhost:3009

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ENCRYPTION_KEY=your-encryption-key-at-least-32-characters-long
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
LOG_LEVEL=info
SENTRY_DSN=https://<EMAIL>/project-id
PROMETHEUS_PORT=9090

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_OCR=true
ENABLE_AI_ANALYSIS=true
ENABLE_FRAUD_DETECTION=true
ENABLE_AUTO_APPROVAL=false

# =============================================================================
# BUSINESS CONFIGURATION
# =============================================================================
MAX_FILE_SIZE_MB=10
VERIFICATION_EXPIRY_DAYS=365
AUTO_APPROVE_THRESHOLD=0.95
MANUAL_REVIEW_THRESHOLD=0.7
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
