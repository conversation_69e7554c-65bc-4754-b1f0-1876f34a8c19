# Environment
NODE_ENV=production
PORT=3000

# Database
DATABASE_URL=postgresql://bidbees_user:@postgres:5432/bidbees
REDIS_URL=redis://:@redis:6379

# JWT Configuration
JWT_SECRET=
JWT_REFRESH_SECRET=
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
PASSWORD_RESET_EXPIRES=3600000
SESSION_SECRET=your-super-secret-session-key-here-32-chars-minimum-length
SESSION_EXPIRES=86400000

# Kafka
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=auth-service

# Service Discovery
SERVICE_NAME=auth-service
SERVICE_VERSION=1.0.0

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# CORS
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net,https://bidbees.com

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net,https://bidbees.com
