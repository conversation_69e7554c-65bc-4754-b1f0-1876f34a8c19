/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZ0b29sc3luYy1zZXJ2aWNlJTJGZnJvbnRlbmQlMkZhcHAlMkZkYXNoYm9hcmQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQW1LIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdG9vbHN5bmMtZnJvbnRlbmQvP2NmNDAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYnJ5YW50YXUvRG9jdW1lbnRzL0dpdEh1Yi9iaWRfYmVlc19mdWxsX3Byb2plY3QvbWljcm9zZXJ2aWNlcy9zZXJ2aWNlcy90b29sc3luYy1zZXJ2aWNlL2Zyb250ZW5kL2FwcC9kYXNoYm9hcmQvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Calendar,ChevronDown,Clock,DollarSign,Eye,Heart,MapPin,Menu,Plus,Search,Share2,Star,User,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Dashboard Layout Component\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    const navigation = [\n        {\n            name: \"Overview\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            current: true\n        },\n        {\n            name: \"Browse Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            current: false\n        },\n        {\n            name: \"My Rentals\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            current: false\n        },\n        {\n            name: \"My Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            current: false\n        },\n        {\n            name: \"Earnings\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            current: false\n        },\n        {\n            name: \"Messages\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            current: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-transparent\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                children: sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 lg:hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm\",\n                            onClick: ()=>setSidebarOpen(false)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                x: -300\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: -300\n                            },\n                            className: \"fixed left-0 top-0 h-full w-80 glass border-r border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                                navigation: navigation\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-80 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass border-r border-white/10 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                        navigation: navigation\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-80\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 glass border-b border-white/10 backdrop-blur-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(true),\n                                    className: \"lg:hidden p-2 rounded-xl glass-hover\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/40\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search tools...\",\n                                                    className: \"w-64 pl-10 pr-4 py-2 glass rounded-xl text-white placeholder-white/40 border-0 focus:ring-2 focus:ring-primary-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                            className: \"relative p-2 glass rounded-xl glass-hover\",\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this),\n                                                notifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.span, {\n                                                    initial: {\n                                                        scale: 0\n                                                    },\n                                                    animate: {\n                                                        scale: 1\n                                                    },\n                                                    className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs text-white flex items-center justify-center\",\n                                                    children: notifications\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                            className: \"flex items-center space-x-2 p-2 glass rounded-xl glass-hover\",\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white/60\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-4 sm:p-6 lg:p-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n// Sidebar Content Component\nfunction SidebarContent({ navigation }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3 p-6 border-b border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-8 h-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"absolute inset-0 bg-primary-500/20 rounded-full blur-lg\",\n                                animate: {\n                                    scale: [\n                                        1,\n                                        1.2,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: [\n                            \"Tool\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gradient\",\n                                children: \"Sync\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-6 space-y-2\",\n                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.a, {\n                        href: \"#\",\n                        className: `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all ${item.current ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\" : \"text-white/60 hover:text-white hover:bg-white/5\"}`,\n                        whileHover: {\n                            scale: 1.02,\n                            x: 4\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, item.name, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-t border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                    className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl text-white font-semibold button-glow\",\n                    whileHover: {\n                        scale: 1.02\n                    },\n                    whileTap: {\n                        scale: 0.98\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"List a Tool\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n// Stats Card Component\nfunction StatsCard({ title, value, change, icon: Icon, color }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        className: \"glass rounded-3xl p-6 card-hover\",\n        whileHover: {\n            y: -5\n        },\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-2xl bg-gradient-to-r ${color}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"w-6 h-6 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: value\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-green-400\",\n                                children: [\n                                    \"+\",\n                                    change,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-white/60 text-sm font-medium\",\n                children: title\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n// Tool Card Component\nfunction ToolCard({ tool, index }) {\n    const [liked, setLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        className: \"glass rounded-3xl overflow-hidden card-hover group\",\n        initial: {\n            opacity: 0,\n            y: 30\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            delay: index * 0.1,\n            duration: 0.5\n        },\n        whileHover: {\n            y: -8\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-48 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: tool.image,\n                        alt: tool.name,\n                        fill: true,\n                        className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-4 right-4 flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                className: \"p-2 glass rounded-full\",\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                },\n                                onClick: ()=>setLiked(!liked),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: `w-5 h-5 ${liked ? \"text-red-500 fill-current\" : \"text-white\"}`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                className: \"p-2 glass rounded-full\",\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-5 h-5 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-4 left-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-3 py-1 bg-green-500 text-white text-xs font-semibold rounded-full\",\n                            children: \"Available\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: tool.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-400 fill-current\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: tool.rating\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60 text-sm mb-4\",\n                        children: tool.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 text-white/60 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tool.location\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-primary-400 font-semibold\",\n                                children: [\n                                    \"R\",\n                                    tool.price,\n                                    \"/day\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                        className: \"w-full py-3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl text-white font-semibold button-glow\",\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        children: \"Rent Now\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n// Main Dashboard Component\nfunction DashboardPage() {\n    const stats = [\n        {\n            title: \"Total Earnings\",\n            value: \"R12,450\",\n            change: 12,\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"from-green-500 to-emerald-500\"\n        },\n        {\n            title: \"Active Rentals\",\n            value: \"8\",\n            change: 25,\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            color: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            title: \"Tools Listed\",\n            value: \"15\",\n            change: 8,\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"from-purple-500 to-pink-500\"\n        },\n        {\n            title: \"Rating\",\n            value: \"4.9\",\n            change: 5,\n            icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            color: \"from-yellow-500 to-orange-500\"\n        }\n    ];\n    const featuredTools = [\n        {\n            id: 1,\n            name: \"DeWalt Circular Saw\",\n            description: \"Professional grade circular saw perfect for construction projects\",\n            price: 150,\n            rating: 4.8,\n            location: \"Cape Town\",\n            image: \"https://images.unsplash.com/photo-1504148455328-c376907d081c?w=400&h=300&fit=crop\"\n        },\n        {\n            id: 2,\n            name: \"Bosch Hammer Drill\",\n            description: \"Heavy-duty hammer drill for concrete and masonry work\",\n            price: 120,\n            rating: 4.9,\n            location: \"Johannesburg\",\n            image: \"https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=400&h=300&fit=crop\"\n        },\n        {\n            id: 3,\n            name: \"Makita Angle Grinder\",\n            description: \"Versatile angle grinder for cutting and grinding applications\",\n            price: 80,\n            rating: 4.7,\n            location: \"Durban\",\n            image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?w=400&h=300&fit=crop\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white mb-2\",\n                                    children: [\n                                        \"Welcome back, \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gradient\",\n                                            children: \"John\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Here's what's happening with your tools today\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                            className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl text-white font-semibold button-glow\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Quick Rent\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                            ...stat\n                        }, stat.title, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Featured Tools\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                    className: \"flex items-center space-x-2 text-primary-400 hover:text-primary-300 transition-colors\",\n                                    whileHover: {\n                                        x: 4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"View All\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: featuredTools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolCard, {\n                                    tool: tool,\n                                    index: index\n                                }, tool.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"glass rounded-3xl p-8\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-white mb-6\",\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                    label: \"List Tool\",\n                                    color: \"from-green-500 to-emerald-500\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                    label: \"Find Tools\",\n                                    color: \"from-blue-500 to-cyan-500\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    label: \"Schedule\",\n                                    color: \"from-purple-500 to-pink-500\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_BarChart3_Bell_Calendar_ChevronDown_Clock_DollarSign_Eye_Heart_MapPin_Menu_Plus_Search_Share2_Star_User_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                    label: \"Analytics\",\n                                    color: \"from-yellow-500 to-orange-500\"\n                                }\n                            ].map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                    className: `flex flex-col items-center space-y-2 p-6 glass rounded-2xl glass-hover`,\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -5\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.4 + index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `p-3 rounded-xl bg-gradient-to-r ${action.color}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-medium\",\n                                            children: action.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, action.label, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n            lineNumber: 343,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx\",\n        lineNumber: 342,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../../../node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers,useAuth,useWebSocket,useTheme auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst WebSocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Auth Provider\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for existing session\n        const checkAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"toolsync_token\");\n                if (token) {\n                    // Validate token and get user data\n                    const response = await fetch(\"/api/v1/auth/me\", {\n                        headers: {\n                            Authorization: `Bearer ${token}`\n                        }\n                    });\n                    if (response.ok) {\n                        const userData = await response.json();\n                        setUser(userData);\n                    } else {\n                        localStorage.removeItem(\"toolsync_token\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/v1/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (response.ok) {\n                const { token, user: userData } = await response.json();\n                localStorage.setItem(\"toolsync_token\", token);\n                setUser(userData);\n            } else {\n                throw new Error(\"Login failed\");\n            }\n        } catch (error) {\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"toolsync_token\");\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            login,\n            logout,\n            loading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n// WebSocket Provider\nfunction WebSocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const wsUrl = \"ws://localhost:6001\" || 0;\n        const ws = new WebSocket(`${wsUrl}/ws/notifications/`);\n        ws.onopen = ()=>{\n            setIsConnected(true);\n            console.log(\"WebSocket connected\");\n        };\n        ws.onclose = ()=>{\n            setIsConnected(false);\n            console.log(\"WebSocket disconnected\");\n        };\n        ws.onerror = (error)=>{\n            console.error(\"WebSocket error:\", error);\n        };\n        setSocket(ws);\n        return ()=>{\n            ws.close();\n        };\n    }, []);\n    const sendMessage = (message)=>{\n        if (socket && isConnected) {\n            socket.send(JSON.stringify(message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebSocketContext.Provider, {\n        value: {\n            socket,\n            isConnected,\n            sendMessage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n// Main Providers Component\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"dark\",\n        enableSystem: true,\n        disableTransitionOnChange: false,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebSocketProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n// Custom hooks\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction useWebSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WebSocketContext);\n    if (context === undefined) {\n        throw new Error(\"useWebSocket must be used within a WebSocketProvider\");\n    }\n    return context;\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"143a7d582437\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90b29sc3luYy1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz82Y2ZiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTQzYTdkNTgyNDM3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains-mono\",\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n    description: \"Experience the future of tool rental with AI-powered recommendations, real-time availability, and seamless P2P sharing.\",\n    keywords: [\n        \"tool rental\",\n        \"equipment sharing\",\n        \"P2P rental\",\n        \"construction tools\",\n        \"AI-powered\"\n    ],\n    authors: [\n        {\n            name: \"ToolSync Team\"\n        }\n    ],\n    creator: \"ToolSync\",\n    publisher: \"ToolSync\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://toolsync.com\"),\n    openGraph: {\n        title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n        description: \"Experience the future of tool rental with AI-powered recommendations and seamless sharing.\",\n        url: \"https://toolsync.com\",\n        siteName: \"ToolSync\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"ToolSync Platform\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n        description: \"Experience the future of tool rental with AI-powered recommendations.\",\n        images: [\n            \"/twitter-image.jpg\"\n        ],\n        creator: \"@toolsync\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"ToolSync\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-config\",\n                        content: \"/browserconfig.xml\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `\n          ${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} \n          ${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default().variable)} \n          ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} \n          font-sans \n          antialiased \n          bg-gradient-to-br \n          from-slate-900 \n          via-purple-900 \n          to-slate-900 \n          min-h-screen\n          overflow-x-hidden\n        `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-mesh opacity-20 animate-gradient\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/20 rounded-full blur-3xl animate-float\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-3/4 right-1/4 w-96 h-96 bg-secondary-500/20 rounded-full blur-3xl animate-float\",\n                                    style: {\n                                        animationDelay: \"1s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/2 left-1/2 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-float\",\n                                    style: {\n                                        animationDelay: \"2s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-10\",\n                                    style: {\n                                        backgroundImage: `\n                  linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n                  linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n                `,\n                                        backgroundSize: \"50px 50px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"rgba(0, 0, 0, 0.8)\",\n                                    color: \"#fff\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                    borderRadius: \"12px\",\n                                    backdropFilter: \"blur(20px)\"\n                                },\n                                success: {\n                                    iconTheme: {\n                                        primary: \"#10b981\",\n                                        secondary: \"#fff\"\n                                    }\n                                },\n                                error: {\n                                    iconTheme: {\n                                        primary: \"#ef4444\",\n                                        secondary: \"#fff\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useTheme: () => (/* binding */ e3),
/* harmony export */   useWebSocket: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#Providers`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useWebSocket`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/framer-motion"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();