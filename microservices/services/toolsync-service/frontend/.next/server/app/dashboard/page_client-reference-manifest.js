globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"80":{"*":{"id":"1868","name":"*","chunks":[],"async":false}},"1060":{"*":{"id":"9727","name":"*","chunks":[],"async":false}},"1720":{"*":{"id":"5472","name":"*","chunks":[],"async":false}},"2846":{"*":{"id":"2994","name":"*","chunks":[],"async":false}},"4707":{"*":{"id":"9671","name":"*","chunks":[],"async":false}},"6423":{"*":{"id":"4759","name":"*","chunks":[],"async":false}},"6973":{"*":{"id":"8143","name":"*","chunks":[],"async":false}},"7340":{"*":{"id":"6990","name":"*","chunks":[],"async":false}},"9064":{"*":{"id":"381","name":"*","chunks":[],"async":false}},"9107":{"*":{"id":"6114","name":"*","chunks":[],"async":false}},"9581":{"*":{"id":"7398","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/client/components/app-router.js":{"id":2846,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/esm/client/components/app-router.js":{"id":2846,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/client/components/client-page.js":{"id":9107,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":9107,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":1060,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":1060,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":4707,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":4707,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/client/components/not-found-boundary.js":{"id":80,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":80,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":6423,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":6423,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx":{"id":6973,"name":"*","chunks":["185","static/chunks/app/layout-109cb9989d2175d2.js"],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":8925,"name":"*","chunks":["185","static/chunks/app/layout-109cb9989d2175d2.js"],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"}":{"id":3804,"name":"*","chunks":["185","static/chunks/app/layout-109cb9989d2175d2.js"],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains-mono\",\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"}":{"id":5996,"name":"*","chunks":["185","static/chunks/app/layout-109cb9989d2175d2.js"],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/globals.css":{"id":7960,"name":"*","chunks":["185","static/chunks/app/layout-109cb9989d2175d2.js"],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/node_modules/react-hot-toast/dist/index.mjs":{"id":9064,"name":"*","chunks":["185","static/chunks/app/layout-109cb9989d2175d2.js"],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page.tsx":{"id":1720,"name":"*","chunks":["802","static/chunks/802-bd0a257e3239eeb4.js","33","static/chunks/33-1ba89d1b4db60706.js","702","static/chunks/app/dashboard/page-6e047519a3fbd435.js"],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx":{"id":9581,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx":{"id":7340,"name":"*","chunks":["802","static/chunks/802-bd0a257e3239eeb4.js","462","static/chunks/462-280b50b74c8c22c4.js","931","static/chunks/app/page-bd208b250e51d0ca.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/":[],"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout":["static/css/abaad5d6236f8431.css","static/css/78dbe58b3d68c943.css"],"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page":[],"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/dashboard/page":[]}}