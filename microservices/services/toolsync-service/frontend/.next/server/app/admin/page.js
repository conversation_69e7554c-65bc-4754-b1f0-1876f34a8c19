/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(rsc)/./app/admin/page.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(ssr)/./app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZ0b29sc3luYy1zZXJ2aWNlJTJGZnJvbnRlbmQlMkZhcHAlMkZhZG1pbiUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBK0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90b29sc3luYy1mcm9udGVuZC8/YTIyYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9icnlhbnRhdS9Eb2N1bWVudHMvR2l0SHViL2JpZF9iZWVzX2Z1bGxfcHJvamVjdC9taWNyb3NlcnZpY2VzL3NlcnZpY2VzL3Rvb2xzeW5jLXNlcnZpY2UvZnJvbnRlbmQvYXBwL2FkbWluL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZ0b29sc3luYy1zZXJ2aWNlJTJGZnJvbnRlbmQlMkZhcHAlMkZwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZ0b29sc3luYy1zZXJ2aWNlJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlMkMlNUMlMjJkaXNwbGF5JTVDJTIyJTNBJTVDJTIyc3dhcCU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZ0b29sc3luYy1zZXJ2aWNlJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMlBvcHBpbnMlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ3ZWlnaHQlNUMlMjIlM0ElNUIlNUMlMjIzMDAlNUMlMjIlMkMlNUMlMjI0MDAlNUMlMjIlMkMlNUMlMjI1MDAlNUMlMjIlMkMlNUMlMjI2MDAlNUMlMjIlMkMlNUMlMjI3MDAlNUMlMjIlMkMlNUMlMjI4MDAlNUMlMjIlMkMlNUMlMjI5MDAlNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1wb3BwaW5zJTVDJTIyJTJDJTVDJTIyZGlzcGxheSU1QyUyMiUzQSU1QyUyMnN3YXAlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJwb3BwaW5zJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZ0b29sc3luYy1zZXJ2aWNlJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkpldEJyYWluc19Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtamV0YnJhaW5zLW1vbm8lNUMlMjIlMkMlNUMlMjJkaXNwbGF5JTVDJTIyJTNBJTVDJTIyc3dhcCU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmpldGJyYWluc01vbm8lNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZicnlhbnRhdSUyRkRvY3VtZW50cyUyRkdpdEh1YiUyRmJpZF9iZWVzX2Z1bGxfcHJvamVjdCUyRm1pY3Jvc2VydmljZXMlMkZzZXJ2aWNlcyUyRnRvb2xzeW5jLXNlcnZpY2UlMkZmcm9udGVuZCUyRmFwcCUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZ0b29sc3luYy1zZXJ2aWNlJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZyZWFjdC1ob3QtdG9hc3QlMkZkaXN0JTJGaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQTZMO0FBQzdMO0FBQ0Esc01BQXFOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdG9vbHN5bmMtZnJvbnRlbmQvPzMyYmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlcnNcIl0gKi8gXCIvVXNlcnMvYnJ5YW50YXUvRG9jdW1lbnRzL0dpdEh1Yi9iaWRfYmVlc19mdWxsX3Byb2plY3QvbWljcm9zZXJ2aWNlcy9zZXJ2aWNlcy90b29sc3luYy1zZXJ2aWNlL2Zyb250ZW5kL2FwcC9wcm92aWRlcnMudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUb2FzdGVyXCJdICovIFwiL1VzZXJzL2JyeWFudGF1L0RvY3VtZW50cy9HaXRIdWIvYmlkX2JlZXNfZnVsbF9wcm9qZWN0L21pY3Jvc2VydmljZXMvc2VydmljZXMvdG9vbHN5bmMtc2VydmljZS9mcm9udGVuZC9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Calendar,DollarSign,Settings,Shield,Star,TrendingUp,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AdminPage() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const tabs = [\n        {\n            id: \"overview\",\n            name: \"Overview\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            id: \"users\",\n            name: \"Users\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            id: \"tools\",\n            name: \"Tools\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: \"analytics\",\n            name: \"Analytics\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: \"settings\",\n            name: \"Settings\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/10 backdrop-blur-sm border-b border-white/20 sticky top-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-8 h-8 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: [\n                                                \"ToolSync \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400\",\n                                                    children: \"Admin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 28\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"p-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"p-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-1 mb-8\",\n                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: `flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all ${activeTab === tab.id ? \"bg-blue-500 text-white shadow-lg\" : \"text-white/60 hover:text-white hover:bg-white/5\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverviewTab, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 40\n                            }, this),\n                            activeTab === \"users\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UsersTab, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 37\n                            }, this),\n                            activeTab === \"tools\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolsTab, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 37\n                            }, this),\n                            activeTab === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsTab, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 41\n                            }, this),\n                            activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsTab, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 40\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n// Overview Tab Component\nfunction OverviewTab() {\n    const stats = [\n        {\n            title: \"Total Users\",\n            value: \"12,543\",\n            change: \"+12%\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            title: \"Active Tools\",\n            value: \"8,921\",\n            change: \"+8%\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"from-green-500 to-emerald-500\"\n        },\n        {\n            title: \"Total Revenue\",\n            value: \"R2.4M\",\n            change: \"+23%\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"from-purple-500 to-pink-500\"\n        },\n        {\n            title: \"Active Rentals\",\n            value: \"1,234\",\n            change: \"+15%\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"from-yellow-500 to-orange-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6 hover:scale-105 transition-transform\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `p-3 rounded-2xl bg-gradient-to-r ${stat.color}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-400\",\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white/60 text-sm font-medium\",\n                                children: stat.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, stat.title, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"Revenue Trend\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"p-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-16 h-16 text-white/40 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60\",\n                                            children: \"Revenue analytics coming soon...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"Tool Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"p-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 bg-gradient-to-br from-green-500/20 to-blue-500/20 rounded-2xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-16 h-16 text-white/40 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60\",\n                                            children: \"Category analytics coming soon...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Recent Activity\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Export\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            {\n                                user: \"John Smith\",\n                                action: \"Listed new tool\",\n                                tool: \"DeWalt Circular Saw\",\n                                time: \"2 minutes ago\",\n                                type: \"listing\"\n                            },\n                            {\n                                user: \"Sarah Johnson\",\n                                action: \"Completed rental\",\n                                tool: \"Bosch Hammer Drill\",\n                                time: \"15 minutes ago\",\n                                type: \"rental\"\n                            },\n                            {\n                                user: \"Mike Wilson\",\n                                action: \"Left review\",\n                                tool: \"Makita Angle Grinder\",\n                                time: \"1 hour ago\",\n                                type: \"review\"\n                            },\n                            {\n                                user: \"Emma Davis\",\n                                action: \"Made payment\",\n                                tool: \"Ryobi Chainsaw\",\n                                time: \"2 hours ago\",\n                                type: \"payment\"\n                            }\n                        ].map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 p-4 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `p-2 rounded-xl ${activity.type === \"listing\" ? \"bg-blue-500/20 text-blue-400\" : activity.type === \"rental\" ? \"bg-green-500/20 text-green-400\" : activity.type === \"review\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-purple-500/20 text-purple-400\"}`,\n                                        children: [\n                                            activity.type === \"listing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 49\n                                            }, this),\n                                            activity.type === \"rental\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 48\n                                            }, this),\n                                            activity.type === \"review\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 48\n                                            }, this),\n                                            activity.type === \"payment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 49\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white font-medium\",\n                                                children: activity.user\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: [\n                                                    activity.action,\n                                                    \": \",\n                                                    activity.tool\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/40 text-sm\",\n                                        children: activity.time\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n// Placeholder components for other tabs\nfunction UsersTab() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-16 h-16 text-blue-400 mx-auto mb-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-bold text-white mb-2\",\n                children: \"User Management\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-white/60\",\n                children: \"Advanced user management features coming soon...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, this);\n}\nfunction ToolsTab() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-16 h-16 text-green-400 mx-auto mb-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-bold text-white mb-2\",\n                children: \"Tool Management\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-white/60\",\n                children: \"Comprehensive tool management dashboard coming soon...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\nfunction AnalyticsTab() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-16 h-16 text-purple-400 mx-auto mb-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-bold text-white mb-2\",\n                children: \"Advanced Analytics\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-white/60\",\n                children: \"AI-powered analytics and insights coming soon...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\nfunction SettingsTab() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Calendar_DollarSign_Settings_Shield_Star_TrendingUp_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-bold text-white mb-2\",\n                children: \"System Settings\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-white/60\",\n                children: \"Platform configuration and settings coming soon...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../../../node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers,useAuth,useWebSocket,useTheme auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst WebSocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Auth Provider\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for existing session\n        const checkAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"toolsync_token\");\n                if (token) {\n                    // Validate token and get user data\n                    const response = await fetch(\"/api/v1/auth/me\", {\n                        headers: {\n                            Authorization: `Bearer ${token}`\n                        }\n                    });\n                    if (response.ok) {\n                        const userData = await response.json();\n                        setUser(userData);\n                    } else {\n                        localStorage.removeItem(\"toolsync_token\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/v1/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (response.ok) {\n                const { token, user: userData } = await response.json();\n                localStorage.setItem(\"toolsync_token\", token);\n                setUser(userData);\n            } else {\n                throw new Error(\"Login failed\");\n            }\n        } catch (error) {\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"toolsync_token\");\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            login,\n            logout,\n            loading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n// WebSocket Provider\nfunction WebSocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const wsUrl = \"ws://localhost:6001\" || 0;\n        const ws = new WebSocket(`${wsUrl}/ws/notifications/`);\n        ws.onopen = ()=>{\n            setIsConnected(true);\n            console.log(\"WebSocket connected\");\n        };\n        ws.onclose = ()=>{\n            setIsConnected(false);\n            console.log(\"WebSocket disconnected\");\n        };\n        ws.onerror = (error)=>{\n            console.error(\"WebSocket error:\", error);\n        };\n        setSocket(ws);\n        return ()=>{\n            ws.close();\n        };\n    }, []);\n    const sendMessage = (message)=>{\n        if (socket && isConnected) {\n            socket.send(JSON.stringify(message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebSocketContext.Provider, {\n        value: {\n            socket,\n            isConnected,\n            sendMessage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n// Main Providers Component\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"dark\",\n        enableSystem: true,\n        disableTransitionOnChange: false,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebSocketProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n// Custom hooks\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction useWebSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WebSocketContext);\n    if (context === undefined) {\n        throw new Error(\"useWebSocket must be used within a WebSocketProvider\");\n    }\n    return context;\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"143a7d582437\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90b29sc3luYy1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz82Y2ZiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTQzYTdkNTgyNDM3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/admin/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains-mono\",\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n    description: \"Experience the future of tool rental with AI-powered recommendations, real-time availability, and seamless P2P sharing.\",\n    keywords: [\n        \"tool rental\",\n        \"equipment sharing\",\n        \"P2P rental\",\n        \"construction tools\",\n        \"AI-powered\"\n    ],\n    authors: [\n        {\n            name: \"ToolSync Team\"\n        }\n    ],\n    creator: \"ToolSync\",\n    publisher: \"ToolSync\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://toolsync.com\"),\n    openGraph: {\n        title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n        description: \"Experience the future of tool rental with AI-powered recommendations and seamless sharing.\",\n        url: \"https://toolsync.com\",\n        siteName: \"ToolSync\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"ToolSync Platform\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n        description: \"Experience the future of tool rental with AI-powered recommendations.\",\n        images: [\n            \"/twitter-image.jpg\"\n        ],\n        creator: \"@toolsync\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"ToolSync\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-config\",\n                        content: \"/browserconfig.xml\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `\n          ${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} \n          ${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default().variable)} \n          ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} \n          font-sans \n          antialiased \n          bg-gradient-to-br \n          from-slate-900 \n          via-purple-900 \n          to-slate-900 \n          min-h-screen\n          overflow-x-hidden\n        `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-mesh opacity-20 animate-gradient\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/20 rounded-full blur-3xl animate-float\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-3/4 right-1/4 w-96 h-96 bg-secondary-500/20 rounded-full blur-3xl animate-float\",\n                                    style: {\n                                        animationDelay: \"1s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/2 left-1/2 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-float\",\n                                    style: {\n                                        animationDelay: \"2s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-10\",\n                                    style: {\n                                        backgroundImage: `\n                  linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n                  linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n                `,\n                                        backgroundSize: \"50px 50px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"rgba(0, 0, 0, 0.8)\",\n                                    color: \"#fff\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                    borderRadius: \"12px\",\n                                    backdropFilter: \"blur(20px)\"\n                                },\n                                success: {\n                                    iconTheme: {\n                                        primary: \"#10b981\",\n                                        secondary: \"#fff\"\n                                    }\n                                },\n                                error: {\n                                    iconTheme: {\n                                        primary: \"#ef4444\",\n                                        secondary: \"#fff\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQU1NQTtBQU1BQztBQU9BQztBQWpCZ0I7QUFDaUI7QUFDRTtBQXFCbEMsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO1FBQUM7UUFBZTtRQUFxQjtRQUFjO1FBQXNCO0tBQWE7SUFDaEdDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQWdCO0tBQUU7SUFDcENDLFNBQVM7SUFDVEMsV0FBVztJQUNYQyxpQkFBaUI7UUFDZkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFdBQVc7SUFDYjtJQUNBQyxjQUFjLElBQUlDLElBQUk7SUFDdEJDLFdBQVc7UUFDVGIsT0FBTztRQUNQQyxhQUFhO1FBQ2JhLEtBQUs7UUFDTEMsVUFBVTtRQUNWQyxRQUFRO1lBQ047Z0JBQ0VGLEtBQUs7Z0JBQ0xHLE9BQU87Z0JBQ1BDLFFBQVE7Z0JBQ1JDLEtBQUs7WUFDUDtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsTUFBTTtJQUNSO0lBQ0FDLFNBQVM7UUFDUEMsTUFBTTtRQUNOdkIsT0FBTztRQUNQQyxhQUFhO1FBQ2JlLFFBQVE7WUFBQztTQUFxQjtRQUM5QlgsU0FBUztJQUNYO0lBQ0FtQixRQUFRO1FBQ05DLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxXQUFXO1lBQ1RGLE9BQU87WUFDUEMsUUFBUTtZQUNSLHFCQUFxQixDQUFDO1lBQ3RCLHFCQUFxQjtZQUNyQixlQUFlLENBQUM7UUFDbEI7SUFDRjtJQUNBRSxjQUFjO1FBQ1pDLFFBQVE7SUFDVjtBQUNGLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyx3QkFBd0I7OzBCQUN0Qyw4REFBQ0M7O2tDQUNDLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7O2tDQUN0Qiw4REFBQ0Y7d0JBQUtDLEtBQUk7d0JBQW1CQyxNQUFLOzs7Ozs7a0NBQ2xDLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBV0MsTUFBSzs7Ozs7O2tDQUMxQiw4REFBQ0M7d0JBQUtuQyxNQUFLO3dCQUFjb0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Q7d0JBQUtuQyxNQUFLO3dCQUErQm9DLFNBQVE7Ozs7OztrQ0FDbEQsOERBQUNEO3dCQUFLbkMsTUFBSzt3QkFBd0NvQyxTQUFROzs7Ozs7a0NBQzNELDhEQUFDRDt3QkFBS25DLE1BQUs7d0JBQTZCb0MsU0FBUTs7Ozs7O2tDQUNoRCw4REFBQ0Q7d0JBQUtuQyxNQUFLO3dCQUF5Qm9DLFNBQVE7Ozs7OztrQ0FDNUMsOERBQUNEO3dCQUFLbkMsTUFBSzt3QkFBMEJvQyxTQUFROzs7Ozs7a0NBQzdDLDhEQUFDRDt3QkFBS25DLE1BQUs7d0JBQXVCb0MsU0FBUTs7Ozs7Ozs7Ozs7OzBCQUU1Qyw4REFBQ0M7Z0JBQ0NDLFdBQVcsQ0FBQztVQUNWLEVBQUVoRCwyTEFBYyxDQUFDO1VBQ2pCLEVBQUVDLG9PQUFnQixDQUFDO1VBQ25CLEVBQUVDLHFOQUFzQixDQUFDOzs7Ozs7Ozs7UUFTM0IsQ0FBQzswQkFFRCw0RUFBQ0MsaURBQVNBOztzQ0FFUiw4REFBQytDOzRCQUFJRixXQUFVOzs4Q0FFYiw4REFBQ0U7b0NBQUlGLFdBQVU7Ozs7Ozs4Q0FHZiw4REFBQ0U7b0NBQUlGLFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQ0U7b0NBQUlGLFdBQVU7b0NBQStGRyxPQUFPO3dDQUFFQyxnQkFBZ0I7b0NBQUs7Ozs7Ozs4Q0FDNUksOERBQUNGO29DQUFJRixXQUFVO29DQUEyRkcsT0FBTzt3Q0FBRUMsZ0JBQWdCO29DQUFLOzs7Ozs7OENBR3hJLDhEQUFDRjtvQ0FDQ0YsV0FBVTtvQ0FDVkcsT0FBTzt3Q0FDTEUsaUJBQWlCLENBQUM7OztnQkFHbEIsQ0FBQzt3Q0FDREMsZ0JBQWdCO29DQUNsQjs7Ozs7Ozs7Ozs7O3NDQUtKLDhEQUFDSjs0QkFBSUYsV0FBVTtzQ0FDWlg7Ozs7OztzQ0FJSCw4REFBQ2pDLG9EQUFPQTs0QkFDTm1ELFVBQVM7NEJBQ1RDLGNBQWM7Z0NBQ1pDLFVBQVU7Z0NBQ1ZOLE9BQU87b0NBQ0xPLFlBQVk7b0NBQ1pDLE9BQU87b0NBQ1BDLFFBQVE7b0NBQ1JDLGNBQWM7b0NBQ2RDLGdCQUFnQjtnQ0FDbEI7Z0NBQ0FDLFNBQVM7b0NBQ1BDLFdBQVc7d0NBQ1RDLFNBQVM7d0NBQ1RDLFdBQVc7b0NBQ2I7Z0NBQ0Y7Z0NBQ0FDLE9BQU87b0NBQ0xILFdBQVc7d0NBQ1RDLFNBQVM7d0NBQ1RDLFdBQVc7b0NBQ2I7Z0NBQ0Y7NEJBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTVoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90b29sc3luYy1mcm9udGVuZC8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciwgUG9wcGlucywgSmV0QnJhaW5zX01vbm8gfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSAnLi9wcm92aWRlcnMnXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAncmVhY3QtaG90LXRvYXN0J1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgXG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtaW50ZXInLFxuICBkaXNwbGF5OiAnc3dhcCcsXG59KVxuXG5jb25zdCBwb3BwaW5zID0gUG9wcGlucyh7IFxuICBzdWJzZXRzOiBbJ2xhdGluJ10sXG4gIHdlaWdodDogWyczMDAnLCAnNDAwJywgJzUwMCcsICc2MDAnLCAnNzAwJywgJzgwMCcsICc5MDAnXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtcG9wcGlucycsXG4gIGRpc3BsYXk6ICdzd2FwJyxcbn0pXG5cbmNvbnN0IGpldGJyYWluc01vbm8gPSBKZXRCcmFpbnNfTW9ubyh7XG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtamV0YnJhaW5zLW1vbm8nLFxuICBkaXNwbGF5OiAnc3dhcCcsXG59KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1Rvb2xTeW5jIC0gVWx0cmEtTW9kZXJuIFRvb2wgUmVudGFsIFBsYXRmb3JtJyxcbiAgZGVzY3JpcHRpb246ICdFeHBlcmllbmNlIHRoZSBmdXR1cmUgb2YgdG9vbCByZW50YWwgd2l0aCBBSS1wb3dlcmVkIHJlY29tbWVuZGF0aW9ucywgcmVhbC10aW1lIGF2YWlsYWJpbGl0eSwgYW5kIHNlYW1sZXNzIFAyUCBzaGFyaW5nLicsXG4gIGtleXdvcmRzOiBbJ3Rvb2wgcmVudGFsJywgJ2VxdWlwbWVudCBzaGFyaW5nJywgJ1AyUCByZW50YWwnLCAnY29uc3RydWN0aW9uIHRvb2xzJywgJ0FJLXBvd2VyZWQnXSxcbiAgYXV0aG9yczogW3sgbmFtZTogJ1Rvb2xTeW5jIFRlYW0nIH1dLFxuICBjcmVhdG9yOiAnVG9vbFN5bmMnLFxuICBwdWJsaXNoZXI6ICdUb29sU3luYycsXG4gIGZvcm1hdERldGVjdGlvbjoge1xuICAgIGVtYWlsOiBmYWxzZSxcbiAgICBhZGRyZXNzOiBmYWxzZSxcbiAgICB0ZWxlcGhvbmU6IGZhbHNlLFxuICB9LFxuICBtZXRhZGF0YUJhc2U6IG5ldyBVUkwoJ2h0dHBzOi8vdG9vbHN5bmMuY29tJyksXG4gIG9wZW5HcmFwaDoge1xuICAgIHRpdGxlOiAnVG9vbFN5bmMgLSBVbHRyYS1Nb2Rlcm4gVG9vbCBSZW50YWwgUGxhdGZvcm0nLFxuICAgIGRlc2NyaXB0aW9uOiAnRXhwZXJpZW5jZSB0aGUgZnV0dXJlIG9mIHRvb2wgcmVudGFsIHdpdGggQUktcG93ZXJlZCByZWNvbW1lbmRhdGlvbnMgYW5kIHNlYW1sZXNzIHNoYXJpbmcuJyxcbiAgICB1cmw6ICdodHRwczovL3Rvb2xzeW5jLmNvbScsXG4gICAgc2l0ZU5hbWU6ICdUb29sU3luYycsXG4gICAgaW1hZ2VzOiBbXG4gICAgICB7XG4gICAgICAgIHVybDogJy9vZy1pbWFnZS5qcGcnLFxuICAgICAgICB3aWR0aDogMTIwMCxcbiAgICAgICAgaGVpZ2h0OiA2MzAsXG4gICAgICAgIGFsdDogJ1Rvb2xTeW5jIFBsYXRmb3JtJyxcbiAgICAgIH0sXG4gICAgXSxcbiAgICBsb2NhbGU6ICdlbl9VUycsXG4gICAgdHlwZTogJ3dlYnNpdGUnLFxuICB9LFxuICB0d2l0dGVyOiB7XG4gICAgY2FyZDogJ3N1bW1hcnlfbGFyZ2VfaW1hZ2UnLFxuICAgIHRpdGxlOiAnVG9vbFN5bmMgLSBVbHRyYS1Nb2Rlcm4gVG9vbCBSZW50YWwgUGxhdGZvcm0nLFxuICAgIGRlc2NyaXB0aW9uOiAnRXhwZXJpZW5jZSB0aGUgZnV0dXJlIG9mIHRvb2wgcmVudGFsIHdpdGggQUktcG93ZXJlZCByZWNvbW1lbmRhdGlvbnMuJyxcbiAgICBpbWFnZXM6IFsnL3R3aXR0ZXItaW1hZ2UuanBnJ10sXG4gICAgY3JlYXRvcjogJ0B0b29sc3luYycsXG4gIH0sXG4gIHJvYm90czoge1xuICAgIGluZGV4OiB0cnVlLFxuICAgIGZvbGxvdzogdHJ1ZSxcbiAgICBnb29nbGVCb3Q6IHtcbiAgICAgIGluZGV4OiB0cnVlLFxuICAgICAgZm9sbG93OiB0cnVlLFxuICAgICAgJ21heC12aWRlby1wcmV2aWV3JzogLTEsXG4gICAgICAnbWF4LWltYWdlLXByZXZpZXcnOiAnbGFyZ2UnLFxuICAgICAgJ21heC1zbmlwcGV0JzogLTEsXG4gICAgfSxcbiAgfSxcbiAgdmVyaWZpY2F0aW9uOiB7XG4gICAgZ29vZ2xlOiAneW91ci1nb29nbGUtdmVyaWZpY2F0aW9uLWNvZGUnLFxuICB9LFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+XG4gICAgICA8aGVhZD5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgICA8bGluayByZWw9XCJhcHBsZS10b3VjaC1pY29uXCIgaHJlZj1cIi9hcHBsZS10b3VjaC1pY29uLnBuZ1wiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cIm1hbmlmZXN0XCIgaHJlZj1cIi9tYW5pZmVzdC5qc29uXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInRoZW1lLWNvbG9yXCIgY29udGVudD1cIiMwZWE1ZTlcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtY2FwYWJsZVwiIGNvbnRlbnQ9XCJ5ZXNcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtc3RhdHVzLWJhci1zdHlsZVwiIGNvbnRlbnQ9XCJkZWZhdWx0XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLXRpdGxlXCIgY29udGVudD1cIlRvb2xTeW5jXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1vYmlsZS13ZWItYXBwLWNhcGFibGVcIiBjb250ZW50PVwieWVzXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1zYXBwbGljYXRpb24tVGlsZUNvbG9yXCIgY29udGVudD1cIiMwZWE1ZTlcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwibXNhcHBsaWNhdGlvbi1jb25maWdcIiBjb250ZW50PVwiL2Jyb3dzZXJjb25maWcueG1sXCIgLz5cbiAgICAgIDwvaGVhZD5cbiAgICAgIDxib2R5IFxuICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAke2ludGVyLnZhcmlhYmxlfSBcbiAgICAgICAgICAke3BvcHBpbnMudmFyaWFibGV9IFxuICAgICAgICAgICR7amV0YnJhaW5zTW9uby52YXJpYWJsZX0gXG4gICAgICAgICAgZm9udC1zYW5zIFxuICAgICAgICAgIGFudGlhbGlhc2VkIFxuICAgICAgICAgIGJnLWdyYWRpZW50LXRvLWJyIFxuICAgICAgICAgIGZyb20tc2xhdGUtOTAwIFxuICAgICAgICAgIHZpYS1wdXJwbGUtOTAwIFxuICAgICAgICAgIHRvLXNsYXRlLTkwMCBcbiAgICAgICAgICBtaW4taC1zY3JlZW5cbiAgICAgICAgICBvdmVyZmxvdy14LWhpZGRlblxuICAgICAgICBgfVxuICAgICAgPlxuICAgICAgICA8UHJvdmlkZXJzPlxuICAgICAgICAgIHsvKiBCYWNrZ3JvdW5kIEVmZmVjdHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIG92ZXJmbG93LWhpZGRlbiBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICB7LyogQW5pbWF0ZWQgZ3JhZGllbnQgbWVzaCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1tZXNoIG9wYWNpdHktMjAgYW5pbWF0ZS1ncmFkaWVudFwiIC8+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBGbG9hdGluZyBvcmJzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMS80IGxlZnQtMS80IHctOTYgaC05NiBiZy1wcmltYXJ5LTUwMC8yMCByb3VuZGVkLWZ1bGwgYmx1ci0zeGwgYW5pbWF0ZS1mbG9hdFwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0zLzQgcmlnaHQtMS80IHctOTYgaC05NiBiZy1zZWNvbmRhcnktNTAwLzIwIHJvdW5kZWQtZnVsbCBibHVyLTN4bCBhbmltYXRlLWZsb2F0XCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcxcycgfX0gLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEvMiBsZWZ0LTEvMiB3LTk2IGgtOTYgYmctYWNjZW50LTUwMC8yMCByb3VuZGVkLWZ1bGwgYmx1ci0zeGwgYW5pbWF0ZS1mbG9hdFwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMnMnIH19IC8+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBHcmlkIHBhdHRlcm4gKi99XG4gICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktMTBcIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYFxuICAgICAgICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KHJnYmEoMjU1LDI1NSwyNTUsMC4xKSAxcHgsIHRyYW5zcGFyZW50IDFweCksXG4gICAgICAgICAgICAgICAgICBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHJnYmEoMjU1LDI1NSwyNTUsMC4xKSAxcHgsIHRyYW5zcGFyZW50IDFweClcbiAgICAgICAgICAgICAgICBgLFxuICAgICAgICAgICAgICAgIGJhY2tncm91bmRTaXplOiAnNTBweCA1MHB4J1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogVG9hc3QgTm90aWZpY2F0aW9ucyAqL31cbiAgICAgICAgICA8VG9hc3RlclxuICAgICAgICAgICAgcG9zaXRpb249XCJ0b3AtcmlnaHRcIlxuICAgICAgICAgICAgdG9hc3RPcHRpb25zPXt7XG4gICAgICAgICAgICAgIGR1cmF0aW9uOiA0MDAwLFxuICAgICAgICAgICAgICBzdHlsZToge1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsIDAsIDAsIDAuOCknLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKScsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDIwcHgpJyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgc3VjY2Vzczoge1xuICAgICAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICAgICAgcHJpbWFyeTogJyMxMGI5ODEnLFxuICAgICAgICAgICAgICAgICAgc2Vjb25kYXJ5OiAnI2ZmZicsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgZXJyb3I6IHtcbiAgICAgICAgICAgICAgICBpY29uVGhlbWU6IHtcbiAgICAgICAgICAgICAgICAgIHByaW1hcnk6ICcjZWY0NDQ0JyxcbiAgICAgICAgICAgICAgICAgIHNlY29uZGFyeTogJyNmZmYnLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwicG9wcGlucyIsImpldGJyYWluc01vbm8iLCJQcm92aWRlcnMiLCJUb2FzdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsImNyZWF0b3IiLCJwdWJsaXNoZXIiLCJmb3JtYXREZXRlY3Rpb24iLCJlbWFpbCIsImFkZHJlc3MiLCJ0ZWxlcGhvbmUiLCJtZXRhZGF0YUJhc2UiLCJVUkwiLCJvcGVuR3JhcGgiLCJ1cmwiLCJzaXRlTmFtZSIsImltYWdlcyIsIndpZHRoIiwiaGVpZ2h0IiwiYWx0IiwibG9jYWxlIiwidHlwZSIsInR3aXR0ZXIiLCJjYXJkIiwicm9ib3RzIiwiaW5kZXgiLCJmb2xsb3ciLCJnb29nbGVCb3QiLCJ2ZXJpZmljYXRpb24iLCJnb29nbGUiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImhlYWQiLCJsaW5rIiwicmVsIiwiaHJlZiIsIm1ldGEiLCJjb250ZW50IiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIiwiZGl2Iiwic3R5bGUiLCJhbmltYXRpb25EZWxheSIsImJhY2tncm91bmRJbWFnZSIsImJhY2tncm91bmRTaXplIiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiIsImJhY2tncm91bmQiLCJjb2xvciIsImJvcmRlciIsImJvcmRlclJhZGl1cyIsImJhY2tkcm9wRmlsdGVyIiwic3VjY2VzcyIsImljb25UaGVtZSIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useTheme: () => (/* binding */ e3),
/* harmony export */   useWebSocket: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#Providers`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useWebSocket`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();