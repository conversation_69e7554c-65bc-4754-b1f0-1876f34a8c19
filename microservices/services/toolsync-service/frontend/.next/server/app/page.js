/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5YW50YXUlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZiaWRfYmVlc19mdWxsX3Byb2plY3QlMkZtaWNyb3NlcnZpY2VzJTJGc2VydmljZXMlMkZ0b29sc3luYy1zZXJ2aWNlJTJGZnJvbnRlbmQlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXlKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdG9vbHN5bmMtZnJvbnRlbmQvPzY5YTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYnJ5YW50YXUvRG9jdW1lbnRzL0dpdEh1Yi9iaWRfYmVlc19mdWxsX3Byb2plY3QvbWljcm9zZXJ2aWNlcy9zZXJ2aWNlcy90b29sc3luYy1zZXJ2aWNlL2Zyb250ZW5kL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,MapPin,Play,Shield,Sparkles,Star,Users,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Hero Section Component\nfunction HeroSection() {\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const slides = [\n        {\n            title: \"Rent Tools\",\n            subtitle: \"Instantly\",\n            description: \"Access thousands of professional tools in your area with AI-powered matching\",\n            color: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            title: \"Share Tools\",\n            subtitle: \"Profitably\",\n            description: \"Turn your unused tools into passive income with our P2P platform\",\n            color: \"from-purple-500 to-pink-500\"\n        },\n        {\n            title: \"Build Projects\",\n            subtitle: \"Efficiently\",\n            description: \"Complete any project with the right tools at the right time\",\n            color: \"from-green-500 to-emerald-500\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentSlide((prev)=>(prev + 1) % slides.length);\n        }, 4000);\n        return ()=>clearInterval(timer);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 1.1\n                        },\n                        animate: {\n                            opacity: 0.3,\n                            scale: 1\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        transition: {\n                            duration: 1\n                        },\n                        className: `absolute inset-0 bg-gradient-to-br ${slides[currentSlide].color}`\n                    }, currentSlide, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex items-center justify-center space-x-3\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 300\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-12 h-12 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            className: \"absolute inset-0 bg-white/20 rounded-full blur-xl\",\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.2,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 2,\n                                                repeat: Infinity\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white font-display\",\n                                    children: [\n                                        \"Tool\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gradient\",\n                                            children: \"Sync\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-32 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30,\n                                        rotateX: -90\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0,\n                                        rotateX: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        y: -30,\n                                        rotateX: 90\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-6xl md:text-8xl font-black text-white mb-4\",\n                                            children: [\n                                                slides[currentSlide].title,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block text-gradient text-glow\",\n                                                    children: slides[currentSlide].subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl md:text-2xl text-white/80 max-w-2xl mx-auto\",\n                                            children: slides[currentSlide].description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, currentSlide, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.4,\n                                duration: 0.6\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        className: \"group relative px-8 py-4 bg-white text-black font-semibold rounded-2xl overflow-hidden button-glow\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Enter Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    className: \"group flex items-center space-x-2 px-8 py-4 glass rounded-2xl text-white font-semibold glass-hover\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Watch Demo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.6,\n                                duration: 0.6\n                            },\n                            children: [\n                                {\n                                    label: \"Tools Available\",\n                                    value: \"10K+\",\n                                    icon: _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                },\n                                {\n                                    label: \"Active Users\",\n                                    value: \"5K+\",\n                                    icon: _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                },\n                                {\n                                    label: \"Cities\",\n                                    value: \"50+\",\n                                    icon: _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                },\n                                {\n                                    label: \"Avg Rating\",\n                                    value: \"4.9\",\n                                    icon: _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                }\n                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"glass rounded-2xl p-6 text-center\",\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -5\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"w-8 h-8 text-white mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-white mb-1\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, stat.label, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2\",\n                children: slides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setCurrentSlide(index),\n                        \"aria-label\": `Go to slide ${index + 1}`,\n                        className: `w-3 h-3 rounded-full transition-all duration-300 ${index === currentSlide ? \"bg-white scale-125\" : \"bg-white/30 hover:bg-white/50\"}`\n                    }, index, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n// Features Section\nfunction FeaturesSection() {\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"AI-Powered Matching\",\n            description: \"Our advanced AI finds the perfect tools for your project needs\",\n            color: \"text-yellow-400\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Secure Transactions\",\n            description: \"End-to-end encryption and secure payment processing\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Real-time Availability\",\n            description: \"Live updates on tool availability and instant booking\",\n            color: \"text-blue-400\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"Dynamic Pricing\",\n            description: \"AI-optimized pricing for maximum value and earnings\",\n            color: \"text-purple-400\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl font-bold text-white mb-4\",\n                            children: [\n                                \"Why Choose \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient\",\n                                    children: \"ToolSync\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 24\n                                }, this),\n                                \"?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/60 max-w-2xl mx-auto\",\n                            children: \"Experience the future of tool rental with cutting-edge technology\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"glass rounded-3xl p-8 text-center card-hover\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1,\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            whileHover: {\n                                y: -10\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `inline-flex p-4 rounded-2xl bg-black/20 mb-6 ${feature.color}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"w-8 h-8\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-4\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, feature.title, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n// Main Page Component\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesSection, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"fixed bottom-8 right-8 z-50\",\n                initial: {\n                    scale: 0,\n                    rotate: -180\n                },\n                animate: {\n                    scale: 1,\n                    rotate: 0\n                },\n                transition: {\n                    delay: 1,\n                    type: \"spring\",\n                    stiffness: 200\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: \"/dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                        className: \"w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-white shadow-2xl neon-glow\",\n                        whileHover: {\n                            scale: 1.1,\n                            rotate: 360\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 300\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_MapPin_Play_Shield_Sparkles_Star_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"w-8 h-8\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../../../node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers,useAuth,useWebSocket,useTheme auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst WebSocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Auth Provider\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for existing session\n        const checkAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"toolsync_token\");\n                if (token) {\n                    // Validate token and get user data\n                    const response = await fetch(\"/api/v1/auth/me\", {\n                        headers: {\n                            Authorization: `Bearer ${token}`\n                        }\n                    });\n                    if (response.ok) {\n                        const userData = await response.json();\n                        setUser(userData);\n                    } else {\n                        localStorage.removeItem(\"toolsync_token\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/v1/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (response.ok) {\n                const { token, user: userData } = await response.json();\n                localStorage.setItem(\"toolsync_token\", token);\n                setUser(userData);\n            } else {\n                throw new Error(\"Login failed\");\n            }\n        } catch (error) {\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"toolsync_token\");\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            login,\n            logout,\n            loading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n// WebSocket Provider\nfunction WebSocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const wsUrl = \"ws://localhost:6001\" || 0;\n        const ws = new WebSocket(`${wsUrl}/ws/notifications/`);\n        ws.onopen = ()=>{\n            setIsConnected(true);\n            console.log(\"WebSocket connected\");\n        };\n        ws.onclose = ()=>{\n            setIsConnected(false);\n            console.log(\"WebSocket disconnected\");\n        };\n        ws.onerror = (error)=>{\n            console.error(\"WebSocket error:\", error);\n        };\n        setSocket(ws);\n        return ()=>{\n            ws.close();\n        };\n    }, []);\n    const sendMessage = (message)=>{\n        if (socket && isConnected) {\n            socket.send(JSON.stringify(message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebSocketContext.Provider, {\n        value: {\n            socket,\n            isConnected,\n            sendMessage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n// Main Providers Component\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"dark\",\n        enableSystem: true,\n        disableTransitionOnChange: false,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebSocketProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n// Custom hooks\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction useWebSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WebSocketContext);\n    if (context === undefined) {\n        throw new Error(\"useWebSocket must be used within a WebSocketProvider\");\n    }\n    return context;\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"143a7d582437\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90b29sc3luYy1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz82Y2ZiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTQzYTdkNTgyNDM3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains-mono\",\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n    description: \"Experience the future of tool rental with AI-powered recommendations, real-time availability, and seamless P2P sharing.\",\n    keywords: [\n        \"tool rental\",\n        \"equipment sharing\",\n        \"P2P rental\",\n        \"construction tools\",\n        \"AI-powered\"\n    ],\n    authors: [\n        {\n            name: \"ToolSync Team\"\n        }\n    ],\n    creator: \"ToolSync\",\n    publisher: \"ToolSync\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://toolsync.com\"),\n    openGraph: {\n        title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n        description: \"Experience the future of tool rental with AI-powered recommendations and seamless sharing.\",\n        url: \"https://toolsync.com\",\n        siteName: \"ToolSync\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"ToolSync Platform\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"ToolSync - Ultra-Modern Tool Rental Platform\",\n        description: \"Experience the future of tool rental with AI-powered recommendations.\",\n        images: [\n            \"/twitter-image.jpg\"\n        ],\n        creator: \"@toolsync\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"ToolSync\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-config\",\n                        content: \"/browserconfig.xml\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `\n          ${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} \n          ${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default().variable)} \n          ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} \n          font-sans \n          antialiased \n          bg-gradient-to-br \n          from-slate-900 \n          via-purple-900 \n          to-slate-900 \n          min-h-screen\n          overflow-x-hidden\n        `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-mesh opacity-20 animate-gradient\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/20 rounded-full blur-3xl animate-float\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-3/4 right-1/4 w-96 h-96 bg-secondary-500/20 rounded-full blur-3xl animate-float\",\n                                    style: {\n                                        animationDelay: \"1s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/2 left-1/2 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-float\",\n                                    style: {\n                                        animationDelay: \"2s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-10\",\n                                    style: {\n                                        backgroundImage: `\n                  linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n                  linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n                `,\n                                        backgroundSize: \"50px 50px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"rgba(0, 0, 0, 0.8)\",\n                                    color: \"#fff\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                    borderRadius: \"12px\",\n                                    backdropFilter: \"blur(20px)\"\n                                },\n                                success: {\n                                    iconTheme: {\n                                        primary: \"#10b981\",\n                                        secondary: \"#fff\"\n                                    }\n                                },\n                                error: {\n                                    iconTheme: {\n                                        primary: \"#ef4444\",\n                                        secondary: \"#fff\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/layout.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useTheme: () => (/* binding */ e3),
/* harmony export */   useWebSocket: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#Providers`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useWebSocket`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/bid_bees_full_project/microservices/services/toolsync-service/frontend/app/providers.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/framer-motion"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbryantau%2FDocuments%2FGitHub%2Fbid_bees_full_project%2Fmicroservices%2Fservices%2Ftoolsync-service%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();