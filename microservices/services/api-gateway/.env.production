# Environment
NODE_ENV=production
PORT=8080

# Database
REDIS_URL=redis://:@redis:6379

# JWT Configuration
JWT_SECRET=
JWT_REFRESH_SECRET=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Kafka
KAFKA_BROKERS=kafka:9092
KAFKA_CLIENT_ID=api-gateway

# Service Discovery
SERVICE_NAME=api-gateway
SERVICE_VERSION=1.0.0

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9091

# CORS - Updated to include CloudFront
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net,https://bidbees.com

# Microservices URLs (Internal communication)
AUTH_SERVICE_URL=http://auth-service:3001
USER_SERVICE_URL=http://user-service:3002
TENDER_SERVICE_URL=http://tender-service:3003
PAYMENT_SERVICE_URL=http://payment-service:3006
COURIER_SERVICE_URL=http://courier-service:3007
ML_SERVICE_URL=http://ml-service:3008
DOCLING_SERVICE_URL=http://docling-processor:3009
NOTIFICATION_SERVICE_URL=http://notification-service:3004
ANALYTICS_SERVICE_URL=http://analytics-service:3005
BIDDING_SERVICE_URL=http://bidding-service:3010
SUPPLIER_SERVICE_URL=http://supplier-service:3011
TRANSPORT_SERVICE_URL=http://transport-service:3012
DOCUMENT_SERVICE_URL=http://document-service:3013
MAP_SERVICE_URL=http://map-service:3014
BEE_TASKS_SERVICE_URL=http://bee-tasks-service:3015
QUEENBEE_SERVICE_URL=http://queenbee-anchor-service:3016

# Load Balancing
ENABLE_LOAD_BALANCING=true
HEALTH_CHECK_INTERVAL=30000
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60000

# Request Timeout
REQUEST_TIMEOUT=30000

# Logging
LOG_LEVEL=info

# API Documentation
ENABLE_SWAGGER=true
SWAGGER_TITLE=BidBeez API Gateway
SWAGGER_DESCRIPTION=Unified API Gateway for BidBeez Microservices

# Security
ENABLE_API_KEY_AUTH=false
API_KEY_HEADER=X-API-Key

# Caching
ENABLE_RESPONSE_CACHING=true
CACHE_TTL=300

# Payment Gateway Configuration
PAYFAST_MERCHANT_ID=your_payfast_merchant_id_here
PAYFAST_MERCHANT_KEY=your_payfast_merchant_key_here

# CloudFront Configuration
CLOUDFRONT_DISTRIBUTION_ID=your_distribution_id_here
CLOUDFRONT_DOMAIN=d58ser5n68qmv.cloudfront.net