# Server Configuration
PORT=3003
NODE_ENV=development

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Database Configuration (Supabase)
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=bee-tasks-service
KAFKA_GROUP_ID=bee-tasks-group

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_KEYS=1000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000

# File Upload
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Business Logic
DEFAULT_TASK_RADIUS=50
MAX_TASK_RADIUS=200
COMMISSION_RATE=0.15
URGENT_TASK_MULTIPLIER=1.5
MAX_TASKS_PER_BEE=5
TASK_TIMEOUT_HOURS=24

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# API Keys (for service-to-service communication)
SERVICE_API_KEY=your_service_api_key

# External Services
TRANSPORT_MODULE_API=http://localhost:3004
NOTIFICATION_SERVICE_API=http://localhost:3005
PAYMENT_SERVICE_API=http://localhost:3006
CORS_ORIGIN=https://d58ser5n68qmv.cloudfront.net
