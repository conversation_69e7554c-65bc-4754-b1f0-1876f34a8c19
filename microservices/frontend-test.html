<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez Backend-Frontend Connectivity Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .config-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐝 BidBeez Backend-Frontend Connectivity Test</h1>
        
        <div class="config-info">
            <h3>Configuration</h3>
            <p><strong>Frontend URL:</strong> <span id="frontend-url">https://d58ser5n68qmv.cloudfront.net</span></p>
            <p><strong>Backend API:</strong> <span id="backend-url">http://localhost:3000</span></p>
            <p><strong>Test Time:</strong> <span id="test-time"></span></p>
        </div>

        <div class="test-section">
            <h3>🔍 Connectivity Tests</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="testHealth()">Test Health</button>
            <button onclick="testAPI()">Test API</button>
            <button onclick="testDatabase()">Test Database</button>
            <button onclick="testEnvironment()">Test Environment</button>
            
            <div id="health-result" class="test-result"></div>
            <div id="api-result" class="test-result"></div>
            <div id="database-result" class="test-result"></div>
            <div id="environment-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="summary-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔧 Raw Response Data</h3>
            <pre id="raw-data"></pre>
        </div>
    </div>

    <script>
        // Configuration
        const BACKEND_URL = 'http://localhost:3000';
        const FRONTEND_URL = 'https://d58ser5n68qmv.cloudfront.net';
        
        // Update display
        document.getElementById('frontend-url').textContent = FRONTEND_URL;
        document.getElementById('backend-url').textContent = BACKEND_URL;
        document.getElementById('test-time').textContent = new Date().toLocaleString();

        let testResults = {};

        async function makeRequest(endpoint) {
            try {
                const response = await fetch(`${BACKEND_URL}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw new Error(`Network error: ${error.message}`);
            }
        }

        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `test-result ${success ? 'success' : 'error'}`;
            element.innerHTML = `
                <strong>${success ? '✅' : '❌'} ${elementId.replace('-result', '').toUpperCase()}:</strong> ${message}
                ${data ? `<br><small>${JSON.stringify(data, null, 2)}</small>` : ''}
            `;
        }

        function showLoading(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'test-result loading';
            element.innerHTML = `<strong>⏳ ${elementId.replace('-result', '').toUpperCase()}:</strong> ${message}`;
        }

        async function testHealth() {
            showLoading('health-result', 'Testing backend health...');
            try {
                const data = await makeRequest('/health');
                testResults.health = { success: true, data };
                showResult('health-result', true, `Backend is healthy (${data.service})`, data);
            } catch (error) {
                testResults.health = { success: false, error: error.message };
                showResult('health-result', false, error.message);
            }
        }

        async function testAPI() {
            showLoading('api-result', 'Testing API endpoint...');
            try {
                const data = await makeRequest('/api/test');
                testResults.api = { success: true, data };
                showResult('api-result', true, data.message, data);
            } catch (error) {
                testResults.api = { success: false, error: error.message };
                showResult('api-result', false, error.message);
            }
        }

        async function testDatabase() {
            showLoading('database-result', 'Testing database connectivity...');
            try {
                const data = await makeRequest('/api/test/database');
                testResults.database = { success: true, data };
                showResult('database-result', true, `Database connected (${data.type})`, data);
            } catch (error) {
                testResults.database = { success: false, error: error.message };
                showResult('database-result', false, error.message);
            }
        }

        async function testEnvironment() {
            showLoading('environment-result', 'Testing environment configuration...');
            try {
                const data = await makeRequest('/api/env');
                testResults.environment = { success: true, data };
                showResult('environment-result', true, `Environment configured (${data.node_env})`, data);
            } catch (error) {
                testResults.environment = { success: false, error: error.message };
                showResult('environment-result', false, error.message);
            }
        }

        async function runAllTests() {
            testResults = {};
            await testHealth();
            await testAPI();
            await testDatabase();
            await testEnvironment();
            
            // Update summary
            const successCount = Object.values(testResults).filter(r => r.success).length;
            const totalCount = Object.keys(testResults).length;
            const allPassed = successCount === totalCount;
            
            showResult('summary-result', allPassed, 
                `${successCount}/${totalCount} tests passed. ${allPassed ? 'All systems operational!' : 'Some issues detected.'}`,
                { 
                    timestamp: new Date().toISOString(),
                    frontend: FRONTEND_URL,
                    backend: BACKEND_URL,
                    results: testResults
                }
            );
            
            // Update raw data
            document.getElementById('raw-data').textContent = JSON.stringify(testResults, null, 2);
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
