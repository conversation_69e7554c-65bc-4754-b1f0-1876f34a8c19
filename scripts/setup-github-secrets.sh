#!/bin/bash

# GitHub Secrets Setup Script for BidBees CI/CD Pipeline
# This script configures all required secrets for the million-user deployment pipeline

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
REPO_OWNER="bryantau"  # Replace with actual GitHub username/org
REPO_NAME="bid_bees_full_project"

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo -e "${RED}Error: GitHub CLI (gh) is not installed.${NC}"
    echo "Please install it from: https://cli.github.com/"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    echo -e "${YELLOW}Please authenticate with GitHub CLI first:${NC}"
    echo "gh auth login"
    exit 1
fi

echo -e "${BLUE}🔐 Setting up GitHub Secrets for BidBees CI/CD Pipeline${NC}"
echo "=================================================="

# Function to set secret with confirmation
set_secret() {
    local secret_name=$1
    local secret_description=$2
    local secret_value=$3
    
    echo -e "${YELLOW}Setting secret: ${secret_name}${NC}"
    echo "Description: $secret_description"
    
    if [ -z "$secret_value" ]; then
        echo -e "${RED}Error: Secret value for $secret_name is empty${NC}"
        return 1
    fi
    
    echo "$secret_value" | gh secret set "$secret_name" --repo="$REPO_OWNER/$REPO_NAME"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Successfully set $secret_name${NC}"
    else
        echo -e "${RED}❌ Failed to set $secret_name${NC}"
        return 1
    fi
    echo ""
}

# AWS Credentials
echo -e "${BLUE}🔧 Setting AWS Credentials${NC}"
set_secret "AWS_ACCESS_KEY_ID" "AWS Access Key ID for deployment" "AKIAQ3EGUR5HXEXAMPLE"
set_secret "AWS_SECRET_ACCESS_KEY" "AWS Secret Access Key for deployment" "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
set_secret "AWS_REGION" "AWS Region for deployment" "us-east-1"
set_secret "AWS_ACCOUNT_ID" "AWS Account ID" "************"

# Supabase Configuration
echo -e "${BLUE}🗄️ Setting Supabase Configuration${NC}"
set_secret "SUPABASE_URL" "Supabase project URL" "https://uvksgkpxeyyssvdsxbts.supabase.co"
set_secret "SUPABASE_ANON_KEY" "Supabase anonymous key" "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.AF1fLlULlM-_NUJYFEL092WETAXvpKKpawUsOidHQ70"
set_secret "SUPABASE_SERVICE_KEY" "Supabase service role key" "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.i3R6lZOWC3Z60L326m20w-NrGh4Nasj9bi1qI6DipDY"

# Database Passwords
echo -e "${BLUE}🔒 Setting Database Passwords${NC}"
echo -e "${YELLOW}Please enter secure passwords for your databases:${NC}"

read -s -p "Enter PostgreSQL password: " db_password
echo ""
set_secret "DB_PASSWORD" "PostgreSQL database password" "$db_password"

read -s -p "Enter DocumentDB password: " docdb_password
echo ""
set_secret "DOCDB_PASSWORD" "DocumentDB password" "$docdb_password"

# Third-party Services
echo -e "${BLUE}🗺️ Setting Third-party Service Keys${NC}"
set_secret "MAPBOX_ACCESS_TOKEN" "Mapbox access token for map services" "pk.eyJ1IjoiYmlkYmVlcyIsImEiOiJjbWJiOGh0cXQwOXJvMmtzb2x1Ymc0NGF6In0.rdxv8HdMok6ZgmXe392Aaw"

# JWT Secret
echo -e "${BLUE}🔐 Setting JWT Secret${NC}"
jwt_secret=$(openssl rand -base64 32)
set_secret "JWT_SECRET" "JWT signing secret" "$jwt_secret"

# Optional Monitoring Keys
echo -e "${BLUE}📊 Setting Optional Monitoring Keys${NC}"
echo -e "${YELLOW}The following secrets are optional but recommended for full monitoring:${NC}"

read -p "Enter Slack Webhook URL (optional): " slack_webhook
if [ ! -z "$slack_webhook" ]; then
    set_secret "SLACK_WEBHOOK_URL" "Slack webhook for notifications" "$slack_webhook"
fi

read -p "Enter Snyk Token (optional): " snyk_token
if [ ! -z "$snyk_token" ]; then
    set_secret "SNYK_TOKEN" "Snyk token for security scanning" "$snyk_token"
fi

read -p "Enter Infracost API Key (optional): " infracost_key
if [ ! -z "$infracost_key" ]; then
    set_secret "INFRACOST_API_KEY" "Infracost API key for cost estimation" "$infracost_key"
fi

# Create environment-specific secrets
echo -e "${BLUE}🌍 Setting Environment-Specific Secrets${NC}"
environments=("staging" "production")

for env in "${environments[@]}"; do
    echo -e "${YELLOW}Setting secrets for $env environment:${NC}"
    
    # RDS Endpoints (these will be output from Terraform)
    echo -e "${YELLOW}Note: RDS and Redis endpoints will be automatically set after infrastructure deployment${NC}"
    
    # ALB ARNs (these will be output from infrastructure)
    echo -e "${YELLOW}Note: ALB ARNs will be automatically set after infrastructure deployment${NC}"
done

# Verify secrets were set
echo -e "${BLUE}🔍 Verifying secrets were set correctly...${NC}"
echo "Listing all secrets (values are hidden for security):"

gh secret list --repo="$REPO_OWNER/$REPO_NAME"

echo ""
echo -e "${GREEN}✅ GitHub Secrets Setup Complete!${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Review the GitHub Actions workflows in .github/workflows/"
echo "2. Push your code to trigger the CI/CD pipeline"
echo "3. Monitor the deployments in the GitHub Actions tab"
echo "4. Check AWS Console for infrastructure provisioning"
echo ""
echo -e "${YELLOW}⚠️ Important Notes:${NC}"
echo "• Keep your secrets secure and rotate them regularly"
echo "• Monitor your AWS costs as the infrastructure scales"
echo "• Set up proper backup strategies for production data"
echo "• Configure monitoring alerts for your team"
echo ""
echo -e "${GREEN}🚀 Your BidBees platform is ready for million-user scale deployment!${NC}"